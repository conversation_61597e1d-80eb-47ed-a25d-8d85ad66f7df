using System.Text.Json;
using System.Text.Json.Serialization;
using System.Net.Http;

namespace ShopBot.Services
{
    public class G2GStoreStatsChecker
    {
        private readonly HttpClient _httpClient;
        private const string StatsUrl = "https://sls.g2g.com/offer/seller/7670672/brands";
        private const string SearchUrl = "https://sls.g2g.com/offer/search";
        private readonly Dictionary<string, string> _gameUrlNames = new()
        {
            {"RaidShadowLegends", "raid-shadow-legends-account"},
            {"LeagueOfLegends", "league-of-legends-account"},
            {"Valorant", "valorant-account"},
            {"GenshinImpact", "genshin-impact-account"},
            {"PUBG", "playerunknowns-battlegrounds-account"},
            {"Rust", "rust-account"},
            {"EscapeFromTarkov", "escape-from-tarkov-account"},
            {"WorldOfWarcraft", "world-of-warcraft-account"},
            {"Dota2", "dota-2-account"},
            {"ApexLegends", "apex-legends-account"},
            {"CallOfDutyWarzone", "call-of-duty-warzone-account"},
            {"MobileLegends", "mobile-legends-account"},
            {"PUBGMobile", "pubg-mobile-account"},
            {"Fortnite", "fortnite-account"},
            {"Albion", "albion-online-account"}
        };

        public G2GStoreStatsChecker()
        {
            _httpClient = new HttpClient();
        }

        public async Task<GameOfferStats> GetGameOffers(string gameName, int pageSize = 100)
        {
            try
            {
                if (!_gameUrlNames.TryGetValue(gameName, out var gameUrlName))
                {
                    throw new Exception($"Nieznana gra: {gameName}");
                }

                var queryParams = new Dictionary<string, string>
                {
                    {"seo_term", gameUrlName},
                    {"sort", "recommended"},
                    {"page_size", pageSize.ToString()},
                    {"currency", "USD"},
                    {"country", "PL"},
                    {"seller", "TrustCraft"},
                    {"include_localization", "0"}
                };

                var url = SearchUrl + "?" + string.Join("&", queryParams.Select(x => $"{x.Key}={x.Value}"));
                var response = await _httpClient.GetAsync(url);
                var content = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    throw new Exception($"Błąd podczas pobierania ofert: {response.StatusCode}");
                }

                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                var searchResponse = JsonSerializer.Deserialize<SearchApiResponse>(content, options);

                if (searchResponse?.Code != 2000)
                {
                    throw new Exception($"Nieprawidłowy kod odpowiedzi API: {searchResponse?.Code}");
                }

                return new GameOfferStats
                {
                    TotalOffers = searchResponse.Payload.TotalResult,
                    PageSize = pageSize,
                    AvailableOffers = searchResponse.Payload.Results.Count,
                    Offers = searchResponse.Payload.Results.Select(o => new OfferDetails
                    {
                        OfferId = o.OfferId,
                        Title = o.Title,
                        Description = o.Description,
                        UnitPrice = o.UnitPrice,
                        DescriptionImages = o.DescriptionImages ?? new List<string>(),
                        BrandId = o.BrandId
                    }).ToList()
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[G2G Stats] Błąd podczas pobierania ofert dla {gameName}: {ex.Message}");
                throw;
            }
        }

        public async Task<StoreStats> GetStoreStats()
        {
            try
            {
                var queryParams = new Dictionary<string, string>
                {
                    {"group_by", "service"},
                    {"root_id", "5830014a-b974-45c6-9672-b51e83112fb7"},
                    {"service_id", "f6a1aba5-473a-4044-836a-8968bbab16d7"},
                    {"include_out_of_stock", "1"},
                    {"include_inactive", "0"}
                };

                var url = StatsUrl + "?" + string.Join("&", queryParams.Select(x => $"{x.Key}={x.Value}"));
                var response = await _httpClient.GetAsync(url);
                var content = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    throw new Exception($"Błąd podczas pobierania statystyk: {response.StatusCode}");
                }

                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                var apiResponse = JsonSerializer.Deserialize<G2GApiResponse>(content, options);

                if (apiResponse?.Code != 2000)
                {
                    throw new Exception($"Nieprawidłowy kod odpowiedzi API: {apiResponse?.Code}");
                }

                var result = apiResponse.Payload.Results.FirstOrDefault();
                if (result == null)
                {
                    throw new Exception("Brak wyników w odpowiedzi API");
                }

                return new StoreStats
                {
                    TotalOffers = result.TotalResult,
                    GameStats = result.Brands.Select(b => new GameStats
                    {
                        GameId = b.BrandId,
                        TotalOffers = b.TotalResult
                    }).ToList()
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[G2G Stats] Błąd podczas pobierania statystyk: {ex.Message}");
                throw;
            }
        }

        public class StoreStats
        {
            public int TotalOffers { get; set; }
            public List<GameStats> GameStats { get; set; } = new();
        }

        public class GameStats
        {
            public string GameId { get; set; }
            public int TotalOffers { get; set; }
        }

        public class GameOfferStats
        {
            public int TotalOffers { get; set; }
            public int PageSize { get; set; }
            public int AvailableOffers { get; set; }
            public List<OfferDetails> Offers { get; set; } = new();
        }

        public class OfferDetails
        {
            public string OfferId { get; set; }
            public string Title { get; set; }
            public string Description { get; set; }
            public decimal UnitPrice { get; set; }
            public List<string> DescriptionImages { get; set; } = new();
            public string BrandId { get; set; }
        }

        public class DeliverySpeedDetail
        {
            public int Min { get; set; }
            public int Max { get; set; }
            public int DeliveryTime { get; set; }
        }

        private class G2GApiResponse
        {
            [JsonPropertyName("code")]
            public int Code { get; set; }

            [JsonPropertyName("messages")]
            public List<string> Messages { get; set; }

            [JsonPropertyName("payload")]
            public PayloadData Payload { get; set; }

            [JsonPropertyName("request_id")]
            public string RequestId { get; set; }
        }

        private class PayloadData
        {
            [JsonPropertyName("results")]
            public List<ResultData> Results { get; set; }
        }

        private class ResultData
        {
            [JsonPropertyName("root_id")]
            public string RootId { get; set; }

            [JsonPropertyName("service_id")]
            public string ServiceId { get; set; }

            [JsonPropertyName("total_result")]
            public int TotalResult { get; set; }

            [JsonPropertyName("brands")]
            public List<BrandData> Brands { get; set; }
        }

        private class BrandData
        {
            [JsonPropertyName("brand_id")]
            public string BrandId { get; set; }

            [JsonPropertyName("regions")]
            public List<string> Regions { get; set; }

            [JsonPropertyName("total_result")]
            public int TotalResult { get; set; }
        }

        private class SearchApiResponse
        {
            [JsonPropertyName("code")]
            public int Code { get; set; }

            [JsonPropertyName("messages")]
            public List<string> Messages { get; set; }

            [JsonPropertyName("payload")]
            public SearchPayloadData Payload { get; set; }

            [JsonPropertyName("request_id")]
            public string RequestId { get; set; }
        }

        private class SearchPayloadData
        {
            [JsonPropertyName("total_result")]
            public int TotalResult { get; set; }

            [JsonPropertyName("results")]
            public List<SearchResultData> Results { get; set; }
        }

        private class SearchResultData
        {
            [JsonPropertyName("offer_id")]
            public string OfferId { get; set; }

            [JsonPropertyName("title")]
            public string Title { get; set; }

            [JsonPropertyName("description")]
            public string Description { get; set; }

            [JsonPropertyName("unit_price")]
            public decimal UnitPrice { get; set; }

            [JsonPropertyName("description_images")]
            public List<string> DescriptionImages { get; set; }

            [JsonPropertyName("brand_id")]
            public string BrandId { get; set; }
        }
    }
} 