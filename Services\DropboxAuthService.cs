using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace ShopBot.Services
{
    public class DropboxAuthService
    {
        private readonly string _clientId;
        private readonly string _clientSecret;
        private readonly HttpClient _httpClient;
        private readonly ILogger<DropboxAuthService> _logger;

        public DropboxAuthService(string clientId, string clientSecret, ILogger<DropboxAuthService> logger)
        {
            _clientId = clientId;
            _clientSecret = clientSecret;
            _logger = logger;
            _httpClient = new HttpClient();
        }

        /// <summary>
        /// Generuje URL autoryzacji dla Dropbox OAuth 2.0
        /// </summary>
        public string GetAuthorizationUrl()
        {
            var redirectUri = "http://localhost:8080/callback"; // Można zmienić na własny
            var scope = "files.content.write files.content.read sharing.write";
            
            return $"https://www.dropbox.com/oauth2/authorize?" +
                   $"client_id={_clientId}&" +
                   $"response_type=code&" +
                   $"redirect_uri={Uri.EscapeDataString(redirectUri)}&" +
                   $"scope={Uri.EscapeDataString(scope)}&" +
                   $"token_access_type=offline"; // Ważne dla refresh token!
        }

        /// <summary>
        /// Wymienia authorization code na access token i refresh token
        /// </summary>
        public async Task<DropboxAuthResponse> GetAccessTokenWithCodeAsync(string authorizationCode)
        {
            try
            {
                _logger.LogInformation("[Dropbox Auth] Wymienianie authorization code na tokeny...");

                var content = new FormUrlEncodedContent(new Dictionary<string, string>
                {
                    { "client_id", _clientId },
                    { "client_secret", _clientSecret },
                    { "code", authorizationCode },
                    { "grant_type", "authorization_code" },
                    { "redirect_uri", "http://localhost:8080/callback" }
                });

                var response = await _httpClient.PostAsync("https://api.dropboxapi.com/oauth2/token", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                _logger.LogDebug("[Dropbox Auth] Token response: {Response}", responseContent);

                if (!response.IsSuccessStatusCode)
                {
                    throw new Exception($"Dropbox token request failed: {response.StatusCode} - {responseContent}");
                }

                using var doc = JsonDocument.Parse(responseContent);
                var root = doc.RootElement;

                var authResponse = new DropboxAuthResponse
                {
                    AccessToken = root.GetProperty("access_token").GetString(),
                    RefreshToken = root.TryGetProperty("refresh_token", out var refreshProp) ? refreshProp.GetString() : null,
                    ExpiresIn = root.TryGetProperty("expires_in", out var expiresProp) ? expiresProp.GetInt32() : 14400, // 4 hours default
                    TokenType = root.TryGetProperty("token_type", out var typeProp) ? typeProp.GetString() : "bearer"
                };

                _logger.LogInformation("[Dropbox Auth] ✅ Otrzymano tokeny - Access: {AccessLength} chars, Refresh: {RefreshLength} chars", 
                    authResponse.AccessToken?.Length ?? 0, authResponse.RefreshToken?.Length ?? 0);

                return authResponse;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[Dropbox Auth] ❌ Błąd podczas wymiany authorization code");
                throw new Exception($"Dropbox authentication failed: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Odświeża access token używając refresh token
        /// </summary>
        public async Task<DropboxAuthResponse> RefreshAccessTokenAsync(string refreshToken)
        {
            try
            {
                _logger.LogInformation("[Dropbox Auth] Odświeżanie access token...");

                var content = new FormUrlEncodedContent(new Dictionary<string, string>
                {
                    { "client_id", _clientId },
                    { "client_secret", _clientSecret },
                    { "refresh_token", refreshToken },
                    { "grant_type", "refresh_token" }
                });

                var response = await _httpClient.PostAsync("https://api.dropboxapi.com/oauth2/token", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                _logger.LogDebug("[Dropbox Auth] Refresh response: {Response}", responseContent);

                if (!response.IsSuccessStatusCode)
                {
                    throw new Exception($"Dropbox token refresh failed: {response.StatusCode} - {responseContent}");
                }

                using var doc = JsonDocument.Parse(responseContent);
                var root = doc.RootElement;

                var authResponse = new DropboxAuthResponse
                {
                    AccessToken = root.GetProperty("access_token").GetString(),
                    RefreshToken = refreshToken, // Refresh token pozostaje ten sam
                    ExpiresIn = root.TryGetProperty("expires_in", out var expiresProp) ? expiresProp.GetInt32() : 14400,
                    TokenType = root.TryGetProperty("token_type", out var typeProp) ? typeProp.GetString() : "bearer"
                };

                _logger.LogInformation("[Dropbox Auth] ✅ Token odświeżony pomyślnie");
                return authResponse;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[Dropbox Auth] ❌ Błąd podczas odświeżania tokena");
                throw new Exception($"Dropbox token refresh failed: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Testuje czy access token jest ważny
        /// </summary>
        public async Task<bool> ValidateAccessTokenAsync(string accessToken)
        {
            try
            {
                using var httpClient = new HttpClient();
                httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await httpClient.PostAsync("https://api.dropboxapi.com/2/users/get_current_account",
                    new StringContent("{}", Encoding.UTF8, "application/json"));

                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    public class DropboxAuthResponse
    {
        public string? AccessToken { get; set; }
        public string? RefreshToken { get; set; }
        public int ExpiresIn { get; set; }
        public string? TokenType { get; set; }
    }
}
