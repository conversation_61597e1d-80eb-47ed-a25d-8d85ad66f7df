using Microsoft.Extensions.Logging;
using ShopBot.Models;

namespace ShopBot.Services
{
    /// <summary>
    /// Prosty test runner d<PERSON> Offer<PERSON>ontentChangeDetector
    /// </summary>
    public class OfferContentChangeDetectorTestRunner
    {
        private readonly ILogger<OfferContentChangeDetectorTestRunner> _logger;
        private readonly OfferContentChangeDetector _detector;

        public OfferContentChangeDetectorTestRunner(
            ILogger<OfferContentChangeDetectorTestRunner> logger,
            OfferContentChangeDetector detector)
        {
            _logger = logger;
            _detector = detector;
        }

        /// <summary>
        /// Uruchamia wszystkie testy detektora zmian zawartości
        /// </summary>
        public void RunAllTests()
        {
            _logger.LogInformation("🧪 Rozpoczynam testy OfferContentChangeDetector");

            var testResults = new List<(string TestName, bool Passed, string Details)>();

            // Test 1: Znacząca zmiana ceny
            testResults.Add(TestSignificantPriceChange());

            // Test 2: Nieznaczna zmiana ceny
            testResults.Add(TestMinorPriceChange());

            // Test 3: Znacząca zmiana tytułu
            testResults.Add(TestSignificantTitleChange());

            // Test 4: Nieznaczna zmiana tytułu
            testResults.Add(TestMinorTitleChange());

            // Test 5: Brak zmian
            testResults.Add(TestNoChanges());

            // Test 6: Nieprawidłowa cena
            testResults.Add(TestInvalidPrice());

            // Test 7: Test algorytmu Levenshteina
            testResults.Add(TestLevenshteinAlgorithm());

            // Podsumowanie wyników
            var passedTests = testResults.Count(r => r.Passed);
            var totalTests = testResults.Count;

            _logger.LogInformation("📊 Wyniki testów: {PassedTests}/{TotalTests} testów przeszło pomyślnie", passedTests, totalTests);

            foreach (var result in testResults)
            {
                var status = result.Passed ? "✅" : "❌";
                _logger.LogInformation("{Status} {TestName}: {Details}", status, result.TestName, result.Details);
            }

            if (passedTests == totalTests)
            {
                _logger.LogInformation("🎉 Wszystkie testy przeszły pomyślnie!");
            }
            else
            {
                _logger.LogWarning("⚠️ {FailedTests} testów nie powiodło się", totalTests - passedTests);
            }
        }

        private (string TestName, bool Passed, string Details) TestSignificantPriceChange()
        {
            try
            {
                var existingOffer = new Offer
                {
                    FunPayOfferId = "test1",
                    Title = "Test Account",
                    OriginalPrice = 100m
                };

                var newOfferDetails = new OfferDetails
                {
                    Id = "test1",
                    ShortDescription = "Test Account",
                    ActualOfferPrice = "200.00" // 100% increase
                };

                var result = _detector.HasSignificantChanges(existingOffer, newOfferDetails);
                var expected = true;

                return ("Znacząca zmiana ceny", result == expected, 
                    $"Oczekiwano: {expected}, Otrzymano: {result} (100% wzrost ceny)");
            }
            catch (Exception ex)
            {
                return ("Znacząca zmiana ceny", false, $"Błąd: {ex.Message}");
            }
        }

        private (string TestName, bool Passed, string Details) TestMinorPriceChange()
        {
            try
            {
                var existingOffer = new Offer
                {
                    FunPayOfferId = "test2",
                    Title = "Test Account",
                    OriginalPrice = 100m
                };

                var newOfferDetails = new OfferDetails
                {
                    Id = "test2",
                    ShortDescription = "Test Account",
                    ActualOfferPrice = "108.00" // 8% increase - poniżej nowego progu 10%
                };

                var result = _detector.HasSignificantChanges(existingOffer, newOfferDetails);
                var expected = false;

                return ("Nieznaczna zmiana ceny", result == expected,
                    $"Oczekiwano: {expected}, Otrzymano: {result} (8% wzrost ceny)");
            }
            catch (Exception ex)
            {
                return ("Nieznaczna zmiana ceny", false, $"Błąd: {ex.Message}");
            }
        }

        private (string TestName, bool Passed, string Details) TestSignificantTitleChange()
        {
            try
            {
                var existingOffer = new Offer
                {
                    FunPayOfferId = "test3",
                    Title = "World of Warcraft Epic Account Level 60 Legendary Gear",
                    OriginalPrice = 100m
                };

                var newOfferDetails = new OfferDetails
                {
                    Id = "test3",
                    ShortDescription = "Mobile Legends Bang Bang Account High Rank Mythic",
                    ActualOfferPrice = "100.00"
                };

                var result = _detector.HasSignificantChanges(existingOffer, newOfferDetails);
                var expected = true;

                return ("Znacząca zmiana tytułu", result == expected,
                    $"Oczekiwano: {expected}, Otrzymano: {result} (WoW Epic → Mobile Legends)");
            }
            catch (Exception ex)
            {
                return ("Znacząca zmiana tytułu", false, $"Błąd: {ex.Message}");
            }
        }

        private (string TestName, bool Passed, string Details) TestMinorTitleChange()
        {
            try
            {
                var existingOffer = new Offer
                {
                    FunPayOfferId = "test4",
                    Title = "World of Warcraft Account Level 60",
                    OriginalPrice = 100m
                };

                var newOfferDetails = new OfferDetails
                {
                    Id = "test4",
                    ShortDescription = "World of Warcraft Account Level 60 with extras",
                    ActualOfferPrice = "100.00"
                };

                var result = _detector.HasSignificantChanges(existingOffer, newOfferDetails);
                var expected = false;

                return ("Nieznaczna zmiana tytułu", result == expected, 
                    $"Oczekiwano: {expected}, Otrzymano: {result} (dodano 'with extras')");
            }
            catch (Exception ex)
            {
                return ("Nieznaczna zmiana tytułu", false, $"Błąd: {ex.Message}");
            }
        }

        private (string TestName, bool Passed, string Details) TestNoChanges()
        {
            try
            {
                var existingOffer = new Offer
                {
                    FunPayOfferId = "test5",
                    Title = "Test Account",
                    OriginalPrice = 100m
                };

                var newOfferDetails = new OfferDetails
                {
                    Id = "test5",
                    ShortDescription = "Test Account",
                    ActualOfferPrice = "100.00"
                };

                var result = _detector.HasSignificantChanges(existingOffer, newOfferDetails);
                var expected = false;

                return ("Brak zmian", result == expected, 
                    $"Oczekiwano: {expected}, Otrzymano: {result} (identyczne dane)");
            }
            catch (Exception ex)
            {
                return ("Brak zmian", false, $"Błąd: {ex.Message}");
            }
        }

        private (string TestName, bool Passed, string Details) TestInvalidPrice()
        {
            try
            {
                var existingOffer = new Offer
                {
                    FunPayOfferId = "test6",
                    Title = "Test Account",
                    OriginalPrice = 100m
                };

                var newOfferDetails = new OfferDetails
                {
                    Id = "test6",
                    ShortDescription = "Test Account",
                    ActualOfferPrice = "invalid_price"
                };

                var result = _detector.HasSignificantChanges(existingOffer, newOfferDetails);
                var expected = false;

                return ("Nieprawidłowa cena", result == expected, 
                    $"Oczekiwano: {expected}, Otrzymano: {result} (graceful handling)");
            }
            catch (Exception ex)
            {
                return ("Nieprawidłowa cena", false, $"Błąd: {ex.Message}");
            }
        }

        private (string TestName, bool Passed, string Details) TestLevenshteinAlgorithm()
        {
            try
            {
                var stats = _detector.GetPerformanceStats();
                var expectedPriceThreshold = 0.10m; // Zaktualizowane z 15% na 10%
                var expectedTitleThreshold = 0.7;

                var priceThresholdCorrect = stats.PriceChangeThreshold == expectedPriceThreshold;
                var titleThresholdCorrect = stats.TitleSimilarityThreshold == expectedTitleThreshold;

                var passed = priceThresholdCorrect && titleThresholdCorrect;

                return ("Progi algorytmu", passed, 
                    $"Próg ceny: {stats.PriceChangeThreshold} (oczekiwano: {expectedPriceThreshold}), " +
                    $"Próg tytułu: {stats.TitleSimilarityThreshold} (oczekiwano: {expectedTitleThreshold})");
            }
            catch (Exception ex)
            {
                return ("Progi algorytmu", false, $"Błąd: {ex.Message}");
            }
        }
    }
}
