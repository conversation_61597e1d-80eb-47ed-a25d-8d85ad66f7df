{"TotalOffers": 634, "AcceptedOffers": 220, "RejectedOffers": 414, "RejectionReasons": {"Cena ponizej minimum (9,83 < 15)": 6, "Ocena ponizej minimum (0 < 5)": 155, "Cena ponizej minimum (5,59 < 15)": 1, "Cena ponizej minimum (5,69 < 15)": 3, "Cena ponizej minimum (11,80 < 15)": 14, "Cena ponizej minimum (10,49 < 15)": 15, "Cena ponizej minimum (11,14 < 15)": 4, "Cena ponizej minimum (9,17 < 15)": 20, "Cena ponizej minimum (10,65 < 15)": 1, "Cena ponizej minimum (13,11 < 15)": 32, "Cena ponizej minimum (6,55 < 15)": 18, "Cena ponizej minimum (5,24 < 15)": 10, "Cena ponizej minimum (7,86 < 15)": 12, "Cena ponizej minimum (7,21 < 15)": 5, "Cena ponizej minimum (5,64 < 15)": 1, "Zawiera zabronioną frazę (hardcoded): your choice": 3, "Cena ponizej minimum (14,42 < 15)": 2, "Cena ponizej minimum (5,90 < 15)": 3, "Cena ponizej minimum (3,28 < 15)": 8, "Cena ponizej minimum (2,62 < 15)": 4, "Cena ponizej minimum (2,77 < 15)": 1, "Cena ponizej minimum (1,31 < 15)": 1, "Cena ponizej minimum (3,93 < 15)": 11, "Ocena ponizej minimum (4 < 5)": 7, "Cena ponizej minimum (6,83 < 15)": 3, "Cena ponizej minimum (7,97 < 15)": 2, "Cena ponizej minimum (13,67 < 15)": 1, "Cena ponizej minimum (12,87 < 15)": 2, "Cena ponizej minimum (14,04 < 15)": 2, "Cena ponizej minimum (11,70 < 15)": 2, "Cena ponizej minimum (8,19 < 15)": 1, "Cena ponizej minimum (3,42 < 15)": 3, "Cena ponizej minimum (4,56 < 15)": 2, "Cena ponizej minimum (5,13 < 15)": 1, "Cena ponizej minimum (1,37 < 15)": 6, "Cena ponizej minimum (1,71 < 15)": 1, "Duplikat oferty": 1, "Ocena ponizej minimum (3 < 5)": 1, "Cena ponizej minimum (3,67 < 15)": 1, "Cena ponizej minimum (4,33 < 15)": 1, "Cena ponizej minimum (1,14 < 15)": 1, "Cena ponizej minimum (1,44 < 15)": 1, "Cena ponizej minimum (0,03 < 15)": 1, "Cena ponizej minimum (2,85 < 15)": 1, "Cena ponizej minimum (2,28 < 15)": 1, "Cena ponizej minimum (7,37 < 15)": 1, "Cena ponizej minimum (4,59 < 15)": 6, "Cena ponizej minimum (8,52 < 15)": 2, "Cena ponizej minimum (1,05 < 15)": 1, "Cena ponizej minimum (13,09 < 15)": 1, "Cena ponizej minimum (6,82 < 15)": 1, "Cena ponizej minimum (11,38 < 15)": 1, "Cena ponizej minimum (0,79 < 15)": 2, "Cena ponizej minimum (7,60 < 15)": 1, "Cena ponizej minimum (6,29 < 15)": 1, "Cena ponizej minimum (5,19 < 15)": 1, "Cena ponizej minimum (6,50 < 15)": 1, "Cena ponizej minimum (9,28 < 15)": 1, "Cena ponizej minimum (4,38 < 15)": 1, "Cena ponizej minimum (5,86 < 15)": 1, "Cena ponizej minimum (7,50 < 15)": 1, "Cena ponizej minimum (6,95 < 15)": 1, "Cena powyzej maximum (3263,46 > 1000)": 1, "Cena ponizej minimum (7,71 < 15)": 1, "Cena ponizej minimum (1,11 < 15)": 1, "Cena ponizej minimum (11,40 < 15)": 1, "Ocena ponizej minimum (1 < 5)": 1, "Cena ponizej minimum (6,17 < 15)": 1, "Cena ponizej minimum (3,19 < 15)": 4, "Cena ponizej minimum (5,32 < 15)": 1, "Cena powyzej maximum (1572,75 > 1000)": 1, "Cena ponizej minimum (0,39 < 15)": 1, "Cena powyzej maximum (1310,63 > 1000)": 1, "Cena ponizej minimum (0,01 < 15)": 1, "Ocena ponizej minimum (2 < 5)": 1, "Cena ponizej minimum (1,17 < 15)": 1, "Cena ponizej minimum (3,51 < 15)": 1}, "GenerationTime": "2025-06-17T18:18:46.3803623+02:00"}