using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;
using ShopBot.Services;

namespace ShopBot.Tasks
{
    public class TaskScheduler : ShopBot.Services.ITaskScheduler
    {
        private readonly ConcurrentDictionary<string, Timer> _recurringTasks = new();
        private readonly CancellationTokenSource _cancellationTokenSource = new();
        private readonly WorkerPool _workerPool;
        private readonly WorkerPool _syncWorkerPool;
        private readonly LoggerService _logger;

        public TaskScheduler(LoggerService logger, int maxWorkers = 20)
        {
            _logger = logger;
            _workerPool = new WorkerPool("MainPool", maxWorkers, logger);
            _syncWorkerPool = new WorkerPool("SyncPool", 3, logger);
        }

        public async Task ScheduleTask(BaseTask task)
        {
            _logger.Log($"Zaplanowano zadanie: {task.GetType().Name} (ID: {task.Id})");
            
            if (task is ScanAndSyncOffersTask)
            {
                _syncWorkerPool.EnqueueTask(task);
            }
            else
            {
                _workerPool.EnqueueTask(task);
            }
        }

        public void ScheduleRecurringTask(BaseTask task, TimeSpan interval)
        {
            _logger.Log($"Zaplanowano zadanie cykliczne: {task.GetType().Name} (ID: {task.Id}, Interwał: {interval.TotalMinutes}min)");
            
            var timer = new Timer(async (state) =>
            {
                if (task is ScanAndSyncOffersTask)
                {
                    _syncWorkerPool.EnqueueTask(task);
                }
                else
                {
                    _workerPool.EnqueueTask(task);
                }
            }, null, TimeSpan.Zero, interval);

            _recurringTasks.TryAdd(task.Id, timer);
        }

        // Przeciążona metoda z callbackiem przed uruchomieniem zadania
        public void ScheduleRecurringTask(BaseTask task, TimeSpan interval, Action beforeTaskCallback)
        {
            _logger.Log($"Zaplanowano zadanie cykliczne z callbackiem: {task.GetType().Name} (ID: {task.Id}, Interwał: {interval.TotalMinutes}min)");
            
            var timer = new Timer(async (state) =>
            {
                // Wywołaj callback przed uruchomieniem zadania
                try
                {
                    beforeTaskCallback?.Invoke();
                }
                catch (Exception ex)
                {
                    _logger.Log($"Błąd w callbacku przed uruchomieniem zadania: {ex.Message}", LoggerService.LogLevel.Error);
                }
                
                if (task is ScanAndSyncOffersTask)
                {
                    _syncWorkerPool.EnqueueTask(task);
                }
                else
                {
                    _workerPool.EnqueueTask(task);
                }
            }, null, TimeSpan.Zero, interval);

            _recurringTasks.TryAdd(task.Id, timer);
        }

        public void StopAllTasks()
        {
            _cancellationTokenSource.Cancel();
            _workerPool.Stop();
            _syncWorkerPool.Stop();
            
            foreach (var timer in _recurringTasks.Values)
            {
                timer.Dispose();
            }
            _recurringTasks.Clear();
            
            _logger.Log("Wszystkie zadania zostały zatrzymane", LoggerService.LogLevel.Warning);
        }

        public void RemoveRecurringTask(string taskId)
        {
            if (_recurringTasks.TryRemove(taskId, out var timer))
            {
                timer.Dispose();
                _logger.Log($"Usunięto zadanie cykliczne {taskId}");
            }
        }

        public async Task WaitForCompletion()
        {
            await Task.WhenAll(
                _workerPool.WaitForCompletion(),
                _syncWorkerPool.WaitForCompletion()
            );
        }

        public (int activeWorkers, int queueLength, int activeSyncWorkers, int syncQueueLength) GetStatus()
        {
            return (
                _workerPool.GetActiveWorkers(),
                _workerPool.GetQueueLength(),
                _syncWorkerPool.GetActiveWorkers(),
                _syncWorkerPool.GetQueueLength()
            );
        }

        public async Task WaitForAllTasks()
        {
            // Czekaj aż wszystkie workery będą bezczynne i kolejka będzie pusta
            while (true)
            {
                var (activeWorkers, queueLength, activeSyncWorkers, syncQueueLength) = GetStatus();
                if (activeWorkers == 0 && queueLength == 0 && activeSyncWorkers == 0 && syncQueueLength == 0)
                {
                    break;
                }
                await Task.Delay(1000); // Sprawdzaj co sekundę
            }
        }

        // Implementacja brakujących właściwości i metod z interfejsu ITaskScheduler
        public bool IsRunning => !_cancellationTokenSource.Token.IsCancellationRequested;

        TaskStatistics ITaskScheduler.GetStatistics()
        {
            return new TaskStatistics
            {
                TotalTasksExecuted = 0, // TODO: Implementować licznik
                SuccessfulTasks = 0,    // TODO: Implementować licznik
                FailedTasks = 0,        // TODO: Implementować licznik
                AverageExecutionTime = TimeSpan.Zero,
                LastTaskExecution = DateTime.Now
            };
        }
    }
}