using Microsoft.Extensions.Logging;
using ShopBot.Services.Interfaces;
using ShopBot.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;

namespace ShopBot.Services
{
    /// <summary>
    /// Smart Offer Tracker - Hybrid Solution (Enhanced Status + In-Memory Cache)
    /// Combines database persistence with session-based performance optimization
    /// 
    /// Status Flow:
    /// Processing → Processed → Published/Failed/Rejected/LimitReached → Inactive
    /// </summary>
    public class SmartOfferTracker : IOfferTracker
    {
        private readonly IShopBotRepository _repository;
        private readonly IProcessingSessionCache _sessionCache;
        private readonly ILogger<SmartOfferTracker> _logger;
        private readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);

        public SmartOfferTracker(
            IShopBotRepository repository,
            IProcessingSessionCache sessionCache,
            ILogger<SmartOfferTracker> logger)
        {
            _repository = repository;
            _sessionCache = sessionCache;
            _logger = logger;
        }

        // Enhanced async methods with smart caching
        public async Task<bool> WasOfferProcessedAsync(string funPayOfferId)
        {
            try
            {
                // 1. Quick check: Session cache (fastest)
                if (_sessionCache.WasProcessedInSession(funPayOfferId))
                {
                    _logger.LogDebug("[SmartTracker] 🚀 Offer {OfferId} found in session cache", funPayOfferId);
                    return true;
                }

                // 2. Check if currently processing (avoid duplicates)
                if (_sessionCache.IsCurrentlyProcessing(funPayOfferId))
                {
                    _logger.LogDebug("[SmartTracker] ⏳ Offer {OfferId} is currently being processed", funPayOfferId);
                    return true;
                }

                // 3. Database check (persistent)
                var offer = await _repository.Offers.GetByFunPayIdAsync(funPayOfferId);
                if (offer != null && offer.Status != "Inactive")
                {
                    // Add to session cache for future quick access
                    _sessionCache.MarkAsProcessed(funPayOfferId);
                    
                    _logger.LogDebug("[SmartTracker] 🗄️ Offer {OfferId} found in database with status: {Status}", 
                        funPayOfferId, offer.Status);
                    return true;
                }

                _logger.LogDebug("[SmartTracker] 🆕 Offer {OfferId} not processed yet", funPayOfferId);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[SmartTracker] ❌ Error checking if offer {OfferId} was processed", funPayOfferId);
                throw;
            }
        }

        public async Task AddProcessedOfferAsync(string funPayOfferId, string gameName)
        {
            try
            {
                // 1. Mark as currently processing in session cache
                _sessionCache.MarkAsCurrentlyProcessing(funPayOfferId);

                // 2. Check if already exists in database
                var existingOffer = await _repository.Offers.GetByFunPayIdAsync(funPayOfferId);
                if (existingOffer != null)
                {
                    _logger.LogDebug("[SmartTracker] ⚠️ Offer {OfferId} already exists with status: {Status}", 
                        funPayOfferId, existingOffer.Status);
                    
                    // Update last attempt time
                    existingOffer.LastAttemptAt = DateTime.UtcNow;
                    await _repository.Offers.UpdateAsync(existingOffer);
                    
                    _sessionCache.MarkAsFinishedProcessing(funPayOfferId);
                    return;
                }

                // 3. Create new offer with "Processing" status
                var offer = new Offer
                {
                    FunPayOfferId = funPayOfferId,
                    GameName = gameName,
                    Title = "Processing...",
                    Status = "Processing", // Initial status
                    ProcessedAt = DateTime.UtcNow,
                    LastAttemptAt = DateTime.UtcNow,
                    OriginalPrice = 0,
                    ConvertedPrice = 0,
                    Currency = "USD",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                await _repository.Offers.CreateAsync(offer);
                
                // 4. Mark as processed in session cache
                _sessionCache.MarkAsFinishedProcessing(funPayOfferId);
                
                _logger.LogInformation("[SmartTracker] ✅ Added offer {OfferId} with status 'Processing'", funPayOfferId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[SmartTracker] ❌ Error adding offer {OfferId}", funPayOfferId);
                _sessionCache.MarkAsFinishedProcessing(funPayOfferId); // Clean up cache
                throw;
            }
        }

        // Enhanced status management methods
        public async Task MarkAsPublishedAsync(string funPayOfferId, string g2gOfferId)
        {
            try
            {
                var offer = await _repository.Offers.GetByFunPayIdAsync(funPayOfferId);
                if (offer != null)
                {
                    offer.Status = "Published";
                    offer.G2GOfferId = g2gOfferId;
                    offer.PublishedAt = DateTime.UtcNow;
                    offer.UpdatedAt = DateTime.UtcNow;
                    
                    await _repository.Offers.UpdateAsync(offer);
                    _logger.LogInformation("[SmartTracker] 📢 Marked offer {OfferId} as Published (G2G: {G2GId})", 
                        funPayOfferId, g2gOfferId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[SmartTracker] ❌ Error marking offer {OfferId} as published", funPayOfferId);
                throw;
            }
        }

        public async Task MarkAsFailedAsync(string funPayOfferId, string reason)
        {
            try
            {
                var offer = await _repository.Offers.GetByFunPayIdAsync(funPayOfferId);
                if (offer != null)
                {
                    offer.Status = "Failed";
                    offer.FailureReason = reason;
                    offer.UpdatedAt = DateTime.UtcNow;
                    
                    await _repository.Offers.UpdateAsync(offer);
                    _logger.LogWarning("[SmartTracker] ❌ Marked offer {OfferId} as Failed: {Reason}", 
                        funPayOfferId, reason);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[SmartTracker] ❌ Error marking offer {OfferId} as failed", funPayOfferId);
                throw;
            }
        }

        public async Task MarkAsRejectedAsync(string funPayOfferId, string reason)
        {
            try
            {
                var offer = await _repository.Offers.GetByFunPayIdAsync(funPayOfferId);
                if (offer != null)
                {
                    offer.Status = "Rejected";
                    offer.FailureReason = reason;
                    offer.UpdatedAt = DateTime.UtcNow;
                    
                    await _repository.Offers.UpdateAsync(offer);
                    _logger.LogInformation("[SmartTracker] 🚫 Marked offer {OfferId} as Rejected: {Reason}", 
                        funPayOfferId, reason);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[SmartTracker] ❌ Error marking offer {OfferId} as rejected", funPayOfferId);
                throw;
            }
        }

        public async Task MarkAsLimitReachedAsync(string funPayOfferId)
        {
            try
            {
                var offer = await _repository.Offers.GetByFunPayIdAsync(funPayOfferId);
                if (offer != null)
                {
                    offer.Status = "LimitReached";
                    offer.FailureReason = "G2G offer limit reached";
                    offer.UpdatedAt = DateTime.UtcNow;
                    
                    await _repository.Offers.UpdateAsync(offer);
                    _logger.LogInformation("[SmartTracker] 🚫 Marked offer {OfferId} as LimitReached", funPayOfferId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[SmartTracker] ❌ Error marking offer {OfferId} as limit reached", funPayOfferId);
                throw;
            }
        }

        public async Task<List<string>> GetAllOfferIdsAsync()
        {
            try
            {
                var offers = await _repository.Offers.GetAllAsync();
                var activeOffers = offers.Where(o => o.Status != "Inactive").Select(o => o.FunPayOfferId).ToList();
                
                _logger.LogDebug("[SmartTracker] 📊 Retrieved {Count} active offer IDs", activeOffers.Count);
                return activeOffers;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[SmartTracker] ❌ Error retrieving offer IDs");
                throw;
            }
        }

        public async Task<DateTime> GetLastProcessingTimeAsync()
        {
            try
            {
                var offers = await _repository.Offers.GetAllAsync();
                if (!offers.Any())
                {
                    return DateTime.MinValue;
                }

                var lastProcessingTime = offers.Max(o => o.ProcessedAt);
                _logger.LogDebug("[SmartTracker] 📅 Last processing time: {LastTime}", lastProcessingTime);
                
                return lastProcessingTime;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[SmartTracker] ❌ Error getting last processing time");
                throw;
            }
        }

        public async Task<bool> TryProcessOfferAsync(string funPayOfferId, string gameName)
        {
            await _semaphore.WaitAsync();
            try
            {
                var wasProcessed = await WasOfferProcessedAsync(funPayOfferId);
                if (wasProcessed)
                {
                    _logger.LogDebug("[SmartTracker] ⚠️ Offer {OfferId} already processed", funPayOfferId);
                    return false;
                }

                await AddProcessedOfferAsync(funPayOfferId, gameName);
                _logger.LogInformation("[SmartTracker] ✅ Successfully started processing offer {OfferId}", funPayOfferId);
                return true;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        public void ClearSessionCache()
        {
            _sessionCache.ClearSession();
            _logger.LogInformation("[SmartTracker] 🔄 Session cache cleared");
        }

        public async Task<Dictionary<string, int>> GetStatusCountsAsync()
        {
            try
            {
                var offers = await _repository.Offers.GetAllAsync();
                var statusCounts = offers.GroupBy(o => o.Status)
                    .ToDictionary(g => g.Key, g => g.Count());

                _logger.LogDebug("[SmartTracker] 📊 Status counts: {Counts}",
                    string.Join(", ", statusCounts.Select(kv => $"{kv.Key}: {kv.Value}")));

                return statusCounts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[SmartTracker] ❌ Error getting status counts");
                throw;
            }
        }

        public async Task SynchronizeWithActiveOffersAsync(ISet<string> verifiedOffers)
        {
            try
            {
                _logger.LogInformation("[SmartTracker] 🔄 Synchronizing with {Count} verified offers", verifiedOffers.Count);

                var allOffers = await _repository.Offers.GetAllAsync();
                var publishedOffers = allOffers.Where(o => o.Status == "Published").ToList();

                // Find offers that were published but are no longer active on G2G
                var inactiveOffers = publishedOffers.Where(o => !verifiedOffers.Contains(o.FunPayOfferId)).ToList();

                if (inactiveOffers.Any())
                {
                    _logger.LogInformation("[SmartTracker] 🗑️ Found {Count} inactive offers to mark as inactive", inactiveOffers.Count);

                    foreach (var inactiveOffer in inactiveOffers)
                    {
                        inactiveOffer.Status = "Inactive";
                        inactiveOffer.UpdatedAt = DateTime.UtcNow;
                        await _repository.Offers.UpdateAsync(inactiveOffer);

                        _logger.LogDebug("[SmartTracker] 🗑️ Marked offer as inactive: {OfferId}", inactiveOffer.FunPayOfferId);
                    }
                }

                // Remove failed/rejected/limit reached offers that are not in verified set
                // This allows them to be retried in future cycles
                var failedOffers = allOffers.Where(o =>
                    (o.Status == "Failed" || o.Status == "Rejected" || o.Status == "LimitReached") &&
                    !verifiedOffers.Contains(o.FunPayOfferId)).ToList();

                if (failedOffers.Any())
                {
                    _logger.LogInformation("[SmartTracker] 🔄 Found {Count} failed offers to mark as inactive for retry", failedOffers.Count);

                    foreach (var failedOffer in failedOffers)
                    {
                        failedOffer.Status = "Inactive";
                        failedOffer.UpdatedAt = DateTime.UtcNow;
                        await _repository.Offers.UpdateAsync(failedOffer);

                        _logger.LogDebug("[SmartTracker] 🔄 Marked failed offer as inactive for retry: {OfferId}", failedOffer.FunPayOfferId);
                    }
                }

                await _repository.SaveChangesAsync();
                _logger.LogInformation("[SmartTracker] ✅ Synchronization completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[SmartTracker] ❌ Error during synchronization");
                throw;
            }
        }

        public async Task<int> GetProcessedOffersCountAsync()
        {
            try
            {
                var offers = await _repository.Offers.GetAllAsync();
                var count = offers.Count(o => o.Status != "Inactive");

                _logger.LogDebug("[SmartTracker] 📊 Active processed offers: {Count}", count);
                return count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[SmartTracker] ❌ Error getting processed offers count");
                throw;
            }
        }

        public async Task<Dictionary<string, int>> GetOfferCountsByGameAsync()
        {
            try
            {
                var offerCounts = await _repository.Offers.GetOfferCountsByGameAsync();

                _logger.LogDebug("[SmartTracker] 📊 Offer counts by game: {Counts}",
                    string.Join(", ", offerCounts.Select(kv => $"{kv.Key}: {kv.Value}")));

                return offerCounts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[SmartTracker] ❌ Error getting offer counts by game");
                throw;
            }
        }

        public async Task MigrateFromJsonToDatabaseAsync()
        {
            _logger.LogInformation("[SmartTracker] ℹ️ Smart tracker - no JSON migration needed");
            // No-op in smart tracker mode
        }

        // Synchronous methods (backward compatibility)
        public bool WasOfferProcessed(string funPayOfferId)
        {
            try
            {
                return WasOfferProcessedAsync(funPayOfferId).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[SmartTracker] ❌ Error in sync WasOfferProcessed for {OfferId}", funPayOfferId);
                throw;
            }
        }

        public void AddProcessedOffer(string funPayOfferId)
        {
            try
            {
                AddProcessedOfferAsync(funPayOfferId, "Unknown").GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[SmartTracker] ❌ Error in sync AddProcessedOffer for {OfferId}", funPayOfferId);
                throw;
            }
        }

        public List<string> GetAllOfferIds()
        {
            try
            {
                return GetAllOfferIdsAsync().GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[SmartTracker] ❌ Error in sync GetAllOfferIds");
                throw;
            }
        }

        public DateTime GetLastProcessingTime()
        {
            try
            {
                return GetLastProcessingTimeAsync().GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[SmartTracker] ❌ Error in sync GetLastProcessingTime");
                throw;
            }
        }

        public void SynchronizeWithActiveOffers(ISet<string> verifiedOffers)
        {
            try
            {
                SynchronizeWithActiveOffersAsync(verifiedOffers).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[SmartTracker] ❌ Error in sync SynchronizeWithActiveOffers");
                throw;
            }
        }

        public void Dispose()
        {
            _semaphore?.Dispose();
        }
    }
}
