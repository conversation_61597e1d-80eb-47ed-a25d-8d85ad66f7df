using Microsoft.Extensions.Logging;
using System;
using System.Text.RegularExpressions;

namespace ShopBot.Services.Parsers
{
    /// <summary>
    /// Validator odpowiedzialny za walidację i normalizację URL-i
    /// </summary>
    public class UrlValidator : IUrlValidator
    {
        private readonly ILogger<UrlValidator> _logger;

        public UrlValidator(ILogger<UrlValidator> logger)
        {
            _logger = logger;
        }

        public string ValidateAndFixUrl(string url)
        {
            if (string.IsNullOrEmpty(url))
                return "";

            try
            {
                _logger.LogTrace("🔍 Walidacja URL: {Url}", url);

                // Usuń białe znaki
                url = url.Trim();

                // Dodaj protokół jeśli brakuje
                if (!url.StartsWith("http://") && !url.StartsWith("https://"))
                {
                    url = "https://" + url;
                }

                // Sprawdź czy URL jest prawidłowy
                if (!Uri.TryCreate(url, UriKind.Absolute, out Uri? result))
                {
                    _logger.LogWarning("⚠️ Nieprawidłowy URL: {Url}", url);
                    return "";
                }

                // Normalizuj URL
                var normalizedUrl = NormalizeUrl(result.ToString());
                
                _logger.LogTrace("✅ URL zwalidowany: {OriginalUrl} -> {NormalizedUrl}", url, normalizedUrl);
                return normalizedUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Błąd walidacji URL: {Url}", url);
                return "";
            }
        }

        public string ExtractIdFromUrl(string url)
        {
            if (string.IsNullOrEmpty(url))
                return "";

            try
            {
                // Wzorce dla różnych typów URL-i FunPay
                var patterns = new[]
                {
                    @"id=(\d+)",                    // ?id=123456
                    @"/lots/(\d+)/",                // /lots/123456/
                    @"/offer/(\d+)",                // /offer/123456
                    @"/(\d+)/?$",                   // /123456 na końcu
                    @"offer_id=(\d+)",              // offer_id=123456
                };

                foreach (var pattern in patterns)
                {
                    var match = Regex.Match(url, pattern);
                    if (match.Success && match.Groups.Count > 1)
                    {
                        var id = match.Groups[1].Value;
                        _logger.LogTrace("🆔 Wyciągnięto ID: {Id} z URL: {Url}", id, url);
                        return id;
                    }
                }

                _logger.LogWarning("⚠️ Nie znaleziono ID w URL: {Url}", url);
                return "";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Błąd wyciągania ID z URL: {Url}", url);
                return "";
            }
        }

        public bool IsValidFunPayUrl(string url)
        {
            if (string.IsNullOrEmpty(url))
                return false;

            try
            {
                if (!Uri.TryCreate(url, UriKind.Absolute, out Uri? uri))
                    return false;

                // Sprawdź czy to domena FunPay
                var validDomains = new[] { "funpay.com", "www.funpay.com" };
                var host = uri.Host.ToLowerInvariant();

                foreach (var domain in validDomains)
                {
                    if (host == domain || host.EndsWith("." + domain))
                    {
                        return true;
                    }
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        public string NormalizeUrl(string url)
        {
            if (string.IsNullOrEmpty(url))
                return "";

            try
            {
                if (!Uri.TryCreate(url, UriKind.Absolute, out Uri? uri))
                    return url;

                var builder = new UriBuilder(uri);

                // Normalizuj protokół na HTTPS
                if (builder.Scheme == "http")
                {
                    builder.Scheme = "https";
                }

                // Usuń domyślny port
                if ((builder.Scheme == "https" && builder.Port == 443) ||
                    (builder.Scheme == "http" && builder.Port == 80))
                {
                    builder.Port = -1;
                }

                // Normalizuj host (małe litery)
                builder.Host = builder.Host.ToLowerInvariant();

                // Usuń trailing slash jeśli nie jest to root
                var path = builder.Path;
                if (path.Length > 1 && path.EndsWith("/"))
                {
                    builder.Path = path.TrimEnd('/');
                }

                // Sortuj parametry query (opcjonalnie)
                if (!string.IsNullOrEmpty(builder.Query))
                {
                    builder.Query = NormalizeQueryString(builder.Query);
                }

                return builder.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "⚠️ Błąd normalizacji URL: {Url}", url);
                return url;
            }
        }

        private string NormalizeQueryString(string query)
        {
            try
            {
                // Usuń początkowy znak ?
                if (query.StartsWith("?"))
                    query = query.Substring(1);

                // Podziel na parametry
                var parameters = query.Split('&');
                
                // Sortuj parametry alfabetycznie (opcjonalnie)
                Array.Sort(parameters);

                return string.Join("&", parameters);
            }
            catch
            {
                return query;
            }
        }
    }
}
