using Microsoft.Playwright;
using Microsoft.Extensions.DependencyInjection;
using ShopBot.Config;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.IO;
using System.Collections.Generic;
using System.Linq;

namespace ShopBot.Services
{
    public class G2GOfferStatusChecker
    {
        private readonly ParserService _parserService;
        private readonly IBrowser _browser;
        private readonly IBrowserContext _context;
        private readonly ProcessedOffersTracker _tracker;
        private readonly IServiceProvider _serviceProvider;
        private const string InactiveOffersFile = "inactive_g2g_offers.json";
        private const string FilteredOffersDir = "filtered_offers";
        private const string OffersTrackingFile = "offers_tracking.json";

        public G2GOfferStatusChecker(ParserService parserService, IBrowser browser, IBrowserContext context, ProcessedOffersTracker tracker, IServiceProvider serviceProvider)
        {
            _parserService = parserService;
            _browser = browser;
            _context = context;
            _tracker = tracker;
            _serviceProvider = serviceProvider;

            // Create filtered_offers directory if it doesn't exist
            if (!Directory.Exists(FilteredOffersDir))
            {
                Directory.CreateDirectory(FilteredOffersDir);
            }
        }

        private async Task CleanupOffersTracking()
        {
            Console.WriteLine("\n=== Rozpoczynam czyszczenie offers_tracking.json ===");
            
            if (!File.Exists(OffersTrackingFile))
            {
                Console.WriteLine("[Info] Plik offers_tracking.json nie istnieje. Nie ma nic do wyczyszczenia.");
                return;
            }

            try
            {
                // Wczytaj aktywne oferty z g2g_offers.json
                var g2gOffers = await LoadG2GOffers();
                var activeIds = g2gOffers.Select(o => o.FunPayId).ToHashSet();

                // Wczytaj i deserializuj offers_tracking.json
                var jsonContent = await File.ReadAllTextAsync(OffersTrackingFile);
                Console.WriteLine($"\n[Debug] Zawartość offers_tracking.json przed czyszczeniem:\n{jsonContent}");

                var tracking = JsonSerializer.Deserialize<TrackedOffersData>(jsonContent);
                if (tracking == null || tracking.ProcessedOffers == null)
                {
                    Console.WriteLine("[Warning] Nie udało się zdeserializować offers_tracking.json");
                    return;
                }

                var beforeCount = tracking.ProcessedOffers.Count;
                
                // Usuń wpisy których nie ma w g2g_offers.json
                var idsToRemove = tracking.ProcessedOffers.Where(id => !activeIds.Contains(id)).ToList();
                foreach (var id in idsToRemove)
                {
                    tracking.ProcessedOffers.Remove(id);
                    Console.WriteLine($"[Info] Usunięto FunPayId: {id} z offers_tracking.json (brak w g2g_offers.json)");
                }

                // Zapisz zaktualizowaną zawartość
                var updatedJson = JsonSerializer.Serialize(tracking, new JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(OffersTrackingFile, updatedJson);

                Console.WriteLine($"\n[Info] Zakończono czyszczenie offers_tracking.json");
                Console.WriteLine($"- Liczba wpisów przed: {beforeCount}");
                Console.WriteLine($"- Liczba usuniętych wpisów: {idsToRemove.Count}");
                Console.WriteLine($"- Liczba pozostałych wpisów: {tracking.ProcessedOffers.Count}");
                Console.WriteLine("\n[Debug] Aktywne FunPayId w g2g_offers.json:");
                foreach (var id in activeIds)
                {
                    Console.WriteLine($"- {id}");
                }
                Console.WriteLine("\n[Debug] Pozostałe FunPayId w offers_tracking.json:");
                foreach (var id in tracking.ProcessedOffers)
                {
                    Console.WriteLine($"- {id}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[Error] Błąd podczas czyszczenia offers_tracking.json: {ex.Message}");
                throw;
            }
        }

        public async Task<List<string>> CheckOffersStatus()
        {
            Console.WriteLine("\n=== Rozpoczynam sprawdzanie statusu ofert ===");
            
            // Najpierw sprawdź czy istnieje g2g_offers.json i czy ma jakieś oferty
            if (!File.Exists("g2g_offers.json"))
            {
                Console.WriteLine("[Warning] Plik g2g_offers.json nie istnieje. Pomijam czyszczenie offers_tracking.json");
                return new List<string>();
            }

            // Wyczyść offers_tracking.json na podstawie g2g_offers.json
            await CleanupOffersTracking();
            
            var g2gOffers = await LoadG2GOffers();
            if (g2gOffers.Count == 0)
            {
                Console.WriteLine("[Warning] Brak ofert w g2g_offers.json");
                return new List<string>();
            }

            var allActiveWebsiteIds = new HashSet<string>();
            var gameFilteredOffers = new Dictionary<string, List<string>>();
            
            Console.WriteLine("\n=== Rozpoczynam sprawdzanie statusu ofert ===");
            
            var allFunPayIds = g2gOffers.Select(o => o.FunPayId).ToList();
            Console.WriteLine($"Liczba ofert w g2g_offers.json: {allFunPayIds.Count}");

            // Get unique games from g2g_offers.json
            var gamesToScan = g2gOffers.Select(o => o.GameName).Distinct().Where(g => !string.IsNullOrEmpty(g)).ToList();
            Console.WriteLine($"Gry do sprawdzenia: {string.Join(", ", gamesToScan)}");

            // Lista zadań do równoległego skanowania
            var scanTasks = new List<Task<(string Game, List<string> ActiveIds)>>();

            // Skanuj każdą grę w osobnej zakładce równolegle
            foreach (var game in gamesToScan)
            {
                var scanTask = Task.Run(async () =>
                {
                    Console.WriteLine($"\nSprawdzam oferty dla gry {game}...");
                    var activeIds = new List<string>();
                    IPage page = null;

                    try
                    {
                        page = await _context.NewPageAsync();
                        var funPayServiceFactory = _serviceProvider.GetRequiredService<Func<IPage, FunPayService>>();
                        var funPayService = funPayServiceFactory(page);
                        var filterConfig = FilterConfig.LoadFromFile();
                        var filteredOffers = await funPayService.GetFilteredOffers(filterConfig.Filter, game);
                        activeIds = filteredOffers.Select(o => o.Id).ToList();
                        Console.WriteLine($"Znaleziono {activeIds.Count} aktywnych ofert dla {game}");
                        return (Game: game, ActiveIds: activeIds);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"❌ Błąd podczas skanowania {game}: {ex.Message}");
                        return (Game: game, ActiveIds: new List<string>());
                    }
                    finally
                    {
                        if (page != null)
                        {
                            await page.CloseAsync();
                        }
                    }
                });

                scanTasks.Add(scanTask);
            }

            // Czekamy na zakończenie wszystkich skanowań
            var results = await Task.WhenAll(scanTasks);

            // Zbieramy wyniki
            foreach (var result in results)
            {
                gameFilteredOffers[result.Game] = result.ActiveIds;
                foreach (var id in result.ActiveIds)
                {
                    allActiveWebsiteIds.Add(id);
                    var offer = g2gOffers.FirstOrDefault(o => o.FunPayId == id);
                    if (offer != null)
                    {
                        offer.GameName = result.Game;
                    }
                }
            }

            // Find which FunPayIds from g2g_offers.json are not present in any game
            var inactiveOffers = allFunPayIds.Where(id => !allActiveWebsiteIds.Contains(id)).ToList();

            if (inactiveOffers.Any())
            {
                Console.WriteLine($"\nZnaleziono {inactiveOffers.Count} nieaktywnych ofert G2G");
                
                // Save and remove inactive offers from all files
                await SaveInactiveOffers(inactiveOffers);
                await RemoveInactiveOffersFromTracking(inactiveOffers);
                await RemoveInactiveOffersFromG2G(inactiveOffers);
                
                Console.WriteLine("=== Zakończono przetwarzanie nieaktywnych ofert ===");
            }
            else
            {
                Console.WriteLine("\nWszystkie oferty G2G są nadal aktywne");
            }

            // Save summary of all filtered offers
            await SaveFilteredOffersSummary(gameFilteredOffers);
            
            return inactiveOffers;
        }

        private async Task<List<G2GOffer>> LoadG2GOffers()
        {
            if (!File.Exists("g2g_offers.json"))
            {
                Console.WriteLine("Plik g2g_offers.json nie istnieje!");
                return new List<G2GOffer>();
            }

            try 
            {
                var lines = await File.ReadAllLinesAsync("g2g_offers.json");
                var jsonContent = string.Join("", lines);
                
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    WriteIndented = true
                };
                
                var offers = JsonSerializer.Deserialize<List<G2GOffer>>(jsonContent, options);
                
                if (offers != null)
                {
                    return offers.Where(o => o != null && o.IsValid()).ToList();
                }
                
                return new List<G2GOffer>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Błąd podczas wczytywania g2g_offers.json: {ex.Message}");
                return new List<G2GOffer>();
            }
        }

        private async Task SaveGameFilteredOffers(string game, List<string> offerIds)
        {
            try
            {
                var filePath = Path.Combine(FilteredOffersDir, $"{game}_filtered_offers.json");
                var data = new
                {
                    Game = game,
                    FilteredAt = DateTime.UtcNow,
                    OffersCount = offerIds.Count,
                    OfferIds = offerIds
                };
                
                var jsonContent = JsonSerializer.Serialize(data, new JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(filePath, jsonContent);
                Console.WriteLine($"Saved {offerIds.Count} filtered offers for {game} to {filePath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving filtered offers for {game}: {ex.Message}");
            }
        }

        private async Task SaveFilteredOffersSummary(Dictionary<string, List<string>> gameFilteredOffers)
        {
            var filePath = Path.Combine(FilteredOffersDir, "filtered_offers_summary.json");
            var json = JsonSerializer.Serialize(gameFilteredOffers, new JsonSerializerOptions { WriteIndented = true });
            await File.WriteAllTextAsync(filePath, json);
        }

        private async Task SaveInactiveOffers(List<string> inactiveFunPayIds)
        {
            try
            {
                Console.WriteLine("[DEBUG] Rozpoczynam zapisywanie nieaktywnych ofert...");
                
                // Load current G2G offers
                var g2gOffers = await LoadG2GOffers();
                Console.WriteLine($"[DEBUG] Załadowano {g2gOffers.Count} ofert z g2g_offers.json");
                
                // Find inactive offers (zachowujemy wszystkie oryginalne dane)
                var inactiveOffers = g2gOffers.Where(o => inactiveFunPayIds.Contains(o.FunPayId)).ToList();
                Console.WriteLine($"[DEBUG] Znaleziono {inactiveOffers.Count} nieaktywnych ofert");

                // Load existing inactive offers if file exists
                var existingInactiveOffers = new List<G2GOffer>();
                if (File.Exists(InactiveOffersFile))
                {
                    try
                    {
                        var existingContent = await File.ReadAllTextAsync(InactiveOffersFile);
                        existingInactiveOffers = JsonSerializer.Deserialize<List<G2GOffer>>(existingContent) ?? new List<G2GOffer>();
                        Console.WriteLine($"[DEBUG] Załadowano {existingInactiveOffers.Count} istniejących nieaktywnych ofert");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"[DEBUG] Błąd podczas wczytywania istniejących nieaktywnych ofert: {ex.Message}");
                        existingInactiveOffers = new List<G2GOffer>();
                    }
                }

                // Add new inactive offers (tylko te, których jeszcze nie ma)
                var newInactiveOffers = inactiveOffers
                    .Where(newOffer => !existingInactiveOffers.Any(existingOffer => 
                        existingOffer.FunPayId == newOffer.FunPayId))
                    .ToList();

                existingInactiveOffers.AddRange(newInactiveOffers);
                Console.WriteLine($"[DEBUG] Dodano {newInactiveOffers.Count} nowych nieaktywnych ofert");

                // Save in the same format as g2g_offers.json
                var lines = new List<string>();
                lines.Add("[");
                
                for (int i = 0; i < existingInactiveOffers.Count; i++)
                {
                    var offer = existingInactiveOffers[i];
                    var line = $"    {{\"funPayId\":\"{offer.FunPayId}\",\"g2GOfferId\":\"{offer.G2GOfferId}\",\"gameName\":\"{offer.GameName}\"}}";
                    if (i < existingInactiveOffers.Count - 1)
                        line += ",";
                    lines.Add(line);
                }
                
                lines.Add("]");

                await File.WriteAllLinesAsync(InactiveOffersFile, lines);
                Console.WriteLine($"Saved {inactiveOffers.Count} inactive offers to {InactiveOffersFile}");
                
                // Debug info
                Console.WriteLine("\n[DEBUG] Przykładowe nieaktywne oferty:");
                foreach (var offer in existingInactiveOffers.Take(5))
                {
                    Console.WriteLine($"- FunPayId: {offer.FunPayId}");
                    Console.WriteLine($"  G2GOfferId: {offer.G2GOfferId}");
                    Console.WriteLine($"  GameName: {offer.GameName}");
                    Console.WriteLine("---");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DEBUG] ❌ Błąd podczas zapisywania nieaktywnych ofert: {ex.Message}");
                Console.WriteLine($"[DEBUG] Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        private async Task RemoveInactiveOffersFromTracking(List<string> inactiveOffers)
        {
            try
            {
                if (!inactiveOffers.Any())
                {
                    Console.WriteLine("No inactive offers to remove from tracking.");
                    return;
                }

                // Remove from offers_tracking.json
                if (File.Exists(OffersTrackingFile))
                {
                    var jsonContent = await File.ReadAllTextAsync(OffersTrackingFile);
                    var tracking = JsonSerializer.Deserialize<TrackedOffersData>(jsonContent);

                    if (tracking != null && tracking.ProcessedOffers != null)
                    {
                        var originalCount = tracking.ProcessedOffers.Count;
                        tracking.ProcessedOffers.RemoveAll(id => inactiveOffers.Contains(id));
                        
                        // Save the updated tracking data
                        var updatedJson = JsonSerializer.Serialize(tracking, new JsonSerializerOptions { WriteIndented = true });
                        await File.WriteAllTextAsync(OffersTrackingFile, updatedJson);
                        
                        var removedCount = originalCount - tracking.ProcessedOffers.Count;
                        Console.WriteLine($"Removed {removedCount} inactive offers from tracking");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error removing inactive offers from tracking: {ex.Message}");
            }
        }

        private async Task RemoveInactiveOffersFromG2G(List<string> inactiveFunPayIds)
        {
            try
            {
                Console.WriteLine("[DEBUG] Rozpoczynam usuwanie nieaktywnych ofert z g2g_offers.json");
                
                // Load current G2G offers
                var g2gOffers = await LoadG2GOffers();
                Console.WriteLine($"[DEBUG] Załadowano {g2gOffers.Count} ofert");
                
                // Remove inactive offers but preserve format
                var activeOffers = g2gOffers.Where(offer => !inactiveFunPayIds.Contains(offer.FunPayId)).ToList();
                Console.WriteLine($"[DEBUG] Pozostało {activeOffers.Count} aktywnych ofert");
                
                // Formatujemy JSON ręcznie, jeden obiekt na linię
                var options = new JsonSerializerOptions 
                { 
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };

                var jsonContent = JsonSerializer.Serialize(activeOffers, options);
                await File.WriteAllTextAsync("g2g_offers.json", jsonContent);
                
                Console.WriteLine($"[DEBUG] Zapisano {activeOffers.Count} aktywnych ofert do g2g_offers.json");
                Console.WriteLine($"Removed {inactiveFunPayIds.Count} inactive offers from g2g_offers.json");
                Console.WriteLine($"Remaining active offers: {activeOffers.Count}");
                
                // Debug info
                if (activeOffers.Any())
                {
                    Console.WriteLine("\n[DEBUG] Przykładowe aktywne oferty:");
                    foreach (var offer in activeOffers.Take(5))
                    {
                        Console.WriteLine($"- FunPayId: {offer.FunPayId}");
                        Console.WriteLine($"  G2GOfferId: {offer.G2GOfferId}");
                        Console.WriteLine($"  GameName: {offer.GameName}");
                        Console.WriteLine("---");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DEBUG] ❌ Błąd podczas usuwania nieaktywnych ofert: {ex.Message}");
                Console.WriteLine($"[DEBUG] Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        private class TrackedOffersData
        {
            public DateTime LastProcessingTime { get; set; }
            public List<string> ProcessedOffers { get; set; } = new();
        }

        private class G2GOffer
        {
            private string _funPayId;
            private string _g2gOfferId;
            private string _gameName;

            public string FunPayId 
            { 
                get => _funPayId;
                set => _funPayId = !string.IsNullOrEmpty(value) ? value : throw new ArgumentException("FunPayId nie może być puste");
            }
            
            public string G2GOfferId 
            { 
                get => _g2gOfferId;
                set => _g2gOfferId = !string.IsNullOrEmpty(value) ? value : throw new ArgumentException("G2GOfferId nie może być puste");
            }
            
            public string GameName 
            { 
                get => _gameName;
                set => _gameName = !string.IsNullOrEmpty(value) ? value : throw new ArgumentException("GameName nie może być puste");
            }

            public bool IsValid()
            {
                return !string.IsNullOrEmpty(FunPayId) && 
                       !string.IsNullOrEmpty(G2GOfferId) && 
                       !string.IsNullOrEmpty(GameName);
            }
        }
    }
}
