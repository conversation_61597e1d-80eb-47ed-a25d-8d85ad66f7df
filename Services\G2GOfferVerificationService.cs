using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Playwright;
using System.Linq;
using System.Net.Http;
using System.Text.Json.Serialization;
using ShopBot.Tasks;
using System.Threading;

namespace ShopBot.Services
{
    public class G2GOfferVerificationService
    {
        private readonly IBrowser _browser;
        private readonly IBrowserContext _context;
        private readonly ShopBot.Tasks.TaskScheduler _taskScheduler;
        private readonly DeleteService _deleteService;
        private readonly ProcessedOffersTracker _processedOffersTracker;
        private readonly HashSet<string> _verifiedActiveOffers;
        private const string ROOT_ID = "5830014a-b974-45c6-9672-b51e83112fb7";
        private const string SERVICE_ID = "f6a1aba5-473a-4044-836a-8968bbab16d7";
        private const string API_URL = "https://sls.g2g.com/offer/seller/7670672/brands?group_by=service&root_id={0}&service_id={1}&include_out_of_stock=1&include_inactive=0";

        private readonly Dictionary<string, string> _brandToGameName = new Dictionary<string, string>();
        private readonly Dictionary<string, string> _gameNameToBrand = new Dictionary<string, string>();

        private Dictionary<string, string> _gameUrlFormats = new Dictionary<string, string>
        {
            {"RaidShadowLegends", "raid-shadow-legends-account"},
            {"MobileLegends", "mobile-legends-account"},
            {"ApexLegends", "apex-legends-account"},
            {"GenshinImpact", "genshin-impact-account"},
            {"WatcherOfRealms", "watcher-of-realms-accounts"},
            {"Albion", "albion-online-global-account"},
            {"EscapeFromTarkov", "escape-from-tarkov-accounts"},
            {"PUBG", "playerunknown-s-battlegrounds-account"},
            {"FC 25", "fc-25-accounts"},
            {"Rust", "rus-accounts-buy"},
            {"LeagueOfLegends", "league-of-legends"}
        };

        private string GetSearchUrl(string gameName, int pageIndex)
        {
            var seoTerm = _gameUrlFormats[gameName];
            return $"https://sls.g2g.com/offer/search?seo_term={seoTerm}&sort=recommended&page={pageIndex + 1}&page_size=100&currency=USD&country=PL&seller=TrustCraft&include_localization=0";
        }

        private async Task VerifyGameOffers(string gameName, int totalOffers, IPage page)
        {
            Console.WriteLine($"\n[Verify] Sprawdzam oferty dla {gameName}");
            var pages = (int)Math.Ceiling(totalOffers / 100.0);
            var activeOffers = new Dictionary<string, string>(); // G2GOfferId -> FunPayId
            var missingFunPayIdOffers = new List<(string G2GOfferId, string Title, string Description)>();
            var processedOfferIds = new HashSet<string>();
            var funPayIdToG2GOffers = new Dictionary<string, List<string>>(); // FunPayId -> List<G2GOfferId>

            for (int i = 0; i < pages; i++)
            {
                var url = GetSearchUrl(gameName, i);
                var startIndex = i * 100 + 1;
                var endIndex = Math.Min((i + 1) * 100, totalOffers);
                Console.WriteLine($"[Verify] Sprawdzam stronę {i+1}/{pages} ({startIndex}-{endIndex} ofert)");
                
                try
                {
                    // Ustawiam timeout na 15 sekund dla każdej operacji
                    var navigationOptions = new PageGotoOptions { Timeout = 15000 };
                    await page.GotoAsync(url, navigationOptions);
                    await Task.Delay(1000); // Zmniejszono z 2000ms

                    var content = await page.TextContentAsync("pre");
                    if (string.IsNullOrEmpty(content))
                    {
                        Console.WriteLine($"[Verify] ⚠️ Nie znaleziono danych na stronie {i+1}, próbuję ponownie...");
                        // Jeszcze jedna próba
                        await Task.Delay(2000);
                        content = await page.TextContentAsync("pre");
                        
                        if (string.IsNullOrEmpty(content))
                        {
                            Console.WriteLine($"[Verify] ❌ Pomijam stronę {i+1} - brak danych");
                            continue;
                        }
                    }

                    try
                    {
                        var searchResponse = JsonSerializer.Deserialize<SearchResponse>(content);
                        if (searchResponse?.Payload?.Results != null)
                        {
                            foreach (var offer in searchResponse.Payload.Results)
                            {
                                // Sprawdzamy czy oferta nie była już przetworzona
                                if (processedOfferIds.Contains(offer.Offer_id))
                                {
                                    Console.WriteLine($"[Verify] ⚠️ Pomijam duplikat oferty G2G: {offer.Offer_id}");
                                    continue;
                                }

                                processedOfferIds.Add(offer.Offer_id);
                                _verifiedActiveOffers.Add(offer.Offer_id);
                                var funPayId = ExtractFunPayId(offer.Description);
                                
                                if (!string.IsNullOrEmpty(funPayId))
                                {
                                    activeOffers[offer.Offer_id] = funPayId;
                                    
                                    // Sprawdzanie duplikatów FunPayId
                                    if (!funPayIdToG2GOffers.ContainsKey(funPayId))
                                    {
                                        funPayIdToG2GOffers[funPayId] = new List<string>();
                                    }
                                    funPayIdToG2GOffers[funPayId].Add(offer.Offer_id);
                                }
                                else
                                {
                                    Console.WriteLine($"[Verify] ⚠️ Nie znaleziono FunPay ID dla oferty {offer.Offer_id}");
                                    missingFunPayIdOffers.Add((offer.Offer_id, offer.Title, offer.Description));
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"[Verify] ❌ Błąd podczas przetwarzania strony {i+1}: {ex.Message}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[Verify] ❌ Błąd podczas przeglądania strony {i+1}: {ex.Message}");
                    continue;
                }
            }

            // Sprawdź i raportuj duplikaty FunPayId
            var duplicates = funPayIdToG2GOffers.Where(kvp => kvp.Value.Count > 1).ToList();
            if (duplicates.Any())
            {
                Console.WriteLine($"\n[Verify] ⚠️ Znaleziono {duplicates.Count} zduplikowanych FunPayId dla {gameName}:");
                var deleteTasks = new List<Task>();
                foreach (var duplicate in duplicates)
                {
                    Console.WriteLine($"[Verify] FunPayId: {duplicate.Key}");
                    Console.WriteLine($"[Verify] G2G Oferty ({duplicate.Value.Count}):");
                    
                    // Zachowaj pierwszą ofertę, usuń pozostałe
                    var offersToDelete = duplicate.Value.Skip(1).ToList();
                    Console.WriteLine($"[Verify] ✓ Zachowuję ofertę: {duplicate.Value[0]}");
                    
                    foreach (var g2gId in offersToDelete)
                    {
                        Console.WriteLine($"[Verify] 🗑️ Planuję usunięcie duplikatu: {g2gId}");
                        var deleteTask = new DeleteOfferTask(g2gId, _deleteService);
                        deleteTasks.Add(_taskScheduler.ScheduleTask(deleteTask));
                    }
                }

                // Nie czekamy na zakończenie zadań - kontynuujemy pracę
                Console.WriteLine($"[Verify] 📋 Zaplanowano usunięcie {deleteTasks.Count} duplikatów");

                // Zapisz raport o duplikatach
                var duplicatesReport = new
                {
                    GameName = gameName,
                    Timestamp = DateTime.Now,
                    TotalDuplicates = duplicates.Count,
                    Details = duplicates.Select(d => new
                    {
                        FunPayId = d.Key,
                        G2GOfferIds = d.Value,
                        Count = d.Value.Count
                    }).ToList()
                };

                var fileName = $"duplicate_funpay_ids_{gameName}_{DateTime.Now:yyyyMMdd_HHmmss}.json";
                await File.WriteAllTextAsync(fileName, 
                    JsonSerializer.Serialize(duplicatesReport, new JsonSerializerOptions { WriteIndented = true }));
                Console.WriteLine($"[Verify] 📝 Zapisano raport o duplikatach do pliku: {fileName}");
            }

            // Zapisz listę ofert bez FunPay ID i opcjonalnie je usuń
            if (missingFunPayIdOffers.Any())
            {
                var fileName = $"missing_funpay_ids_{gameName}_{DateTime.Now:yyyyMMdd_HHmmss}.json";
                var report = new
                {
                    GameName = gameName,
                    Timestamp = DateTime.Now,
                    TotalMissingIds = missingFunPayIdOffers.Count,
                    Offers = missingFunPayIdOffers.Select(o => new
                    {
                        G2GOfferId = o.G2GOfferId,
                        Title = o.Title,
                        Description = o.Description
                    }).ToList()
                };

                await File.WriteAllTextAsync(fileName, 
                    JsonSerializer.Serialize(report, new JsonSerializerOptions { WriteIndented = true }));
                Console.WriteLine($"[Verify] 📝 Zapisano {missingFunPayIdOffers.Count} ofert bez FunPay ID do pliku: {fileName}");

                // Usuń oferty bez FunPay ID
                Console.WriteLine($"[Verify] 🗑️ Usuwam {missingFunPayIdOffers.Count} ofert bez FunPay ID...");
                var deleteTasks = new List<Task>();
                foreach (var offer in missingFunPayIdOffers)
                {
                    var deleteTask = new DeleteOfferTask(offer.G2GOfferId, _deleteService);
                    deleteTasks.Add(_taskScheduler.ScheduleTask(deleteTask));
                    Console.WriteLine($"[Verify] ❌ Zaplanowano usunięcie oferty: {offer.G2GOfferId}");
                }
                
                // Nie czekamy na zakończenie zadań - kontynuujemy pracę
                Console.WriteLine($"[Verify] 📋 Zaplanowano usunięcie {deleteTasks.Count} ofert bez FunPay ID");
            }

            // Aktualizuj g2g_offers.json
            var trackedOffers = G2GOfferTracker.LoadAllOffers();
            
            // Usuń stare oferty dla tej gry
            foreach (var oldOffer in trackedOffers.Where(o => o.GameName == gameName).ToList())
            {
                G2GOfferTracker.RemoveOffer(oldOffer.G2GOfferId);
            }

            // Dodaj aktywne oferty
            foreach (var (g2gId, funPayId) in activeOffers)
            {
                G2GOfferTracker.AddOrUpdateOffer(new G2GOfferTracker.TrackedOffer
                {
                    G2GOfferId = g2gId,
                    FunPayId = funPayId,
                    GameName = gameName
                });
            }

            Console.WriteLine($"\n[Verify] Podsumowanie dla {gameName}:");
            Console.WriteLine($"[Verify] • Znalezione aktywne oferty: {activeOffers.Count}");
            Console.WriteLine($"[Verify] • Oferty bez FunPay ID: {missingFunPayIdOffers.Count}");
            Console.WriteLine($"[Verify] • Zaktualizowano g2g_offers.json");
        }

        private string ExtractFunPayId(string description)
        {
            if (string.IsNullOrEmpty(description)) return null;

            // FunPay ID jest na końcu opisu w nawiasach
            var match = System.Text.RegularExpressions.Regex.Match(description, @"\((\d+)\)\s*$");
            return match.Success ? match.Groups[1].Value : null;
        }

        public class ApiResponse
        {
            [JsonPropertyName("code")]
            public int Code { get; set; }
            
            [JsonPropertyName("messages")]
            public List<string> Messages { get; set; }
            
            [JsonPropertyName("payload")]
            public ApiPayload Payload { get; set; }
        }

        public class ApiPayload
        {
            [JsonPropertyName("results")]
            public List<ApiResult> Results { get; set; }
        }

        public class ApiResult
        {
            [JsonPropertyName("root_id")]
            public string Root_id { get; set; }
            
            [JsonPropertyName("service_id")]
            public string Service_id { get; set; }
            
            [JsonPropertyName("total_result")]
            public int Total_result { get; set; }
            
            [JsonPropertyName("brands")]
            public List<ApiBrand> Brands { get; set; }
        }

        public class ApiBrand
        {
            [JsonPropertyName("brand_id")]
            public string Brand_id { get; set; }
            
            [JsonPropertyName("regions")]
            public List<object> Regions { get; set; }
            
            [JsonPropertyName("total_result")]
            public int Total_result { get; set; }
        }

        public class SearchResponse
        {
            [JsonPropertyName("code")]
            public int Code { get; set; }

            [JsonPropertyName("messages")]
            public List<string> Messages { get; set; }

            [JsonPropertyName("payload")]
            public SearchPayload Payload { get; set; }
        }

        public class SearchPayload
        {
            [JsonPropertyName("results")]
            public List<SearchResult> Results { get; set; }
        }

        public class SearchResult
        {
            [JsonPropertyName("offer_id")]
            public string Offer_id { get; set; }

            [JsonPropertyName("title")]
            public string Title { get; set; }

            [JsonPropertyName("status")]
            public string Status { get; set; }

            [JsonPropertyName("description")]
            public string Description { get; set; }
        }

        private void InitializeGameMappings()
        {
            var mappings = new Dictionary<string, string>
            {
                // ✅ AKTYWNE GRY (włączone w ActiveGames)
                {"lgc_game_26258", "RaidShadowLegends"},  // Raid: Shadow Legends
                {"lgc_game_32536", "WatcherOfRealms"},    // Watcher of Realms

                // 💤 NIEAKTYWNE GRY (wyłączone w ActiveGames - można łatwo włączyć)
                {"lgc_game_23957", "MobileLegends"},      // Mobile Legends
                {"lgc_game_25694", "ApexLegends"},        // Apex Legends
                {"lgc_game_28151", "GenshinImpact"},      // Genshin Impact
                {"lgc_game_21695", "Albion"},             // Albion Online
                {"lgc_game_24162", "PUBG"},               // PUBG
                {"lgc_game_24640", "EscapeFromTarkov"},   // Escape from Tarkov
                {"lgc_game_34234", "FC 25"},              // FIFA 25
                {"lgc_game_24849", "Rust"},               // Rust
                {"lgc_game_22666", "LeagueOfLegends"},    // League of Legends
                {"lgc_game_24641", "Valorant"},           // Valorant
                {"lgc_game_24643", "PUBGMobile"},         // PUBG Mobile
                {"lgc_game_24644", "Fortnite"},           // Fortnite
                {"lgc_game_24645", "WorldOfWarcraft"},    // World of Warcraft
                {"lgc_game_24646", "Dota2"}               // Dota 2
            };

            foreach (var mapping in mappings)
            {
                _brandToGameName[mapping.Key] = mapping.Value;
                _gameNameToBrand[mapping.Value] = mapping.Key;
            }
        }

        public G2GOfferVerificationService(IBrowser browser, IBrowserContext context, ShopBot.Tasks.TaskScheduler taskScheduler, DeleteService deleteService)
        {
            _browser = browser;
            _context = context;
            _taskScheduler = taskScheduler;
            _deleteService = deleteService;
            _processedOffersTracker = new ProcessedOffersTracker();
            _verifiedActiveOffers = new HashSet<string>();
            InitializeGameMappings();
        }

        public async Task VerifyOffers()
        {
            IPage page = null;
            var startTime = DateTime.Now;
            try 
            {
                Console.WriteLine("\n=== Rozpoczynam weryfikację ofert na G2G ===");
                Console.WriteLine($"[Verify] ⏱️ Czas rozpoczęcia: {startTime.ToString("HH:mm:ss")}");
                page = await _context.NewPageAsync();
                var apiUrl = string.Format(API_URL, ROOT_ID, SERVICE_ID);
                
                Console.WriteLine("[Verify] 🌐 Pobieram ogólne statystyki z G2G...");
                await page.GotoAsync(apiUrl);
                
                var content = await page.TextContentAsync("pre");
                if (string.IsNullOrEmpty(content))
                {
                    Console.WriteLine("[Verify] ℹ️ Brak ofert na G2G - czyszczę pliki śledzenia");
                    await ClearTrackingFiles();
                    return;
                }

                try 
                {
                    var apiData = JsonSerializer.Deserialize<ApiResponse>(content);
                    if (apiData?.Payload?.Results == null || !apiData.Payload.Results.Any())
                    {
                        Console.WriteLine("[Verify] ℹ️ Brak ofert na G2G - czyszczę pliki śledzenia");
                        await ClearTrackingFiles();
                        return;
                    }

                    var result = apiData.Payload.Results.First();
                    var totalOffersOnG2G = result.Total_result;
                    Console.WriteLine($"[Verify] 📊 Liczba ofert na G2G: {totalOffersOnG2G}");

                    // Przetwarzanie równoległe - do 3 gier jednocześnie
                    var gameTasks = new List<Task>();
                    var maxConcurrentTasks = 3;
                    var semaphore = new SemaphoreSlim(maxConcurrentTasks);
                    
                    Console.WriteLine("[Verify] 🚀 Uruchamiam równoległe przetwarzanie gier (max 3 jednocześnie)");

                    foreach (var brand in result.Brands)
                    {
                        var gameName = _brandToGameName.GetValueOrDefault(brand.Brand_id, "Unknown Game");
                        if (gameName != "Unknown Game")
                        {
                            // Czekaj na dostępny slot
                            await semaphore.WaitAsync();
                            
                            // Utwórz i uruchom zadanie dla gry
                            var gameTask = Task.Run(async () => 
                            {
                                IPage gamePage = null;
                                try 
                                {
                                    // Otwórz nową kartę dla każdej gry
                                    gamePage = await _context.NewPageAsync();
                                    await VerifyGameOffers(gameName, brand.Total_result, gamePage);
                                }
                                finally 
                                {
                                    // Zamknij stronę po zakończeniu
                                    if (gamePage != null)
                                    {
                                        await gamePage.CloseAsync();
                                    }
                                    // Zwolnij slot po zakończeniu
                                    semaphore.Release();
                                }
                            });
                            
                            gameTasks.Add(gameTask);
                        }
                    }
                    
                    // Czekaj na zakończenie wszystkich zadań
                    await Task.WhenAll(gameTasks);
                    Console.WriteLine("[Verify] ✅ Zakończono weryfikację wszystkich gier");
                    
                    // Czekamy na zakończenie wszystkich zaplanowanych zadań
                    Console.WriteLine("\n[Verify] 🔄 Wszystkie gry zweryfikowane. Oczekuję na zakończenie zadań usuwania...");
                    Console.WriteLine("[Verify] ⏳ Ten proces może potrwać kilka minut, w zależności od liczby ofert do usunięcia");
                    
                    // Dodajemy timeout na oczekiwanie na zadania usuwania
                    try 
                    {
                        var waitTask = _taskScheduler.WaitForAllTasks();
                        if (await Task.WhenAny(waitTask, Task.Delay(TimeSpan.FromMinutes(1))) == waitTask)
                        {
                            // Zadania zakończyły się w czasie
                            Console.WriteLine("[Verify] ✅ Wszystkie zadania usuwania zakończone pomyślnie");
                        }
                        else
                        {
                            // Przekroczono limit czasu
                            Console.WriteLine("[Verify] ⚠️ Przekroczono limit czasu oczekiwania (1 min)");
                            Console.WriteLine("[Verify] ℹ️ Kontynuuję pracę - pozostałe zadania będą wykonywane w tle");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"[Verify] ⚠️ Błąd podczas oczekiwania na zadania: {ex.Message}");
                        Console.WriteLine("[Verify] ℹ️ Kontynuuję pracę mimo błędu");
                    }

                    // Zapisz raport końcowy
                    await SaveFinalReport(totalOffersOnG2G, G2GOfferTracker.LoadAllOffers().Count, 
                        result.Brands.ToDictionary(b => b.Brand_id, b => b.Total_result));

                    // Na końcu metody, przed zwrotem
                    var endTime = DateTime.Now;
                    var duration = endTime - startTime;
                    Console.WriteLine($"\n[Verify] ⏱️ Zakończono weryfikację w {duration.TotalMinutes:F1} minut ({duration.TotalSeconds:F0} sekund)");
                    Console.WriteLine($"[Verify] ⏱️ Czas rozpoczęcia: {startTime.ToString("HH:mm:ss")}, zakończenia: {endTime.ToString("HH:mm:ss")}");
                    Console.WriteLine("\n=== Zakończono weryfikację ofert G2G ===");
                }
                catch (JsonException)
                {
                    Console.WriteLine("[Verify] ℹ️ Brak ofert na G2G - czyszczę pliki śledzenia");
                    await ClearTrackingFiles();
                    return;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[Verify] ❌ Błąd podczas weryfikacji: {ex.Message}");
                throw;
            }
            finally
            {
                if (page != null)
                {
                    await page.CloseAsync();
                }
            }
        }

        private async Task ClearTrackingFiles()
        {
            // Wyczyść g2g_offers.json
            G2GOfferTracker.ClearAllOffers();
            
            // Wyczyść offers_tracking.json
            if (File.Exists("offers_tracking.json"))
            {
                var tracking = new
                {
                    LastProcessingTime = DateTime.UtcNow,
                    ProcessedOffers = new List<string>()
                };
                await File.WriteAllTextAsync("offers_tracking.json", 
                    JsonSerializer.Serialize(tracking, new JsonSerializerOptions { WriteIndented = true }));
            }
            
            Console.WriteLine("[Verify] ✅ Pliki śledzenia zostały wyczyszczone");
        }

        private async Task SaveFinalReport(int totalActual, int totalJson, Dictionary<string, int> offersByGame)
        {
            var report = new
            {
                Timestamp = DateTime.Now,
                TotalOffersOnG2G = totalActual,
                TotalOffersInJson = totalJson,
                TotalDifference = totalActual - totalJson,
                OffersByGame = offersByGame.ToDictionary(
                    kvp => _brandToGameName.GetValueOrDefault(kvp.Key, "Unknown Game"),
                    kvp => kvp.Value
                ),
                ApiDetails = new
                {
                    RootId = ROOT_ID,
                    ServiceId = SERVICE_ID,
                    BrandDetails = offersByGame.Select(kvp => new
                    {
                        BrandId = kvp.Key,
                        GameName = _brandToGameName.GetValueOrDefault(kvp.Key, "Unknown Game"),
                        OffersCount = kvp.Value
                    }).ToList()
                }
            };

            var fileName = $"verification_final_report_{DateTime.Now:yyyyMMdd_HHmmss}.json";
            await File.WriteAllTextAsync(fileName, JsonSerializer.Serialize(report, new JsonSerializerOptions { WriteIndented = true }));
            Console.WriteLine($"\n[Verify] 📝 Zapisano raport końcowy: {fileName}");
        }

        public async Task TestApiRequest()
        {
            Console.WriteLine("\n=== Test połączenia z API G2G ===");
            IPage page = null;
            try
            {
                page = await _context.NewPageAsync();
                var apiUrl = string.Format(API_URL, ROOT_ID, SERVICE_ID);
                
                Console.WriteLine($"[Test] 🌐 Otwieram stronę z API G2G...");
                await page.GotoAsync(apiUrl);
                
                // Czekamy na pojawienie się tagu pre z zawartością JSON
                Console.WriteLine($"[Test] ⏳ Czekam na odpowiedź...");
                await page.WaitForSelectorAsync("pre", new() { State = WaitForSelectorState.Visible, Timeout = 15000 });
                await Task.Delay(1000); // Zmniejszono z 2000ms
                
                // Pobierz zawartość pre z odpowiedzią JSON
                var content = await page.TextContentAsync("pre");
                Console.WriteLine($"[Test] ✅ Otrzymano dane!");
                Console.WriteLine($"[Test] 📝 Odpowiedź: {content}");

                try 
                {
                    var apiData = JsonSerializer.Deserialize<ApiResponse>(content);
                    Console.WriteLine($"[Test] Kod odpowiedzi: {apiData?.Code}");
                    Console.WriteLine($"[Test] Czy jest payload: {apiData?.Payload != null}");
                    Console.WriteLine($"[Test] Czy są results: {apiData?.Payload?.Results?.Any()}");
                    
                    if (apiData?.Payload?.Results?.FirstOrDefault() is ApiResult result)
                    {
                        Console.WriteLine($"[Test] 📊 Całkowita liczba ofert: {result.Total_result}");
                        Console.WriteLine("\n[Test] Lista gier:");

                        foreach (var brand in result.Brands.OrderByDescending(b => b.Total_result))
                        {
                            var gameName = _brandToGameName.GetValueOrDefault(brand.Brand_id, "Unknown Game");
                            Console.WriteLine($"[Test] • {gameName,-20} : {brand.Total_result,4} ofert (Brand ID: {brand.Brand_id})");
                        }

                        // Zapisz surową odpowiedź do pliku dla debugowania
                        var debugFileName = $"api_response_debug_{DateTime.Now:yyyyMMdd_HHmmss}.json";
                        await File.WriteAllTextAsync(debugFileName, content);
                        Console.WriteLine($"\n[Test] 📝 Zapisano surową odpowiedź do pliku: {debugFileName}");
                    }
                    else
                    {
                        throw new Exception("Brak danych w odpowiedzi");
                    }
                }
                catch (JsonException ex)
                {
                    Console.WriteLine($"[Test] ❌ Błąd deserializacji JSON: {ex.Message}");
                    Console.WriteLine($"[Test] Otrzymany JSON: {content}");
                    throw;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[Test] ❌ Błąd podczas testu: {ex.Message}");
                throw;
            }
            finally
            {
                if (page != null)
                {
                    await page.CloseAsync();
                }
            }
        }
    }
} 