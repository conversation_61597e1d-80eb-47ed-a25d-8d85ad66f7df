using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using ShopBot.Services;
using System.Threading.Tasks;
using System.Threading;

public class ProcessedOffersTracker
{
    private readonly object _lock = new();
    private readonly string _trackingFile = "offers_tracking.json";
    private TrackingData _cachedData = null;
    private DateTime _cacheExpiry = DateTime.MinValue;
    private readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);

    private class TrackingData
    {
        public DateTime LastProcessingTime { get; set; }
        public List<string> ProcessedOffers { get; set; } = new();
    }

    public ProcessedOffersTracker()
    {
        LoadTrackingData();
    }

    private TrackingData LoadTrackingData()
    {
        try
        {
            if (_cachedData == null || DateTime.UtcNow > _cacheExpiry)
            {
                if (File.Exists(_trackingFile))
                {
                    var json = File.ReadAllText(_trackingFile);
                    var data = JsonSerializer.Deserialize<TrackingData>(json) ?? CreateNewTrackingData();

                    var timeSinceLastProcessing = DateTime.UtcNow - data.LastProcessingTime;
                    Console.WriteLine($"[Tracker] Last processing time: {data.LastProcessingTime:yyyy-MM-dd HH:mm:ss} UTC ({timeSinceLastProcessing.TotalHours:F1} hours ago)");
                    Console.WriteLine($"[Tracker] Loaded {data.ProcessedOffers.Count} processed offers");
                    _cachedData = data;
                }
                else
                {
                    var data = CreateNewTrackingData();
                    SaveTrackingData(data);
                    _cachedData = data;
                }
                _cacheExpiry = DateTime.UtcNow.AddMinutes(15);
            }

            return _cachedData;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Tracker] Error loading tracking data: {ex.Message}");
            var data = CreateNewTrackingData();
            SaveTrackingData(data);
            _cachedData = data;
            _cacheExpiry = DateTime.UtcNow.AddMinutes(15);
            return data;
        }
    }

    private TrackingData CreateNewTrackingData()
    {
        return new TrackingData
        {
            LastProcessingTime = DateTime.UtcNow,
            ProcessedOffers = new List<string>()
        };
    }

    private void SaveTrackingData(TrackingData data)
    {
        try
        {
            var json = JsonSerializer.Serialize(data, new JsonSerializerOptions { WriteIndented = true });
            File.WriteAllText(_trackingFile, json);
            _cachedData = null; // Invalidate cache
            Console.WriteLine($"[Tracker] Saved tracking data with {data.ProcessedOffers.Count} offers");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Tracker] Error saving tracking data: {ex.Message}");
        }
    }

    public void AddProcessedOffer(string offerId)
    {
        lock (_lock)
        {
            var data = LoadTrackingData();
            if (!data.ProcessedOffers.Contains(offerId))
            {
                data.ProcessedOffers.Add(offerId);
                Console.WriteLine($"[Tracker] Added offer ID: {offerId} to processed offers");
                SaveTrackingData(data);
            }
        }
    }

    public bool WasOfferProcessed(string offerId)
    {
        lock (_lock)
        {
            var data = LoadTrackingData();
            return data.ProcessedOffers.Contains(offerId);
        }
    }

    public List<string> GetAllOfferIds()
    {
        lock (_lock)
        {
            var data = LoadTrackingData();
            return data.ProcessedOffers.ToList();
        }
    }

    public DateTime GetLastProcessingTime()
    {
        var data = LoadTrackingData();
        return data.LastProcessingTime;
    }

    public async Task<bool> TryProcessOffer(string offerId)
    {
        await _semaphore.WaitAsync();
        try
        {
            if (WasOfferProcessed(offerId))
                return false;
            
            AddProcessedOffer(offerId);
            return true;
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public void SynchronizeWithActiveOffers(ISet<string> verifiedOffers)
    {
        lock (_lock)
        {
            var trackingData = LoadTrackingData();

            // Pobierz aktualne oferty z g2g_offers.json
            var currentG2GOffers = G2GOfferTracker.LoadAllOffers();
            
            if (currentG2GOffers.Count == 0)
            {
                Console.WriteLine("[Tracker] ⚠️ Nie znaleziono żadnych ofert w g2g_offers.json, zachowuję obecną historię");
                return;
            }

            var activeFunPayIds = currentG2GOffers.Select(o => o.FunPayId).ToHashSet();
            var beforeCount = trackingData.ProcessedOffers.Count;

            // 1. Usuń nieaktywne oferty (te których FunPayId nie ma w g2g_offers.json)
            var inactiveOffers = trackingData.ProcessedOffers
                .Where(funPayId => !activeFunPayIds.Contains(funPayId))
                .ToList();

            foreach (var inactiveOffer in inactiveOffers)
            {
                trackingData.ProcessedOffers.Remove(inactiveOffer);
            }

            // 2. Dodaj nowe aktywne oferty (FunPayId)
            var addedCount = 0;
            foreach (var activeOffer in currentG2GOffers)
            {
                if (!trackingData.ProcessedOffers.Contains(activeOffer.FunPayId))
                {
                    trackingData.ProcessedOffers.Add(activeOffer.FunPayId);
                    addedCount++;
                }
            }

            if (inactiveOffers.Any() || addedCount > 0)
            {
                if (inactiveOffers.Any())
                {
                    Console.WriteLine($"[Tracker] Usunięto {inactiveOffers.Count} nieaktywnych ofert z {beforeCount} ofert w historii");
                }
                if (addedCount > 0)
                {
                    Console.WriteLine($"[Tracker] Dodano {addedCount} nowych aktywnych ofert do historii");
                }
                Console.WriteLine($"[Tracker] Obecnie w historii: {trackingData.ProcessedOffers.Count} ofert");
                Console.WriteLine($"[Tracker] Liczba ofert w g2g_offers.json: {currentG2GOffers.Count}");
                
                trackingData.LastProcessingTime = DateTime.UtcNow;
                SaveTrackingData(trackingData);
            }
            else
            {
                Console.WriteLine($"[Tracker] Brak zmian w historii ({trackingData.ProcessedOffers.Count} ofert)");
                Console.WriteLine($"[Tracker] Liczba ofert w g2g_offers.json: {currentG2GOffers.Count}");
            }
        }
    }
} 