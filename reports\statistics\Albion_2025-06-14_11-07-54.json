{"TotalOffers": 543, "AcceptedOffers": 73, "RejectedOffers": 470, "RejectionReasons": {"Cena ponizej minimum (6,45 < 15)": 5, "Cena powyzej maximum (100,83 > 99)": 1, "Cena powyzej maximum (190,45 > 99)": 1, "Cena powyzej maximum (322,30 > 99)": 1, "Cena ponizej minimum (0,21 < 15)": 2, "Duplikat oferty": 33, "Cena ponizej minimum (0,20 < 15)": 1, "Cena ponizej minimum (0,19 < 15)": 4, "Cena ponizej minimum (0,18 < 15)": 1, "Cena ponizej minimum (0,17 < 15)": 1, "Cena ponizej minimum (0,16 < 15)": 1, "Cena ponizej minimum (0,15 < 15)": 1, "Cena ponizej minimum (0,14 < 15)": 1, "Cena ponizej minimum (0,13 < 15)": 2, "Cena ponizej minimum (0,12 < 15)": 1, "Cena ponizej minimum (0,11 < 15)": 1, "Cena ponizej minimum (12,89 < 15)": 6, "Cena ponizej minimum (0,28 < 15)": 2, "Cena ponizej minimum (0,10 < 15)": 2, "Cena powyzej maximum (187,79 > 99)": 1, "Cena ponizej minimum (3,87 < 15)": 4, "Cena ponizej minimum (10,31 < 15)": 7, "Cena ponizej minimum (1,61 < 15)": 3, "Cena ponizej minimum (1,29 < 15)": 16, "Cena powyzej maximum (180,48 > 99)": 6, "Cena powyzej maximum (103,13 > 99)": 7, "Cena ponizej minimum (0,56 < 15)": 15, "Cena ponizej minimum (4,26 < 15)": 1, "Cena ponizej minimum (0,22 < 15)": 1, "Cena ponizej minimum (1,03 < 15)": 7, "Cena ponizej minimum (1,07 < 15)": 1, "Cena powyzej maximum (213,27 > 99)": 1, "Cena powyzej maximum (135,27 > 99)": 1, "Cena powyzej maximum (112,72 > 99)": 1, "Cena ponizej minimum (7,73 < 15)": 2, "Cena powyzej maximum (199,82 > 99)": 1, "Cena ponizej minimum (1,82 < 15)": 1, "Cena ponizej minimum (14,32 < 15)": 1, "Cena ponizej minimum (12,88 < 15)": 2, "Cena ponizej minimum (3,22 < 15)": 3, "Cena ponizej minimum (1,26 < 15)": 1, "Cena ponizej minimum (0,26 < 15)": 4, "Cena ponizej minimum (0,06 < 15)": 1, "Cena ponizej minimum (10,65 < 15)": 4, "Cena ponizej minimum (5,32 < 15)": 13, "Cena ponizej minimum (3,07 < 15)": 1, "Cena ponizej minimum (0,98 < 15)": 5, "Cena powyzej maximum (1418,08 > 99)": 1, "Cena powyzej maximum (193,37 > 99)": 7, "Cena ponizej minimum (4,51 < 15)": 5, "Cena ponizej minimum (1,02 < 15)": 2, "Cena ponizej minimum (2,57 < 15)": 2, "Cena ponizej minimum (2,84 < 15)": 1, "Cena ponizej minimum (1,12 < 15)": 4, "Cena ponizej minimum (0,64 < 15)": 3, "Cena powyzej maximum (206,27 > 99)": 2, "Cena ponizej minimum (14,81 < 15)": 9, "Cena ponizej minimum (0,33 < 15)": 1, "Cena powyzej maximum (1289,16 > 99)": 3, "Cena powyzej maximum (336,10 > 99)": 1, "Cena powyzej maximum (257,83 > 99)": 5, "Cena ponizej minimum (4,48 < 15)": 1, "Cena ponizej minimum (0,45 < 15)": 3, "Cena ponizej minimum (0,46 < 15)": 4, "Cena powyzej maximum (106,47 > 99)": 2, "Ocena ponizej minimum (0 < 5)": 29, "Cena powyzej maximum (644,58 > 99)": 3, "Cena ponizej minimum (8,66 < 15)": 3, "Ocena ponizej minimum (4 < 5)": 13, "Cena ponizej minimum (11,60 < 15)": 3, "Cena powyzej maximum (386,75 > 99)": 7, "Cena ponizej minimum (1,97 < 15)": 1, "Cena ponizej minimum (0,01 < 15)": 2, "Cena ponizej minimum (10,76 < 15)": 1, "Cena ponizej minimum (5,41 < 15)": 1, "Cena powyzej maximum (322,29 > 99)": 2, "Cena ponizej minimum (8,38 < 15)": 4, "Cena powyzej maximum (109,58 > 99)": 1, "Cena powyzej maximum (158,65 > 99)": 1, "Cena ponizej minimum (1,14 < 15)": 1, "Cena ponizej minimum (2,58 < 15)": 2, "Cena ponizej minimum (5,80 < 15)": 3, "Cena ponizej minimum (10,96 < 15)": 1, "Cena powyzej maximum (112,02 > 99)": 1, "Cena ponizej minimum (0,89 < 15)": 1, "Cena ponizej minimum (0,90 < 15)": 2, "Cena ponizej minimum (14,18 < 15)": 1, "Cena powyzej maximum (122,47 > 99)": 1, "Cena ponizej minimum (9,52 < 15)": 1, "Cena powyzej maximum (160,37 > 99)": 1, "Cena powyzej maximum (122,98 > 99)": 1, "Cena powyzej maximum (1121,57 > 99)": 1, "Cena powyzej maximum (476,99 > 99)": 1, "Cena ponizej minimum (2,46 < 15)": 1, "Cena powyzej maximum (167,59 > 99)": 3, "Cena powyzej maximum (161,15 > 99)": 1, "Cena powyzej maximum (203,69 > 99)": 1, "Cena ponizej minimum (5,16 < 15)": 3, "Cena ponizej minimum (1,68 < 15)": 1, "Cena ponizej minimum (4,64 < 15)": 1, "Cena ponizej minimum (2,45 < 15)": 1, "Cena powyzej maximum (1031,33 > 99)": 3, "Cena powyzej maximum (104,08 > 99)": 1, "Cena powyzej maximum (103,19 > 99)": 1, "Cena powyzej maximum (103,78 > 99)": 1, "Cena powyzej maximum (222,98 > 99)": 1, "Cena powyzej maximum (360,80 > 99)": 1, "Cena powyzej maximum (102,00 > 99)": 1, "Cena powyzej maximum (957,37 > 99)": 1, "Cena powyzej maximum (101,70 > 99)": 2, "Cena powyzej maximum (104,38 > 99)": 2, "Cena powyzej maximum (101,07 > 99)": 1, "Cena powyzej maximum (100,15 > 99)": 1, "Cena powyzej maximum (101,97 > 99)": 1, "Cena powyzej maximum (219,16 > 99)": 1, "Cena powyzej maximum (128,92 > 99)": 9, "Cena powyzej maximum (348,07 > 99)": 2, "Cena ponizej minimum (1,11 < 15)": 1, "Cena ponizej minimum (1,30 < 15)": 1, "Cena ponizej minimum (1,43 < 15)": 1, "Cena ponizej minimum (1,93 < 15)": 2, "Cena ponizej minimum (0,32 < 15)": 2, "Cena ponizej minimum (0,97 < 15)": 1, "Cena powyzej maximum (296,51 > 99)": 1, "Cena powyzej maximum (116,02 > 99)": 1, "Cena powyzej maximum (244,89 > 99)": 1, "Cena powyzej maximum (372,66 > 99)": 1, "Cena ponizej minimum (9,84 < 15)": 1, "Cena ponizej minimum (5,29 < 15)": 1, "Cena powyzej maximum (154,70 > 99)": 2, "Cena ponizej minimum (7,09 < 15)": 1, "Cena powyzej maximum (1160,25 > 99)": 1, "Cena powyzej maximum (2191,58 > 99)": 1, "Cena powyzej maximum (100,55 > 99)": 1, "Cena powyzej maximum (580,12 > 99)": 2, "Cena powyzej maximum (266,19 > 99)": 1, "Cena powyzej maximum (585,61 > 99)": 1, "Cena powyzej maximum (134,07 > 99)": 1, "Cena ponizej minimum (10,02 < 15)": 1, "Cena ponizej minimum (8,59 < 15)": 1, "Cena powyzej maximum (283,46 > 99)": 1, "Cena ponizej minimum (3,86 < 15)": 1, "Cena powyzej maximum (335,18 > 99)": 1, "Cena ponizej minimum (0,08 < 15)": 1, "Ocena ponizej minimum (1 < 5)": 1, "Cena powyzej maximum (515,67 > 99)": 4, "Cena powyzej maximum (141,81 > 99)": 3, "Cena powyzej maximum (165,44 > 99)": 1, "Cena powyzej maximum (319,42 > 99)": 3, "Cena powyzej maximum (368,95 > 99)": 1, "Cena ponizej minimum (9,02 < 15)": 3, "Cena ponizej minimum (4,92 < 15)": 1, "Cena ponizej minimum (8,61 < 15)": 1, "Cena ponizej minimum (6,15 < 15)": 1, "Cena powyzej maximum (211,88 > 99)": 1, "Cena powyzej maximum (254,47 > 99)": 1, "Cena powyzej maximum (339,65 > 99)": 1, "Cena powyzej maximum (392,89 > 99)": 1, "Cena powyzej maximum (228,92 > 99)": 1, "Cena powyzej maximum (424,83 > 99)": 1, "Cena powyzej maximum (190,59 > 99)": 1, "Cena powyzej maximum (275,77 > 99)": 1, "Cena powyzej maximum (137,35 > 99)": 1, "Cena powyzej maximum (126,70 > 99)": 1, "Cena powyzej maximum (175,68 > 99)": 2, "Cena powyzej maximum (148,00 > 99)": 1, "Cena powyzej maximum (318,36 > 99)": 1, "Cena powyzej maximum (532,37 > 99)": 1, "Cena powyzej maximum (286,42 > 99)": 1, "Cena powyzej maximum (165,03 > 99)": 1, "Cena powyzej maximum (108,29 > 99)": 1, "Cena powyzej maximum (309,40 > 99)": 1, "Cena powyzej maximum (101,84 > 99)": 1, "Cena powyzej maximum (837,96 > 99)": 1, "Cena powyzej maximum (532,36 > 99)": 1, "Cena powyzej maximum (291,87 > 99)": 1, "Cena powyzej maximum (614,92 > 99)": 1, "Cena powyzej maximum (173,55 > 99)": 1, "Cena ponizej minimum (12,25 < 15)": 1, "Cena powyzej maximum (593,01 > 99)": 1, "Cena powyzej maximum (387,92 > 99)": 1, "Cena powyzej maximum (148,25 > 99)": 1, "Cena powyzej maximum (244,94 > 99)": 1, "Cena ponizej minimum (4,25 < 15)": 1, "Cena powyzej maximum (425,42 > 99)": 2, "Cena powyzej maximum (149,06 > 99)": 1, "Cena powyzej maximum (167,58 > 99)": 1, "Cena powyzej maximum (554,34 > 99)": 1, "Cena ponizej minimum (6,39 < 15)": 1, "Cena powyzej maximum (166,03 > 99)": 1, "Cena ponizej minimum (0,77 < 15)": 1, "Cena powyzej maximum (111,80 > 99)": 1, "Cena ponizej minimum (0,02 < 15)": 2, "Cena powyzej maximum (412,53 > 99)": 1, "Ocena ponizej minimum (3 < 5)": 1, "Cena powyzej maximum (683,28 > 99)": 1, "Cena ponizej minimum (0,71 < 15)": 1, "Cena ponizej minimum (9,67 < 15)": 1, "Cena ponizej minimum (1,69 < 15)": 1, "Cena powyzej maximum (232,05 > 99)": 1, "Cena powyzej maximum (773,50 > 99)": 2, "Cena powyzej maximum (249,52 > 99)": 1, "Cena powyzej maximum (533,89 > 99)": 1, "Cena powyzej maximum (537,29 > 99)": 1, "Cena powyzej maximum (902,41 > 99)": 1, "Cena ponizej minimum (7,85 < 15)": 1}, "GenerationTime": "2025-06-14T11:07:54.2743534+02:00"}