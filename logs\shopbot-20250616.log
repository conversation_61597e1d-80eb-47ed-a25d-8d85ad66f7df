[2025-06-16 12:42:26.022 +02:00 INF] ShopBot.Services.CliCommandHandler: 📊 Generuję raport analytics...
[2025-06-16 12:42:26.272 +02:00 INF] ShopBot.Services.AnalyticsService: [Analytics] 📊 Generating dashboard data
[2025-06-16 12:42:26.901 +02:00 INF] ShopBot.Services.AnalyticsService: [Analytics] ✅ Dashboard data generated: 0 offers, 0.0% success rate
[2025-06-16 12:42:26.907 +02:00 INF] ShopBot.Services.AnalyticsService: [Analytics] ⚡ Generating performance report for 30 days
[2025-06-16 12:42:26.929 +02:00 INF] ShopBot.Services.AnalyticsService: [Analytics] ✅ Performance report generated: 0.0% success, ¤0.00 revenue
[2025-06-16 12:42:26.932 +02:00 INF] ShopBot.Services.AnalyticsService: [Analytics] 🎮 Generating game analytics
[2025-06-16 12:42:26.935 +02:00 INF] ShopBot.Services.AnalyticsService: [Analytics] ✅ Game analytics generated for 0 games
[2025-06-16 12:42:26.937 +02:00 INF] ShopBot.Services.AnalyticsService: [Analytics] 🚨 Analyzing top 5 errors
[2025-06-16 12:42:26.939 +02:00 INF] ShopBot.Services.AnalyticsService: [Analytics] ✅ Top errors analyzed: 0 unique error types
[2025-06-16 12:42:26.940 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Analytics report generated successfully
[2025-06-16 15:23:52.654 +02:00 INF] ShopBot.Services.CliCommandHandler: ➕ Dodaję ofertę FunPay ID: ********, Gra: RaidShadowLegends
[2025-06-16 15:23:54.511 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Mythical champions = 1
[2025-06-16 15:23:54.512 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 35
[2025-06-16 15:23:54.513 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 59
[2025-06-16 15:23:54.515 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-16 15:23:54.516 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-16 15:23:54.517 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-16 15:23:54.521 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 15:23:54.523 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 15:23:54.533 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 15:23:54.535 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 15:23:54.538 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-16 15:23:54.540 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 15:23:54.541 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 15:23:54.542 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 15:23:54.543 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 15:23:54.544 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 15:23:54.545 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-16 15:23:54.546 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-16 15:23:54.547 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-16 15:23:54.548 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-16 15:23:54.549 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-16 15:23:54.552 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-16 15:23:54.554 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 51.05 $</span></span>
[2025-06-16 15:23:54.555 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>56.10 $</span></span>
[2025-06-16 15:23:54.557 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>56.10 $</span></span>
[2025-06-16 15:23:54.558 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>56.10 $</span></span>
[2025-06-16 15:23:54.560 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>51.05 USDT</span></span>
[2025-06-16 15:23:54.562 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 51.05
[2025-06-16 15:23:54.563 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 51.05 USDT
[2025-06-16 15:23:54.569 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 15:23:54.570 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 15:23:54.571 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 15:23:54.573 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 15:23:54.574 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 15:23:54.575 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-16 15:23:54.576 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-16 15:23:55.314 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] 🔄 Rozpoczynam dodawanie oferty ********
[2025-06-16 15:23:55.983 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Mythical champions = 1
[2025-06-16 15:23:55.985 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 35
[2025-06-16 15:23:55.986 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 59
[2025-06-16 15:23:55.987 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-16 15:23:55.988 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-16 15:23:55.989 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-16 15:23:55.991 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 15:23:55.992 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 15:23:55.995 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 15:23:55.996 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 15:23:55.999 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-16 15:23:56.000 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 15:23:56.001 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 15:23:56.002 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 15:23:56.003 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 15:23:56.004 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 15:23:56.005 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-16 15:23:56.006 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-16 15:23:56.007 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-16 15:23:56.008 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-16 15:23:56.009 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-16 15:23:56.010 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-16 15:23:56.012 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 51.05 $</span></span>
[2025-06-16 15:23:56.013 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>56.10 $</span></span>
[2025-06-16 15:23:56.014 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>56.10 $</span></span>
[2025-06-16 15:23:56.015 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>56.10 $</span></span>
[2025-06-16 15:23:56.017 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>51.05 USDT</span></span>
[2025-06-16 15:23:56.018 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 51.05
[2025-06-16 15:23:56.019 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 51.05 USDT
[2025-06-16 15:23:56.021 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 15:23:56.022 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 15:23:56.023 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 15:23:56.024 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 15:23:56.025 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 15:23:56.026 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-16 15:23:56.027 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-16 15:23:58.039 +02:00 ERR] ShopBot.Tasks.AddOfferTask: [AddOffer] ❌ Błąd podczas dodawania oferty ********: Nie udało się sparsować ceny: '51.05 USDT'
[2025-06-16 15:23:58.061 +02:00 ERR] ShopBot.Services.CliCommandHandler: Błąd podczas wykonywania polecenia add
System.Exception: Nie udało się sparsować ceny: '51.05 USDT'
   at ShopBot.Tasks.AddOfferTask.ExecuteAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Desktop\SHOPBOT v7 dzialajaca\Tasks\AddOfferTask.cs:line 84
   at ShopBot.Tasks.AddOfferTask.ExecuteAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Desktop\SHOPBOT v7 dzialajaca\Tasks\AddOfferTask.cs:line 147
   at ShopBot.Services.CliCommandHandler.HandleAddCommand(String[] args) in C:\Users\<USER>\Desktop\SHOPBOT v7 dzialajaca\Services\CliCommandHandler.cs:line 154
   at ShopBot.Services.CliCommandHandler.HandleCommandAsync(String[] args) in C:\Users\<USER>\Desktop\SHOPBOT v7 dzialajaca\Services\CliCommandHandler.cs:line 71
[2025-06-16 15:25:53.658 +02:00 INF] ShopBot.Services.CliCommandHandler: ➕ Dodaję ofertę FunPay ID: ********, Gra: RaidShadowLegends
[2025-06-16 15:25:55.079 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Mythical champions = 1
[2025-06-16 15:25:55.081 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 35
[2025-06-16 15:25:55.082 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 59
[2025-06-16 15:25:55.083 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-16 15:25:55.084 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-16 15:25:55.086 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-16 15:25:55.088 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 15:25:55.090 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 15:25:55.100 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 15:25:55.101 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 15:25:55.105 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-16 15:25:55.106 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 15:25:55.107 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 15:25:55.108 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 15:25:55.109 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 15:25:55.110 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 15:25:55.111 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-16 15:25:55.112 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-16 15:25:55.113 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-16 15:25:55.114 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-16 15:25:55.115 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-16 15:25:55.116 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-16 15:25:55.118 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 51.05 $</span></span>
[2025-06-16 15:25:55.120 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>56.10 $</span></span>
[2025-06-16 15:25:55.121 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>56.10 $</span></span>
[2025-06-16 15:25:55.122 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>56.10 $</span></span>
[2025-06-16 15:25:55.124 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>51.05 USDT</span></span>
[2025-06-16 15:25:55.125 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 51.05
[2025-06-16 15:25:55.126 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 51.05 USDT
[2025-06-16 15:25:55.131 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 15:25:55.132 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 15:25:55.133 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 15:25:55.134 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 15:25:55.135 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 15:25:55.136 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-16 15:25:55.137 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-16 15:25:55.667 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] 🔄 Rozpoczynam dodawanie oferty ********
[2025-06-16 15:25:56.300 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Mythical champions = 1
[2025-06-16 15:25:56.302 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 35
[2025-06-16 15:25:56.303 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 59
[2025-06-16 15:25:56.304 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-16 15:25:56.306 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-16 15:25:56.307 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-16 15:25:56.308 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 15:25:56.309 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 15:25:56.313 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 15:25:56.314 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 15:25:56.317 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-16 15:25:56.318 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 15:25:56.319 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 15:25:56.320 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 15:25:56.321 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 15:25:56.322 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 15:25:56.322 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-16 15:25:56.323 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-16 15:25:56.324 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-16 15:25:56.325 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-16 15:25:56.326 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-16 15:25:56.327 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-16 15:25:56.328 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 51.05 $</span></span>
[2025-06-16 15:25:56.330 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>56.10 $</span></span>
[2025-06-16 15:25:56.331 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>56.10 $</span></span>
[2025-06-16 15:25:56.332 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>56.10 $</span></span>
[2025-06-16 15:25:56.333 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>51.05 USDT</span></span>
[2025-06-16 15:25:56.335 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 51.05
[2025-06-16 15:25:56.336 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 51.05 USDT
[2025-06-16 15:25:56.338 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 15:25:56.339 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 15:25:56.340 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 15:25:56.341 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 15:25:56.341 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 15:25:56.342 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-16 15:25:56.343 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-16 15:25:58.352 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] 💰 Parsed USDT price: 51.05 USDT → 51.05 USD
[2025-06-16 15:26:00.312 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 🎮 Rozpoczynam przetwarzanie gry: RaidShadowLegends
[2025-06-16 15:26:20.082 +02:00 WRN] ShopBot.Services.G2GPublisherService: [G2G Publisher] ⚠️ Nie udało się sparsować ceny: 51.05 USDT
[2025-06-16 15:26:20.089 +02:00 INF] ShopBot.Services.G2GPublisherService: 💰 Cena oryginalna: $0.00 → Cena G2G: $15.00 (gra: RaidShadowLegends)
[2025-06-16 15:26:21.258 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Zakończono przetwarzanie gry: RaidShadowLegends
[2025-06-16 15:26:21.259 +02:00 INF] ShopBot.Services.G2GPublisherService: 🚀 Rozpoczynam wypełnianie tytułu i opisu dla gry: RaidShadowLegends
[2025-06-16 15:26:21.261 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: RaidShadowLegends
[2025-06-16 15:26:21.288 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: RaidShadowLegends
[2025-06-16 15:26:21.290 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Formatting Raid Shadow Legends description for offer ********
[2025-06-16 15:26:21.292 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Split by | into 5 lines
[2025-06-16 15:26:21.294 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Adding ID to description: ********
[2025-06-16 15:26:21.295 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Final formatted description:
• Selling an account with top mythic komidus echidnik + narses + gleykad + elva + chagur + esme and modo + lonataril + legi for entry + many good epics. there is a ratnik for ankyl | At the moment 5kb is closed for 2 keys. 8.5k energy | 1.9k rubies. there is very little left until the arbiter (13 missions). mine 3/3. i will send any screenshots upon request | Reserve your ideal account now!

===============================================================
Got questions? Contact me on G2G chat before buying! Offers like this go fast – reserve your ideal account now!

(********)

[2025-06-16 15:26:21.296 +02:00 DBG] ShopBot.Services.G2GPublisherService: 📝 Wypełniam opis (587 znaków): • Selling an account with top mythic komidus echidnik + narses + gleykad + elva + chagur + esme and ...
[2025-06-16 15:26:21.337 +02:00 INF] ShopBot.Services.G2GPublisherService: ✅ Zakończono wypełnianie tytułu i opisu
[2025-06-16 15:26:21.338 +02:00 INF] ShopBot.Services.G2GPublisherService: 📸 Rozpoczynam upload 4 obrazów
[2025-06-16 15:26:21.340 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 🖼️ Rozpoczynam upload 4 obrazów do Dropbox
[2025-06-16 15:26:21.342 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] Rozpoczynam upload 4 obrazów do Dropbox dla G2G
[2025-06-16 15:26:21.344 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] 🚀 Rozpoczynam PARALLEL upload 4 obrazów (Dropbox: true)
[2025-06-16 15:26:21.345 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 15:26:21.347 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 15:26:21.349 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 15:26:21.377 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 15:26:21.378 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 15:26:21.379 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 15:26:21.381 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 15:26:21.382 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 15:26:21.383 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 15:26:22.119 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 544KB w 0.8s
[2025-06-16 15:26:22.151 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 594KB w 0.8s
[2025-06-16 15:26:22.197 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 656KB w 0.8s
[2025-06-16 15:26:22.801 +02:00 ERR] ShopBot.Services.DropboxService: [Dropbox] ❌ Błąd uploadu: Błąd uploadu: HTTP Unauthorized: {"error_summary": "expired_access_token/.", "error": {".tag": "expired_access_token"}}
[2025-06-16 15:26:22.803 +02:00 ERR] ShopBot.Services.ImageUploadService: [Image Upload] ❌ Upload nieudany: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg - HTTP Unauthorized: {"error_summary": "expired_access_token/.", "error": {".tag": "expired_access_token"}}
[2025-06-16 15:26:22.804 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg
[2025-06-16 15:26:22.805 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg
[2025-06-16 15:26:22.806 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg
[2025-06-16 15:26:23.026 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 253KB w 0.2s
[2025-06-16 15:26:23.333 +02:00 ERR] ShopBot.Services.DropboxService: [Dropbox] ❌ Błąd uploadu: Błąd uploadu: HTTP Unauthorized: {"error_summary": "expired_access_token/..", "error": {".tag": "expired_access_token"}}
[2025-06-16 15:26:23.334 +02:00 ERR] ShopBot.Services.ImageUploadService: [Image Upload] ❌ Upload nieudany: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg - HTTP Unauthorized: {"error_summary": "expired_access_token/..", "error": {".tag": "expired_access_token"}}
[2025-06-16 15:26:23.341 +02:00 ERR] ShopBot.Services.DropboxService: [Dropbox] ❌ Błąd uploadu: Błąd uploadu: HTTP Unauthorized: {"error_summary": "expired_access_token/...", "error": {".tag": "expired_access_token"}}
[2025-06-16 15:26:23.342 +02:00 ERR] ShopBot.Services.ImageUploadService: [Image Upload] ❌ Upload nieudany: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg - HTTP Unauthorized: {"error_summary": "expired_access_token/...", "error": {".tag": "expired_access_token"}}
[2025-06-16 15:26:23.414 +02:00 ERR] ShopBot.Services.DropboxService: [Dropbox] ❌ Błąd uploadu: Błąd uploadu: HTTP Unauthorized: {"error_summary": "expired_access_token/..", "error": {".tag": "expired_access_token"}}
[2025-06-16 15:26:23.416 +02:00 ERR] ShopBot.Services.ImageUploadService: [Image Upload] ❌ Upload nieudany: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg - HTTP Unauthorized: {"error_summary": "expired_access_token/..", "error": {".tag": "expired_access_token"}}
[2025-06-16 15:26:23.417 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] 📊 Upload zakończony: 0/4 pomyślnych
[2025-06-16 15:26:23.419 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 📊 Pomyślnie zauploadowano 0/4 obrazów
[2025-06-16 15:26:23.420 +02:00 WRN] ShopBot.Services.G2GPublisherService: [G2G] ⚠️ Nie udało się zauploadować żadnego obrazu
[2025-06-16 15:26:23.421 +02:00 INF] ShopBot.Services.G2GPublisherService: ✅ Zakończono upload obrazów
[2025-06-16 15:26:23.422 +02:00 WRN] ShopBot.Services.G2GPublisherService: [G2G Publisher] ⚠️ Nie udało się sparsować ceny: 51.05 USDT
[2025-06-16 15:26:23.423 +02:00 INF] ShopBot.Services.G2GPublisherService: 💰 Cena oryginalna: $0.00 → Cena G2G: $15.00 (gra: RaidShadowLegends)
[2025-06-16 15:26:24.558 +02:00 INF] ShopBot.Services.G2GPublisherService: 🚀 Publikuję ofertę na G2G
[2025-06-16 15:26:24.561 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🔄 Próba publikacji 1/3
[2025-06-16 15:26:24.575 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🖱️ Klikam przycisk Publish
[2025-06-16 15:26:26.046 +02:00 DBG] ShopBot.Services.G2GPublisherService: 📡 Odpowiedź API (Status: 200): {"code": 2000, "messages": [], "payload": {"offer_id": "G1750080386720SA", "seller_id": "7670672", "service_id": "f6a1aba5-473a-4044-836a-8968bbab16d7", "brand_id": "lgc_game_26258", "region_id": "", "relation_id": "59ca631d-a789-4799-a5cc-b3bb50b40dc7", "offer_type": "public", "offer_attributes": [{"collection_id": "lgc_26258_platform", "dataset_id": "lgc_26258_platform_30659"}], "offer_title_collection_tree": ["lgc_26258_platform"], "primary_img_attributes": [], "offer_group": "G1750080386720SA", "title": "mythic comidus + na***s + gleikad + elva + esme and modo", "description": "\u2022 Selling an account with top mythic komidus echidnik + narses + gleykad + elva + chagur + esme and modo + lonataril + legi for entry + many good epics. there is a ratnik for ankyl | At the moment 5kb is closed for 2 keys. 8.5k energy | 1.9k rubies. there is very little left until the arbiter (13 missions). mine 3/3. i will send any screenshots upon request | Reserve your ideal account now!\n\n===============================================================\nGot questions? Contact me on G2G chat before buying! Offers like this go fast \u2013 reserve your ideal account now!\n\n(********)", "api_qty": 0, "low_stock_alert_qty": 0, "available_qty": 1, "min_qty": 1, "actual_qty": 1, "currency": "USD", "unit_price": 15, "other_pricing": [], "unit_name": "", "qty_metric": "1", "wholesale_details": [], "other_wholesale_details": [], "is_official": false, "delivery_mode": [], "delivery_method_ids": [], "delivery_speed": "manual", "delivery_speed_details": [{"min": 1, "max": 1, "delivery_time": 540}], "sales_territory_settings": {"settings_type": "global", "countries": []}, "cat_path": "5830014a-b974-45c6-9672-b51e83112fb7", "cat_id": "5830014a-b974-45c6-9672-b51e83112fb7", "ancestor_id": "5830014a-b974-45c6-9672-b51e83112fb7", "status": "live", "created_at": *************, "updated_at": *************, "seller_updated_at": *************, "external_images_mapping": []}, "request_id": "9988caf0-3b07-43d1-b777-d751e99ae707"}
[2025-06-16 15:26:26.051 +02:00 INF] ShopBot.Services.G2GPublisherService: 🎉 Sukces! Otrzymano ID oferty: G1750080386720SA
[2025-06-16 15:26:26.052 +02:00 INF] ShopBot.Services.G2GPublisherService: ✅ Oferta opublikowana pomyślnie (ID: G1750080386720SA)
[2025-06-16 15:26:26.345 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] ✅ Pomyślnie opublikowano ofertę ******** -> G1750080386720SA
[2025-06-16 15:26:26.376 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Oferta ******** została dodana
[2025-06-16 15:28:04.120 +02:00 INF] ShopBot.Services.CliCommandHandler: ➕ Dodaję ofertę FunPay ID: ********, Gra: RaidShadowLegends
[2025-06-16 15:28:05.530 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Mythical champions = 1
[2025-06-16 15:28:05.531 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 35
[2025-06-16 15:28:05.532 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 59
[2025-06-16 15:28:05.534 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-16 15:28:05.535 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-16 15:28:05.536 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-16 15:28:05.539 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 15:28:05.541 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 15:28:05.550 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 15:28:05.551 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 15:28:05.555 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-16 15:28:05.556 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 15:28:05.557 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 15:28:05.558 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 15:28:05.559 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 15:28:05.560 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 15:28:05.561 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-16 15:28:05.561 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-16 15:28:05.563 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-16 15:28:05.564 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-16 15:28:05.565 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-16 15:28:05.567 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-16 15:28:05.569 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 51.05 $</span></span>
[2025-06-16 15:28:05.570 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>56.10 $</span></span>
[2025-06-16 15:28:05.571 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>56.10 $</span></span>
[2025-06-16 15:28:05.573 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>56.10 $</span></span>
[2025-06-16 15:28:05.574 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>51.05 USDT</span></span>
[2025-06-16 15:28:05.575 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 51.05
[2025-06-16 15:28:05.577 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 51.05 USDT
[2025-06-16 15:28:05.581 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 15:28:05.582 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 15:28:05.583 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 15:28:05.584 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 15:28:05.585 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 15:28:05.586 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-16 15:28:05.588 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-16 15:28:07.127 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] 🔄 Rozpoczynam dodawanie oferty ********
[2025-06-16 15:28:07.730 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Mythical champions = 1
[2025-06-16 15:28:07.732 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 35
[2025-06-16 15:28:07.733 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 59
[2025-06-16 15:28:07.734 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-16 15:28:07.736 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-16 15:28:07.737 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-16 15:28:07.739 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 15:28:07.740 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 15:28:07.744 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 15:28:07.745 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 15:28:07.748 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-16 15:28:07.749 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 15:28:07.750 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 15:28:07.751 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 15:28:07.752 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 15:28:07.753 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 15:28:07.754 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-16 15:28:07.755 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-16 15:28:07.756 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-16 15:28:07.757 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-16 15:28:07.758 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-16 15:28:07.759 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-16 15:28:07.760 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 51.05 $</span></span>
[2025-06-16 15:28:07.761 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>56.10 $</span></span>
[2025-06-16 15:28:07.762 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>56.10 $</span></span>
[2025-06-16 15:28:07.764 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>56.10 $</span></span>
[2025-06-16 15:28:07.765 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>51.05 USDT</span></span>
[2025-06-16 15:28:07.766 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 51.05
[2025-06-16 15:28:07.767 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 51.05 USDT
[2025-06-16 15:28:07.769 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 15:28:07.770 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 15:28:07.771 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 15:28:07.772 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 15:28:07.773 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 15:28:07.774 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-16 15:28:07.775 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-16 15:28:09.792 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] 💰 Parsed USDT price: 51.05 USDT → 51.05 USD
[2025-06-16 15:28:11.476 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 🎮 Rozpoczynam przetwarzanie gry: RaidShadowLegends
[2025-06-16 15:28:30.901 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G Publisher] 💰 Parsed USDT price: 51.05 USDT → 51.05 USD
[2025-06-16 15:28:30.908 +02:00 INF] ShopBot.Services.G2GPublisherService: 💰 Cena oryginalna: $51.05 → Cena G2G: $66.36 (gra: RaidShadowLegends)
[2025-06-16 15:28:32.082 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Zakończono przetwarzanie gry: RaidShadowLegends
[2025-06-16 15:28:32.084 +02:00 INF] ShopBot.Services.G2GPublisherService: 🚀 Rozpoczynam wypełnianie tytułu i opisu dla gry: RaidShadowLegends
[2025-06-16 15:28:32.085 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: RaidShadowLegends
[2025-06-16 15:28:32.113 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: RaidShadowLegends
[2025-06-16 15:28:32.115 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Formatting Raid Shadow Legends description for offer ********
[2025-06-16 15:28:32.117 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Split by | into 5 lines
[2025-06-16 15:28:32.119 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Adding ID to description: ********
[2025-06-16 15:28:32.120 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Final formatted description:
• Selling an account with top mythic komidus echidnik + narses + gleykad + elva + chagur + esme and modo + lonataril + legi for entry + many good epics. there is a ratnik for ankyl | At the moment 5kb is closed for 2 keys. 8.5k energy | 1.9k rubies. there is very little left until the arbiter (13 missions). mine 3/3. i will send any screenshots upon request | Reserve your ideal account now!

===============================================================
Got questions? Contact me on G2G chat before buying! Offers like this go fast – reserve your ideal account now!

(********)

[2025-06-16 15:28:32.121 +02:00 DBG] ShopBot.Services.G2GPublisherService: 📝 Wypełniam opis (587 znaków): • Selling an account with top mythic komidus echidnik + narses + gleykad + elva + chagur + esme and ...
[2025-06-16 15:28:32.163 +02:00 INF] ShopBot.Services.G2GPublisherService: ✅ Zakończono wypełnianie tytułu i opisu
[2025-06-16 15:28:32.164 +02:00 INF] ShopBot.Services.G2GPublisherService: 📸 Rozpoczynam upload 4 obrazów
[2025-06-16 15:28:32.166 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 🖼️ Rozpoczynam upload 4 obrazów do Dropbox
[2025-06-16 15:28:32.167 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] Rozpoczynam upload 4 obrazów do Dropbox dla G2G
[2025-06-16 15:28:32.169 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] 🚀 Rozpoczynam PARALLEL upload 4 obrazów (Dropbox: true)
[2025-06-16 15:28:32.171 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 15:28:32.173 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 15:28:32.175 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 15:28:32.196 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 15:28:32.197 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 15:28:32.198 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 15:28:32.199 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 15:28:32.200 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 15:28:32.201 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 15:28:32.961 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 544KB w 0.8s
[2025-06-16 15:28:32.998 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 594KB w 0.8s
[2025-06-16 15:28:33.045 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 656KB w 0.8s
[2025-06-16 15:28:34.119 +02:00 ERR] ShopBot.Services.DropboxService: [Dropbox] ❌ Błąd uploadu: Błąd uploadu: HTTP Unauthorized: {"error_summary": "expired_access_token/.", "error": {".tag": "expired_access_token"}}
[2025-06-16 15:28:34.120 +02:00 ERR] ShopBot.Services.ImageUploadService: [Image Upload] ❌ Upload nieudany: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg - HTTP Unauthorized: {"error_summary": "expired_access_token/.", "error": {".tag": "expired_access_token"}}
[2025-06-16 15:28:34.121 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg
[2025-06-16 15:28:34.122 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg
[2025-06-16 15:28:34.123 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg
[2025-06-16 15:28:34.143 +02:00 ERR] ShopBot.Services.DropboxService: [Dropbox] ❌ Błąd uploadu: Błąd uploadu: HTTP Unauthorized: {"error_summary": "expired_access_token/.", "error": {".tag": "expired_access_token"}}
[2025-06-16 15:28:34.144 +02:00 ERR] ShopBot.Services.ImageUploadService: [Image Upload] ❌ Upload nieudany: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg - HTTP Unauthorized: {"error_summary": "expired_access_token/.", "error": {".tag": "expired_access_token"}}
[2025-06-16 15:28:34.403 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 253KB w 0.3s
[2025-06-16 15:28:34.598 +02:00 ERR] ShopBot.Services.DropboxService: [Dropbox] ❌ Błąd uploadu: Błąd uploadu: HTTP Unauthorized: {"error_summary": "expired_access_token/..", "error": {".tag": "expired_access_token"}}
[2025-06-16 15:28:34.599 +02:00 ERR] ShopBot.Services.ImageUploadService: [Image Upload] ❌ Upload nieudany: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg - HTTP Unauthorized: {"error_summary": "expired_access_token/..", "error": {".tag": "expired_access_token"}}
[2025-06-16 15:28:34.619 +02:00 ERR] ShopBot.Services.DropboxService: [Dropbox] ❌ Błąd uploadu: Błąd uploadu: HTTP Unauthorized: {"error_summary": "expired_access_token/", "error": {".tag": "expired_access_token"}}
[2025-06-16 15:28:34.620 +02:00 ERR] ShopBot.Services.ImageUploadService: [Image Upload] ❌ Upload nieudany: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg - HTTP Unauthorized: {"error_summary": "expired_access_token/", "error": {".tag": "expired_access_token"}}
[2025-06-16 15:28:34.621 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] 📊 Upload zakończony: 0/4 pomyślnych
[2025-06-16 15:28:34.623 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 📊 Pomyślnie zauploadowano 0/4 obrazów
[2025-06-16 15:28:34.624 +02:00 WRN] ShopBot.Services.G2GPublisherService: [G2G] ⚠️ Nie udało się zauploadować żadnego obrazu
[2025-06-16 15:28:34.625 +02:00 INF] ShopBot.Services.G2GPublisherService: ✅ Zakończono upload obrazów
[2025-06-16 15:28:34.626 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G Publisher] 💰 Parsed USDT price: 51.05 USDT → 51.05 USD
[2025-06-16 15:28:34.627 +02:00 INF] ShopBot.Services.G2GPublisherService: 💰 Cena oryginalna: $51.05 → Cena G2G: $66.36 (gra: RaidShadowLegends)
[2025-06-16 15:28:35.740 +02:00 INF] ShopBot.Services.G2GPublisherService: 🚀 Publikuję ofertę na G2G
[2025-06-16 15:28:35.743 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🔄 Próba publikacji 1/3
[2025-06-16 15:28:35.760 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🖱️ Klikam przycisk Publish
[2025-06-16 15:28:37.450 +02:00 DBG] ShopBot.Services.G2GPublisherService: 📡 Odpowiedź API (Status: 200): {"code": 2000, "messages": [], "payload": {"offer_id": "G1750080518139GL", "seller_id": "7670672", "service_id": "f6a1aba5-473a-4044-836a-8968bbab16d7", "brand_id": "lgc_game_26258", "region_id": "", "relation_id": "59ca631d-a789-4799-a5cc-b3bb50b40dc7", "offer_type": "public", "offer_attributes": [{"collection_id": "lgc_26258_platform", "dataset_id": "lgc_26258_platform_30659"}], "offer_title_collection_tree": ["lgc_26258_platform"], "primary_img_attributes": [], "offer_group": "G1750080518139GL", "title": "mythic comidus + na***s + gleikad + elva + esme and modo", "description": "\u2022 Selling an account with top mythic komidus echidnik + narses + gleykad + elva + chagur + esme and modo + lonataril + legi for entry + many good epics. there is a ratnik for ankyl | At the moment 5kb is closed for 2 keys. 8.5k energy | 1.9k rubies. there is very little left until the arbiter (13 missions). mine 3/3. i will send any screenshots upon request | Reserve your ideal account now!\n\n===============================================================\nGot questions? Contact me on G2G chat before buying! Offers like this go fast \u2013 reserve your ideal account now!\n\n(********)", "api_qty": 0, "low_stock_alert_qty": 0, "available_qty": 1, "min_qty": 1, "actual_qty": 1, "currency": "USD", "unit_price": 66.36, "other_pricing": [], "unit_name": "", "qty_metric": "1", "wholesale_details": [], "other_wholesale_details": [], "is_official": false, "delivery_mode": [], "delivery_method_ids": [], "delivery_speed": "manual", "delivery_speed_details": [{"min": 1, "max": 1, "delivery_time": 540}], "sales_territory_settings": {"settings_type": "global", "countries": []}, "cat_path": "5830014a-b974-45c6-9672-b51e83112fb7", "cat_id": "5830014a-b974-45c6-9672-b51e83112fb7", "ancestor_id": "5830014a-b974-45c6-9672-b51e83112fb7", "status": "live", "created_at": *************, "updated_at": *************, "seller_updated_at": *************, "external_images_mapping": []}, "request_id": "37acf6c7-743d-445c-90a3-8d6d715120f7"}
[2025-06-16 15:28:37.454 +02:00 INF] ShopBot.Services.G2GPublisherService: 🎉 Sukces! Otrzymano ID oferty: G1750080518139GL
[2025-06-16 15:28:37.455 +02:00 INF] ShopBot.Services.G2GPublisherService: ✅ Oferta opublikowana pomyślnie (ID: G1750080518139GL)
[2025-06-16 15:28:38.119 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] ✅ Pomyślnie opublikowano ofertę ******** -> G1750080518139GL
[2025-06-16 15:28:38.155 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Oferta ******** została dodana
[2025-06-16 16:43:14.884 +02:00 INF] ShopBot.Services.CliCommandHandler: 🔧 Testowanie nowego Dropbox Refresh Token System...
[2025-06-16 16:43:14.910 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Inicjalizacja token managera...
[2025-06-16 16:43:14.911 +02:00 WRN] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Brak zapisanego refresh token - wymagana autoryzacja
[2025-06-16 16:43:32.839 +02:00 INF] ShopBot.Services.DropboxAuthService: [Dropbox Auth] Wymienianie authorization code na tokeny...
[2025-06-16 16:43:33.216 +02:00 DBG] ShopBot.Services.DropboxAuthService: [Dropbox Auth] Token response: {"error": "invalid_client: Invalid client_id or client_secret"}
[2025-06-16 16:43:33.219 +02:00 ERR] ShopBot.Services.DropboxAuthService: [Dropbox Auth] ❌ Błąd podczas wymiany authorization code
System.Exception: Dropbox token request failed: BadRequest - {"error": "invalid_client: Invalid client_id or client_secret"}
   at ShopBot.Services.DropboxAuthService.GetAccessTokenWithCodeAsync(String authorizationCode) in C:\Users\<USER>\Desktop\SHOPBOT v7 dzialajaca\Services\DropboxAuthService.cs:line 67
[2025-06-16 16:43:33.222 +02:00 ERR] ShopBot.Services.CliCommandHandler: ❌ Error during Dropbox test
System.Exception: Dropbox authentication failed: Dropbox token request failed: BadRequest - {"error": "invalid_client: Invalid client_id or client_secret"}
 ---> System.Exception: Dropbox token request failed: BadRequest - {"error": "invalid_client: Invalid client_id or client_secret"}
   at ShopBot.Services.DropboxAuthService.GetAccessTokenWithCodeAsync(String authorizationCode) in C:\Users\<USER>\Desktop\SHOPBOT v7 dzialajaca\Services\DropboxAuthService.cs:line 67
   --- End of inner exception stack trace ---
   at ShopBot.Services.DropboxAuthService.GetAccessTokenWithCodeAsync(String authorizationCode) in C:\Users\<USER>\Desktop\SHOPBOT v7 dzialajaca\Services\DropboxAuthService.cs:line 89
   at ShopBot.Services.DropboxTokenManager.RequestInitialAuthorizationAsync() in C:\Users\<USER>\Desktop\SHOPBOT v7 dzialajaca\Services\DropboxTokenManager.cs:line 86
   at ShopBot.Services.DropboxTokenManager.InitializeAsync() in C:\Users\<USER>\Desktop\SHOPBOT v7 dzialajaca\Services\DropboxTokenManager.cs:line 53
   at ShopBot.Services.CliCommandHandler.HandleTestDropboxCommand(String[] args) in C:\Users\<USER>\Desktop\SHOPBOT v7 dzialajaca\Services\CliCommandHandler.cs:line 325
[2025-06-16 16:48:15.701 +02:00 INF] ShopBot.Services.CliCommandHandler: 🔧 Testowanie nowego Dropbox Refresh Token System...
[2025-06-16 16:48:15.727 +02:00 ERR] ShopBot.Services.CliCommandHandler: ❌ Error during Dropbox test
System.InvalidOperationException: No service for type 'ShopBot.Services.DropboxTokenManager' has been registered.
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at ShopBot.Services.CliCommandHandler.HandleTestDropboxCommand(String[] args) in C:\Users\<USER>\Desktop\SHOPBOT v7 dzialajaca\Services\CliCommandHandler.cs:line 320
[2025-06-16 16:48:57.477 +02:00 INF] ShopBot.Services.CliCommandHandler: 🔧 Testowanie nowego Dropbox Refresh Token System...
[2025-06-16 16:48:57.499 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Inicjalizacja token managera...
[2025-06-16 16:48:57.501 +02:00 WRN] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Brak zapisanego refresh token - wymagana autoryzacja
[2025-06-16 16:52:07.211 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using static access token (legacy mode)
[2025-06-16 16:52:07.232 +02:00 INF] ShopBot.Services.CliCommandHandler: 🔧 Testowanie nowego Dropbox Refresh Token System...
[2025-06-16 16:52:07.235 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Inicjalizacja token managera...
[2025-06-16 16:52:07.237 +02:00 WRN] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Brak zapisanego refresh token - wymagana autoryzacja
[2025-06-16 16:53:11.908 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using static access token (legacy mode)
[2025-06-16 16:53:11.930 +02:00 INF] ShopBot.Services.CliCommandHandler: 🔧 Testowanie nowego Dropbox Refresh Token System...
[2025-06-16 16:53:11.933 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Inicjalizacja token managera...
[2025-06-16 16:53:11.934 +02:00 WRN] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Brak zapisanego refresh token - wymagana autoryzacja
[2025-06-16 16:55:12.597 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using static access token (legacy mode)
[2025-06-16 16:55:12.621 +02:00 INF] ShopBot.Services.CliCommandHandler: 🔧 Testowanie Dropbox System...
[2025-06-16 16:55:12.627 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://funpay.com/img/layout/logo.png
[2025-06-16 16:55:13.113 +02:00 ERR] ShopBot.Services.DropboxService: [Dropbox] ❌ Błąd uploadu: Błąd pobierania: NotFound
[2025-06-16 16:55:13.114 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Dropbox test completed successfully
[2025-06-16 16:55:49.477 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using static access token (legacy mode)
[2025-06-16 16:55:49.503 +02:00 INF] ShopBot.Services.CliCommandHandler: 🔧 Testowanie Dropbox System...
[2025-06-16 16:55:49.509 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://httpbin.org/image/png
[2025-06-16 16:55:50.084 +02:00 ERR] ShopBot.Services.DropboxService: [Dropbox] ❌ Błąd uploadu: Błąd pobierania: ServiceUnavailable
[2025-06-16 16:55:50.086 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Dropbox test completed successfully
[2025-06-16 16:56:29.861 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using static access token (legacy mode)
[2025-06-16 16:56:29.882 +02:00 INF] ShopBot.Services.CliCommandHandler: 🔧 Testowanie Dropbox System...
[2025-06-16 16:56:30.462 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Dropbox test completed successfully
[2025-06-16 17:04:00.597 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-16 17:04:00.619 +02:00 INF] ShopBot.Services.CliCommandHandler: 🔧 Testowanie Dropbox System...
[2025-06-16 17:04:00.620 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Inicjalizacja token managera...
[2025-06-16 17:04:00.622 +02:00 WRN] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Brak zapisanego refresh token - wymagana autoryzacja
[2025-06-16 17:05:47.472 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-16 17:05:47.498 +02:00 INF] ShopBot.Services.CliCommandHandler: 🔧 Testowanie Dropbox System...
[2025-06-16 17:05:47.502 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Inicjalizacja token managera...
[2025-06-16 17:05:47.504 +02:00 WRN] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Brak zapisanego refresh token - wymagana autoryzacja
[2025-06-16 17:06:35.745 +02:00 INF] ShopBot.Services.DropboxAuthService: [Dropbox Auth] Wymienianie authorization code na tokeny...
[2025-06-16 17:06:36.776 +02:00 DBG] ShopBot.Services.DropboxAuthService: [Dropbox Auth] Token response: {"access_token": "sl.u.AFxTNIgB4Ed6OSh33V0F0_bSl9DiU69yPI5N6i2JKRAz5GQPHEiIK54g1iP8pkeSElmOsiZrtBbY01TpeAgdrHNbULCaUvquUPd2DbA0y02DaU494lCs9iax7DCrOT3VijLaeOn_ZMn5vsAAr2oqtku0FVbDithC6fxnNBjX0izKD1zSNhrx9Xg2zcvsYLp2K_ePYz435tmsjVQmzKcjR16asRT1YFtyQxBtbp8jDTJdIYQbzVyfaNSTjF9nps5BZarV410R5QIULkLzDHwMtO2CAm6WW3meMzA0wY9I9ANxkoWdDkgLTZErf6VX-9Rv5-jm4Q3QyJQyDsJEDvNo7fnffrmJ_7Y_t9R73E9k0iqdSUEQClPhWlcT2f5iTMgU3HhNqxI6e2w8f18zDJiZvFf9xRw8i8VioJv81b6J1zMwDgCm4FmI7vRhmPZWGFlLF45Xjuwi9ALuLVQCNuUAXwvsVm2QZlqHR63d-2i14_QKnu36FDKP481zkTbOqs8a_FdYfjG3WztyIulsX-v8i0SLi1Fxccn40W5fZ4YGfc4TnC2_psm_cZdOlA6FiwdKbQubUHnGZHOVN9fYVOEodHtm-8PI10UkhhDYyPW8tW6ksss6BxwL8Pwaon9YJTQ3y7R7EmA0RBnzN8dqw9adKmwhWTucRydl7kc-gEwS_sDmIfyr3uRu_fFKQoGpOzAnKlxjgr7moLtn-9_-fTBAVoKvUV5jifL1AAeyWSlKidoBudABuKvx4q_ipDihrVMxZvvElXr_U-wRWkz97tEO6UvsR0FnNm8wQHkXbig0hRgf0xAVs8_VPkPWRffjAJcA6lAYaZZmmax-H2c6aekMWuwutF4kIhs1EcCuXBog1GQHNKuQZN-D-69c2YlpVTRu1h3Ecfw2mqBj553rNlgqK2_zA8UWN0lOa9fDpYqQnjfdW5zcn847HvHTqqrmmueDUNBivSNV5Yfhh7LLcWT_F7D_gQi4piOvqdic8GKBrP5OSnptK12P1kYOuWlXjeWlXodvtkbizCYx0MHPmzywU5anMBe2j1TqqJuSKAQeP1MLv54gLWlxRM7IcdjgRCftmWfRmZ3137vCd4L5uhgSA9EIIQoob5wYellKKLx5KRfcCckrFgaRebAS44SPoWtiwwcOP1f-d1jGZT2Ok8cQoQBOeG0b9VuaR5SUi9NNtRlHvbGLNjSXCVxfpk4Gn90FWgoJqlZIJ3GtOtHLYFAS4AdEYNYlwkFRNYPwnS2hBIiVqnQimhSvN7DyevPSNwq0SzcJDaGa2Gnl9kOZnE9W7jz-UdxmcjxyaUWf_8ybtvrjM16LEu-ZWwG-GqqyuG2IyRraRVk_ycDmFEqLQC5tXiB-aMDKw13wXRBQ3AEM2a1AkvvDhbV2xB2w8ntFVsaV81CGHUt9NqcIGQThg_9TLAaH", "token_type": "bearer", "expires_in": 14400, "refresh_token": "VLTk0nph2nYAAAAAAAAAAcel6FZc2d_yrTdqs9gGJtlnGHUmD_-QWZHyBViLmNyW", "scope": "files.content.read files.content.write files.metadata.read sharing.read sharing.write", "uid": "**********", "account_id": "dbid:AABc3Qic1YxGOS3CnFQ8XisudugWPzXwOTo"}
[2025-06-16 17:06:36.779 +02:00 INF] ShopBot.Services.DropboxAuthService: [Dropbox Auth] ✅ Otrzymano tokeny - Access: 1437 chars, Refresh: 64 chars
[2025-06-16 17:06:36.785 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Zapisano token do pliku
[2025-06-16 17:06:37.033 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Dropbox test completed successfully
[2025-06-16 17:09:16.240 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-16 17:09:16.261 +02:00 INF] ShopBot.Services.CliCommandHandler: 🔧 Testowanie Dropbox System...
[2025-06-16 17:09:16.263 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Inicjalizacja token managera...
[2025-06-16 17:09:16.270 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Załadowano token z pliku - wygasa: "2025-06-16T19:01:36.7819719Z"
[2025-06-16 17:09:16.274 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Znaleziono zapisany refresh token
[2025-06-16 17:09:16.275 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Access token jest jeszcze ważny
[2025-06-16 17:09:16.278 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Dropbox test completed successfully
[2025-06-16 17:18:50.998 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-16 17:18:51.023 +02:00 INF] ShopBot.Services.CliCommandHandler: ➕ Dodaję ofertę FunPay ID: ********, Gra: RaidShadowLegends
[2025-06-16 17:18:52.710 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Mythical champions = 1
[2025-06-16 17:18:52.713 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 35
[2025-06-16 17:18:52.715 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 59
[2025-06-16 17:18:52.716 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-16 17:18:52.718 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-16 17:18:52.720 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-16 17:18:52.724 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 17:18:52.727 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 17:18:52.740 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 17:18:52.741 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 17:18:52.746 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-16 17:18:52.747 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 17:18:52.749 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 17:18:52.750 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 17:18:52.751 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 17:18:52.752 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 17:18:52.754 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-16 17:18:52.755 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-16 17:18:52.756 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-16 17:18:52.757 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-16 17:18:52.758 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-16 17:18:52.761 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-16 17:18:52.763 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 51.14 $</span></span>
[2025-06-16 17:18:52.764 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:18:52.766 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:18:52.767 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:18:52.769 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>51.13 USDT</span></span>
[2025-06-16 17:18:52.771 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 51.13
[2025-06-16 17:18:52.773 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 51.13 USDT
[2025-06-16 17:18:52.779 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 17:18:52.781 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 17:18:52.782 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 17:18:52.783 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 17:18:52.785 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 17:18:52.786 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-16 17:18:52.787 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-16 17:18:53.770 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] 🔄 Rozpoczynam dodawanie oferty ********
[2025-06-16 17:18:54.389 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Mythical champions = 1
[2025-06-16 17:18:54.391 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 35
[2025-06-16 17:18:54.392 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 59
[2025-06-16 17:18:54.394 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-16 17:18:54.395 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-16 17:18:54.396 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-16 17:18:54.398 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 17:18:54.399 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 17:18:54.402 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 17:18:54.403 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 17:18:54.405 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-16 17:18:54.406 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 17:18:54.407 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 17:18:54.409 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 17:18:54.410 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 17:18:54.411 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 17:18:54.412 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-16 17:18:54.413 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-16 17:18:54.414 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-16 17:18:54.415 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-16 17:18:54.416 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-16 17:18:54.417 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-16 17:18:54.418 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 51.14 $</span></span>
[2025-06-16 17:18:54.419 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:18:54.421 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:18:54.422 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:18:54.423 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>51.13 USDT</span></span>
[2025-06-16 17:18:54.424 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 51.13
[2025-06-16 17:18:54.426 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 51.13 USDT
[2025-06-16 17:18:54.427 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 17:18:54.429 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 17:18:54.430 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 17:18:54.430 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 17:18:54.432 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 17:18:54.433 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-16 17:18:54.434 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-16 17:18:56.448 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] 💰 Parsed USDT price: 51.13 USDT → 51.13 USD
[2025-06-16 17:18:58.422 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 🎮 Rozpoczynam przetwarzanie gry: RaidShadowLegends
[2025-06-16 17:19:20.874 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G Publisher] 💰 Parsed USDT price: 51.13 USDT → 51.13 USD
[2025-06-16 17:19:20.881 +02:00 INF] ShopBot.Services.G2GPublisherService: 💰 Cena oryginalna: $51.13 → Cena G2G: $66.47 (gra: RaidShadowLegends)
[2025-06-16 17:19:22.056 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Zakończono przetwarzanie gry: RaidShadowLegends
[2025-06-16 17:19:22.057 +02:00 INF] ShopBot.Services.G2GPublisherService: 🚀 Rozpoczynam wypełnianie tytułu i opisu dla gry: RaidShadowLegends
[2025-06-16 17:19:22.059 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: RaidShadowLegends
[2025-06-16 17:19:22.087 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: RaidShadowLegends
[2025-06-16 17:19:22.089 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Formatting Raid Shadow Legends description for offer ********
[2025-06-16 17:19:22.091 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Split by | into 5 lines
[2025-06-16 17:19:22.093 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Adding ID to description: ********
[2025-06-16 17:19:22.094 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Final formatted description:
• Selling an account with top mythic komidus echidnik + narses + gleykad + elva + chagur + esme and modo + lonataril + legi for entry + many good epics. there is a ratnik for ankyl | At the moment 5kb is closed for 2 keys. 8.5k energy | 1.9k rubies. there is very little left until the arbiter (13 missions). mine 3/3. i will send any screenshots upon request | Reserve your ideal account now!

===============================================================
Got questions? Contact me on G2G chat before buying! Offers like this go fast – reserve your ideal account now!

(********)

[2025-06-16 17:19:22.095 +02:00 DBG] ShopBot.Services.G2GPublisherService: 📝 Wypełniam opis (587 znaków): • Selling an account with top mythic komidus echidnik + narses + gleykad + elva + chagur + esme and ...
[2025-06-16 17:19:22.137 +02:00 INF] ShopBot.Services.G2GPublisherService: ✅ Zakończono wypełnianie tytułu i opisu
[2025-06-16 17:19:22.138 +02:00 INF] ShopBot.Services.G2GPublisherService: 📸 Rozpoczynam upload 4 obrazów
[2025-06-16 17:19:22.140 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 🖼️ Rozpoczynam upload 4 obrazów do Dropbox
[2025-06-16 17:19:22.141 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] Rozpoczynam upload 4 obrazów do Dropbox dla G2G
[2025-06-16 17:19:22.143 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] 🚀 Rozpoczynam PARALLEL upload 4 obrazów (Dropbox: true)
[2025-06-16 17:19:22.145 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 17:19:22.147 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 17:19:22.149 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 17:19:22.169 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 17:19:22.170 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 17:19:22.172 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 17:19:22.173 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 17:19:22.174 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 17:19:22.175 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 17:19:22.927 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 544KB w 0.8s
[2025-06-16 17:19:22.930 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Token wygaśnie wkrótce, odświeżam...
[2025-06-16 17:19:22.932 +02:00 ERR] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Brak refresh token - wymagana ponowna autoryzacja
[2025-06-16 17:19:22.963 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 594KB w 0.8s
[2025-06-16 17:19:22.964 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Token wygaśnie wkrótce, odświeżam...
[2025-06-16 17:19:22.965 +02:00 ERR] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Brak refresh token - wymagana ponowna autoryzacja
[2025-06-16 17:19:23.030 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 656KB w 0.9s
[2025-06-16 17:19:23.031 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Token wygaśnie wkrótce, odświeżam...
[2025-06-16 17:19:23.032 +02:00 ERR] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Brak refresh token - wymagana ponowna autoryzacja
[2025-06-16 17:22:35.908 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-16 17:22:35.931 +02:00 INF] ShopBot.Services.CliCommandHandler: ➕ Dodaję ofertę FunPay ID: ********, Gra: RaidShadowLegends
[2025-06-16 17:22:37.606 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Mythical champions = 1
[2025-06-16 17:22:37.608 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 35
[2025-06-16 17:22:37.609 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 59
[2025-06-16 17:22:37.610 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-16 17:22:37.612 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-16 17:22:37.613 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-16 17:22:37.616 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 17:22:37.618 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 17:22:37.628 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 17:22:37.629 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 17:22:37.632 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-16 17:22:37.633 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 17:22:37.635 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 17:22:37.636 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 17:22:37.637 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 17:22:37.638 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 17:22:37.639 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-16 17:22:37.640 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-16 17:22:37.641 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-16 17:22:37.642 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-16 17:22:37.643 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-16 17:22:37.646 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-16 17:22:37.647 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 51.14 $</span></span>
[2025-06-16 17:22:37.648 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:22:37.650 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:22:37.651 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:22:37.652 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>51.13 USDT</span></span>
[2025-06-16 17:22:37.654 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 51.13
[2025-06-16 17:22:37.655 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 51.13 USDT
[2025-06-16 17:22:37.660 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 17:22:37.661 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 17:22:37.662 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 17:22:37.663 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 17:22:37.664 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 17:22:37.665 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-16 17:22:37.666 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-16 17:22:38.181 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] 🔄 Rozpoczynam dodawanie oferty ********
[2025-06-16 17:22:38.797 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Mythical champions = 1
[2025-06-16 17:22:38.799 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 35
[2025-06-16 17:22:38.800 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 59
[2025-06-16 17:22:38.801 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-16 17:22:38.802 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-16 17:22:38.804 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-16 17:22:38.805 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 17:22:38.806 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 17:22:38.810 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 17:22:38.811 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 17:22:38.813 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-16 17:22:38.814 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 17:22:38.815 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 17:22:38.816 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 17:22:38.818 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 17:22:38.819 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 17:22:38.820 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-16 17:22:38.821 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-16 17:22:38.821 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-16 17:22:38.822 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-16 17:22:38.823 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-16 17:22:38.824 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-16 17:22:38.825 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 51.14 $</span></span>
[2025-06-16 17:22:38.826 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:22:38.828 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:22:38.829 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:22:38.830 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>51.13 USDT</span></span>
[2025-06-16 17:22:38.832 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 51.13
[2025-06-16 17:22:38.833 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 51.13 USDT
[2025-06-16 17:22:38.835 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 17:22:38.836 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 17:22:38.837 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 17:22:38.837 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 17:22:38.838 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 17:22:38.839 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-16 17:22:38.840 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-16 17:22:40.844 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] 💰 Parsed USDT price: 51.13 USDT → 51.13 USD
[2025-06-16 17:22:43.209 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 🎮 Rozpoczynam przetwarzanie gry: RaidShadowLegends
[2025-06-16 17:23:03.817 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G Publisher] 💰 Parsed USDT price: 51.13 USDT → 51.13 USD
[2025-06-16 17:23:03.824 +02:00 INF] ShopBot.Services.G2GPublisherService: 💰 Cena oryginalna: $51.13 → Cena G2G: $66.47 (gra: RaidShadowLegends)
[2025-06-16 17:23:04.969 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Zakończono przetwarzanie gry: RaidShadowLegends
[2025-06-16 17:23:04.970 +02:00 INF] ShopBot.Services.G2GPublisherService: 🚀 Rozpoczynam wypełnianie tytułu i opisu dla gry: RaidShadowLegends
[2025-06-16 17:23:04.972 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: RaidShadowLegends
[2025-06-16 17:23:05.000 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: RaidShadowLegends
[2025-06-16 17:23:05.001 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Formatting Raid Shadow Legends description for offer ********
[2025-06-16 17:23:05.004 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Split by | into 5 lines
[2025-06-16 17:23:05.005 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Adding ID to description: ********
[2025-06-16 17:23:05.006 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Final formatted description:
• Selling an account with top mythic komidus echidnik + narses + gleykad + elva + chagur + esme and modo + lonataril + legi for entry + many good epics. there is a ratnik for ankyl | At the moment 5kb is closed for 2 keys. 8.5k energy | 1.9k rubies. there is very little left until the arbiter (13 missions). mine 3/3. i will send any screenshots upon request | Reserve your ideal account now!

===============================================================
Got questions? Contact me on G2G chat before buying! Offers like this go fast – reserve your ideal account now!

(********)

[2025-06-16 17:23:05.007 +02:00 DBG] ShopBot.Services.G2GPublisherService: 📝 Wypełniam opis (587 znaków): • Selling an account with top mythic komidus echidnik + narses + gleykad + elva + chagur + esme and ...
[2025-06-16 17:23:05.049 +02:00 INF] ShopBot.Services.G2GPublisherService: ✅ Zakończono wypełnianie tytułu i opisu
[2025-06-16 17:23:05.050 +02:00 INF] ShopBot.Services.G2GPublisherService: 📸 Rozpoczynam upload 4 obrazów
[2025-06-16 17:23:05.052 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 🖼️ Rozpoczynam upload 4 obrazów do Dropbox
[2025-06-16 17:23:05.053 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] Rozpoczynam upload 4 obrazów do Dropbox dla G2G
[2025-06-16 17:23:05.054 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] 🚀 Rozpoczynam PARALLEL upload 4 obrazów (Dropbox: true)
[2025-06-16 17:23:05.056 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 17:23:05.058 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 17:23:05.060 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 17:23:05.081 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 17:23:05.082 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 17:23:05.083 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 17:23:05.085 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 17:23:05.086 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 17:23:05.087 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 17:23:05.826 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 544KB w 0.8s
[2025-06-16 17:23:05.829 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Token wygaśnie wkrótce, odświeżam...
[2025-06-16 17:23:05.830 +02:00 ERR] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Brak refresh token - wymagana ponowna autoryzacja
[2025-06-16 17:23:05.873 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 594KB w 0.8s
[2025-06-16 17:23:05.875 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Token wygaśnie wkrótce, odświeżam...
[2025-06-16 17:23:05.876 +02:00 ERR] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Brak refresh token - wymagana ponowna autoryzacja
[2025-06-16 17:23:05.891 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 656KB w 0.8s
[2025-06-16 17:23:05.892 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Token wygaśnie wkrótce, odświeżam...
[2025-06-16 17:23:05.893 +02:00 ERR] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Brak refresh token - wymagana ponowna autoryzacja
[2025-06-16 17:27:14.457 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-16 17:27:14.480 +02:00 INF] ShopBot.Services.CliCommandHandler: ➕ Dodaję ofertę FunPay ID: ********, Gra: RaidShadowLegends
[2025-06-16 17:27:15.802 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Mythical champions = 1
[2025-06-16 17:27:15.804 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 35
[2025-06-16 17:27:15.805 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 59
[2025-06-16 17:27:15.806 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-16 17:27:15.807 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-16 17:27:15.809 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-16 17:27:15.812 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 17:27:15.814 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 17:27:15.824 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 17:27:15.825 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 17:27:15.829 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-16 17:27:15.830 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 17:27:15.831 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 17:27:15.832 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 17:27:15.834 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 17:27:15.835 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 17:27:15.836 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-16 17:27:15.837 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-16 17:27:15.838 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-16 17:27:15.839 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-16 17:27:15.840 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-16 17:27:15.842 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-16 17:27:15.844 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 51.14 $</span></span>
[2025-06-16 17:27:15.845 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:27:15.846 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:27:15.848 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:27:15.849 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>51.13 USDT</span></span>
[2025-06-16 17:27:15.851 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 51.13
[2025-06-16 17:27:15.852 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 51.13 USDT
[2025-06-16 17:27:15.857 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 17:27:15.859 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 17:27:15.860 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 17:27:15.861 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 17:27:15.863 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 17:27:15.864 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-16 17:27:15.866 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-16 17:27:16.378 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] 🔄 Rozpoczynam dodawanie oferty ********
[2025-06-16 17:27:17.027 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Mythical champions = 1
[2025-06-16 17:27:17.029 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 35
[2025-06-16 17:27:17.030 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 59
[2025-06-16 17:27:17.031 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-16 17:27:17.032 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-16 17:27:17.033 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-16 17:27:17.035 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 17:27:17.036 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 17:27:17.039 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 17:27:17.040 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 17:27:17.043 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-16 17:27:17.044 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 17:27:17.045 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 17:27:17.046 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 17:27:17.047 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 17:27:17.048 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 17:27:17.049 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-16 17:27:17.050 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-16 17:27:17.051 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-16 17:27:17.052 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-16 17:27:17.053 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-16 17:27:17.054 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-16 17:27:17.055 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 51.14 $</span></span>
[2025-06-16 17:27:17.056 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:27:17.058 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:27:17.059 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:27:17.060 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>51.13 USDT</span></span>
[2025-06-16 17:27:17.062 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 51.13
[2025-06-16 17:27:17.063 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 51.13 USDT
[2025-06-16 17:27:17.065 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 17:27:17.066 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 17:27:17.067 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 17:27:17.067 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 17:27:17.068 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 17:27:17.069 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-16 17:27:17.070 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-16 17:27:19.084 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] 💰 Parsed USDT price: 51.13 USDT → 51.13 USD
[2025-06-16 17:27:21.753 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 🎮 Rozpoczynam przetwarzanie gry: RaidShadowLegends
[2025-06-16 17:27:40.166 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G Publisher] 💰 Parsed USDT price: 51.13 USDT → 51.13 USD
[2025-06-16 17:27:40.174 +02:00 INF] ShopBot.Services.G2GPublisherService: 💰 Cena oryginalna: $51.13 → Cena G2G: $66.47 (gra: RaidShadowLegends)
[2025-06-16 17:27:41.327 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Zakończono przetwarzanie gry: RaidShadowLegends
[2025-06-16 17:27:41.336 +02:00 INF] ShopBot.Services.G2GPublisherService: 🚀 Rozpoczynam wypełnianie tytułu i opisu dla gry: RaidShadowLegends
[2025-06-16 17:27:41.338 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: RaidShadowLegends
[2025-06-16 17:27:41.368 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: RaidShadowLegends
[2025-06-16 17:27:41.370 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Formatting Raid Shadow Legends description for offer ********
[2025-06-16 17:27:41.372 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Split by | into 5 lines
[2025-06-16 17:27:41.374 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Adding ID to description: ********
[2025-06-16 17:27:41.375 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Final formatted description:
• Selling an account with top mythic komidus echidnik + narses + gleykad + elva + chagur + esme and modo + lonataril + legi for entry + many good epics. there is a ratnik for ankyl | At the moment 5kb is closed for 2 keys. 8.5k energy | 1.9k rubies. there is very little left until the arbiter (13 missions). mine 3/3. i will send any screenshots upon request | Reserve your ideal account now!

===============================================================
Got questions? Contact me on G2G chat before buying! Offers like this go fast – reserve your ideal account now!

(********)

[2025-06-16 17:27:41.377 +02:00 DBG] ShopBot.Services.G2GPublisherService: 📝 Wypełniam opis (587 znaków): • Selling an account with top mythic komidus echidnik + narses + gleykad + elva + chagur + esme and ...
[2025-06-16 17:27:41.417 +02:00 INF] ShopBot.Services.G2GPublisherService: ✅ Zakończono wypełnianie tytułu i opisu
[2025-06-16 17:27:41.418 +02:00 INF] ShopBot.Services.G2GPublisherService: 📸 Rozpoczynam upload 4 obrazów
[2025-06-16 17:27:41.420 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 🖼️ Rozpoczynam upload 4 obrazów do Dropbox
[2025-06-16 17:27:41.421 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] Rozpoczynam upload 4 obrazów do Dropbox dla G2G
[2025-06-16 17:27:41.423 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] 🚀 Rozpoczynam PARALLEL upload 4 obrazów (Dropbox: true)
[2025-06-16 17:27:41.425 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 17:27:41.427 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 17:27:41.428 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 17:27:41.449 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 17:27:41.450 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 17:27:41.451 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 17:27:41.453 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 17:27:41.454 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 17:27:41.455 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 17:27:42.204 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 544KB w 0.8s
[2025-06-16 17:27:42.207 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Token wygaśnie wkrótce, odświeżam...
[2025-06-16 17:27:42.209 +02:00 WRN] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Refresh token pusty, próbuję załadować z pliku...
[2025-06-16 17:27:42.212 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Załadowano token z pliku - wygasa: "2025-06-16T10:00:00.0000000Z"
[2025-06-16 17:27:42.214 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Refresh token length: 64
[2025-06-16 17:27:42.215 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Access token length: 22
[2025-06-16 17:27:42.216 +02:00 INF] ShopBot.Services.DropboxAuthService: [Dropbox Auth] Odświeżanie access token...
[2025-06-16 17:27:42.228 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 594KB w 0.8s
[2025-06-16 17:27:42.230 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Token wygaśnie wkrótce, odświeżam...
[2025-06-16 17:27:42.230 +02:00 INF] ShopBot.Services.DropboxAuthService: [Dropbox Auth] Odświeżanie access token...
[2025-06-16 17:27:42.293 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 656KB w 0.8s
[2025-06-16 17:27:42.295 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Token wygaśnie wkrótce, odświeżam...
[2025-06-16 17:27:42.296 +02:00 INF] ShopBot.Services.DropboxAuthService: [Dropbox Auth] Odświeżanie access token...
[2025-06-16 17:27:42.592 +02:00 DBG] ShopBot.Services.DropboxAuthService: [Dropbox Auth] Refresh response: {"access_token": "sl.u.AFzJLejzg1mxIkKQCbL4nmU66uv9Qzadt5H8HxbL8t0LaxcEqt7JN_xJzhkcHLKUwql_kmf_8T6hAI7WTS59rcVJeFy2e_kaqG-XKaJ2EoB2fdN9CApjtsyS6guF9Ep9myopueEbUJregXxImIRMSNqoaH2umGhvTO5ayWosPU_IreRHW1RRWE-RuyPCweRSsZEfDmpDFpz3sYddBbBSQNATg5ijswlXylwZe2pCXUlGuGikpPnJ7JaRzZ4meFJqv5e6KBp-tAmX3rUEK-k5ydqvHUVTXe_4U92sjJ3fJCf4cgv50kMSoFH3FW1RydxAFqZVv2U8fM1ZVGV_coee2m5J8xZmZaVYPdPspVcYYpvSmEJjR9Zqd3YNh88Ba3VTE4ncOgUD-ha6DIjbC2YAsm9lxLaL2ALKI-JJmDqGvKdBT8B5ZLbNsT6eKy90azhFLP3fgZSxL0afZoGvEAXiC9p7Ub5KNMgj5pxppKGCrGOZ3hiDyIJOOsRndfFKZdZVoJ4_M6FuAfMxs9pzhykQtbVe8OMp1VLchLqpXO2tNuqAcU67E3Xu1O1qQV-heMLr6e6pfwRZgPbeB_GbEzLUQaF0Ulo02ajRrN1Ho1Haz-V5z1BeN5JhSl0-QFH9fbBE3HrjlUVKLbQXjN-teM4FhVggTjzVp84ZlNd17k-FNR_Dls9Te68YFBK57Z5OigQ95jQapSNeoIVLY-1cgUobs5AN9-0Rgjcym2xUTwiMQtVyqVomnp5s23uiDOl23ZoGjWf0kSo0Y2ubY1-_EVJ34VMVB5FRj9PzkETyYoddhQWU9ZjYGBNoDo5g7s34hge4Tfii6UojL9-9LJpRUXGjez7HSv5OmmbbibXWoJu3eyYJd1l0dC500Bf1lYZxuLntwL3AFgptIcuSgabB81OKt1G-di-aSWkehmZ1ZJiD8aNzHNBRjjB7qmZBKuAjYAAcXrjauPZ7oStJp29yb_QsvckMMW5oHD2eVsnilKApvfSrqnOmMBzi6ZiI3rlGzIL73AL9vUXqzPDT2-dy-_xdK4rUPdkcfdhpnZG4axRexqny_XLXV9yOuyWnsRy-Id-FQLzGwJlX9RBcpsO_0He8C0oyryEApa9x5Ae6PbjADw5XvcuT3gmBZ9smYlAeqmExHCgGjdjaIWJqlIhoBW9pMP5opE5hPSWPLBrDB7uk0Tp2TrninYv7UzRf9_-RquIu576kG0bwQqOt1nL97CEeLMr5DB4S5KHS6u-zSYq5Yoxp2WEk-Ky14n8Z2PvRO6SDPGwzPVuYSbjwBahCJgR-tw36IpNTcEaDMq1LyosXB0Pspql62WP2rFOM5Pexom8wJWhTz7Lay4J-W_QVDRzLlOtY5AgXHHfBbtBX8Meu6uJnbIqV9woBObPi5jR81B-eeHwEEDfXJNWhblAoPyg_RXQI", "token_type": "bearer", "expires_in": 14400}
[2025-06-16 17:27:42.595 +02:00 INF] ShopBot.Services.DropboxAuthService: [Dropbox Auth] ✅ Token odświeżony pomyślnie
[2025-06-16 17:27:42.598 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Zapisano token do pliku
[2025-06-16 17:27:42.598 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] ✅ Token został odświeżony. Następne odświeżenie za 3 godzin
[2025-06-16 17:27:42.601 +02:00 DBG] ShopBot.Services.DropboxAuthService: [Dropbox Auth] Refresh response: {"access_token": "sl.u.AFyHp8jeUWxF99J2OgHRY_Gk6mkaWQ9OUrOlJI5Hc5JMrk0yDLDBmL134ar_VRkXKcLfl3x6ItPcXbdZoB_ynwDJHzuQZ8KEpIhk3YgzEy1t1h1utyfHqYapczJ0sK49LB6q_EBUpWtxzUJRBvuvvReEf-z7ZlFrfHqrcuWrqydHAhtjPPFYX37SoWO9KnW6aGmdtWNpjHhhDj8l8ynY8XeQr1yJE_XyixUzaRH51jAmno4TdRxIAMxO4beJCKN03fmSgSXlbxAOAeO-kQeswA4hoGp8VgLgcRYdBmhDn8ytulIuAn8rgw786zxvzEvZYXDhKuvdRtCSkR67KYLbOLcjo7DD3zEHum9HMXQhOtffXqn9ADUFD1_5Rb5CySFnHHxL_cgprwXhcutXBAzQ_lmP8I5X4oGJuM8MijBeJUPXv0hFRscm7478V-5Fw6yCq8UnVG_ptDBYONhb5-j618KZ0hDBAyezSaovni8wwQQg3K7geNmBy0ytvv2EGQiyJ6PzvgGYmaDphBUNVLXs2tvPEphOHUVq0pVgJnC1v587Yk91Ers6Mmij-UEV8t7odJ1ELyNBd9U6EJtMNUtW2R6eBk--VZgQVxHcwCNf-RsdGvhWwreaY1bZRYWozn5m855b6gj-Y1xIEmJEa6bXRa9DIzSprgZrJmhb57c3J9KO2iMWEKyY0vYmrr2J5C24zFqefdyYLI3c7hrqZaAO0Nkgi_7Plmc4ilSd4143hWoDWIpEceNIBOHdeBXjn6t2KeTz6f_MFxr7wB9CildOaKOD5Ra3JHdHU7AY-IV4h2cdCNwvE1pIT5VvNs4NQnLqN0tb4j4Py0fs8dp0co3VizpPk_9p7lrW_w82jJKOR6ruroEkWuN5UONYF2y1O7_dwTScMyehi2vFQZ4CDwB22Q06WdXPvQ_zl0sheMOsytkbvL5qf8Ah0mnpUXqz0Jwgy6EY9F9MDKE0XQNlrf5iIPWTLac527dYd7SFpKtAv_2zhgATyfuOkFk6Fr2CtJYrxwZdFpJD9PIqep-ZjP3AkP69EhwE_J5I9ZE8LZlbCw3UfOvEreB-NQFv3HEkzVmS0A6dgmM-6ya1kz13cEQvsi1Bxhq0p42RhsO6t2JFd_EbWv5ZLiY6RDuOzNBFCznt-NGR9xeKyFISgclvraJDGkvl8I0wDq2768Mu0BGcF-3tOSG0baA9EZxPgPhY2NoRKjIJbiQBQSjvAporUhpBwLrjPD1pfRx68BH_R2-VRYWh60lb5XhJ2vhx00IrCmtl_PhBNaKw0baF5sA0hCFXBr9_32ORu9SGesdpC6NJXb7KiOPwrjuFqLnFs3uM2tc6NNeXT0_JIZlzQeIGYxoK7E7TR47HdjUflSLB79sCDs4f8xzVb4xwXHSfG_-DSoUmnE5ljwc_qa_TJ0PLj8NABEYh", "token_type": "bearer", "expires_in": 14400}
[2025-06-16 17:27:42.603 +02:00 INF] ShopBot.Services.DropboxAuthService: [Dropbox Auth] ✅ Token odświeżony pomyślnie
[2025-06-16 17:27:42.604 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Zapisano token do pliku
[2025-06-16 17:27:42.605 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] ✅ Token został odświeżony. Następne odświeżenie za 3 godzin
[2025-06-16 17:27:42.668 +02:00 DBG] ShopBot.Services.DropboxAuthService: [Dropbox Auth] Refresh response: {"access_token": "sl.u.AFwrl5j2-JH2Kyu-UeeRE25jpuHHB8dfYAQ6l6A2nlzhrYMQvp8FantoJbw4_r7eprH5uH9agwPLgEcIkMhOzT9TGRusBQVV0F2QH8NzlFMbrwVrcBHPuKD2hDpV08yY7DWCD-3nUc1Rnxz9Cs8ZPxnDsXdqP7c30tVHBVerwYeJ0vu5rFs3TWzR547Il7Of2rz2gxkT7bSf08lakyqn1nWtJ1H3tiU7YKl_vK-942ua2qkbRGhfK_Uv-Y0p2zoj0Eile7mfuGevxsV9vO2Xjspr_QlTTth7nJjs-6V_0jsm2-anu1D1VGakB6dviztAMqTW2A7w2k1-d8R2wDHb46RzVpOZDGAZvfGmOqO3zrotGEjpJj07EB7ucXbrklkKg27q7vlMOdGyYrL7cev29m5hirht20JlTAitnx8ydA1tZtbRJrqiSS9bylZshot1s539yFzkAUNROZIlh449gbpcbGeDntO6a4MtXcYOPnRhOFxdVK7_MOsvxk9jWr3M3ud7tY5okgYZjj_BO-AbH5NvE7BpX1tFD5X95eEkDdo6rJAM4P78Ee8fSOWD6s8b3hxirR6fjtP8Xp7EZZkQ0d1CNdVJ_ZX7slGnd3SbewsMjobj2uqyOBxTxwllLW3Ab0zWBbSvlhffihZy0Osv-4HH_ndc9zVMytHKj_TAHctMl4ClPvFku96v0nhSeDOsKJVmaM14GSZBQ2rsR1CQ6bSJIj2kgATDL3aiKMLNbBY3pOKO388SVhzAncXnPz5Fepeq05Unv37J4bFF7wDTgJ-ilc8Luk8Zp3NF5aWunmYHOi7HOypfXXNg3zh6uRtGEV5BNWANR_50_JBuesPGcGurjxRD6lcKH9fhjI4tbXrjJcEmB0n692WXS3l4SHh-x6UhW1YuTmKNxL7MvN9cnYQf408TZE_2XLt3YkmgtBWjEOPQAWrSTQ_tNBzc_o-ToMNjLQmhLUaXkuPnz2kUEYum7c6zz40ap_aV2zD7hN4_PTkVb-0plkvlIQB4-PGn60lBPv66iJLkSKe41AvOKVW-5T1kDRCTglms9D5DFTHOYIPrkbGsY1DzC4-BayqTJhQpGTxjPCrfsEaRWFtdkN9AzynC5MXEk6rPbSk9DcY7sxTbOKzIXJuPbFyocL49SjPUvTjBWrhIRu2x7cHjYZGMN3UKHu5ivemku-Pg8xRNAP1c_jw1LdzfL53GHEH6KYwDENi3NZ1t7pKAr_pWDmd0EOrU_32kPN6MFQxx5gByKoNwswr1k30ik-JVu1TW-mQMq_oeEWxqXFbWMrhHXzsoWeNC_tBDoTVj2SYHNfQ9b8UL6BzO0K4PWmOfFwgRfZvIybWLsawKMW7qs1FgSdPLs5ubErqyEA663MeZMzHfRBKh_4ws4EQVkX3knxYthOWIfLKTppz2DxMn2eK9XwwZ", "token_type": "bearer", "expires_in": 14400}
[2025-06-16 17:27:42.670 +02:00 INF] ShopBot.Services.DropboxAuthService: [Dropbox Auth] ✅ Token odświeżony pomyślnie
[2025-06-16 17:27:42.671 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Zapisano token do pliku
[2025-06-16 17:27:42.672 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] ✅ Token został odświeżony. Następne odświeżenie za 3 godzin
[2025-06-16 17:27:44.219 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Upload do /G2G_Screenshots/screenshot_20250616_172742_3656c579.jpg pomyślny
[2025-06-16 17:27:44.786 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Upload do /G2G_Screenshots/screenshot_20250616_172742_716e94d3.jpg pomyślny
[2025-06-16 17:27:45.257 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Upload do /G2G_Screenshots/screenshot_20250616_172742_6047d9ce.jpg pomyślny
[2025-06-16 17:27:45.773 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Otrzymano shared URL: https://www.dropbox.com/scl/fi/l3w47o1ov9x9toampjzom/screenshot_20250616_172742_3656c579.jpg?rlkey=67oxc4ae5cqfcoonr367t4ab9&dl=0
[2025-06-16 17:27:45.775 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Konwersja URL: https://www.dropbox.com/scl/fi/l3w47o1ov9x9toampjzom/screenshot_20250616_172742_3656c579.jpg?rlkey=67oxc4ae5cqfcoonr367t4ab9&dl=0 -> https://www.dl.dropboxusercontent.com/scl/fi/l3w47o1ov9x9toampjzom/screenshot_20250616_172742_3656c579.jpg?rlkey=67oxc4ae5cqfcoonr367t4ab9
[2025-06-16 17:27:45.776 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ✅ Upload pomyślny w 4.3s: https://www.dl.dropboxusercontent.com/scl/fi/l3w47o1ov9x9toampjzom/screenshot_20250616_172742_3656c579.jpg?rlkey=67oxc4ae5cqfcoonr367t4ab9
[2025-06-16 17:27:45.778 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ✅ Upload pomyślny: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 17:27:45.778 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg
[2025-06-16 17:27:45.779 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg
[2025-06-16 17:27:45.780 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg
[2025-06-16 17:27:46.074 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 253KB w 0.3s
[2025-06-16 17:27:46.374 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Otrzymano shared URL: https://www.dropbox.com/scl/fi/bma5dbugato3q2xlhauzj/screenshot_20250616_172742_716e94d3.jpg?rlkey=wnoyszqlkpwq36fel4e6qxnke&dl=0
[2025-06-16 17:27:46.376 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Konwersja URL: https://www.dropbox.com/scl/fi/bma5dbugato3q2xlhauzj/screenshot_20250616_172742_716e94d3.jpg?rlkey=wnoyszqlkpwq36fel4e6qxnke&dl=0 -> https://www.dl.dropboxusercontent.com/scl/fi/bma5dbugato3q2xlhauzj/screenshot_20250616_172742_716e94d3.jpg?rlkey=wnoyszqlkpwq36fel4e6qxnke
[2025-06-16 17:27:46.377 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ✅ Upload pomyślny w 4.9s: https://www.dl.dropboxusercontent.com/scl/fi/bma5dbugato3q2xlhauzj/screenshot_20250616_172742_716e94d3.jpg?rlkey=wnoyszqlkpwq36fel4e6qxnke
[2025-06-16 17:27:46.378 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ✅ Upload pomyślny: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 17:27:46.809 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Otrzymano shared URL: https://www.dropbox.com/scl/fi/tn1dxdk5gfbphqthf4nhu/screenshot_20250616_172742_6047d9ce.jpg?rlkey=eiwk7p2ysdynmd56ops1mevdg&dl=0
[2025-06-16 17:27:46.810 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Konwersja URL: https://www.dropbox.com/scl/fi/tn1dxdk5gfbphqthf4nhu/screenshot_20250616_172742_6047d9ce.jpg?rlkey=eiwk7p2ysdynmd56ops1mevdg&dl=0 -> https://www.dl.dropboxusercontent.com/scl/fi/tn1dxdk5gfbphqthf4nhu/screenshot_20250616_172742_6047d9ce.jpg?rlkey=eiwk7p2ysdynmd56ops1mevdg
[2025-06-16 17:27:46.811 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ✅ Upload pomyślny w 5.4s: https://www.dl.dropboxusercontent.com/scl/fi/tn1dxdk5gfbphqthf4nhu/screenshot_20250616_172742_6047d9ce.jpg?rlkey=eiwk7p2ysdynmd56ops1mevdg
[2025-06-16 17:27:46.812 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ✅ Upload pomyślny: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 17:27:47.210 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Upload do /G2G_Screenshots/screenshot_20250616_172746_ef6ce461.jpg pomyślny
[2025-06-16 17:27:48.645 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Otrzymano shared URL: https://www.dropbox.com/scl/fi/nhfzdtj69zyh2vb9cu28l/screenshot_20250616_172746_ef6ce461.jpg?rlkey=2ffbn0m2p4z1t92f70vswqsmm&dl=0
[2025-06-16 17:27:48.646 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Konwersja URL: https://www.dropbox.com/scl/fi/nhfzdtj69zyh2vb9cu28l/screenshot_20250616_172746_ef6ce461.jpg?rlkey=2ffbn0m2p4z1t92f70vswqsmm&dl=0 -> https://www.dl.dropboxusercontent.com/scl/fi/nhfzdtj69zyh2vb9cu28l/screenshot_20250616_172746_ef6ce461.jpg?rlkey=2ffbn0m2p4z1t92f70vswqsmm
[2025-06-16 17:27:48.647 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ✅ Upload pomyślny w 2.9s: https://www.dl.dropboxusercontent.com/scl/fi/nhfzdtj69zyh2vb9cu28l/screenshot_20250616_172746_ef6ce461.jpg?rlkey=2ffbn0m2p4z1t92f70vswqsmm
[2025-06-16 17:27:48.648 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ✅ Upload pomyślny: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg
[2025-06-16 17:27:48.650 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] 📊 Upload zakończony: 4/4 pomyślnych
[2025-06-16 17:27:48.651 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 📊 Pomyślnie zauploadowano 4/4 obrazów
[2025-06-16 17:27:48.653 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 📸 Dodaję 4 obrazów do oferty
[2025-06-16 17:27:48.655 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 🎬 Próbuję dodać obrazy przez sekcję Media
[2025-06-16 17:27:48.656 +02:00 DBG] ShopBot.Services.G2GPublisherService: [G2G] 📤 Dodaję obraz 1/4: https://www.dl.dropboxusercontent.com/scl/fi/bma5dbugato3q2xlhauzj/screenshot_20250616_172742_716e94d3.jpg?rlkey=wnoyszqlkpwq36fel4e6qxnke
[2025-06-16 17:27:48.668 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🔍 Znaleziono 1 pól tytułu i 1 pól URL
[2025-06-16 17:27:48.680 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Znaleziono puste pola Media
[2025-06-16 17:27:51.278 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Dodano obraz 1: Screenshot 1 -> https://www.dl.dropboxusercontent.com/scl/fi/bma5dbugato3q2xlhauzj/screenshot_20250616_172742_716e94d3.jpg?rlkey=wnoyszqlkpwq36fel4e6qxnke
[2025-06-16 17:27:52.842 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Kliknięto 'Add media' dla następnego obrazu
[2025-06-16 17:27:52.843 +02:00 DBG] ShopBot.Services.G2GPublisherService: [G2G] 📤 Dodaję obraz 2/4: https://www.dl.dropboxusercontent.com/scl/fi/l3w47o1ov9x9toampjzom/screenshot_20250616_172742_3656c579.jpg?rlkey=67oxc4ae5cqfcoonr367t4ab9
[2025-06-16 17:27:52.853 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🔍 Znaleziono 2 pól tytułu i 2 pól URL
[2025-06-16 17:27:52.869 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Znaleziono puste pola Media
[2025-06-16 17:27:55.464 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Dodano obraz 2: Screenshot 2 -> https://www.dl.dropboxusercontent.com/scl/fi/l3w47o1ov9x9toampjzom/screenshot_20250616_172742_3656c579.jpg?rlkey=67oxc4ae5cqfcoonr367t4ab9
[2025-06-16 17:27:57.036 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Kliknięto 'Add media' dla następnego obrazu
[2025-06-16 17:27:57.037 +02:00 DBG] ShopBot.Services.G2GPublisherService: [G2G] 📤 Dodaję obraz 3/4: https://www.dl.dropboxusercontent.com/scl/fi/tn1dxdk5gfbphqthf4nhu/screenshot_20250616_172742_6047d9ce.jpg?rlkey=eiwk7p2ysdynmd56ops1mevdg
[2025-06-16 17:27:57.046 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🔍 Znaleziono 3 pól tytułu i 3 pól URL
[2025-06-16 17:27:57.069 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Znaleziono puste pola Media
[2025-06-16 17:27:59.767 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Dodano obraz 3: Screenshot 3 -> https://www.dl.dropboxusercontent.com/scl/fi/tn1dxdk5gfbphqthf4nhu/screenshot_20250616_172742_6047d9ce.jpg?rlkey=eiwk7p2ysdynmd56ops1mevdg
[2025-06-16 17:28:01.327 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Kliknięto 'Add media' dla następnego obrazu
[2025-06-16 17:28:01.328 +02:00 DBG] ShopBot.Services.G2GPublisherService: [G2G] 📤 Dodaję obraz 4/4: https://www.dl.dropboxusercontent.com/scl/fi/nhfzdtj69zyh2vb9cu28l/screenshot_20250616_172746_ef6ce461.jpg?rlkey=2ffbn0m2p4z1t92f70vswqsmm
[2025-06-16 17:28:01.337 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🔍 Znaleziono 4 pól tytułu i 4 pól URL
[2025-06-16 17:28:01.368 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Znaleziono puste pola Media
[2025-06-16 17:28:04.224 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Dodano obraz 4: Screenshot 4 -> https://www.dl.dropboxusercontent.com/scl/fi/nhfzdtj69zyh2vb9cu28l/screenshot_20250616_172746_ef6ce461.jpg?rlkey=2ffbn0m2p4z1t92f70vswqsmm
[2025-06-16 17:28:04.226 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Pomyślnie dodano 4 obrazów przez sekcję Media
[2025-06-16 17:28:04.227 +02:00 INF] ShopBot.Services.G2GPublisherService: ✅ Zakończono upload obrazów
[2025-06-16 17:28:04.227 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G Publisher] 💰 Parsed USDT price: 51.13 USDT → 51.13 USD
[2025-06-16 17:28:04.229 +02:00 INF] ShopBot.Services.G2GPublisherService: 💰 Cena oryginalna: $51.13 → Cena G2G: $66.47 (gra: RaidShadowLegends)
[2025-06-16 17:28:05.340 +02:00 INF] ShopBot.Services.G2GPublisherService: 🚀 Publikuję ofertę na G2G
[2025-06-16 17:28:05.342 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🔄 Próba publikacji 1/3
[2025-06-16 17:28:05.356 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🖱️ Klikam przycisk Publish
[2025-06-16 17:28:06.589 +02:00 DBG] ShopBot.Services.G2GPublisherService: 📡 Odpowiedź API (Status: 200): {"code": 2000, "messages": [], "payload": {"offer_id": "G1750087687412YD", "seller_id": "7670672", "service_id": "f6a1aba5-473a-4044-836a-8968bbab16d7", "brand_id": "lgc_game_26258", "region_id": "", "relation_id": "59ca631d-a789-4799-a5cc-b3bb50b40dc7", "offer_type": "public", "offer_attributes": [{"collection_id": "lgc_26258_platform", "dataset_id": "lgc_26258_platform_30659"}], "offer_title_collection_tree": ["lgc_26258_platform"], "primary_img_attributes": [], "offer_group": "G1750087687412YD", "title": "mythic comidus + na***s + gleikad + elva + esme and modo", "description": "\u2022 Selling an account with top mythic komidus echidnik + narses + gleykad + elva + chagur + esme and modo + lonataril + legi for entry + many good epics. there is a ratnik for ankyl | At the moment 5kb is closed for 2 keys. 8.5k energy | 1.9k rubies. there is very little left until the arbiter (13 missions). mine 3/3. i will send any screenshots upon request | Reserve your ideal account now!\n\n===============================================================\nGot questions? Contact me on G2G chat before buying! Offers like this go fast \u2013 reserve your ideal account now!\n\n(********)", "api_qty": 0, "low_stock_alert_qty": 0, "available_qty": 1, "min_qty": 1, "actual_qty": 1, "currency": "USD", "unit_price": 66.47, "other_pricing": [], "unit_name": "", "qty_metric": "1", "wholesale_details": [], "other_wholesale_details": [], "is_official": false, "delivery_mode": [], "delivery_method_ids": [], "delivery_speed": "manual", "delivery_speed_details": [{"min": 1, "max": 1, "delivery_time": 540}], "sales_territory_settings": {"settings_type": "global", "countries": []}, "cat_path": "5830014a-b974-45c6-9672-b51e83112fb7", "cat_id": "5830014a-b974-45c6-9672-b51e83112fb7", "ancestor_id": "5830014a-b974-45c6-9672-b51e83112fb7", "status": "live", "created_at": *************, "updated_at": *************, "seller_updated_at": *************, "external_images_mapping": [{"content_type": "", "image_name": "Screenshot 1", "image_url": "https://www.dl.dropboxusercontent.com/scl/fi/bma5dbugato3q2xlhauzj/screenshot_20250616_172742_716e94d3.jpg?rlkey=wnoyszqlkpwq36fel4e6qxnke"}, {"content_type": "", "image_name": "Screenshot 2", "image_url": "https://www.dl.dropboxusercontent.com/scl/fi/l3w47o1ov9x9toampjzom/screenshot_20250616_172742_3656c579.jpg?rlkey=67oxc4ae5cqfcoonr367t4ab9"}, {"content_type": "", "image_name": "Screenshot 3", "image_url": "https://www.dl.dropboxusercontent.com/scl/fi/tn1dxdk5gfbphqthf4nhu/screenshot_20250616_172742_6047d9ce.jpg?rlkey=eiwk7p2ysdynmd56ops1mevdg"}, {"content_type": "", "image_name": "Screenshot 4", "image_url": "https://www.dl.dropboxusercontent.com/scl/fi/nhfzdtj69zyh2vb9cu28l/screenshot_20250616_172746_ef6ce461.jpg?rlkey=2ffbn0m2p4z1t92f70vswqsmm"}]}, "request_id": "c64f0407-0270-4f52-9af2-ff4cf268692e"}
[2025-06-16 17:28:06.594 +02:00 INF] ShopBot.Services.G2GPublisherService: 🎉 Sukces! Otrzymano ID oferty: G1750087687412YD
[2025-06-16 17:28:06.595 +02:00 INF] ShopBot.Services.G2GPublisherService: ✅ Oferta opublikowana pomyślnie (ID: G1750087687412YD)
[2025-06-16 17:28:06.890 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] ✅ Pomyślnie opublikowano ofertę ******** -> G1750087687412YD
[2025-06-16 17:28:06.922 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Oferta ******** została dodana
[2025-06-16 17:31:42.741 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-16 17:31:42.765 +02:00 INF] ShopBot.Services.CliCommandHandler: ➕ Dodaję ofertę FunPay ID: ********, Gra: RaidShadowLegends
[2025-06-16 17:31:44.169 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Mythical champions = 1
[2025-06-16 17:31:44.171 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 35
[2025-06-16 17:31:44.172 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 59
[2025-06-16 17:31:44.174 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-16 17:31:44.175 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-16 17:31:44.177 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-16 17:31:44.180 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 17:31:44.183 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 17:31:44.192 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 17:31:44.194 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 17:31:44.198 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-16 17:31:44.199 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 17:31:44.200 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 17:31:44.201 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 17:31:44.203 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 17:31:44.204 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 17:31:44.205 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-16 17:31:44.206 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-16 17:31:44.207 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-16 17:31:44.208 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-16 17:31:44.209 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-16 17:31:44.212 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-16 17:31:44.213 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 51.14 $</span></span>
[2025-06-16 17:31:44.215 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:31:44.216 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:31:44.217 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:31:44.218 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>51.13 USDT</span></span>
[2025-06-16 17:31:44.220 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 51.13
[2025-06-16 17:31:44.221 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 51.13 USDT
[2025-06-16 17:31:44.225 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 17:31:44.227 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 17:31:44.228 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 17:31:44.229 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 17:31:44.230 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 17:31:44.231 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-16 17:31:44.232 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-16 17:31:44.834 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] 🔄 Rozpoczynam dodawanie oferty ********
[2025-06-16 17:31:45.438 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Mythical champions = 1
[2025-06-16 17:31:45.440 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 35
[2025-06-16 17:31:45.441 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 59
[2025-06-16 17:31:45.443 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-16 17:31:45.444 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-16 17:31:45.445 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-16 17:31:45.447 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 17:31:45.448 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 17:31:45.452 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 17:31:45.453 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 17:31:45.455 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-16 17:31:45.456 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 17:31:45.457 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 17:31:45.459 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 17:31:45.460 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 17:31:45.461 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 17:31:45.462 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-16 17:31:45.463 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-16 17:31:45.463 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-16 17:31:45.464 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-16 17:31:45.465 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-16 17:31:45.467 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-16 17:31:45.468 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 51.14 $</span></span>
[2025-06-16 17:31:45.469 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:31:45.470 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:31:45.472 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:31:45.473 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>51.13 USDT</span></span>
[2025-06-16 17:31:45.474 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 51.13
[2025-06-16 17:31:45.476 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 51.13 USDT
[2025-06-16 17:31:45.478 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 17:31:45.479 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 17:31:45.480 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 17:31:45.481 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 17:31:45.482 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 17:31:45.483 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-16 17:31:45.484 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-16 17:31:47.500 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] 💰 Parsed USDT price: 51.13 USDT → 51.13 USD
[2025-06-16 17:31:49.741 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 🎮 Rozpoczynam przetwarzanie gry: RaidShadowLegends
[2025-06-16 17:32:08.486 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G Publisher] 💰 Parsed USDT price: 51.13 USDT → 51.13 USD
[2025-06-16 17:32:08.493 +02:00 INF] ShopBot.Services.G2GPublisherService: 💰 Cena oryginalna: $51.13 → Cena G2G: $66.47 (gra: RaidShadowLegends)
[2025-06-16 17:32:09.648 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Zakończono przetwarzanie gry: RaidShadowLegends
[2025-06-16 17:32:09.650 +02:00 INF] ShopBot.Services.G2GPublisherService: 🚀 Rozpoczynam wypełnianie tytułu i opisu dla gry: RaidShadowLegends
[2025-06-16 17:32:09.651 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: RaidShadowLegends
[2025-06-16 17:32:09.684 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: RaidShadowLegends
[2025-06-16 17:32:09.686 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Formatting Raid Shadow Legends description for offer ********
[2025-06-16 17:32:09.688 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Split by | into 5 lines
[2025-06-16 17:32:09.690 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Adding ID to description: ********
[2025-06-16 17:32:09.691 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Final formatted description:
• Selling an account with top mythic komidus echidnik + narses + gleykad + elva + chagur + esme and modo + lonataril + legi for entry + many good epics. there is a ratnik for ankyl | At the moment 5kb is closed for 2 keys. 8.5k energy | 1.9k rubies. there is very little left until the arbiter (13 missions). mine 3/3. i will send any screenshots upon request | Reserve your ideal account now!

===============================================================
Got questions? Contact me on G2G chat before buying! Offers like this go fast – reserve your ideal account now!

(********)

[2025-06-16 17:32:09.693 +02:00 DBG] ShopBot.Services.G2GPublisherService: 📝 Wypełniam opis (587 znaków): • Selling an account with top mythic komidus echidnik + narses + gleykad + elva + chagur + esme and ...
[2025-06-16 17:32:09.738 +02:00 INF] ShopBot.Services.G2GPublisherService: ✅ Zakończono wypełnianie tytułu i opisu
[2025-06-16 17:32:09.739 +02:00 INF] ShopBot.Services.G2GPublisherService: 📸 Rozpoczynam upload 4 obrazów
[2025-06-16 17:32:09.741 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 🖼️ Rozpoczynam upload 4 obrazów do Dropbox
[2025-06-16 17:32:09.743 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] Rozpoczynam upload 4 obrazów do Dropbox dla G2G
[2025-06-16 17:32:09.744 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] 🚀 Rozpoczynam PARALLEL upload 4 obrazów (Dropbox: true)
[2025-06-16 17:32:09.746 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 17:32:09.748 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 17:32:09.750 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 17:32:09.772 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 17:32:09.774 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 17:32:09.775 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 17:32:09.776 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 17:32:09.777 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 17:32:09.778 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 17:32:10.522 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 544KB w 0.8s
[2025-06-16 17:32:10.525 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Token wygaśnie wkrótce, odświeżam...
[2025-06-16 17:32:10.527 +02:00 WRN] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Refresh token pusty, próbuję załadować z pliku...
[2025-06-16 17:32:10.530 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Załadowano token z pliku - wygasa: "2025-06-16T19:22:42.6711956Z"
[2025-06-16 17:32:10.531 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Refresh token length: 64
[2025-06-16 17:32:10.532 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Access token length: 1437
[2025-06-16 17:32:10.534 +02:00 INF] ShopBot.Services.DropboxAuthService: [Dropbox Auth] Odświeżanie access token...
[2025-06-16 17:32:10.572 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 594KB w 0.8s
[2025-06-16 17:32:10.599 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 656KB w 0.8s
[2025-06-16 17:32:10.903 +02:00 DBG] ShopBot.Services.DropboxAuthService: [Dropbox Auth] Refresh response: {"access_token": "sl.u.AFz8otTdvrfunUZMuF5Iw_qu0aNEY3_Zc_Q_kXAjXv5auqEv6MjbpMys3Sak1H9sNAGmU0GuDDt-_hGUWwv4fLQuom99J5iHMlmQTQ86Vf_Vk9eitceU7iOSq2llxnI22o5dtM2oI6gesPvxWWvLbXPABNEG5P8xDbqwW2CDYXbHglrsdpw7-32JDawr4pRM1wQ-g0aKu1YWR46tFJVidD___tVKxDBINqSyjTLD8bt9I7dQL4lTGKz8LJAE0kwhe39uBCMe2RUGhv5SlexkC8y-Bpf5PFj3xGjFf38mSFLDCD20tJ6sxJYk6thZG4yhYIOXmVrm9Sxd20PrtBfxaw74xJ7nRK-08QwSXlI0CP-LdvyLYLwPhtsXXUT_9Us8yMuKgaAnNr1KcOehiRLan2wpEBITGdTRkSOjE3izaf52YTNX0U4KRgZYyxU7IPViMtpPyQlZWUzIaIMGVAdq-zqrjgmpSIVTSkC5V2XLpKp1A-9vCRwB9SszyQMa_nsRmqK-GVG8auMueHhy1LN2g6Jtx6U3YPVn99P8h0I3Okim8-pcElz8Kgatinyeq1UITFcQK1H7lefeKCLqj0pDNiqKlvVpmvFI5AT3WhHq6M3r6z0BxmtMmGa0zqsNxsa1rkTNdFS1W4LYw5YQLiJcS2sjzBiC-aDNHgzl8cRa7FoXSBjvxeSVFFmFT5py2Jc0UDxvPOeBBAcXcXCh_QucRbsSypsyKPjGyRm7r6mA7tqsobZRHnJFp58YTDhCfmrBCn_qCgSaTimnFIb4iEIglr0RrSg5gUrhdORK0ZI346SWRDdyv7rSNUH6hn9tOUi-nFLmHfRIZXQolrdQQmsQrwitDTCFy2Yoc4SQYLPnjJgIyoz_vqxkrh8a7lV65jwyopWPJg0UnoZNBfJWKF7U0XhzeqA5g3KSFuiF73Py1hiqb-N_u9oHu6ND7XwADcKUNcefeWJrCRFkVu4GMQ8ehsAFRENJgxM2hPk4zdwZIHHL6N_iHJ8kTO46VTju-rymURoGRevLFAJyEh7Sw0rQUYnzImsrlGwlRXgWTGoDSf7m9ncKI8mxya9TS33UC_A2NwjU3Gu0DmUlffc958GGWT_q804-Gu3Jfq4d3UxaiOgu_gLXaYz6G_ejMqvcemt8t3rypeY8yf4oAy_FlCa_HbPO2i1nouEy3qxFFZHri5KK7w3iM6xxRMfP-HX3jjFTrYqDD57-bh5gV4psMYKgSaRjF2ZYVOwTR3GDuW-JdL8WRvSYGm1T07u1j91u4JU40E3H7W2zL76k_m6VdallhoynGoiRcwWv2Auesg6zWTnxXxpnyf9rd3wW7EFpDwq-SPiQLb1IEBl5AZIpKp43ewnzpRd0MKNzU9-LmiqH3TvedwYyVbqr4onvhGnvom0nSnsfdXpdaO6r5CdNzDqMFKRv", "token_type": "bearer", "expires_in": 14400}
[2025-06-16 17:32:10.905 +02:00 INF] ShopBot.Services.DropboxAuthService: [Dropbox Auth] ✅ Token odświeżony pomyślnie
[2025-06-16 17:32:10.908 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Zapisano token do pliku
[2025-06-16 17:32:10.909 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] ✅ Token został odświeżony. Następne odświeżenie za 3 godzin
[2025-06-16 17:32:11.974 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Upload do /G2G_Screenshots/screenshot_20250616_173210_e5e22e31.jpg pomyślny
[2025-06-16 17:32:12.385 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Upload do /G2G_Screenshots/screenshot_20250616_173210_70421c2b.jpg pomyślny
[2025-06-16 17:32:13.032 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Upload do /G2G_Screenshots/screenshot_20250616_173210_706bc417.jpg pomyślny
[2025-06-16 17:32:13.512 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Otrzymano shared URL: https://www.dropbox.com/scl/fi/8oozs38463v3awypfsckh/screenshot_20250616_173210_e5e22e31.jpg?rlkey=i9ojfy83788wky4v1ba55wpzl&dl=0
[2025-06-16 17:32:13.513 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Konwersja URL: https://www.dropbox.com/scl/fi/8oozs38463v3awypfsckh/screenshot_20250616_173210_e5e22e31.jpg?rlkey=i9ojfy83788wky4v1ba55wpzl&dl=0 -> https://www.dl.dropboxusercontent.com/scl/fi/8oozs38463v3awypfsckh/screenshot_20250616_173210_e5e22e31.jpg?rlkey=i9ojfy83788wky4v1ba55wpzl
[2025-06-16 17:32:13.514 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ✅ Upload pomyślny w 3.7s: https://www.dl.dropboxusercontent.com/scl/fi/8oozs38463v3awypfsckh/screenshot_20250616_173210_e5e22e31.jpg?rlkey=i9ojfy83788wky4v1ba55wpzl
[2025-06-16 17:32:13.516 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ✅ Upload pomyślny: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 17:32:13.517 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg
[2025-06-16 17:32:13.518 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg
[2025-06-16 17:32:13.519 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg
[2025-06-16 17:32:13.819 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 253KB w 0.3s
[2025-06-16 17:32:14.100 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Otrzymano shared URL: https://www.dropbox.com/scl/fi/bajulkahoni5dyjlqnipo/screenshot_20250616_173210_70421c2b.jpg?rlkey=4xlhatahccflly7ayq5r452hv&dl=0
[2025-06-16 17:32:14.101 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Konwersja URL: https://www.dropbox.com/scl/fi/bajulkahoni5dyjlqnipo/screenshot_20250616_173210_70421c2b.jpg?rlkey=4xlhatahccflly7ayq5r452hv&dl=0 -> https://www.dl.dropboxusercontent.com/scl/fi/bajulkahoni5dyjlqnipo/screenshot_20250616_173210_70421c2b.jpg?rlkey=4xlhatahccflly7ayq5r452hv
[2025-06-16 17:32:14.102 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ✅ Upload pomyślny w 4.4s: https://www.dl.dropboxusercontent.com/scl/fi/bajulkahoni5dyjlqnipo/screenshot_20250616_173210_70421c2b.jpg?rlkey=4xlhatahccflly7ayq5r452hv
[2025-06-16 17:32:14.103 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ✅ Upload pomyślny: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 17:32:14.678 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Otrzymano shared URL: https://www.dropbox.com/scl/fi/0j2onkzevmbkppflqfzn3/screenshot_20250616_173210_706bc417.jpg?rlkey=02wbkkf3r3fast3wc0gxrzs26&dl=0
[2025-06-16 17:32:14.680 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Konwersja URL: https://www.dropbox.com/scl/fi/0j2onkzevmbkppflqfzn3/screenshot_20250616_173210_706bc417.jpg?rlkey=02wbkkf3r3fast3wc0gxrzs26&dl=0 -> https://www.dl.dropboxusercontent.com/scl/fi/0j2onkzevmbkppflqfzn3/screenshot_20250616_173210_706bc417.jpg?rlkey=02wbkkf3r3fast3wc0gxrzs26
[2025-06-16 17:32:14.681 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ✅ Upload pomyślny w 4.9s: https://www.dl.dropboxusercontent.com/scl/fi/0j2onkzevmbkppflqfzn3/screenshot_20250616_173210_706bc417.jpg?rlkey=02wbkkf3r3fast3wc0gxrzs26
[2025-06-16 17:32:14.682 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ✅ Upload pomyślny: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 17:32:16.101 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Upload do /G2G_Screenshots/screenshot_20250616_173213_54b9fd20.jpg pomyślny
[2025-06-16 17:32:17.637 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Otrzymano shared URL: https://www.dropbox.com/scl/fi/um6djklu5rgxwvvvrzt3d/screenshot_20250616_173213_54b9fd20.jpg?rlkey=endqy6umzcssw501t5td80is4&dl=0
[2025-06-16 17:32:17.638 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Konwersja URL: https://www.dropbox.com/scl/fi/um6djklu5rgxwvvvrzt3d/screenshot_20250616_173213_54b9fd20.jpg?rlkey=endqy6umzcssw501t5td80is4&dl=0 -> https://www.dl.dropboxusercontent.com/scl/fi/um6djklu5rgxwvvvrzt3d/screenshot_20250616_173213_54b9fd20.jpg?rlkey=endqy6umzcssw501t5td80is4
[2025-06-16 17:32:17.640 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ✅ Upload pomyślny w 4.1s: https://www.dl.dropboxusercontent.com/scl/fi/um6djklu5rgxwvvvrzt3d/screenshot_20250616_173213_54b9fd20.jpg?rlkey=endqy6umzcssw501t5td80is4
[2025-06-16 17:32:17.641 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ✅ Upload pomyślny: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg
[2025-06-16 17:32:17.642 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] 📊 Upload zakończony: 4/4 pomyślnych
[2025-06-16 17:32:17.644 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 📊 Pomyślnie zauploadowano 4/4 obrazów
[2025-06-16 17:32:17.646 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 📸 Dodaję 4 obrazów do oferty
[2025-06-16 17:32:17.648 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 🎬 Próbuję dodać obrazy przez sekcję Media
[2025-06-16 17:32:17.649 +02:00 DBG] ShopBot.Services.G2GPublisherService: [G2G] 📤 Dodaję obraz 1/4: https://www.dl.dropboxusercontent.com/scl/fi/bajulkahoni5dyjlqnipo/screenshot_20250616_173210_70421c2b.jpg?rlkey=4xlhatahccflly7ayq5r452hv
[2025-06-16 17:32:17.661 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🔍 Znaleziono 1 pól tytułu i 1 pól URL
[2025-06-16 17:32:17.673 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Znaleziono puste pola Media
[2025-06-16 17:32:20.421 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Dodano obraz 1: Screenshot 1 -> https://www.dl.dropboxusercontent.com/scl/fi/bajulkahoni5dyjlqnipo/screenshot_20250616_173210_70421c2b.jpg?rlkey=4xlhatahccflly7ayq5r452hv
[2025-06-16 17:32:21.988 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Kliknięto 'Add media' dla następnego obrazu
[2025-06-16 17:32:21.989 +02:00 DBG] ShopBot.Services.G2GPublisherService: [G2G] 📤 Dodaję obraz 2/4: https://www.dl.dropboxusercontent.com/scl/fi/0j2onkzevmbkppflqfzn3/screenshot_20250616_173210_706bc417.jpg?rlkey=02wbkkf3r3fast3wc0gxrzs26
[2025-06-16 17:32:21.998 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🔍 Znaleziono 2 pól tytułu i 2 pól URL
[2025-06-16 17:32:22.015 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Znaleziono puste pola Media
[2025-06-16 17:32:24.643 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Dodano obraz 2: Screenshot 2 -> https://www.dl.dropboxusercontent.com/scl/fi/0j2onkzevmbkppflqfzn3/screenshot_20250616_173210_706bc417.jpg?rlkey=02wbkkf3r3fast3wc0gxrzs26
[2025-06-16 17:32:26.216 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Kliknięto 'Add media' dla następnego obrazu
[2025-06-16 17:32:26.217 +02:00 DBG] ShopBot.Services.G2GPublisherService: [G2G] 📤 Dodaję obraz 3/4: https://www.dl.dropboxusercontent.com/scl/fi/8oozs38463v3awypfsckh/screenshot_20250616_173210_e5e22e31.jpg?rlkey=i9ojfy83788wky4v1ba55wpzl
[2025-06-16 17:32:26.226 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🔍 Znaleziono 3 pól tytułu i 3 pól URL
[2025-06-16 17:32:26.249 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Znaleziono puste pola Media
[2025-06-16 17:32:28.901 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Dodano obraz 3: Screenshot 3 -> https://www.dl.dropboxusercontent.com/scl/fi/8oozs38463v3awypfsckh/screenshot_20250616_173210_e5e22e31.jpg?rlkey=i9ojfy83788wky4v1ba55wpzl
[2025-06-16 17:32:30.476 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Kliknięto 'Add media' dla następnego obrazu
[2025-06-16 17:32:30.477 +02:00 DBG] ShopBot.Services.G2GPublisherService: [G2G] 📤 Dodaję obraz 4/4: https://www.dl.dropboxusercontent.com/scl/fi/um6djklu5rgxwvvvrzt3d/screenshot_20250616_173213_54b9fd20.jpg?rlkey=endqy6umzcssw501t5td80is4
[2025-06-16 17:32:30.487 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🔍 Znaleziono 4 pól tytułu i 4 pól URL
[2025-06-16 17:32:30.520 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Znaleziono puste pola Media
[2025-06-16 17:32:33.377 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Dodano obraz 4: Screenshot 4 -> https://www.dl.dropboxusercontent.com/scl/fi/um6djklu5rgxwvvvrzt3d/screenshot_20250616_173213_54b9fd20.jpg?rlkey=endqy6umzcssw501t5td80is4
[2025-06-16 17:32:33.378 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Pomyślnie dodano 4 obrazów przez sekcję Media
[2025-06-16 17:32:33.380 +02:00 INF] ShopBot.Services.G2GPublisherService: ✅ Zakończono upload obrazów
[2025-06-16 17:32:33.380 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G Publisher] 💰 Parsed USDT price: 51.13 USDT → 51.13 USD
[2025-06-16 17:32:33.382 +02:00 INF] ShopBot.Services.G2GPublisherService: 💰 Cena oryginalna: $51.13 → Cena G2G: $66.47 (gra: RaidShadowLegends)
[2025-06-16 17:32:34.491 +02:00 INF] ShopBot.Services.G2GPublisherService: 🚀 Publikuję ofertę na G2G
[2025-06-16 17:32:34.494 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🔄 Próba publikacji 1/3
[2025-06-16 17:32:34.507 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🖱️ Klikam przycisk Publish
[2025-06-16 17:32:35.363 +02:00 DBG] ShopBot.Services.G2GPublisherService: 📡 Odpowiedź API (Status: 200): {"code": 2000, "messages": [], "payload": {"offer_id": "G1750087956275VU", "seller_id": "7670672", "service_id": "f6a1aba5-473a-4044-836a-8968bbab16d7", "brand_id": "lgc_game_26258", "region_id": "", "relation_id": "59ca631d-a789-4799-a5cc-b3bb50b40dc7", "offer_type": "public", "offer_attributes": [{"collection_id": "lgc_26258_platform", "dataset_id": "lgc_26258_platform_30659"}], "offer_title_collection_tree": ["lgc_26258_platform"], "primary_img_attributes": [], "offer_group": "G1750087956275VU", "title": "mythic comidus + na***s + gleikad + elva + esme and modo", "description": "\u2022 Selling an account with top mythic komidus echidnik + narses + gleykad + elva + chagur + esme and modo + lonataril + legi for entry + many good epics. there is a ratnik for ankyl | At the moment 5kb is closed for 2 keys. 8.5k energy | 1.9k rubies. there is very little left until the arbiter (13 missions). mine 3/3. i will send any screenshots upon request | Reserve your ideal account now!\n\n===============================================================\nGot questions? Contact me on G2G chat before buying! Offers like this go fast \u2013 reserve your ideal account now!\n\n(********)", "api_qty": 0, "low_stock_alert_qty": 0, "available_qty": 1, "min_qty": 1, "actual_qty": 1, "currency": "USD", "unit_price": 66.47, "other_pricing": [], "unit_name": "", "qty_metric": "1", "wholesale_details": [], "other_wholesale_details": [], "is_official": false, "delivery_mode": [], "delivery_method_ids": [], "delivery_speed": "manual", "delivery_speed_details": [{"min": 1, "max": 1, "delivery_time": 540}], "sales_territory_settings": {"settings_type": "global", "countries": []}, "cat_path": "5830014a-b974-45c6-9672-b51e83112fb7", "cat_id": "5830014a-b974-45c6-9672-b51e83112fb7", "ancestor_id": "5830014a-b974-45c6-9672-b51e83112fb7", "status": "live", "created_at": *************, "updated_at": *************, "seller_updated_at": *************, "external_images_mapping": [{"content_type": "", "image_name": "Screenshot 1", "image_url": "https://www.dl.dropboxusercontent.com/scl/fi/bajulkahoni5dyjlqnipo/screenshot_20250616_173210_70421c2b.jpg?rlkey=4xlhatahccflly7ayq5r452hv"}, {"content_type": "", "image_name": "Screenshot 2", "image_url": "https://www.dl.dropboxusercontent.com/scl/fi/0j2onkzevmbkppflqfzn3/screenshot_20250616_173210_706bc417.jpg?rlkey=02wbkkf3r3fast3wc0gxrzs26"}, {"content_type": "", "image_name": "Screenshot 3", "image_url": "https://www.dl.dropboxusercontent.com/scl/fi/8oozs38463v3awypfsckh/screenshot_20250616_173210_e5e22e31.jpg?rlkey=i9ojfy83788wky4v1ba55wpzl"}, {"content_type": "", "image_name": "Screenshot 4", "image_url": "https://www.dl.dropboxusercontent.com/scl/fi/um6djklu5rgxwvvvrzt3d/screenshot_20250616_173213_54b9fd20.jpg?rlkey=endqy6umzcssw501t5td80is4"}]}, "request_id": "4e65851a-a98c-4e31-824f-5e4d041925d2"}
[2025-06-16 17:32:35.367 +02:00 INF] ShopBot.Services.G2GPublisherService: 🎉 Sukces! Otrzymano ID oferty: G1750087956275VU
[2025-06-16 17:32:35.369 +02:00 INF] ShopBot.Services.G2GPublisherService: ✅ Oferta opublikowana pomyślnie (ID: G1750087956275VU)
[2025-06-16 17:32:35.658 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] ✅ Pomyślnie opublikowano ofertę ******** -> G1750087956275VU
[2025-06-16 17:32:35.690 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Oferta ******** została dodana
[2025-06-16 17:36:37.453 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-16 17:36:38.127 +02:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (23ms) [Parameters=[@__funPayOfferId_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT `t`.`Id`, `t`.`ConvertedPrice`, `t`.`CreatedAt`, `t`.`Currency`, `t`.`Description`, `t`.`FailureReason`, `t`.`FunPayOfferId`, `t`.`G2GOfferId`, `t`.`G2GUrl`, `t`.`GameName`, `t`.`ImageCount`, `t`.`ImageUrls`, `t`.`LastAttemptAt`, `t`.`OriginalPrice`, `t`.`ProcessedAt`, `t`.`PublishedAt`, `t`.`SourceUrl`, `t`.`Status`, `t`.`Title`, `t`.`UpdatedAt`, `i`.`Id`, `i`.`CreatedAt`, `i`.`DropboxUrl`, `i`.`ErrorMessage`, `i`.`FileSize`, `i`.`ImgurUrl`, `i`.`OfferId`, `i`.`OriginalUrl`, `i`.`UploadStatus`, `i`.`UploadedAt`
FROM (
    SELECT `o`.`Id`, `o`.`ConvertedPrice`, `o`.`CreatedAt`, `o`.`Currency`, `o`.`Description`, `o`.`FailureReason`, `o`.`FunPayOfferId`, `o`.`G2GOfferId`, `o`.`G2GUrl`, `o`.`GameName`, `o`.`ImageCount`, `o`.`ImageUrls`, `o`.`LastAttemptAt`, `o`.`OriginalPrice`, `o`.`ProcessedAt`, `o`.`PublishedAt`, `o`.`SourceUrl`, `o`.`Status`, `o`.`Title`, `o`.`UpdatedAt`
    FROM `Offers` AS `o`
    WHERE `o`.`FunPayOfferId` = @__funPayOfferId_0
    LIMIT 1
) AS `t`
LEFT JOIN `ImageUploads` AS `i` ON `t`.`Id` = `i`.`OfferId`
ORDER BY `t`.`Id`
[2025-06-16 17:36:38.141 +02:00 ERR] Microsoft.EntityFrameworkCore.Query: An exception occurred while iterating over the results of a query for context type 'ShopBot.Data.ShopBotDbContext'.
MySqlConnector.MySqlException (0x80004005): Unknown column 'o.FailureReason' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
MySqlConnector.MySqlException (0x80004005): Unknown column 'o.FailureReason' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
[2025-06-16 17:36:38.152 +02:00 ERR] ShopBot.Services.CliCommandHandler: [CLI] Database check failed
MySqlConnector.MySqlException (0x80004005): Unknown column 'o.FailureReason' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at ShopBot.Services.OfferRepository.GetByFunPayIdAsync(String funPayOfferId) in C:\Users\<USER>\Desktop\SHOPBOT v7 dzialajaca\Services\OfferRepository.cs:line 31
   at ShopBot.Services.CliCommandHandler.HandleCheckDatabaseCommand(String[] args) in C:\Users\<USER>\Desktop\SHOPBOT v7 dzialajaca\Services\CliCommandHandler.cs:line 427
[2025-06-16 17:37:29.817 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-16 17:37:44.353 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-16 17:44:07.063 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-16 17:44:07.088 +02:00 INF] ShopBot.Services.CliCommandHandler: ➕ Dodaję ofertę FunPay ID: ********, Gra: RaidShadowLegends
[2025-06-16 17:44:08.447 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Mythical champions = 1
[2025-06-16 17:44:08.449 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 35
[2025-06-16 17:44:08.450 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 59
[2025-06-16 17:44:08.451 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-16 17:44:08.452 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-16 17:44:08.453 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-16 17:44:08.457 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 17:44:08.458 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 17:44:08.468 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 17:44:08.469 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 17:44:08.472 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-16 17:44:08.473 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 17:44:08.474 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 17:44:08.475 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 17:44:08.476 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 17:44:08.477 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 17:44:08.479 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-16 17:44:08.479 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-16 17:44:08.480 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-16 17:44:08.481 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-16 17:44:08.483 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-16 17:44:08.485 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-16 17:44:08.486 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 51.14 $</span></span>
[2025-06-16 17:44:08.487 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:44:08.489 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:44:08.490 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:44:08.491 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>51.13 USDT</span></span>
[2025-06-16 17:44:08.493 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 51.13
[2025-06-16 17:44:08.494 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 51.13 USDT
[2025-06-16 17:44:08.498 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 17:44:08.500 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 17:44:08.500 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 17:44:08.501 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 17:44:08.503 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 17:44:08.504 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-16 17:44:08.505 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-16 17:44:09.350 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] 🔄 Rozpoczynam dodawanie oferty ********
[2025-06-16 17:44:09.972 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Mythical champions = 1
[2025-06-16 17:44:09.974 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 35
[2025-06-16 17:44:09.975 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 59
[2025-06-16 17:44:09.976 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-16 17:44:09.977 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-16 17:44:09.979 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-16 17:44:09.980 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 17:44:09.981 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 17:44:09.984 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 17:44:09.986 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 17:44:09.988 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-16 17:44:09.989 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 17:44:09.990 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 17:44:09.991 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 17:44:09.992 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 17:44:09.993 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 17:44:09.994 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-16 17:44:09.995 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-16 17:44:09.996 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-16 17:44:09.997 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-16 17:44:09.998 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-16 17:44:09.999 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-16 17:44:10.000 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 51.14 $</span></span>
[2025-06-16 17:44:10.001 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:44:10.003 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:44:10.004 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 17:44:10.005 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>51.13 USDT</span></span>
[2025-06-16 17:44:10.007 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 51.13
[2025-06-16 17:44:10.008 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 51.13 USDT
[2025-06-16 17:44:10.010 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 17:44:10.010 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 17:44:10.012 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 17:44:10.013 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 17:44:10.013 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 17:44:10.014 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-16 17:44:10.015 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-16 17:44:12.028 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] 💰 Parsed USDT price: 51.13 USDT → 51.13 USD
[2025-06-16 17:44:14.297 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 🎮 Rozpoczynam przetwarzanie gry: RaidShadowLegends
[2025-06-16 17:44:35.149 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G Publisher] 💰 Parsed USDT price: 51.13 USDT → 51.13 USD
[2025-06-16 17:44:35.157 +02:00 INF] ShopBot.Services.G2GPublisherService: 💰 Cena oryginalna: $51.13 → Cena G2G: $66.47 (gra: RaidShadowLegends)
[2025-06-16 17:44:36.320 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Zakończono przetwarzanie gry: RaidShadowLegends
[2025-06-16 17:44:36.321 +02:00 INF] ShopBot.Services.G2GPublisherService: 🚀 Rozpoczynam wypełnianie tytułu i opisu dla gry: RaidShadowLegends
[2025-06-16 17:44:36.323 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: RaidShadowLegends
[2025-06-16 17:44:36.351 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: RaidShadowLegends
[2025-06-16 17:44:36.352 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Formatting Raid Shadow Legends description for offer ********
[2025-06-16 17:44:36.355 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Split by | into 5 lines
[2025-06-16 17:44:36.357 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Adding ID to description: ********
[2025-06-16 17:44:36.357 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Final formatted description:
• Selling an account with top mythic komidus echidnik + narses + gleykad + elva + chagur + esme and modo + lonataril + legi for entry + many good epics. there is a ratnik for ankyl | At the moment 5kb is closed for 2 keys. 8.5k energy | 1.9k rubies. there is very little left until the arbiter (13 missions). mine 3/3. i will send any screenshots upon request | Reserve your ideal account now!

===============================================================
Got questions? Contact me on G2G chat before buying! Offers like this go fast – reserve your ideal account now!

(********)

[2025-06-16 17:44:36.359 +02:00 DBG] ShopBot.Services.G2GPublisherService: 📝 Wypełniam opis (587 znaków): • Selling an account with top mythic komidus echidnik + narses + gleykad + elva + chagur + esme and ...
[2025-06-16 17:44:36.399 +02:00 INF] ShopBot.Services.G2GPublisherService: ✅ Zakończono wypełnianie tytułu i opisu
[2025-06-16 17:44:36.400 +02:00 INF] ShopBot.Services.G2GPublisherService: 📸 Rozpoczynam upload 4 obrazów
[2025-06-16 17:44:36.402 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 🖼️ Rozpoczynam upload 4 obrazów do Dropbox
[2025-06-16 17:44:36.404 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] Rozpoczynam upload 4 obrazów do Dropbox dla G2G
[2025-06-16 17:44:36.405 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] 🚀 Rozpoczynam PARALLEL upload 4 obrazów (Dropbox: true)
[2025-06-16 17:44:36.407 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 17:44:36.409 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 17:44:36.411 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 17:44:36.430 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 17:44:36.432 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 17:44:36.433 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 17:44:36.435 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 17:44:36.435 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 17:44:36.436 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 17:44:37.163 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 544KB w 0.7s
[2025-06-16 17:44:37.166 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Token wygaśnie wkrótce, odświeżam...
[2025-06-16 17:44:37.168 +02:00 WRN] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Refresh token pusty, próbuję załadować z pliku...
[2025-06-16 17:44:37.171 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Załadowano token z pliku - wygasa: "2025-06-16T19:27:10.9065525Z"
[2025-06-16 17:44:37.172 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Refresh token length: 64
[2025-06-16 17:44:37.173 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Access token length: 1437
[2025-06-16 17:44:37.175 +02:00 INF] ShopBot.Services.DropboxAuthService: [Dropbox Auth] Odświeżanie access token...
[2025-06-16 17:44:37.190 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 594KB w 0.8s
[2025-06-16 17:44:37.234 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 656KB w 0.8s
[2025-06-16 17:44:37.581 +02:00 DBG] ShopBot.Services.DropboxAuthService: [Dropbox Auth] Refresh response: {"access_token": "sl.u.AFwND2He9Yfadijj1PqCBTBvhYq89Gjcm7MuLziIsbHQ8CwBN4dtUOg5RJwxqrzp3y2s-d5wzZVx1XAgjng60pTcAslycWIc-dqFGbVAzOsbhiYqeWjOSjkMFE8aRLqiz36npAV9knqD_kWynhJQXOgvBJXFxhBTL3HvKJ68T3OzXbC7p6cOkojZqRJBKfberz_UcHznIcSZgt-MYC_EEIOpFw9bl6PFsdKtyVuxfjbK4MSU57lXhyzvKMt_dGuBTCU0gOT6DixQ-vBgJnvYbsX3I-Loj3gTaP18eyyYo4fA3G2W66FpFnjL9RKXy9o4Ir_Son6Llo8fQ_5nzqyxmsJU923CF6-92BDBd5jdAbi-e3wMNtYzrn-jWfgXiZNpp-jWiAKXw-ZZYkTlw8GhhIqjiBDoLw6as2ZB0R7oItD0YxhMtvhr7MIF64abCivcZ83eTlLpXn1Ps4kZRz7564kR2JQKZ55nC94JdK-kVnj6rx9RpH0hhGGaLSz1Hx-zl19ad2g4IFCnb9-mTTpPOVWoJOm-PlsfztUHqiFZ4x_PzWVXdyZVbpy2Af97fhcm41KN51zMSV1D3ZDwHnbvp2MCf71HnMz_ofRqYhx1obPfOhCK48Yxuk4nqlAaHsgwsIESdwIPPcTud3MziMZFLX9SJnQZ90-3H7muSX5ShnZN5uMVzGA9cisIzPzzglLq-Jyv12GbzLJNSDz50axAU0CS_jbjWjLP5ANbzF6KPjR8JrniHC-SUv3AkTQtY1lvwBbBQ2Lw_tiJs7ax3GYx7wdx6gBBzUCL3GTlnhp6TE_cIioeSizT3KHDW233Aqi60i8IQ1jmYGN9RtexlUnzpXqlOaPxohF5YusMuGjbDpStw6GnzKB1TUHkKSGLcyaMk6o8iSRnL3K6jY9vRijfCp1mCcv4Tzlnex2F4qMT_n75waSzFnq5-GECes_P2-Gc2H0OAUUk_kZE9Phj-txg0bx6ySQNqtMJsNiUJrjqplz1bF2Ey140xBWoCP5puLVgCVSZuNIaKLsnjPTPMIPlmrOlIwF5uui7PRAn5G_bRfRV8njQUOVmHyvwwGw3Ox4ojiA4GWl-cAjT-6A_SUWeTcoLnV6SpUSs7CMTaTHwgmzkIMdwk-2jbwO3Rqab1Tid4vErLo_meBWPjRuhQZX3IqtcE7XLYP_8QGaueCw3jYa8ojpVSNUzqrqPX7av0fMNqu0E8nQ7rRZ2NRruWWxd8AcWyCe8W6Uqky4oke3wy4Ba-HLLYBvaDWFfAfsOxbtVrRh3OZo7PfPPMuI0YAKmnNHIcDkP_gYbWPJS9UoZ4eUKWn-9k6F7aplRnBRDs4ZJ725leoo5DWrQbj8xZN1HmLtH36oqM-mdRNjybMvaKZxM2QuNeZa2KTHzFLBArW_nmsubE0ZSNUddf8y_Jq12BkDX", "token_type": "bearer", "expires_in": 14400}
[2025-06-16 17:44:37.583 +02:00 INF] ShopBot.Services.DropboxAuthService: [Dropbox Auth] ✅ Token odświeżony pomyślnie
[2025-06-16 17:44:37.586 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Zapisano token do pliku
[2025-06-16 17:44:37.587 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] ✅ Token został odświeżony. Następne odświeżenie za 3 godzin
[2025-06-16 17:44:38.563 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Upload do /G2G_Screenshots/screenshot_20250616_174437_95cba136.jpg pomyślny
[2025-06-16 17:44:39.000 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Upload do /G2G_Screenshots/screenshot_20250616_174437_ebc363a9.jpg pomyślny
[2025-06-16 17:44:39.392 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Upload do /G2G_Screenshots/screenshot_20250616_174437_dca6ce92.jpg pomyślny
[2025-06-16 17:44:40.287 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Otrzymano shared URL: https://www.dropbox.com/scl/fi/mcptklczuu1xyn1fa7nr2/screenshot_20250616_174437_95cba136.jpg?rlkey=k8tmh1ku33rn383bwgtx1nf9u&dl=0
[2025-06-16 17:44:40.289 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Konwersja URL: https://www.dropbox.com/scl/fi/mcptklczuu1xyn1fa7nr2/screenshot_20250616_174437_95cba136.jpg?rlkey=k8tmh1ku33rn383bwgtx1nf9u&dl=0 -> https://www.dl.dropboxusercontent.com/scl/fi/mcptklczuu1xyn1fa7nr2/screenshot_20250616_174437_95cba136.jpg?rlkey=k8tmh1ku33rn383bwgtx1nf9u
[2025-06-16 17:44:40.290 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ✅ Upload pomyślny w 3.9s: https://www.dl.dropboxusercontent.com/scl/fi/mcptklczuu1xyn1fa7nr2/screenshot_20250616_174437_95cba136.jpg?rlkey=k8tmh1ku33rn383bwgtx1nf9u
[2025-06-16 17:44:40.291 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ✅ Upload pomyślny: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 17:44:40.292 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg
[2025-06-16 17:44:40.293 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg
[2025-06-16 17:44:40.294 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg
[2025-06-16 17:44:40.582 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 253KB w 0.3s
[2025-06-16 17:44:40.614 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Otrzymano shared URL: https://www.dropbox.com/scl/fi/tmxwlok6kbbr0xqhv92zd/screenshot_20250616_174437_ebc363a9.jpg?rlkey=m1gdu1u3hn9rwk29bgcae6ycb&dl=0
[2025-06-16 17:44:40.615 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Konwersja URL: https://www.dropbox.com/scl/fi/tmxwlok6kbbr0xqhv92zd/screenshot_20250616_174437_ebc363a9.jpg?rlkey=m1gdu1u3hn9rwk29bgcae6ycb&dl=0 -> https://www.dl.dropboxusercontent.com/scl/fi/tmxwlok6kbbr0xqhv92zd/screenshot_20250616_174437_ebc363a9.jpg?rlkey=m1gdu1u3hn9rwk29bgcae6ycb
[2025-06-16 17:44:40.616 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ✅ Upload pomyślny w 4.2s: https://www.dl.dropboxusercontent.com/scl/fi/tmxwlok6kbbr0xqhv92zd/screenshot_20250616_174437_ebc363a9.jpg?rlkey=m1gdu1u3hn9rwk29bgcae6ycb
[2025-06-16 17:44:40.617 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ✅ Upload pomyślny: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 17:44:41.164 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Otrzymano shared URL: https://www.dropbox.com/scl/fi/0og4b4h30i9nq0xzf82al/screenshot_20250616_174437_dca6ce92.jpg?rlkey=7von8chn0gktv4aybrhndmpg5&dl=0
[2025-06-16 17:44:41.165 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Konwersja URL: https://www.dropbox.com/scl/fi/0og4b4h30i9nq0xzf82al/screenshot_20250616_174437_dca6ce92.jpg?rlkey=7von8chn0gktv4aybrhndmpg5&dl=0 -> https://www.dl.dropboxusercontent.com/scl/fi/0og4b4h30i9nq0xzf82al/screenshot_20250616_174437_dca6ce92.jpg?rlkey=7von8chn0gktv4aybrhndmpg5
[2025-06-16 17:44:41.166 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ✅ Upload pomyślny w 4.8s: https://www.dl.dropboxusercontent.com/scl/fi/0og4b4h30i9nq0xzf82al/screenshot_20250616_174437_dca6ce92.jpg?rlkey=7von8chn0gktv4aybrhndmpg5
[2025-06-16 17:44:41.167 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ✅ Upload pomyślny: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 17:44:42.401 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Upload do /G2G_Screenshots/screenshot_20250616_174440_ad88f057.jpg pomyślny
[2025-06-16 17:44:43.906 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Otrzymano shared URL: https://www.dropbox.com/scl/fi/ga48nayz80v2r2m18d0s8/screenshot_20250616_174440_ad88f057.jpg?rlkey=dofnik5dp94dqqyjoxy47vk20&dl=0
[2025-06-16 17:44:43.907 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Konwersja URL: https://www.dropbox.com/scl/fi/ga48nayz80v2r2m18d0s8/screenshot_20250616_174440_ad88f057.jpg?rlkey=dofnik5dp94dqqyjoxy47vk20&dl=0 -> https://www.dl.dropboxusercontent.com/scl/fi/ga48nayz80v2r2m18d0s8/screenshot_20250616_174440_ad88f057.jpg?rlkey=dofnik5dp94dqqyjoxy47vk20
[2025-06-16 17:44:43.908 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ✅ Upload pomyślny w 3.6s: https://www.dl.dropboxusercontent.com/scl/fi/ga48nayz80v2r2m18d0s8/screenshot_20250616_174440_ad88f057.jpg?rlkey=dofnik5dp94dqqyjoxy47vk20
[2025-06-16 17:44:43.910 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ✅ Upload pomyślny: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg
[2025-06-16 17:44:43.911 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] 📊 Upload zakończony: 4/4 pomyślnych
[2025-06-16 17:44:43.912 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 📊 Pomyślnie zauploadowano 4/4 obrazów
[2025-06-16 17:44:43.914 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 📸 Dodaję 4 obrazów do oferty
[2025-06-16 17:44:43.916 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 🎬 Próbuję dodać obrazy przez sekcję Media
[2025-06-16 17:44:43.917 +02:00 DBG] ShopBot.Services.G2GPublisherService: [G2G] 📤 Dodaję obraz 1/4: https://www.dl.dropboxusercontent.com/scl/fi/0og4b4h30i9nq0xzf82al/screenshot_20250616_174437_dca6ce92.jpg?rlkey=7von8chn0gktv4aybrhndmpg5
[2025-06-16 17:44:43.929 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🔍 Znaleziono 1 pól tytułu i 1 pól URL
[2025-06-16 17:44:43.941 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Znaleziono puste pola Media
[2025-06-16 17:44:46.588 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Dodano obraz 1: Screenshot 1 -> https://www.dl.dropboxusercontent.com/scl/fi/0og4b4h30i9nq0xzf82al/screenshot_20250616_174437_dca6ce92.jpg?rlkey=7von8chn0gktv4aybrhndmpg5
[2025-06-16 17:44:48.159 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Kliknięto 'Add media' dla następnego obrazu
[2025-06-16 17:44:48.160 +02:00 DBG] ShopBot.Services.G2GPublisherService: [G2G] 📤 Dodaję obraz 2/4: https://www.dl.dropboxusercontent.com/scl/fi/tmxwlok6kbbr0xqhv92zd/screenshot_20250616_174437_ebc363a9.jpg?rlkey=m1gdu1u3hn9rwk29bgcae6ycb
[2025-06-16 17:44:48.169 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🔍 Znaleziono 2 pól tytułu i 2 pól URL
[2025-06-16 17:44:48.186 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Znaleziono puste pola Media
[2025-06-16 17:44:50.887 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Dodano obraz 2: Screenshot 2 -> https://www.dl.dropboxusercontent.com/scl/fi/tmxwlok6kbbr0xqhv92zd/screenshot_20250616_174437_ebc363a9.jpg?rlkey=m1gdu1u3hn9rwk29bgcae6ycb
[2025-06-16 17:44:52.446 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Kliknięto 'Add media' dla następnego obrazu
[2025-06-16 17:44:52.447 +02:00 DBG] ShopBot.Services.G2GPublisherService: [G2G] 📤 Dodaję obraz 3/4: https://www.dl.dropboxusercontent.com/scl/fi/mcptklczuu1xyn1fa7nr2/screenshot_20250616_174437_95cba136.jpg?rlkey=k8tmh1ku33rn383bwgtx1nf9u
[2025-06-16 17:44:52.456 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🔍 Znaleziono 3 pól tytułu i 3 pól URL
[2025-06-16 17:44:52.479 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Znaleziono puste pola Media
[2025-06-16 17:44:55.286 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Dodano obraz 3: Screenshot 3 -> https://www.dl.dropboxusercontent.com/scl/fi/mcptklczuu1xyn1fa7nr2/screenshot_20250616_174437_95cba136.jpg?rlkey=k8tmh1ku33rn383bwgtx1nf9u
[2025-06-16 17:44:56.857 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Kliknięto 'Add media' dla następnego obrazu
[2025-06-16 17:44:56.858 +02:00 DBG] ShopBot.Services.G2GPublisherService: [G2G] 📤 Dodaję obraz 4/4: https://www.dl.dropboxusercontent.com/scl/fi/ga48nayz80v2r2m18d0s8/screenshot_20250616_174440_ad88f057.jpg?rlkey=dofnik5dp94dqqyjoxy47vk20
[2025-06-16 17:44:56.868 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🔍 Znaleziono 4 pól tytułu i 4 pól URL
[2025-06-16 17:44:56.913 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Znaleziono puste pola Media
[2025-06-16 17:44:59.807 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Dodano obraz 4: Screenshot 4 -> https://www.dl.dropboxusercontent.com/scl/fi/ga48nayz80v2r2m18d0s8/screenshot_20250616_174440_ad88f057.jpg?rlkey=dofnik5dp94dqqyjoxy47vk20
[2025-06-16 17:44:59.809 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Pomyślnie dodano 4 obrazów przez sekcję Media
[2025-06-16 17:44:59.810 +02:00 INF] ShopBot.Services.G2GPublisherService: ✅ Zakończono upload obrazów
[2025-06-16 17:44:59.810 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G Publisher] 💰 Parsed USDT price: 51.13 USDT → 51.13 USD
[2025-06-16 17:44:59.812 +02:00 INF] ShopBot.Services.G2GPublisherService: 💰 Cena oryginalna: $51.13 → Cena G2G: $66.47 (gra: RaidShadowLegends)
[2025-06-16 17:45:00.932 +02:00 INF] ShopBot.Services.G2GPublisherService: 🚀 Publikuję ofertę na G2G
[2025-06-16 17:45:00.935 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🔄 Próba publikacji 1/3
[2025-06-16 17:45:00.948 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🖱️ Klikam przycisk Publish
[2025-06-16 17:45:02.132 +02:00 DBG] ShopBot.Services.G2GPublisherService: 📡 Odpowiedź API (Status: 200): {"code": 2000, "messages": [], "payload": {"offer_id": "G1750088703052NI", "seller_id": "7670672", "service_id": "f6a1aba5-473a-4044-836a-8968bbab16d7", "brand_id": "lgc_game_26258", "region_id": "", "relation_id": "59ca631d-a789-4799-a5cc-b3bb50b40dc7", "offer_type": "public", "offer_attributes": [{"collection_id": "lgc_26258_platform", "dataset_id": "lgc_26258_platform_30659"}], "offer_title_collection_tree": ["lgc_26258_platform"], "primary_img_attributes": [], "offer_group": "G1750088703052NI", "title": "mythic comidus + na***s + gleikad + elva + esme and modo", "description": "\u2022 Selling an account with top mythic komidus echidnik + narses + gleykad + elva + chagur + esme and modo + lonataril + legi for entry + many good epics. there is a ratnik for ankyl | At the moment 5kb is closed for 2 keys. 8.5k energy | 1.9k rubies. there is very little left until the arbiter (13 missions). mine 3/3. i will send any screenshots upon request | Reserve your ideal account now!\n\n===============================================================\nGot questions? Contact me on G2G chat before buying! Offers like this go fast \u2013 reserve your ideal account now!\n\n(********)", "api_qty": 0, "low_stock_alert_qty": 0, "available_qty": 1, "min_qty": 1, "actual_qty": 1, "currency": "USD", "unit_price": 66.47, "other_pricing": [], "unit_name": "", "qty_metric": "1", "wholesale_details": [], "other_wholesale_details": [], "is_official": false, "delivery_mode": [], "delivery_method_ids": [], "delivery_speed": "manual", "delivery_speed_details": [{"min": 1, "max": 1, "delivery_time": 540}], "sales_territory_settings": {"settings_type": "global", "countries": []}, "cat_path": "5830014a-b974-45c6-9672-b51e83112fb7", "cat_id": "5830014a-b974-45c6-9672-b51e83112fb7", "ancestor_id": "5830014a-b974-45c6-9672-b51e83112fb7", "status": "live", "created_at": *************, "updated_at": *************, "seller_updated_at": *************, "external_images_mapping": [{"content_type": "", "image_name": "Screenshot 1", "image_url": "https://www.dl.dropboxusercontent.com/scl/fi/0og4b4h30i9nq0xzf82al/screenshot_20250616_174437_dca6ce92.jpg?rlkey=7von8chn0gktv4aybrhndmpg5"}, {"content_type": "", "image_name": "Screenshot 2", "image_url": "https://www.dl.dropboxusercontent.com/scl/fi/tmxwlok6kbbr0xqhv92zd/screenshot_20250616_174437_ebc363a9.jpg?rlkey=m1gdu1u3hn9rwk29bgcae6ycb"}, {"content_type": "", "image_name": "Screenshot 3", "image_url": "https://www.dl.dropboxusercontent.com/scl/fi/mcptklczuu1xyn1fa7nr2/screenshot_20250616_174437_95cba136.jpg?rlkey=k8tmh1ku33rn383bwgtx1nf9u"}, {"content_type": "", "image_name": "Screenshot 4", "image_url": "https://www.dl.dropboxusercontent.com/scl/fi/ga48nayz80v2r2m18d0s8/screenshot_20250616_174440_ad88f057.jpg?rlkey=dofnik5dp94dqqyjoxy47vk20"}]}, "request_id": "d5d88c25-6c5b-403c-b391-23b6610fb3c8"}
[2025-06-16 17:45:02.137 +02:00 INF] ShopBot.Services.G2GPublisherService: 🎉 Sukces! Otrzymano ID oferty: G1750088703052NI
[2025-06-16 17:45:02.138 +02:00 INF] ShopBot.Services.G2GPublisherService: ✅ Oferta opublikowana pomyślnie (ID: G1750088703052NI)
[2025-06-16 17:45:02.652 +02:00 DBG] ShopBot.Services.OfferRepository: [OfferRepo] Creating offer: ******** for game: RaidShadowLegends
[2025-06-16 17:45:02.758 +02:00 INF] ShopBot.Services.OfferRepository: [OfferRepo] ✅ Created offer: "539194fd-6d08-4035-a7ba-64701dbe7d10"
[2025-06-16 17:45:02.759 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] ✅ Zapisano ofertę ******** do bazy danych (G2G: G1750088703052NI)
[2025-06-16 17:45:03.041 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] ✅ Pomyślnie opublikowano ofertę ******** -> G1750088703052NI
[2025-06-16 17:45:03.082 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Oferta ******** została dodana
[2025-06-16 17:45:30.763 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-16 18:17:03.866 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-16 18:17:03.896 +02:00 INF] ShopBot.Services.CliCommandHandler: ➕ Dodaję ofertę FunPay ID: ********, Gra: RaidShadowLegends
[2025-06-16 18:17:06.371 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Mythical champions = 1
[2025-06-16 18:17:06.372 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 35
[2025-06-16 18:17:06.373 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 59
[2025-06-16 18:17:06.375 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-16 18:17:06.376 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-16 18:17:06.377 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-16 18:17:06.381 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 18:17:06.383 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 18:17:06.393 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 18:17:06.394 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 18:17:06.398 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-16 18:17:06.399 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 18:17:06.400 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 18:17:06.401 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 18:17:06.402 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 18:17:06.403 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 18:17:06.404 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-16 18:17:06.405 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-16 18:17:06.406 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-16 18:17:06.407 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-16 18:17:06.408 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-16 18:17:06.410 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-16 18:17:06.412 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 51.13 $</span></span>
[2025-06-16 18:17:06.414 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 18:17:06.415 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 18:17:06.416 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 18:17:06.418 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>51.13 USDT</span></span>
[2025-06-16 18:17:06.419 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 51.13
[2025-06-16 18:17:06.420 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 51.13 USDT
[2025-06-16 18:17:06.425 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 18:17:06.426 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 18:17:06.427 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 18:17:06.428 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 18:17:06.430 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 18:17:06.431 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-16 18:17:06.432 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-16 18:17:07.152 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] 🔄 Rozpoczynam dodawanie oferty ********
[2025-06-16 18:17:07.882 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Mythical champions = 1
[2025-06-16 18:17:07.884 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 35
[2025-06-16 18:17:07.885 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 59
[2025-06-16 18:17:07.887 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-16 18:17:07.888 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-16 18:17:07.889 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-16 18:17:07.891 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 18:17:07.892 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 18:17:07.896 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 18:17:07.897 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 18:17:07.900 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-16 18:17:07.901 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 18:17:07.902 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 18:17:07.903 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 18:17:07.904 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 18:17:07.905 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 18:17:07.906 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-16 18:17:07.907 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-16 18:17:07.908 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-16 18:17:07.909 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-16 18:17:07.910 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-16 18:17:07.912 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-16 18:17:07.913 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 51.13 $</span></span>
[2025-06-16 18:17:07.914 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 18:17:07.915 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 18:17:07.917 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 18:17:07.918 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>51.13 USDT</span></span>
[2025-06-16 18:17:07.920 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 51.13
[2025-06-16 18:17:07.921 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 51.13 USDT
[2025-06-16 18:17:07.923 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 18:17:07.925 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 18:17:07.926 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 18:17:07.927 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 18:17:07.928 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 18:17:07.929 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-16 18:17:07.930 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-16 18:17:09.942 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] 💰 Parsed USDT price: 51.13 USDT → 51.13 USD
[2025-06-16 18:17:11.715 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 🎮 Rozpoczynam przetwarzanie gry: RaidShadowLegends
[2025-06-16 18:17:30.958 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G Publisher] 💰 Parsed USDT price: 51.13 USDT → 51.13 USD
[2025-06-16 18:17:30.966 +02:00 INF] ShopBot.Services.G2GPublisherService: 💰 Cena oryginalna: $51.13 → Cena G2G: $66.47 (gra: RaidShadowLegends)
[2025-06-16 18:17:32.111 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Zakończono przetwarzanie gry: RaidShadowLegends
[2025-06-16 18:17:32.112 +02:00 INF] ShopBot.Services.G2GPublisherService: 🚀 Rozpoczynam wypełnianie tytułu i opisu dla gry: RaidShadowLegends
[2025-06-16 18:17:32.114 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: RaidShadowLegends
[2025-06-16 18:17:32.146 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: RaidShadowLegends
[2025-06-16 18:17:32.148 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Formatting Raid Shadow Legends description for offer ********
[2025-06-16 18:17:32.150 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Split by | into 5 lines
[2025-06-16 18:17:32.152 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Adding ID to description: ********
[2025-06-16 18:17:32.153 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Final formatted description:
• Selling an account with top mythic komidus echidnik + narses + gleykad + elva + chagur + esme and modo + lonataril + legi for entry + many good epics. there is a ratnik for ankyl | At the moment 5kb is closed for 2 keys. 8.5k energy | 1.9k rubies. there is very little left until the arbiter (13 missions). mine 3/3. i will send any screenshots upon request | Reserve your ideal account now!

===============================================================
Got questions? Contact me on G2G chat before buying! Offers like this go fast – reserve your ideal account now!

(********)

[2025-06-16 18:17:32.154 +02:00 DBG] ShopBot.Services.G2GPublisherService: 📝 Wypełniam opis (587 znaków): • Selling an account with top mythic komidus echidnik + narses + gleykad + elva + chagur + esme and ...
[2025-06-16 18:17:32.199 +02:00 INF] ShopBot.Services.G2GPublisherService: ✅ Zakończono wypełnianie tytułu i opisu
[2025-06-16 18:17:32.200 +02:00 INF] ShopBot.Services.G2GPublisherService: 📸 Rozpoczynam upload 4 obrazów
[2025-06-16 18:17:32.202 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 🖼️ Rozpoczynam upload 4 obrazów do Dropbox
[2025-06-16 18:17:32.203 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] Rozpoczynam upload 4 obrazów do Dropbox dla G2G
[2025-06-16 18:17:32.205 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] 🚀 Rozpoczynam PARALLEL upload 4 obrazów (Dropbox: true)
[2025-06-16 18:17:32.207 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 18:17:32.209 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 18:17:32.211 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 18:17:32.229 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 18:17:32.230 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 18:17:32.231 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 18:17:32.232 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 18:17:32.233 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 18:17:32.234 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 18:17:32.970 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 544KB w 0.8s
[2025-06-16 18:17:32.973 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Token wygaśnie wkrótce, odświeżam...
[2025-06-16 18:17:32.975 +02:00 WRN] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Refresh token pusty, próbuję załadować z pliku...
[2025-06-16 18:17:32.978 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Załadowano token z pliku - wygasa: "2025-06-16T19:39:37.5840658Z"
[2025-06-16 18:17:32.979 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Refresh token length: 64
[2025-06-16 18:17:32.980 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Access token length: 1437
[2025-06-16 18:17:32.982 +02:00 INF] ShopBot.Services.DropboxAuthService: [Dropbox Auth] Odświeżanie access token...
[2025-06-16 18:17:33.017 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 594KB w 0.8s
[2025-06-16 18:17:33.155 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 656KB w 0.9s
[2025-06-16 18:17:33.697 +02:00 DBG] ShopBot.Services.DropboxAuthService: [Dropbox Auth] Refresh response: {"access_token": "sl.u.AFwT-D62PAlKNbaQem9KiErPeqDLQ54VCDf_x-OaLmrvoy7gMjVSiof5rc4QgcTlOPKi-xfNb1rvbhAjWtu8SdkVAJY4MSp7zEDr5m1Dv78aZ0nhGGvVhWAkLPoer_jcm9YKUOYqGRAWmz8nMOvlryO0mgdwcy3blIkqk7-Ds60t_MZLtzVADXJdJtHzn8i1mORF2HjdOBhdJXMT9stEVBN-CEecRsEyg7LtUuwKesOCjZOpYdYrtwAigvy4L9EwoJVnCAPonh7UkNSz_Myx-4_v12ZalXQpf7MZbxzn8I45Q4fxvQmPeGIW9iTkZ_icnsj1pRBOBDTxTPuDtQy-lA0dNXcsPweeVHKJMde2aXccjic4sq1fLHS9lxsR_JQRiwEWmi947-7IHxkyKdfbBH4oJyjk6VqxoK7ZtEfL951Tg6UbjUguPAz_kvRrT3SoZn08FcfhtxWARn2WQG9Nh7X5F3jmgs947HRSD_tuRRdUEsZRMxsKjb54kra3e5bmNqFZ1tRTP5tRqVTcdWqavNCB2NRaouQl0U35KpgMdB7357qLL_IHjYiTUGgaNlT9TqXyQvpC5PsI2bP-Kuu8b0c6xz1ix83H4Tgu8e8hxZsT5JJsWNnOFWBavlrjxIWL5yYDYzbs8ITAI0SieEDjr3-0hF1NzZqsb6-K8GMI2MxSR4YYBEbk3tjf6JDsgLKEQa05yA5TM4GCxcEZ2XnlZJoyUOgsXBdeQaDHO7HHAOecg2iotjHnCc7cBeLxzUcdlgCuSUI9oU-WzXBArG-jEmXoW72d6PRUR1mgHp6EoGV0kvSK67h5o2gAbA7s3cRytvP0zBSIc0cSG-i9gdE3l3MCH-cOz6TtRDJVE45yqKGPH_QVAwQmhGGjFiWUtIZFtKpS5KOkSYeCdhESmKBpiKa9204o1kdlHkAKhItC_YTAe6OZYsSGprDgvxd1L0KCdQ76wfmJFrjHORphY9MTh8MvYapBq2XZGo51BVGGhbv30Ojj-bdx83GalRj3yAXp4aL99nQSDyijqDnEotflpMebfLlezvMDNlX_fj4iO9O01ApCibxdR0_3Cju4HoxHNe-m3AmbqzG0tCoUJnC4YWlsQI4gvvGp1jSRQE6FrsJuAFCvmORVvjaKTmQ5sEY8MMHG2zKkos9GbwaiV79tIWvCOJj_o01L8JXexxK2GGrkmumuaycjL4eSntv-bZnhnUdjQb5vjb00LCKWue2cOS99x_X9H_EVRgUQMmtgghDyfPEztEM4VIqbguWD8rl535zt8b0331rvBZp-iPiOSIi1Da99CyxvHSttOLbWuz53YuskrTq1e5g9DQ63DcI-I4eP8PDxS115ML7f0AaGVHyaHfSiaXnyoFqDfilrk4tYCaEM2YfqDA2-DTyKGqoxYAWJgIK0WBEjhhWzPLZYgegW", "token_type": "bearer", "expires_in": 14400}
[2025-06-16 18:17:33.699 +02:00 INF] ShopBot.Services.DropboxAuthService: [Dropbox Auth] ✅ Token odświeżony pomyślnie
[2025-06-16 18:17:33.702 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Zapisano token do pliku
[2025-06-16 18:17:33.703 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] ✅ Token został odświeżony. Następne odświeżenie za 3 godzin
[2025-06-16 18:17:36.634 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Upload do /G2G_Screenshots/screenshot_20250616_181733_ed5a32ec.jpg pomyślny
[2025-06-16 18:17:37.039 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Upload do /G2G_Screenshots/screenshot_20250616_181732_5ec63ec4.jpg pomyślny
[2025-06-16 18:17:37.659 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Upload do /G2G_Screenshots/screenshot_20250616_181733_22c2eb60.jpg pomyślny
[2025-06-16 18:17:38.235 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Otrzymano shared URL: https://www.dropbox.com/scl/fi/05t00fe36p5w67s49q3ta/screenshot_20250616_181733_ed5a32ec.jpg?rlkey=lstppppb8sov3v6ylui7tuqca&dl=0
[2025-06-16 18:17:38.236 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Konwersja URL: https://www.dropbox.com/scl/fi/05t00fe36p5w67s49q3ta/screenshot_20250616_181733_ed5a32ec.jpg?rlkey=lstppppb8sov3v6ylui7tuqca&dl=0 -> https://www.dl.dropboxusercontent.com/scl/fi/05t00fe36p5w67s49q3ta/screenshot_20250616_181733_ed5a32ec.jpg?rlkey=lstppppb8sov3v6ylui7tuqca
[2025-06-16 18:17:38.238 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ✅ Upload pomyślny w 6.0s: https://www.dl.dropboxusercontent.com/scl/fi/05t00fe36p5w67s49q3ta/screenshot_20250616_181733_ed5a32ec.jpg?rlkey=lstppppb8sov3v6ylui7tuqca
[2025-06-16 18:17:38.239 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ✅ Upload pomyślny: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 18:17:38.240 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg
[2025-06-16 18:17:38.241 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg
[2025-06-16 18:17:38.242 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg
[2025-06-16 18:17:38.598 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 253KB w 0.4s
[2025-06-16 18:17:38.720 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Otrzymano shared URL: https://www.dropbox.com/scl/fi/iumfnef57g2ufedxr8jka/screenshot_20250616_181732_5ec63ec4.jpg?rlkey=0u4as041io754bvt3tsx10sk6&dl=0
[2025-06-16 18:17:38.721 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Konwersja URL: https://www.dropbox.com/scl/fi/iumfnef57g2ufedxr8jka/screenshot_20250616_181732_5ec63ec4.jpg?rlkey=0u4as041io754bvt3tsx10sk6&dl=0 -> https://www.dl.dropboxusercontent.com/scl/fi/iumfnef57g2ufedxr8jka/screenshot_20250616_181732_5ec63ec4.jpg?rlkey=0u4as041io754bvt3tsx10sk6
[2025-06-16 18:17:38.722 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ✅ Upload pomyślny w 6.5s: https://www.dl.dropboxusercontent.com/scl/fi/iumfnef57g2ufedxr8jka/screenshot_20250616_181732_5ec63ec4.jpg?rlkey=0u4as041io754bvt3tsx10sk6
[2025-06-16 18:17:38.723 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ✅ Upload pomyślny: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 18:17:39.187 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Otrzymano shared URL: https://www.dropbox.com/scl/fi/53pl83vc8wpsogdqitggz/screenshot_20250616_181733_22c2eb60.jpg?rlkey=mbqfcxxg45e5m1ltyhldd53z7&dl=0
[2025-06-16 18:17:39.188 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Konwersja URL: https://www.dropbox.com/scl/fi/53pl83vc8wpsogdqitggz/screenshot_20250616_181733_22c2eb60.jpg?rlkey=mbqfcxxg45e5m1ltyhldd53z7&dl=0 -> https://www.dl.dropboxusercontent.com/scl/fi/53pl83vc8wpsogdqitggz/screenshot_20250616_181733_22c2eb60.jpg?rlkey=mbqfcxxg45e5m1ltyhldd53z7
[2025-06-16 18:17:39.190 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ✅ Upload pomyślny w 7.0s: https://www.dl.dropboxusercontent.com/scl/fi/53pl83vc8wpsogdqitggz/screenshot_20250616_181733_22c2eb60.jpg?rlkey=mbqfcxxg45e5m1ltyhldd53z7
[2025-06-16 18:17:39.191 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ✅ Upload pomyślny: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 18:17:39.531 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Upload do /G2G_Screenshots/screenshot_20250616_181738_7ca20ad4.jpg pomyślny
[2025-06-16 18:17:41.029 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Otrzymano shared URL: https://www.dropbox.com/scl/fi/a6lpva6k3adn95c60c5i9/screenshot_20250616_181738_7ca20ad4.jpg?rlkey=7n314x256i4xsicmkihc037nb&dl=0
[2025-06-16 18:17:41.030 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Konwersja URL: https://www.dropbox.com/scl/fi/a6lpva6k3adn95c60c5i9/screenshot_20250616_181738_7ca20ad4.jpg?rlkey=7n314x256i4xsicmkihc037nb&dl=0 -> https://www.dl.dropboxusercontent.com/scl/fi/a6lpva6k3adn95c60c5i9/screenshot_20250616_181738_7ca20ad4.jpg?rlkey=7n314x256i4xsicmkihc037nb
[2025-06-16 18:17:41.031 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ✅ Upload pomyślny w 2.8s: https://www.dl.dropboxusercontent.com/scl/fi/a6lpva6k3adn95c60c5i9/screenshot_20250616_181738_7ca20ad4.jpg?rlkey=7n314x256i4xsicmkihc037nb
[2025-06-16 18:17:41.032 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ✅ Upload pomyślny: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg
[2025-06-16 18:17:41.033 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] 📊 Upload zakończony: 4/4 pomyślnych
[2025-06-16 18:17:41.035 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 📊 Pomyślnie zauploadowano 4/4 obrazów
[2025-06-16 18:17:41.036 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 📸 Dodaję 4 obrazów do oferty
[2025-06-16 18:17:41.039 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 🎬 Próbuję dodać obrazy przez sekcję Media
[2025-06-16 18:17:41.040 +02:00 DBG] ShopBot.Services.G2GPublisherService: [G2G] 📤 Dodaję obraz 1/4: https://www.dl.dropboxusercontent.com/scl/fi/iumfnef57g2ufedxr8jka/screenshot_20250616_181732_5ec63ec4.jpg?rlkey=0u4as041io754bvt3tsx10sk6
[2025-06-16 18:17:41.052 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🔍 Znaleziono 1 pól tytułu i 1 pól URL
[2025-06-16 18:17:41.064 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Znaleziono puste pola Media
[2025-06-16 18:17:43.702 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Dodano obraz 1: Screenshot 1 -> https://www.dl.dropboxusercontent.com/scl/fi/iumfnef57g2ufedxr8jka/screenshot_20250616_181732_5ec63ec4.jpg?rlkey=0u4as041io754bvt3tsx10sk6
[2025-06-16 18:17:45.265 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Kliknięto 'Add media' dla następnego obrazu
[2025-06-16 18:17:45.266 +02:00 DBG] ShopBot.Services.G2GPublisherService: [G2G] 📤 Dodaję obraz 2/4: https://www.dl.dropboxusercontent.com/scl/fi/05t00fe36p5w67s49q3ta/screenshot_20250616_181733_ed5a32ec.jpg?rlkey=lstppppb8sov3v6ylui7tuqca
[2025-06-16 18:17:45.275 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🔍 Znaleziono 2 pól tytułu i 2 pól URL
[2025-06-16 18:17:45.292 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Znaleziono puste pola Media
[2025-06-16 18:17:47.918 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Dodano obraz 2: Screenshot 2 -> https://www.dl.dropboxusercontent.com/scl/fi/05t00fe36p5w67s49q3ta/screenshot_20250616_181733_ed5a32ec.jpg?rlkey=lstppppb8sov3v6ylui7tuqca
[2025-06-16 18:17:49.488 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Kliknięto 'Add media' dla następnego obrazu
[2025-06-16 18:17:49.489 +02:00 DBG] ShopBot.Services.G2GPublisherService: [G2G] 📤 Dodaję obraz 3/4: https://www.dl.dropboxusercontent.com/scl/fi/53pl83vc8wpsogdqitggz/screenshot_20250616_181733_22c2eb60.jpg?rlkey=mbqfcxxg45e5m1ltyhldd53z7
[2025-06-16 18:17:49.499 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🔍 Znaleziono 3 pól tytułu i 3 pól URL
[2025-06-16 18:17:49.524 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Znaleziono puste pola Media
[2025-06-16 18:17:52.225 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Dodano obraz 3: Screenshot 3 -> https://www.dl.dropboxusercontent.com/scl/fi/53pl83vc8wpsogdqitggz/screenshot_20250616_181733_22c2eb60.jpg?rlkey=mbqfcxxg45e5m1ltyhldd53z7
[2025-06-16 18:17:53.793 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Kliknięto 'Add media' dla następnego obrazu
[2025-06-16 18:17:53.794 +02:00 DBG] ShopBot.Services.G2GPublisherService: [G2G] 📤 Dodaję obraz 4/4: https://www.dl.dropboxusercontent.com/scl/fi/a6lpva6k3adn95c60c5i9/screenshot_20250616_181738_7ca20ad4.jpg?rlkey=7n314x256i4xsicmkihc037nb
[2025-06-16 18:17:53.804 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🔍 Znaleziono 4 pól tytułu i 4 pól URL
[2025-06-16 18:17:53.833 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Znaleziono puste pola Media
[2025-06-16 18:17:56.674 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Dodano obraz 4: Screenshot 4 -> https://www.dl.dropboxusercontent.com/scl/fi/a6lpva6k3adn95c60c5i9/screenshot_20250616_181738_7ca20ad4.jpg?rlkey=7n314x256i4xsicmkihc037nb
[2025-06-16 18:17:56.675 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Pomyślnie dodano 4 obrazów przez sekcję Media
[2025-06-16 18:17:56.676 +02:00 INF] ShopBot.Services.G2GPublisherService: ✅ Zakończono upload obrazów
[2025-06-16 18:17:56.677 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G Publisher] 💰 Parsed USDT price: 51.13 USDT → 51.13 USD
[2025-06-16 18:17:56.679 +02:00 INF] ShopBot.Services.G2GPublisherService: 💰 Cena oryginalna: $51.13 → Cena G2G: $66.47 (gra: RaidShadowLegends)
[2025-06-16 18:17:57.774 +02:00 INF] ShopBot.Services.G2GPublisherService: 🚀 Publikuję ofertę na G2G
[2025-06-16 18:17:57.777 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🔄 Próba publikacji 1/3
[2025-06-16 18:17:57.790 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🖱️ Klikam przycisk Publish
[2025-06-16 18:17:58.715 +02:00 DBG] ShopBot.Services.G2GPublisherService: 📡 Odpowiedź API (Status: 200): {"code": 2000, "messages": [], "payload": {"offer_id": "G1750090679691XP", "seller_id": "7670672", "service_id": "f6a1aba5-473a-4044-836a-8968bbab16d7", "brand_id": "lgc_game_26258", "region_id": "", "relation_id": "59ca631d-a789-4799-a5cc-b3bb50b40dc7", "offer_type": "public", "offer_attributes": [{"collection_id": "lgc_26258_platform", "dataset_id": "lgc_26258_platform_30659"}], "offer_title_collection_tree": ["lgc_26258_platform"], "primary_img_attributes": [], "offer_group": "G1750090679691XP", "title": "mythic comidus + na***s + gleikad + elva + esme and modo", "description": "\u2022 Selling an account with top mythic komidus echidnik + narses + gleykad + elva + chagur + esme and modo + lonataril + legi for entry + many good epics. there is a ratnik for ankyl | At the moment 5kb is closed for 2 keys. 8.5k energy | 1.9k rubies. there is very little left until the arbiter (13 missions). mine 3/3. i will send any screenshots upon request | Reserve your ideal account now!\n\n===============================================================\nGot questions? Contact me on G2G chat before buying! Offers like this go fast \u2013 reserve your ideal account now!\n\n(********)", "api_qty": 0, "low_stock_alert_qty": 0, "available_qty": 1, "min_qty": 1, "actual_qty": 1, "currency": "USD", "unit_price": 66.47, "other_pricing": [], "unit_name": "", "qty_metric": "1", "wholesale_details": [], "other_wholesale_details": [], "is_official": false, "delivery_mode": [], "delivery_method_ids": [], "delivery_speed": "manual", "delivery_speed_details": [{"min": 1, "max": 1, "delivery_time": 540}], "sales_territory_settings": {"settings_type": "global", "countries": []}, "cat_path": "5830014a-b974-45c6-9672-b51e83112fb7", "cat_id": "5830014a-b974-45c6-9672-b51e83112fb7", "ancestor_id": "5830014a-b974-45c6-9672-b51e83112fb7", "status": "live", "created_at": *************, "updated_at": *************, "seller_updated_at": *************, "external_images_mapping": [{"content_type": "", "image_name": "Screenshot 1", "image_url": "https://www.dl.dropboxusercontent.com/scl/fi/iumfnef57g2ufedxr8jka/screenshot_20250616_181732_5ec63ec4.jpg?rlkey=0u4as041io754bvt3tsx10sk6"}, {"content_type": "", "image_name": "Screenshot 2", "image_url": "https://www.dl.dropboxusercontent.com/scl/fi/05t00fe36p5w67s49q3ta/screenshot_20250616_181733_ed5a32ec.jpg?rlkey=lstppppb8sov3v6ylui7tuqca"}, {"content_type": "", "image_name": "Screenshot 3", "image_url": "https://www.dl.dropboxusercontent.com/scl/fi/53pl83vc8wpsogdqitggz/screenshot_20250616_181733_22c2eb60.jpg?rlkey=mbqfcxxg45e5m1ltyhldd53z7"}, {"content_type": "", "image_name": "Screenshot 4", "image_url": "https://www.dl.dropboxusercontent.com/scl/fi/a6lpva6k3adn95c60c5i9/screenshot_20250616_181738_7ca20ad4.jpg?rlkey=7n314x256i4xsicmkihc037nb"}]}, "request_id": "9e40aa91-5be4-491c-b984-b9c69938a760"}
[2025-06-16 18:17:58.720 +02:00 INF] ShopBot.Services.G2GPublisherService: 🎉 Sukces! Otrzymano ID oferty: G1750090679691XP
[2025-06-16 18:17:58.721 +02:00 INF] ShopBot.Services.G2GPublisherService: ✅ Oferta opublikowana pomyślnie (ID: G1750090679691XP)
[2025-06-16 18:17:59.276 +02:00 DBG] ShopBot.Services.OfferRepository: [OfferRepo] Updating offer: "539194fd-6d08-4035-a7ba-64701dbe7d10"
[2025-06-16 18:17:59.338 +02:00 DBG] ShopBot.Services.OfferRepository: [OfferRepo] ✅ Updated offer: "539194fd-6d08-4035-a7ba-64701dbe7d10"
[2025-06-16 18:17:59.339 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] 📝 Zaktualizowano ofertę ******** w bazie danych
[2025-06-16 18:17:59.627 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] ✅ Pomyślnie opublikowano ofertę ******** -> G1750090679691XP
[2025-06-16 18:17:59.658 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Oferta ******** została dodana
[2025-06-16 18:18:20.787 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-16 18:18:39.988 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-16 21:50:51.013 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-16 21:50:51.043 +02:00 INF] ShopBot.Services.CliCommandHandler: ➕ Dodaję ofertę FunPay ID: ********, Gra: RaidShadowLegends
[2025-06-16 21:50:52.627 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Mythical champions = 1
[2025-06-16 21:50:52.629 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 35
[2025-06-16 21:50:52.630 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 59
[2025-06-16 21:50:52.631 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-16 21:50:52.632 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-16 21:50:52.633 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-16 21:50:52.636 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 21:50:52.638 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 21:50:52.648 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 21:50:52.649 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 21:50:52.652 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-16 21:50:52.653 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 21:50:52.654 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 21:50:52.655 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 21:50:52.656 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 21:50:52.657 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 21:50:52.658 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-16 21:50:52.659 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-16 21:50:52.660 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-16 21:50:52.662 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-16 21:50:52.663 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-16 21:50:52.665 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-16 21:50:52.667 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 51.14 $</span></span>
[2025-06-16 21:50:52.668 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 21:50:52.669 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 21:50:52.671 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 21:50:52.672 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>51.13 USDT</span></span>
[2025-06-16 21:50:52.674 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 51.13
[2025-06-16 21:50:52.675 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 51.13 USDT
[2025-06-16 21:50:52.680 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 21:50:52.681 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 21:50:52.682 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 21:50:52.683 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 21:50:52.684 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 21:50:52.685 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-16 21:50:52.686 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-16 21:50:53.965 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] 🔄 Rozpoczynam dodawanie oferty ********
[2025-06-16 21:50:54.625 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Mythical champions = 1
[2025-06-16 21:50:54.627 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 35
[2025-06-16 21:50:54.628 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 59
[2025-06-16 21:50:54.629 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-16 21:50:54.631 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-16 21:50:54.632 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-16 21:50:54.634 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 21:50:54.635 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 21:50:54.639 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 21:50:54.640 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 21:50:54.643 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-16 21:50:54.644 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 21:50:54.645 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 21:50:54.646 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 21:50:54.647 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 21:50:54.648 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 21:50:54.649 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-16 21:50:54.650 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-16 21:50:54.651 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-16 21:50:54.652 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-16 21:50:54.653 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-16 21:50:54.655 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-16 21:50:54.656 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 51.14 $</span></span>
[2025-06-16 21:50:54.657 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 21:50:54.658 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 21:50:54.659 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 21:50:54.661 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>51.13 USDT</span></span>
[2025-06-16 21:50:54.662 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 51.13
[2025-06-16 21:50:54.664 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 51.13 USDT
[2025-06-16 21:50:54.666 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 21:50:54.667 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 21:50:54.668 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 21:50:54.669 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 21:50:54.670 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 21:50:54.671 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-16 21:50:54.672 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-16 21:50:56.677 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] 💰 Parsed USDT price: 51.13 USDT → 51.13 USD
[2025-06-16 21:50:58.373 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 🎮 Rozpoczynam przetwarzanie gry: RaidShadowLegends
[2025-06-16 21:51:18.753 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G Publisher] 💰 Parsed USDT price: 51.13 USDT → 51.13 USD
[2025-06-16 21:51:18.761 +02:00 INF] ShopBot.Services.G2GPublisherService: 💰 Cena oryginalna: $51.13 → Cena G2G: $66.47 (gra: RaidShadowLegends)
[2025-06-16 21:51:19.887 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Zakończono przetwarzanie gry: RaidShadowLegends
[2025-06-16 21:51:19.888 +02:00 INF] ShopBot.Services.G2GPublisherService: 🚀 Rozpoczynam wypełnianie tytułu i opisu dla gry: RaidShadowLegends
[2025-06-16 21:51:19.889 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: RaidShadowLegends
[2025-06-16 21:51:19.922 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: RaidShadowLegends
[2025-06-16 21:51:19.923 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Formatting Raid Shadow Legends description for offer ********
[2025-06-16 21:51:19.926 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Split by | into 5 lines
[2025-06-16 21:51:19.927 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Adding ID to description: ********
[2025-06-16 21:51:19.928 +02:00 INF] ShopBot.Services.Formatters.RaidShadowLegendsDescriptionFormatter: Final formatted description:
• Selling an account with top mythic komidus echidnik + narses + gleykad + elva + chagur + esme and modo + lonataril + legi for entry + many good epics. there is a ratnik for ankyl | At the moment 5kb is closed for 2 keys. 8.5k energy | 1.9k rubies. there is very little left until the arbiter (13 missions). mine 3/3. i will send any screenshots upon request | Reserve your ideal account now!

===============================================================
Got questions? Contact me on G2G chat before buying! Offers like this go fast – reserve your ideal account now!

(********)

[2025-06-16 21:51:19.929 +02:00 DBG] ShopBot.Services.G2GPublisherService: 📝 Wypełniam opis (587 znaków): • Selling an account with top mythic komidus echidnik + narses + gleykad + elva + chagur + esme and ...
[2025-06-16 21:51:19.972 +02:00 INF] ShopBot.Services.G2GPublisherService: ✅ Zakończono wypełnianie tytułu i opisu
[2025-06-16 21:51:19.973 +02:00 INF] ShopBot.Services.G2GPublisherService: 📸 Rozpoczynam upload 4 obrazów
[2025-06-16 21:51:19.975 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 🖼️ Rozpoczynam upload 4 obrazów do Dropbox
[2025-06-16 21:51:19.977 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] Rozpoczynam upload 4 obrazów do Dropbox dla G2G
[2025-06-16 21:51:19.979 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] 🚀 Rozpoczynam PARALLEL upload 4 obrazów (Dropbox: true)
[2025-06-16 21:51:19.981 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 21:51:19.983 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 21:51:19.984 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 21:51:20.001 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 21:51:20.002 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 21:51:20.003 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 21:51:20.004 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 21:51:20.005 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 21:51:20.006 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 21:51:20.766 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 544KB w 0.8s
[2025-06-16 21:51:20.772 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 594KB w 0.8s
[2025-06-16 21:51:20.775 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Token wygaśnie wkrótce, odświeżam...
[2025-06-16 21:51:20.775 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Token wygaśnie wkrótce, odświeżam...
[2025-06-16 21:51:20.776 +02:00 WRN] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Refresh token pusty, próbuję załadować z pliku...
[2025-06-16 21:51:20.776 +02:00 WRN] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Refresh token pusty, próbuję załadować z pliku...
[2025-06-16 21:51:20.779 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Załadowano token z pliku - wygasa: "2025-06-16T20:12:33.7002705Z"
[2025-06-16 21:51:20.779 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Załadowano token z pliku - wygasa: "2025-06-16T20:12:33.7002705Z"
[2025-06-16 21:51:20.780 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Refresh token length: 64
[2025-06-16 21:51:20.781 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Refresh token length: 64
[2025-06-16 21:51:20.782 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Access token length: 1437
[2025-06-16 21:51:20.783 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Access token length: 1437
[2025-06-16 21:51:20.785 +02:00 INF] ShopBot.Services.DropboxAuthService: [Dropbox Auth] Odświeżanie access token...
[2025-06-16 21:51:20.785 +02:00 INF] ShopBot.Services.DropboxAuthService: [Dropbox Auth] Odświeżanie access token...
[2025-06-16 21:51:20.811 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 656KB w 0.8s
[2025-06-16 21:51:21.156 +02:00 DBG] ShopBot.Services.DropboxAuthService: [Dropbox Auth] Refresh response: {"access_token": "sl.u.AFzZalvSTjIujmPEJfMjENK9FjW5Py0T2Csbarta8T2qkHvcb1ZU6ijykMIUjj_QyZRsX3VjQpSocDrqhw1InmamkJPfXrySpWTkdBnnfRV1DIle-W63bIo7KK1yvraqmRIkyMt_ilwIB_ztICOlMBV4pe-3USOGeaSIILWBx7wtEiCNHUuD6D_HW5O8nh343dGIdnoct00AK4kQ6urNIuUnOeWLzDUlAy5XGag7k7vorimm71iFB34yWrxkDrskimOuFd4szciJVLumeOZf3ro8hLWCTyhOLmccEAwU-aqwqT8_PqRfgo6rgYqcQzpllDMigaW5y3-5EXpbwpyb8iwhL3TbQbwQZtJd3Q7jFMc7bcQ0SVvyeYOo5p7rIfFpZfZcpzl9pJgSCmpWzwyRpYW12LSPvouI9FQDEwbkgjNQG9Jmy2lAUOsy7YJCbEPY3hc19ZSIoK8nhVCP4dVGQDcjH_rdP4FXHtklbzEIQf7FVsA0KxiZ_V6XBDEZWAHIXH0y81ROMg5WeYBt-ImmOZkuhTmDvsSI247jFLVrisZV-p9gHrBDd89vaVtg1T1OZ_5xgnm5S35QHvteJhiAMDMXYhvCqCdysovx-oIwOwzGeI_LEDphkuHq2Fhpi6A6oUfUjeIljZ5oqGoRSrrnjn7DO0SYg-WeEr5qmWDFLU7tiFaghIJDDACB7h32Qf8MnM_O10fUIiYH54GDvzoub6UvYDFZKzOssjYJmduBhDsbT1a0TzkETqUcwLytKht4LycQdwrYf--rn8KLSvejEqSdOzhrt6JddZd0-l0MhK3LzOeW1MWREIDHkQFtR3ZsNAlyEe1Oq9ZYa-U_EuMZa5Rjne-Ax_6HojK2W0yjWlrlqnfy2vTc3zancufv-D7oKhqlBoqMcfljDLhEgi6DJp6bQOgZezAPuYIqt5Hii6DG3A84-aBujBOT8SVo6mqVL431wWXRshxRBDwCyvzqRq3P8aqTeJ82MIoZeZlwi81-2LHWeeI_Ki0rHGb6P3qcN0vrrteznVVlT_IIC4GeBdm0BwmpSDG9T5Uyil1ugQx8gj1u7_WFAThLLGqwa6SWbg0xFXLDCbOLdU38po96PVXU3UVz-OQjaIa6vjUHg-zRExpb_50GVLrzVuIY27MC1tRzPR4owVtLAt8ezmWyrj1BetBPngntCXshjV-6q8zFQDWwvKBW38IZwkJRxl8OHrI9mfc53Bl9VePnDPzcLAweXnlSOnGYRQOZGBhe8GvoVcumiWckAyjvGy94RRXr1rpExHnAL8kZTpvLVV2DYpwjByhxUJ4uqRswrHexgXbEiIOuO88sLfCMVGXHc-OOpTCxbHQpAtIroawP_9OQbaKdvji_IHbY7Nt_vMatA1YZAkulXCmyBOZCPbRuYHFgf8pBKsH26Ia5f50u_U6o3OfI", "token_type": "bearer", "expires_in": 14400}
[2025-06-16 21:51:21.158 +02:00 INF] ShopBot.Services.DropboxAuthService: [Dropbox Auth] ✅ Token odświeżony pomyślnie
[2025-06-16 21:51:21.161 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Zapisano token do pliku
[2025-06-16 21:51:21.162 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] ✅ Token został odświeżony. Następne odświeżenie za 3 godzin
[2025-06-16 21:51:21.187 +02:00 DBG] ShopBot.Services.DropboxAuthService: [Dropbox Auth] Refresh response: {"access_token": "sl.u.AFxgFO0yRlKEvlYflQz3KNHj3R6-Kg27ElsVPyWgm7yBuyAaGwdRpVRwjmmrIplAaXHHEQ0fa2CwwL8mpvHYh5j1cAD3mMi-CXnGJwRDWqCZIZvwDhYM_F5dVukhvpGAkv9O-4Eyd_Goi58Pcqg3ohD9ua7AJM0QBKJEXwLUalrZCynudQcx4F4i0Iue1sN7HMXnn1_26igqupKJByyyoyPvp2lscUzhmnRXo7klqEruffb5QZwRgLO3CKM2yayanxnRi3soiq5V5adwTuCDAjz4v2l3WCSZyhmbvJnZtbS9l4qjDH1By5ijPgGL7LopXz1y1PVb1uZAZw14J1WUQU2HJadN7-cVdr77lTldbA8t7kwKgVkf8rR9QxVLG6N2dkIFQujYEjd11oZj_n-my6sx15jIjGAM7Tbn2BbWv4rPHmTsv7CjVKfUFvFD0c7THpxfoxDR5FCoxAioAyDfOZ0aNt0EL5CNUo-y7klwbnHIxTXT_wXU4T1cj226sZXHAsLE9mDtbXa-piQM0ukXE1Lc6F8XQIjTkGWIw1I0uUe8Voj42uKya2JNRqz97gj5CJUgWyhSZFBi6suCuQIjc87q_4K_PLQpKYWwAooI9E6t9lW52wALp2yYbdTvghD6SDeLNaAnSP7SUTQRKDzYoMYJzE8uN9vhvhBhEXK2-rSDK0Jq_ZkvM9_HHsUJlNKBmDNOMVA4CI5z84if_qPBr_KXWPV9Dporhzpc3g5tdC50DfuwK8yoTAykKZIFVdpsjEpcBH2_mwUi7RpVziAlJ4vvMkRjL8TL7EmWX4jCIIQ-cf3NH8a7Y4jKOvd0U3_r0K0ww3HFnoI15dLS9-fKngBSg5PfsBwLFp9TdtlwIiTwLwn2a87MFHEBIJSLwVIHHUnA7BfW3p22rH1b_qVKz-VHihtTIYzNNRpdjGoR5nIbGSywd32XEKsz9cFffenynIAGVttzf_-EJoVS465clE6oRPuB-Q3s-pef2lv6Z-AW6vCWJvf4sJC_LmUDKf5uLBTDpfQAaKHKeSOsJtjWtLmQUmtrhKsg7ZzJF4j7spXmqnRAvhbrECdYL-Up-MqPVvn2P_F-z6gZcvxSxUzzhBems-iZwDUoRegkFQ1x_Q2tiiTLHSWAbDXRRSVaKaQaxqg4f3riqhIVYSymEDuJSpKzHvGwW-GozgfF_DXs_M0fM6DlV_cdljOqLOHmE09Yf-XQIVWcnsX1U1zMLsq3AGzHAg2mRy3P3nj4qYaj6td-6WF_r4kwrDeuGlXv-Lmz6wcwfHcgS_EyB07TV-52dPsOuivC04iBJDsL6wszvTHNx9KTeqDrQ7QZ6l1SdPNvUeTOxKf4F8tfK7shQCL-k0wF8NoGEZVQuXXf2j5qzOyEPR28XLodaeGLKBESECOAbW0mGdL2p4oinbZXSc1ITFcf", "token_type": "bearer", "expires_in": 14400}
[2025-06-16 21:51:21.189 +02:00 INF] ShopBot.Services.DropboxAuthService: [Dropbox Auth] ✅ Token odświeżony pomyślnie
[2025-06-16 21:51:21.190 +02:00 DBG] ShopBot.Services.DropboxTokenManager: [Dropbox Token] Zapisano token do pliku
[2025-06-16 21:51:21.191 +02:00 INF] ShopBot.Services.DropboxTokenManager: [Dropbox Token] ✅ Token został odświeżony. Następne odświeżenie za 3 godzin
[2025-06-16 21:51:22.728 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Upload do /G2G_Screenshots/screenshot_20250616_215120_530d4ef9.jpg pomyślny
[2025-06-16 21:51:23.085 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Upload do /G2G_Screenshots/screenshot_20250616_215120_8905c37f.jpg pomyślny
[2025-06-16 21:51:23.508 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Upload do /G2G_Screenshots/screenshot_20250616_215120_de58b00d.jpg pomyślny
[2025-06-16 21:51:24.233 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Otrzymano shared URL: https://www.dropbox.com/scl/fi/8n5gwwkkqzjeks31fbe06/screenshot_20250616_215120_530d4ef9.jpg?rlkey=p9g0azbp1f2yfal79k51rem2h&dl=0
[2025-06-16 21:51:24.234 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Konwersja URL: https://www.dropbox.com/scl/fi/8n5gwwkkqzjeks31fbe06/screenshot_20250616_215120_530d4ef9.jpg?rlkey=p9g0azbp1f2yfal79k51rem2h&dl=0 -> https://www.dl.dropboxusercontent.com/scl/fi/8n5gwwkkqzjeks31fbe06/screenshot_20250616_215120_530d4ef9.jpg?rlkey=p9g0azbp1f2yfal79k51rem2h
[2025-06-16 21:51:24.235 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ✅ Upload pomyślny w 4.2s: https://www.dl.dropboxusercontent.com/scl/fi/8n5gwwkkqzjeks31fbe06/screenshot_20250616_215120_530d4ef9.jpg?rlkey=p9g0azbp1f2yfal79k51rem2h
[2025-06-16 21:51:24.237 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ✅ Upload pomyślny: https://sfunpay.com/s/offer/k7/n3/k7n3kwexnoxi9jpk3msp.jpg
[2025-06-16 21:51:24.238 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ⬆️ Rozpoczynam upload: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg
[2025-06-16 21:51:24.239 +02:00 DBG] ShopBot.Services.ImageUploadService: [Image Upload] Używam Dropbox dla: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg
[2025-06-16 21:51:24.240 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobieram obraz z URL: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg
[2025-06-16 21:51:24.535 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ⬇️ Pobrano 253KB w 0.3s
[2025-06-16 21:51:24.572 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Otrzymano shared URL: https://www.dropbox.com/scl/fi/e8ypmnhwev2kdgzjm787v/screenshot_20250616_215120_8905c37f.jpg?rlkey=9us95dhklcb13kz441wxu3wmr&dl=0
[2025-06-16 21:51:24.573 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Konwersja URL: https://www.dropbox.com/scl/fi/e8ypmnhwev2kdgzjm787v/screenshot_20250616_215120_8905c37f.jpg?rlkey=9us95dhklcb13kz441wxu3wmr&dl=0 -> https://www.dl.dropboxusercontent.com/scl/fi/e8ypmnhwev2kdgzjm787v/screenshot_20250616_215120_8905c37f.jpg?rlkey=9us95dhklcb13kz441wxu3wmr
[2025-06-16 21:51:24.574 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ✅ Upload pomyślny w 4.6s: https://www.dl.dropboxusercontent.com/scl/fi/e8ypmnhwev2kdgzjm787v/screenshot_20250616_215120_8905c37f.jpg?rlkey=9us95dhklcb13kz441wxu3wmr
[2025-06-16 21:51:24.575 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ✅ Upload pomyślny: https://sfunpay.com/s/offer/66/7l/667ltt0dn0u9hdjpsmiv.jpg
[2025-06-16 21:51:24.919 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Otrzymano shared URL: https://www.dropbox.com/scl/fi/c0zktqzz2z42iupzvx2mj/screenshot_20250616_215120_de58b00d.jpg?rlkey=k9t7o7wro2wu315skh64999j9&dl=0
[2025-06-16 21:51:24.921 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Konwersja URL: https://www.dropbox.com/scl/fi/c0zktqzz2z42iupzvx2mj/screenshot_20250616_215120_de58b00d.jpg?rlkey=k9t7o7wro2wu315skh64999j9&dl=0 -> https://www.dl.dropboxusercontent.com/scl/fi/c0zktqzz2z42iupzvx2mj/screenshot_20250616_215120_de58b00d.jpg?rlkey=k9t7o7wro2wu315skh64999j9
[2025-06-16 21:51:24.922 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ✅ Upload pomyślny w 4.9s: https://www.dl.dropboxusercontent.com/scl/fi/c0zktqzz2z42iupzvx2mj/screenshot_20250616_215120_de58b00d.jpg?rlkey=k9t7o7wro2wu315skh64999j9
[2025-06-16 21:51:24.923 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ✅ Upload pomyślny: https://sfunpay.com/s/offer/in/90/in9061v4120onbxep19h.jpg
[2025-06-16 21:51:26.505 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Upload do /G2G_Screenshots/screenshot_20250616_215124_5585dcc7.jpg pomyślny
[2025-06-16 21:51:28.022 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Otrzymano shared URL: https://www.dropbox.com/scl/fi/ltbld8cvwg0sm1crg9wbo/screenshot_20250616_215124_5585dcc7.jpg?rlkey=eyynns0eg98kxo1ns182905tw&dl=0
[2025-06-16 21:51:28.023 +02:00 DBG] ShopBot.Services.DropboxService: [Dropbox] Konwersja URL: https://www.dropbox.com/scl/fi/ltbld8cvwg0sm1crg9wbo/screenshot_20250616_215124_5585dcc7.jpg?rlkey=eyynns0eg98kxo1ns182905tw&dl=0 -> https://www.dl.dropboxusercontent.com/scl/fi/ltbld8cvwg0sm1crg9wbo/screenshot_20250616_215124_5585dcc7.jpg?rlkey=eyynns0eg98kxo1ns182905tw
[2025-06-16 21:51:28.024 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] ✅ Upload pomyślny w 3.8s: https://www.dl.dropboxusercontent.com/scl/fi/ltbld8cvwg0sm1crg9wbo/screenshot_20250616_215124_5585dcc7.jpg?rlkey=eyynns0eg98kxo1ns182905tw
[2025-06-16 21:51:28.025 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] ✅ Upload pomyślny: https://sfunpay.com/s/offer/lw/11/lw11yzsllzjf8u5uow3l.jpg
[2025-06-16 21:51:28.027 +02:00 INF] ShopBot.Services.ImageUploadService: [Image Upload] 📊 Upload zakończony: 4/4 pomyślnych
[2025-06-16 21:51:28.029 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 📊 Pomyślnie zauploadowano 4/4 obrazów
[2025-06-16 21:51:28.031 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 📸 Dodaję 4 obrazów do oferty
[2025-06-16 21:51:28.033 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 🎬 Próbuję dodać obrazy przez sekcję Media
[2025-06-16 21:51:28.034 +02:00 DBG] ShopBot.Services.G2GPublisherService: [G2G] 📤 Dodaję obraz 1/4: https://www.dl.dropboxusercontent.com/scl/fi/e8ypmnhwev2kdgzjm787v/screenshot_20250616_215120_8905c37f.jpg?rlkey=9us95dhklcb13kz441wxu3wmr
[2025-06-16 21:51:28.047 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🔍 Znaleziono 1 pól tytułu i 1 pól URL
[2025-06-16 21:51:28.058 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Znaleziono puste pola Media
[2025-06-16 21:51:30.643 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Dodano obraz 1: Screenshot 1 -> https://www.dl.dropboxusercontent.com/scl/fi/e8ypmnhwev2kdgzjm787v/screenshot_20250616_215120_8905c37f.jpg?rlkey=9us95dhklcb13kz441wxu3wmr
[2025-06-16 21:51:32.211 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Kliknięto 'Add media' dla następnego obrazu
[2025-06-16 21:51:32.212 +02:00 DBG] ShopBot.Services.G2GPublisherService: [G2G] 📤 Dodaję obraz 2/4: https://www.dl.dropboxusercontent.com/scl/fi/8n5gwwkkqzjeks31fbe06/screenshot_20250616_215120_530d4ef9.jpg?rlkey=p9g0azbp1f2yfal79k51rem2h
[2025-06-16 21:51:32.221 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🔍 Znaleziono 2 pól tytułu i 2 pól URL
[2025-06-16 21:51:32.239 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Znaleziono puste pola Media
[2025-06-16 21:51:34.880 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Dodano obraz 2: Screenshot 2 -> https://www.dl.dropboxusercontent.com/scl/fi/8n5gwwkkqzjeks31fbe06/screenshot_20250616_215120_530d4ef9.jpg?rlkey=p9g0azbp1f2yfal79k51rem2h
[2025-06-16 21:51:36.436 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Kliknięto 'Add media' dla następnego obrazu
[2025-06-16 21:51:36.437 +02:00 DBG] ShopBot.Services.G2GPublisherService: [G2G] 📤 Dodaję obraz 3/4: https://www.dl.dropboxusercontent.com/scl/fi/c0zktqzz2z42iupzvx2mj/screenshot_20250616_215120_de58b00d.jpg?rlkey=k9t7o7wro2wu315skh64999j9
[2025-06-16 21:51:36.446 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🔍 Znaleziono 3 pól tytułu i 3 pól URL
[2025-06-16 21:51:36.471 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Znaleziono puste pola Media
[2025-06-16 21:51:39.217 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Dodano obraz 3: Screenshot 3 -> https://www.dl.dropboxusercontent.com/scl/fi/c0zktqzz2z42iupzvx2mj/screenshot_20250616_215120_de58b00d.jpg?rlkey=k9t7o7wro2wu315skh64999j9
[2025-06-16 21:51:40.777 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Kliknięto 'Add media' dla następnego obrazu
[2025-06-16 21:51:40.778 +02:00 DBG] ShopBot.Services.G2GPublisherService: [G2G] 📤 Dodaję obraz 4/4: https://www.dl.dropboxusercontent.com/scl/fi/ltbld8cvwg0sm1crg9wbo/screenshot_20250616_215124_5585dcc7.jpg?rlkey=eyynns0eg98kxo1ns182905tw
[2025-06-16 21:51:40.788 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🔍 Znaleziono 4 pól tytułu i 4 pól URL
[2025-06-16 21:51:40.818 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Znaleziono puste pola Media
[2025-06-16 21:51:43.747 +02:00 DBG] ShopBot.Services.G2GPublisherService: ✅ Dodano obraz 4: Screenshot 4 -> https://www.dl.dropboxusercontent.com/scl/fi/ltbld8cvwg0sm1crg9wbo/screenshot_20250616_215124_5585dcc7.jpg?rlkey=eyynns0eg98kxo1ns182905tw
[2025-06-16 21:51:43.749 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] ✅ Pomyślnie dodano 4 obrazów przez sekcję Media
[2025-06-16 21:51:43.750 +02:00 INF] ShopBot.Services.G2GPublisherService: ✅ Zakończono upload obrazów
[2025-06-16 21:51:43.751 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G Publisher] 💰 Parsed USDT price: 51.13 USDT → 51.13 USD
[2025-06-16 21:51:43.752 +02:00 INF] ShopBot.Services.G2GPublisherService: 💰 Cena oryginalna: $51.13 → Cena G2G: $66.47 (gra: RaidShadowLegends)
[2025-06-16 21:51:44.839 +02:00 INF] ShopBot.Services.G2GPublisherService: 🚀 Publikuję ofertę na G2G
[2025-06-16 21:51:44.842 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🔄 Próba publikacji 1/3
[2025-06-16 21:51:44.855 +02:00 DBG] ShopBot.Services.G2GPublisherService: 🖱️ Klikam przycisk Publish
[2025-06-16 21:51:45.737 +02:00 DBG] ShopBot.Services.G2GPublisherService: 📡 Odpowiedź API (Status: 200): {"code": 2000, "messages": [], "payload": {"offer_id": "G1750103505631LW", "seller_id": "7670672", "service_id": "f6a1aba5-473a-4044-836a-8968bbab16d7", "brand_id": "lgc_game_26258", "region_id": "", "relation_id": "59ca631d-a789-4799-a5cc-b3bb50b40dc7", "offer_type": "public", "offer_attributes": [{"collection_id": "lgc_26258_platform", "dataset_id": "lgc_26258_platform_30659"}], "offer_title_collection_tree": ["lgc_26258_platform"], "primary_img_attributes": [], "offer_group": "G1750103505631LW", "title": "mythic comidus + na***s + gleikad + elva + esme and modo", "description": "\u2022 Selling an account with top mythic komidus echidnik + narses + gleykad + elva + chagur + esme and modo + lonataril + legi for entry + many good epics. there is a ratnik for ankyl | At the moment 5kb is closed for 2 keys. 8.5k energy | 1.9k rubies. there is very little left until the arbiter (13 missions). mine 3/3. i will send any screenshots upon request | Reserve your ideal account now!\n\n===============================================================\nGot questions? Contact me on G2G chat before buying! Offers like this go fast \u2013 reserve your ideal account now!\n\n(********)", "api_qty": 0, "low_stock_alert_qty": 0, "available_qty": 1, "min_qty": 1, "actual_qty": 1, "currency": "USD", "unit_price": 66.47, "other_pricing": [], "unit_name": "", "qty_metric": "1", "wholesale_details": [], "other_wholesale_details": [], "is_official": false, "delivery_mode": [], "delivery_method_ids": [], "delivery_speed": "manual", "delivery_speed_details": [{"min": 1, "max": 1, "delivery_time": 540}], "sales_territory_settings": {"settings_type": "global", "countries": []}, "cat_path": "5830014a-b974-45c6-9672-b51e83112fb7", "cat_id": "5830014a-b974-45c6-9672-b51e83112fb7", "ancestor_id": "5830014a-b974-45c6-9672-b51e83112fb7", "status": "live", "created_at": *************, "updated_at": *************, "seller_updated_at": *************, "external_images_mapping": [{"content_type": "", "image_name": "Screenshot 1", "image_url": "https://www.dl.dropboxusercontent.com/scl/fi/e8ypmnhwev2kdgzjm787v/screenshot_20250616_215120_8905c37f.jpg?rlkey=9us95dhklcb13kz441wxu3wmr"}, {"content_type": "", "image_name": "Screenshot 2", "image_url": "https://www.dl.dropboxusercontent.com/scl/fi/8n5gwwkkqzjeks31fbe06/screenshot_20250616_215120_530d4ef9.jpg?rlkey=p9g0azbp1f2yfal79k51rem2h"}, {"content_type": "", "image_name": "Screenshot 3", "image_url": "https://www.dl.dropboxusercontent.com/scl/fi/c0zktqzz2z42iupzvx2mj/screenshot_20250616_215120_de58b00d.jpg?rlkey=k9t7o7wro2wu315skh64999j9"}, {"content_type": "", "image_name": "Screenshot 4", "image_url": "https://www.dl.dropboxusercontent.com/scl/fi/ltbld8cvwg0sm1crg9wbo/screenshot_20250616_215124_5585dcc7.jpg?rlkey=eyynns0eg98kxo1ns182905tw"}]}, "request_id": "2a5ceaa9-741b-473b-9955-7e528c830e10"}
[2025-06-16 21:51:45.741 +02:00 INF] ShopBot.Services.G2GPublisherService: 🎉 Sukces! Otrzymano ID oferty: G1750103505631LW
[2025-06-16 21:51:45.742 +02:00 INF] ShopBot.Services.G2GPublisherService: ✅ Oferta opublikowana pomyślnie (ID: G1750103505631LW)
[2025-06-16 21:51:46.340 +02:00 DBG] ShopBot.Services.OfferRepository: [OfferRepo] Updating offer: "539194fd-6d08-4035-a7ba-64701dbe7d10"
[2025-06-16 21:51:46.409 +02:00 DBG] ShopBot.Services.OfferRepository: [OfferRepo] ✅ Updated offer: "539194fd-6d08-4035-a7ba-64701dbe7d10"
[2025-06-16 21:51:46.410 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] 📝 Zaktualizowano ofertę ******** w bazie danych
[2025-06-16 21:51:46.737 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] ✅ Pomyślnie opublikowano ofertę ******** -> G1750103505631LW
[2025-06-16 21:51:46.773 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Oferta ******** została dodana
[2025-06-16 21:52:00.697 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-16 21:54:00.705 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-16 21:54:41.076 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-16 21:54:41.102 +02:00 INF] ShopBot.Services.CliCommandHandler: 🔍 Rozpoczynam analizę ofert...
[2025-06-16 21:54:41.108 +02:00 ERR] ShopBot.Services.CliCommandHandler: Błąd podczas wykonywania polecenia analyze
System.InvalidOperationException: No service for type 'ShopBot.Services.FunPayService' has been registered.
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at ShopBot.Services.Factories.TaskFactory.CreateAnalyzeOffersTask() in C:\Users\<USER>\Desktop\SHOPBOT v7 dzialajaca\Services\Factories\TaskFactory.cs:line 97
   at ShopBot.Services.CliCommandHandler.HandleAnalyzeCommand(String[] args) in C:\Users\<USER>\Desktop\SHOPBOT v7 dzialajaca\Services\CliCommandHandler.cs:line 174
   at ShopBot.Services.CliCommandHandler.HandleCommandAsync(String[] args) in C:\Users\<USER>\Desktop\SHOPBOT v7 dzialajaca\Services\CliCommandHandler.cs:line 76
[2025-06-16 22:02:28.835 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-16 22:04:48.710 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-16 22:04:48.739 +02:00 INF] ShopBot.Services.CliCommandHandler: ➕ Dodaję ofertę FunPay ID: ********, Gra: RaidShadowLegends
[2025-06-16 22:04:50.070 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Mythical champions = 1
[2025-06-16 22:04:50.071 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 35
[2025-06-16 22:04:50.072 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 59
[2025-06-16 22:04:50.074 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-16 22:04:50.075 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-16 22:04:50.076 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-16 22:04:50.079 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 22:04:50.081 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 22:04:50.090 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 22:04:50.091 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 22:04:50.095 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-16 22:04:50.096 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 22:04:50.097 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 22:04:50.098 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 22:04:50.099 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 22:04:50.100 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 22:04:50.102 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-16 22:04:50.103 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-16 22:04:50.104 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-16 22:04:50.105 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-16 22:04:50.106 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-16 22:04:50.108 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-16 22:04:50.110 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 51.14 $</span></span>
[2025-06-16 22:04:50.111 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 22:04:50.112 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 22:04:50.114 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 22:04:50.115 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>51.13 USDT</span></span>
[2025-06-16 22:04:50.117 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 51.13
[2025-06-16 22:04:50.118 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 51.13 USDT
[2025-06-16 22:04:50.123 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 22:04:50.124 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 22:04:50.125 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 22:04:50.126 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 22:04:50.127 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 22:04:50.128 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-16 22:04:50.130 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-16 22:04:50.844 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] 🔄 Rozpoczynam dodawanie oferty ********
[2025-06-16 22:04:51.510 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Mythical champions = 1
[2025-06-16 22:04:51.511 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 35
[2025-06-16 22:04:51.512 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 59
[2025-06-16 22:04:51.513 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-16 22:04:51.515 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-16 22:04:51.516 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-16 22:04:51.517 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 22:04:51.518 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 22:04:51.522 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 22:04:51.523 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 22:04:51.526 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-16 22:04:51.527 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 22:04:51.528 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 22:04:51.529 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 22:04:51.530 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 22:04:51.531 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 22:04:51.532 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-16 22:04:51.533 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-16 22:04:51.534 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-16 22:04:51.535 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-16 22:04:51.536 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-16 22:04:51.537 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-16 22:04:51.538 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 51.14 $</span></span>
[2025-06-16 22:04:51.540 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 22:04:51.541 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 22:04:51.542 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 22:04:51.543 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>51.13 USDT</span></span>
[2025-06-16 22:04:51.545 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 51.13
[2025-06-16 22:04:51.546 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 51.13 USDT
[2025-06-16 22:04:51.548 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 22:04:51.549 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 22:04:51.550 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 22:04:51.552 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 22:04:51.552 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 22:04:51.553 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-16 22:04:51.554 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-16 22:04:53.564 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] 💰 Parsed USDT price: 51.13 USDT → 51.13 USD
[2025-06-16 22:04:55.971 +02:00 INF] ShopBot.Services.G2GPublisherService: [G2G] 🎮 Rozpoczynam przetwarzanie gry: RaidShadowLegends
[2025-06-16 22:54:50.586 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-16 22:54:50.611 +02:00 INF] ShopBot.Services.CliCommandHandler: ➕ Dodaję ofertę FunPay ID: ********, Gra: RaidShadowLegends
[2025-06-16 22:54:52.038 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Mythical champions = 1
[2025-06-16 22:54:52.040 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 35
[2025-06-16 22:54:52.041 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 59
[2025-06-16 22:54:52.042 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-16 22:54:52.045 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-16 22:54:52.046 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-16 22:54:52.049 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 22:54:52.051 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 22:54:52.061 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-16 22:54:52.062 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-16 22:54:52.066 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-16 22:54:52.067 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 22:54:52.068 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 22:54:52.068 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 22:54:52.069 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 22:54:52.070 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 22:54:52.071 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-16 22:54:52.072 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-16 22:54:52.073 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-16 22:54:52.074 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-16 22:54:52.075 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-16 22:54:52.078 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-16 22:54:52.079 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 51.14 $</span></span>
[2025-06-16 22:54:52.080 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 22:54:52.082 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 22:54:52.083 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>56.19 $</span></span>
[2025-06-16 22:54:52.084 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>51.13 USDT</span></span>
[2025-06-16 22:54:52.086 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 51.13
[2025-06-16 22:54:52.087 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 51.13 USDT
[2025-06-16 22:54:52.091 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-16 22:54:52.092 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-16 22:54:52.093 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-16 22:54:52.094 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-16 22:54:52.095 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-16 22:54:52.096 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-16 22:54:52.097 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-16 22:54:52.844 +02:00 INF] ShopBot.Tasks.AddOfferTask: [AddOffer] 🔄 Rozpoczynam dodawanie oferty ********
[2025-06-16 22:54:52.847 +02:00 ERR] ShopBot.Tasks.AddOfferTask: [AddOffer] ❌ Błąd podczas dodawania oferty ********: FunPayDetailsService nie jest uruchomiony. Wywołaj StartAsync() najpierw.
[2025-06-16 22:54:52.848 +02:00 ERR] ShopBot.Services.CliCommandHandler: Błąd podczas wykonywania polecenia add
System.InvalidOperationException: FunPayDetailsService nie jest uruchomiony. Wywołaj StartAsync() najpierw.
   at ShopBot.Services.FunPayDetailsService.GetOfferDetailsAsync(String offerId, String offerUrl, String gameName) in C:\Users\<USER>\Desktop\SHOPBOT v7 dzialajaca\Services\FunPayDetailsService.cs:line 121
   at ShopBot.Tasks.AddOfferTask.ExecuteAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Desktop\SHOPBOT v7 dzialajaca\Tasks\AddOfferTask.cs:line 77
   at ShopBot.Tasks.AddOfferTask.ExecuteAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Desktop\SHOPBOT v7 dzialajaca\Tasks\AddOfferTask.cs:line 173
   at ShopBot.Services.CliCommandHandler.HandleAddCommand(String[] args) in C:\Users\<USER>\Desktop\SHOPBOT v7 dzialajaca\Services\CliCommandHandler.cs:line 161
   at ShopBot.Services.CliCommandHandler.HandleCommandAsync(String[] args) in C:\Users\<USER>\Desktop\SHOPBOT v7 dzialajaca\Services\CliCommandHandler.cs:line 72
