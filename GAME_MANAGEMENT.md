# 🎮 Zarządzanie grami w ShopBot

## 🚀 Jak w<PERSON>/w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> grę

### ✅ Włączanie gry:
1. <PERSON>tw<PERSON><PERSON> `gamesconfig.json`
2. Dodaj nazwę gry do tablicy `ActiveGames`
3. **To wszystko!** Restart aplikacji i gra jest aktywna

**Przykład - włączenie Valorant:**
```json
{
  "ActiveGames": [
    "RaidShadowLegends",
    "WatcherOfRealms",
    "Valorant"  // ← Dodaj tutaj
  ]
}
```

### ❌ Wyłączanie gry:
1. Usuń nazwę gry z tablicy `ActiveGames` w `gamesconfig.json`
2. **To wszystko!** Gra pozostanie w `GamesData` - gotowa do ponownego włączenia

## 📋 Dostępne gry

### ✅ Aktywne:
- `RaidShadowLegends` - Raid: Shadow Legends
- `WatcherOfRealms` - Watcher of Realms

### 💤 Nieaktywne (gotowe do włączenia):
- `ApexLegends` - Apex Legends
- `Albion` - Albion Online  
- `GenshinImpact` - Genshin Impact
- `EscapeFromTarkov` - Escape from Tarkov
- `Rust` - Rust
- `PUBG` - PlayerUnknown's Battlegrounds
- `LeagueOfLegends` - League of Legends
- `Valorant` - Valorant
- `MobileLegends` - Mobile Legends: Bang Bang
- `PUBGMobile` - PUBG Mobile
- `Fortnite` - Fortnite
- `WorldOfWarcraft` - World of Warcraft
- `Dota2` - Dota 2
- `FC 25` - FC 25

## 🔧 Gry ze specjalną logiką

**Wszystkie gry mają już gotowe metody!** Nie musisz nic odkomentowywać w kodzie.

### Gry ze specjalną logiką (gotowe do użycia):
- `RaidShadowLegends` ✅ (aktywna)
- `LeagueOfLegends` 🎮 (gotowa)
- `Valorant` 🎮 (gotowa)
- `ApexLegends` 🎮 (gotowa)
- `GenshinImpact` 🎮 (gotowa)
- `EscapeFromTarkov` 🎮 (gotowa)
- `PUBG` 🎮 (gotowa)
- `Rust` 🎮 (gotowa)
- `MobileLegends` 🎮 (gotowa)
- `PUBGMobile` 🎮 (gotowa)
- `Fortnite` 🎮 (gotowa)
- `Dota2` 🎮 (gotowa)
- `FC 25` 🎮 (gotowa)
- `WorldOfWarcraft` 🎮 (gotowa)

### Gry używające standardowej logiki:
- `WatcherOfRealms` ✅ (aktywna)
- `Albion` 🎮 (używa ProcessGame)

## 🎯 Przykłady

### Włączenie League of Legends:
**Tylko gamesconfig.json:**
```json
"ActiveGames": [
  "RaidShadowLegends",
  "WatcherOfRealms",
  "LeagueOfLegends"
]
```
**To wszystko!** Metoda `ProcessLeagueOfLegends()` jest już gotowa.

### Włączenie Albion:
**Tylko gamesconfig.json:**
```json
"ActiveGames": [
  "RaidShadowLegends",
  "WatcherOfRealms",
  "Albion"
]
```
**To wszystko!** Albion używa standardowej logiki `ProcessGame()`.

## ⚠️ Ważne uwagi

1. **Nazwy gier** muszą dokładnie pasować do tych w `GamesData`
2. **Wielkość liter** ma znaczenie: `"RaidShadowLegends"` ≠ `"raidshadowlegends"`
3. **Restart aplikacji** jest wymagany po zmianie konfiguracji
4. **Wszystkie gry** w `GamesData` są gotowe do użycia - wystarczy je włączyć
5. **Nie musisz edytować kodu** - wszystkie metody są już dostępne

## 🚀 Szybkie włączenie wszystkich gier

Jeśli chcesz włączyć wszystkie dostępne gry:

```json
{
  "ActiveGames": [
    "RaidShadowLegends",
    "WatcherOfRealms", 
    "ApexLegends",
    "Albion",
    "GenshinImpact",
    "EscapeFromTarkov",
    "Rust",
    "PUBG",
    "LeagueOfLegends",
    "Valorant",
    "MobileLegends",
    "PUBGMobile", 
    "Fortnite",
    "WorldOfWarcraft",
    "Dota2",
    "FC 25"
  ]
}
```

**Wszystkie metody są już gotowe - nie musisz edytować kodu!**
