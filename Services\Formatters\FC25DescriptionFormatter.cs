using System;
using System.Collections.Generic;
using System.Text;
using Microsoft.Extensions.Logging;
using ShopBot;

namespace ShopBot.Services.Formatters
{
    public class FC25DescriptionFormatter : BaseDescriptionFormatter
    {
        public FC25DescriptionFormatter(ILogger<FC25DescriptionFormatter> logger) : base(logger)
        {
        }
        
        public override string FormatDescription(OfferDetails offerDetails, Dictionary<string, string> parameters = null)
        {
            if (offerDetails == null)
            {
                _logger.LogWarning("OfferDetails is null in FC25 formatter");
                return string.Empty;
            }

            _logger.LogInformation("Formatting FC 25 description for offer {Id}", offerDetails.Id);
            
            // Formatowanie informacji dodatkowych
            var additionalInfo = FormatAdditionalInfo(offerDetails);
            
            // Formatowanie głównego opisu
            var baseDescription = base.FormatDescription(offerDetails, parameters);
            
            // Połączenie obu części
            return additionalInfo + "\n\n" + baseDescription;
        }
        
        public override string FormatAdditionalInfo(OfferDetails offerDetails)
        {
            _logger.LogInformation("FC 25 Details before formatting:");
            _logger.LogInformation("Squad Price: {SquadPrice}", offerDetails.SquadPrice);
            _logger.LogInformation("Coins Balance: {CoinsBalance}", offerDetails.CoinsOnBalance);
            _logger.LogInformation("Web App: {WebApp}", offerDetails.WebApp);
            _logger.LogInformation("Rating FC25: {RatingFC25}", offerDetails.RatingFC25);
            
            var sb = new StringBuilder();
            sb.AppendLine("=== FC 25 Offer Details ===");
            
            // Dodaję zawsze te pola, nawet jeśli są puste
            sb.AppendLine($"Squad Price: {offerDetails.SquadPrice ?? "N/A"}");
            sb.AppendLine($"Coins on Balance: {offerDetails.CoinsOnBalance ?? "N/A"}");
            sb.AppendLine($"Web App: {offerDetails.WebApp ?? "N/A"}");
            
            // Pozostałe pola tylko jeśli nie są puste
            if (!string.IsNullOrEmpty(offerDetails.Edition))
                sb.AppendLine($"Edition Type: {offerDetails.Edition}");
            if (!string.IsNullOrEmpty(offerDetails.RatingFC25))
                sb.AppendLine($"Rating: {offerDetails.RatingFC25}");
            
            sb.AppendLine("========================================");
            
            var result = sb.ToString().Trim();
            _logger.LogInformation("Generated FC 25 Description section: {Result}", result);
            
            return result;
        }
        
        public override string FormatTitle(OfferDetails offerDetails)
        {
            if (offerDetails == null)
            {
                _logger.LogWarning("OfferDetails is null in FC25 title formatter");
                return string.Empty;
            }

            var title = base.FormatTitle(offerDetails);
            
            // Dodaj dodatkowe informacje do tytułu jeśli są dostępne
            if (!string.IsNullOrEmpty(offerDetails.RatingFC25))
            {
                title = $"[Rating {offerDetails.RatingFC25}] {title}";
            }
            
            return title;
        }
    }
} 