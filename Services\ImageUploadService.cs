#nullable disable
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ImgurUploader;
using ShopBot.Config;
using ShopBot.Services;
using ShopBot.Services.Extensions;
using Microsoft.Extensions.Logging;

namespace ShopBot.Services
{
    public class ImageUploadResult
    {
        public bool Success { get; set; }
        public string? OriginalUrl { get; set; }
        public string? ImageUrl { get; set; }
        public string? ErrorMessage { get; set; }
    }

    public class ImageUploadService : IImageUploadService
    {
        private readonly ImgurService _imgurService;
        private readonly ImgurTokenManager _tokenManager;
        private readonly DropboxService _dropboxService;
        private readonly AppSettings _appSettings;
        private readonly ILogger<ImageUploadService> _logger;
        private const int DelayBetweenUploads = 1000; // 1 sekunda między uploadami

        public ImageUploadService(ImgurService imgurService, ImgurTokenManager tokenManager, AppSettings appSettings, DropboxService dropboxService = null, ILogger<ImageUploadService> logger = null)
        {
            _imgurService = imgurService;
            _tokenManager = tokenManager;
            _appSettings = appSettings;
            _dropboxService = dropboxService;
            _logger = logger ?? Microsoft.Extensions.Logging.Abstractions.NullLogger<ImageUploadService>.Instance;
        }

        public async Task InitializeAsync()
        {
            if (_appSettings.Imgur.Enabled)
            {
                _logger.LogInformation("[ImageUpload] Inicjalizacja Imgur...");
                await _tokenManager.InitializeAsync();
                _imgurService.SetAccessToken(_tokenManager.AccessToken);
                _logger.LogInformation("[ImageUpload] ✅ Imgur zainicjalizowany");
            }
            else
            {
                _logger.LogInformation("[ImageUpload] ⚠️ Imgur wyłączony w konfiguracji - pomijam inicjalizację");
            }

            if (_appSettings.Dropbox.Enabled)
            {
                _logger.LogInformation("[ImageUpload] ✅ Dropbox jest włączony - token manager będzie zainicjalizowany przez DI");
            }
            else
            {
                _logger.LogInformation("[ImageUpload] ⚠️ Dropbox wyłączony w konfiguracji");
            }
        }

        public async Task<List<ImageUploadResult>> UploadMultipleImagesAsync(List<string> imageUrls, bool useDropbox = true)
        {
            if (!imageUrls.Any())
                return new List<ImageUploadResult>();

            _logger.LogImageUploadStart(imageUrls.Count, useDropbox);

            // SEKWENCYJNY upload zamiast równoległego - zapobiega rate limitom Dropbox
            var results = new List<ImageUploadResult>();

            for (int i = 0; i < imageUrls.Count; i++)
            {
                var imageUrl = imageUrls[i];

                try
                {
                    _logger.LogImageUploadProgress(imageUrl);
                    var result = await UploadImageAsync(imageUrl, useDropbox);

                    if (result.Success)
                    {
                        _logger.LogImageUploadSuccess(imageUrl);
                    }
                    else
                    {
                        _logger.LogImageUploadFailure(imageUrl, result.ErrorMessage);
                    }

                    results.Add(result);

                    // Opóźnienie między uploadami (zapobiega rate limitom)
                    if (i < imageUrls.Count - 1) // Nie czekaj po ostatnim uploadzie
                    {
                        _logger.LogDebug("[Image Upload] Waiting {DelayMs}ms before next upload to prevent rate limits", DelayBetweenUploads);
                        await Task.Delay(DelayBetweenUploads);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "[Image Upload] 💥 Wyjątek podczas uploadu {ImageUrl}", imageUrl);
                    results.Add(new ImageUploadResult
                    {
                        Success = false,
                        OriginalUrl = imageUrl,
                        ErrorMessage = ex.Message
                    });
                }
            }
            var successful = results.Count(r => r.Success);

            _logger.LogImageUploadSummary(successful, imageUrls.Count);

            return results.ToList();
        }

        public async Task<List<ImageUploadResult>> UploadMultipleImagesForG2G(List<string> imageUrls)
        {
            _logger.LogInformation("[Image Upload] Rozpoczynam upload {Count} obrazów do Dropbox dla G2G", imageUrls.Count);
            return await UploadMultipleImagesAsync(imageUrls, useDropbox: true);
        }

        private async Task<ImageUploadResult> UploadImageAsync(string imageUrl, bool useDropbox = true)
        {
            if (useDropbox && _dropboxService != null)
            {
                _logger.LogDebug("[Image Upload] Używam Dropbox dla: {ImageUrl}", imageUrl);
                var dropboxResult = await _dropboxService.UploadImageFromUrlAsync(imageUrl);

                return new ImageUploadResult
                {
                    Success = dropboxResult.Success,
                    OriginalUrl = imageUrl,
                    ImageUrl = dropboxResult.DirectUrl,
                    ErrorMessage = dropboxResult.Success ? null : dropboxResult.ErrorMessage
                };
            }
            else
            {
                _logger.LogDebug("[Image Upload] Używam Imgur dla: {ImageUrl}", imageUrl);
                await _tokenManager.EnsureValidTokenAsync();
                _imgurService.SetAccessToken(_tokenManager.AccessToken);

                var imgurResult = await _imgurService.UploadImageFromUrlAsync(imageUrl);

                return new ImageUploadResult
                {
                    Success = imgurResult.Success,
                    OriginalUrl = imageUrl,
                    ImageUrl = imgurResult.ImgurUrl,
                    ErrorMessage = imgurResult.Success ? null : imgurResult.ErrorMessage
                };
            }
        }

        // Implementacja brakujących metod z interfejsu IImageUploadService
        async Task<ImageUploadResult> IImageUploadService.UploadImageAsync(string imageUrl)
        {
            return await UploadImageAsync(imageUrl, useDropbox: true);
        }

        async Task<List<ImageUploadResult>> IImageUploadService.UploadMultipleImagesAsync(List<string> imageUrls)
        {
            return await UploadMultipleImagesAsync(imageUrls, useDropbox: true);
        }

        public async Task<bool> IsServiceAvailable()
        {
            try
            {
                return _dropboxService != null || (_imgurService != null && _tokenManager != null);
            }
            catch
            {
                return false;
            }
        }

        async Task<UploadStatistics> IImageUploadService.GetUploadStatistics()
        {
            return new UploadStatistics
            {
                TotalUploads = 0,      // TODO: Implementować licznik
                SuccessfulUploads = 0, // TODO: Implementować licznik
                FailedUploads = 0      // TODO: Implementować licznik
            };
        }
    }
}
