using System;

namespace ShopBot.Exceptions
{
    /// <summary>
    /// Bazowy wyjątek dla wszystkich błędów specyficznych dla aplikacji ShopBot
    /// </summary>
    public class ShopBotException : Exception
    {
        public string ErrorCode { get; }
        public string Component { get; }

        public ShopBotException(string message, string errorCode = "SHOPBOT_ERROR", string component = "Unknown") 
            : base(message)
        {
            ErrorCode = errorCode;
            Component = component;
        }

        public ShopBotException(string message, Exception innerException, string errorCode = "SHOPBOT_ERROR", string component = "Unknown") 
            : base(message, innerException)
        {
            ErrorCode = errorCode;
            Component = component;
        }

        public override string ToString()
        {
            return $"[{Component}] {ErrorCode}: {Message}";
        }
    }

    /// <summary>
    /// Wyjątek związany z błędami konfiguracji
    /// </summary>
    public class ConfigurationException : ShopBotException
    {
        public ConfigurationException(string message) 
            : base(message, "CONFIG_ERROR", "Configuration")
        {
        }

        public ConfigurationException(string message, Exception innerException) 
            : base(message, innerException, "CONFIG_ERROR", "Configuration")
        {
        }
    }

    /// <summary>
    /// Wyjątek związany z błędami parsowania
    /// </summary>
    public class ParsingException : ShopBotException
    {
        public string SourceData { get; }

        public ParsingException(string message, string sourceData = "") 
            : base(message, "PARSING_ERROR", "Parser")
        {
            SourceData = sourceData;
        }

        public ParsingException(string message, Exception innerException, string sourceData = "") 
            : base(message, innerException, "PARSING_ERROR", "Parser")
        {
            SourceData = sourceData;
        }
    }

    /// <summary>
    /// Wyjątek związany z błędami API G2G
    /// </summary>
    public class G2GApiException : ShopBotException
    {
        public int? StatusCode { get; }
        public string ApiEndpoint { get; }

        public G2GApiException(string message, string apiEndpoint = "", int? statusCode = null) 
            : base(message, "G2G_API_ERROR", "G2G")
        {
            ApiEndpoint = apiEndpoint;
            StatusCode = statusCode;
        }

        public G2GApiException(string message, Exception innerException, string apiEndpoint = "", int? statusCode = null) 
            : base(message, innerException, "G2G_API_ERROR", "G2G")
        {
            ApiEndpoint = apiEndpoint;
            StatusCode = statusCode;
        }
    }

    /// <summary>
    /// Wyjątek związany z błędami FunPay
    /// </summary>
    public class FunPayException : ShopBotException
    {
        public string OfferId { get; }

        public FunPayException(string message, string offerId = "") 
            : base(message, "FUNPAY_ERROR", "FunPay")
        {
            OfferId = offerId;
        }

        public FunPayException(string message, Exception innerException, string offerId = "") 
            : base(message, innerException, "FUNPAY_ERROR", "FunPay")
        {
            OfferId = offerId;
        }
    }

    /// <summary>
    /// Wyjątek związany z błędami usuwania ofert
    /// </summary>
    public class OfferDeletionException : ShopBotException
    {
        public string OfferId { get; }
        public TimeSpan TimeoutDuration { get; }

        public OfferDeletionException(string message, string offerId = "", TimeSpan? timeoutDuration = null) 
            : base(message, "OFFER_DELETION_ERROR", "DeleteService")
        {
            OfferId = offerId;
            TimeoutDuration = timeoutDuration ?? TimeSpan.Zero;
        }

        public OfferDeletionException(string message, Exception innerException, string offerId = "", TimeSpan? timeoutDuration = null) 
            : base(message, innerException, "OFFER_DELETION_ERROR", "DeleteService")
        {
            OfferId = offerId;
            TimeoutDuration = timeoutDuration ?? TimeSpan.Zero;
        }
    }

    /// <summary>
    /// Wyjątek związany z błędami uploadu obrazów
    /// </summary>
    public class ImageUploadException : ShopBotException
    {
        public string ImageUrl { get; }

        public ImageUploadException(string message, string imageUrl = "") 
            : base(message, "IMAGE_UPLOAD_ERROR", "ImageUpload")
        {
            ImageUrl = imageUrl;
        }

        public ImageUploadException(string message, Exception innerException, string imageUrl = "") 
            : base(message, innerException, "IMAGE_UPLOAD_ERROR", "ImageUpload")
        {
            ImageUrl = imageUrl;
        }
    }

    /// <summary>
    /// Wyjątek związany z przekroczeniem limitów
    /// </summary>
    public class LimitExceededException : ShopBotException
    {
        public int CurrentValue { get; }
        public int MaxValue { get; }

        public LimitExceededException(string message, int currentValue = 0, int maxValue = 0) 
            : base(message, "LIMIT_EXCEEDED", "LimitChecker")
        {
            CurrentValue = currentValue;
            MaxValue = maxValue;
        }

        public LimitExceededException(string message, Exception innerException, int currentValue = 0, int maxValue = 0) 
            : base(message, innerException, "LIMIT_EXCEEDED", "LimitChecker")
        {
            CurrentValue = currentValue;
            MaxValue = maxValue;
        }
    }
}
