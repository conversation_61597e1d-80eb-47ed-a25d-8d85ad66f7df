using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using ShopBot;
using System;

namespace ShopBot.Services.Formatters
{
    public class DescriptionFormatterFactory
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<DescriptionFormatterFactory> _logger;
        
        public DescriptionFormatterFactory(IServiceProvider serviceProvider, ILogger<DescriptionFormatterFactory> logger = null)
        {
            _serviceProvider = serviceProvider;
            _logger = logger ?? NullLogger<DescriptionFormatterFactory>.Instance;
        }
        
        public IDescriptionFormatter GetFormatter(string gameName)
        {
            _logger.LogInformation("Getting formatter for game: {GameName}", gameName);
            
            if (gameName == null)
            {
                _logger.LogInformation("Using default formatter for null game name");
                return _serviceProvider.GetRequiredService<DefaultDescriptionFormatter>();
            }
            
            return gameName.ToLower() switch
            {
                "fc 25" => _serviceProvider.GetRequiredService<FC25DescriptionFormatter>(),
                "mobile legends" => _serviceProvider.GetRequiredService<MobileLegendsDescriptionFormatter>(),
                "raid: shadow legend" => _serviceProvider.GetRequiredService<RaidShadowLegendsDescriptionFormatter>(),
                "raid: shadow legends" => _serviceProvider.GetRequiredService<RaidShadowLegendsDescriptionFormatter>(),
                "raidshadowlegend" => _serviceProvider.GetRequiredService<RaidShadowLegendsDescriptionFormatter>(),
                "raidshadowlegends" => _serviceProvider.GetRequiredService<RaidShadowLegendsDescriptionFormatter>(),
                _ => _serviceProvider.GetRequiredService<DefaultDescriptionFormatter>()
            };
        }
    }
} 