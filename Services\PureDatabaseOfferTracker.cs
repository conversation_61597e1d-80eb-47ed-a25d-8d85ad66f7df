using Microsoft.Extensions.Logging;
using ShopBot.Services.Interfaces;
using ShopBot.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;

namespace ShopBot.Services
{
    /// <summary>
    /// Pure Database Offer Tracker - 100% database-based solution
    /// No JSON fallback - relies entirely on MySQL database
    /// Optimized for performance and rich data storage
    /// </summary>
    public class PureDatabaseOfferTracker : IOfferTracker
    {
        private readonly IShopBotRepository _repository;
        private readonly ILogger<PureDatabaseOfferTracker> _logger;
        private readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);

        public PureDatabaseOfferTracker(
            IShopBotRepository repository,
            ILogger<PureDatabaseOfferTracker> logger)
        {
            _repository = repository;
            _logger = logger;
        }

        // Asynchronous methods (primary interface)
        public async Task<bool> WasOfferProcessedAsync(string funPayOfferId)
        {
            try
            {
                var offer = await _repository.Offers.GetByFunPayIdAsync(funPayOfferId);
                var wasProcessed = offer != null;
                
                _logger.LogDebug("[PureDB] 🔍 Offer {OfferId} processed: {WasProcessed}", 
                    funPayOfferId, wasProcessed);
                
                return wasProcessed;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[PureDB] ❌ Error checking if offer {OfferId} was processed", funPayOfferId);
                throw; // Re-throw to handle at higher level
            }
        }

        public async Task AddProcessedOfferAsync(string funPayOfferId, string gameName)
        {
            try
            {
                // Check if offer already exists
                var existingOffer = await _repository.Offers.GetByFunPayIdAsync(funPayOfferId);
                if (existingOffer != null)
                {
                    _logger.LogDebug("[PureDB] ⚠️ Offer {OfferId} already exists", funPayOfferId);
                    return;
                }

                // Create new offer record with rich data
                var offer = new Offer
                {
                    FunPayOfferId = funPayOfferId,
                    GameName = gameName,
                    Title = "Processing...",
                    Status = "Pending",
                    ProcessedAt = DateTime.UtcNow,
                    OriginalPrice = 0,
                    ConvertedPrice = 0,
                    Currency = "USD",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                await _repository.Offers.CreateAsync(offer);
                _logger.LogInformation("[PureDB] ✅ Added offer {OfferId} to database", funPayOfferId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[PureDB] ❌ Error adding offer {OfferId}", funPayOfferId);
                throw;
            }
        }

        public async Task<List<string>> GetAllOfferIdsAsync()
        {
            try
            {
                var offers = await _repository.Offers.GetAllAsync();
                var offerIds = offers.Select(o => o.FunPayOfferId).ToList();
                
                _logger.LogDebug("[PureDB] 📊 Retrieved {Count} offer IDs", offerIds.Count);
                return offerIds;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[PureDB] ❌ Error retrieving offer IDs");
                throw;
            }
        }

        public async Task<DateTime> GetLastProcessingTimeAsync()
        {
            try
            {
                var offers = await _repository.Offers.GetAllAsync();
                if (!offers.Any())
                {
                    return DateTime.MinValue;
                }

                var lastProcessingTime = offers.Max(o => o.ProcessedAt);
                _logger.LogDebug("[PureDB] 📅 Last processing time: {LastTime}", lastProcessingTime);
                
                return lastProcessingTime;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[PureDB] ❌ Error getting last processing time");
                throw;
            }
        }

        public async Task<bool> TryProcessOfferAsync(string funPayOfferId, string gameName)
        {
            await _semaphore.WaitAsync();
            try
            {
                var wasProcessed = await WasOfferProcessedAsync(funPayOfferId);
                if (wasProcessed)
                {
                    _logger.LogDebug("[PureDB] ⚠️ Offer {OfferId} already processed", funPayOfferId);
                    return false;
                }

                await AddProcessedOfferAsync(funPayOfferId, gameName);
                _logger.LogInformation("[PureDB] ✅ Successfully processed offer {OfferId}", funPayOfferId);
                return true;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        public async Task SynchronizeWithActiveOffersAsync(ISet<string> verifiedOffers)
        {
            try
            {
                _logger.LogInformation("[PureDB] 🔄 Synchronizing with {Count} verified offers", verifiedOffers.Count);

                var allOffers = await _repository.Offers.GetAllAsync();
                var currentOfferIds = allOffers.Select(o => o.FunPayOfferId).ToHashSet();

                // Find inactive offers (in database but not in verified set)
                var inactiveOffers = currentOfferIds.Except(verifiedOffers).ToList();
                
                if (inactiveOffers.Any())
                {
                    _logger.LogInformation("[PureDB] 🗑️ Found {Count} inactive offers to remove", inactiveOffers.Count);
                    
                    foreach (var inactiveOfferId in inactiveOffers)
                    {
                        var offer = await _repository.Offers.GetByFunPayIdAsync(inactiveOfferId);
                        if (offer != null)
                        {
                            // Soft delete - mark as inactive instead of hard delete
                            offer.Status = "Inactive";
                            offer.UpdatedAt = DateTime.UtcNow;
                            await _repository.Offers.UpdateAsync(offer);
                            
                            _logger.LogDebug("[PureDB] 🗑️ Marked offer as inactive: {OfferId}", inactiveOfferId);
                        }
                    }
                }

                // Add new verified offers that aren't in database
                var newOffers = verifiedOffers.Except(currentOfferIds).ToList();
                if (newOffers.Any())
                {
                    _logger.LogInformation("[PureDB] ➕ Found {Count} new offers to add", newOffers.Count);
                    
                    foreach (var newOfferId in newOffers)
                    {
                        await AddProcessedOfferAsync(newOfferId, "Unknown"); // Game will be updated later
                    }
                }

                await _repository.SaveChangesAsync();
                _logger.LogInformation("[PureDB] ✅ Synchronization completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[PureDB] ❌ Error during synchronization");
                throw;
            }
        }

        public async Task<int> GetProcessedOffersCountAsync()
        {
            try
            {
                var offers = await _repository.Offers.GetAllAsync();
                var count = offers.Count(o => o.Status != "Inactive");
                
                _logger.LogDebug("[PureDB] 📊 Active processed offers: {Count}", count);
                return count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[PureDB] ❌ Error getting processed offers count");
                throw;
            }
        }

        public async Task<Dictionary<string, int>> GetOfferCountsByGameAsync()
        {
            try
            {
                var offerCounts = await _repository.Offers.GetOfferCountsByGameAsync();
                
                _logger.LogDebug("[PureDB] 📊 Offer counts by game: {Counts}", 
                    string.Join(", ", offerCounts.Select(kv => $"{kv.Key}: {kv.Value}")));
                
                return offerCounts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[PureDB] ❌ Error getting offer counts by game");
                throw;
            }
        }

        public async Task MigrateFromJsonToDatabaseAsync()
        {
            _logger.LogInformation("[PureDB] ℹ️ Pure database mode - no JSON migration needed");
            // No-op in pure database mode
        }

        // Enhanced status management (stub implementations for compatibility)
        public async Task MarkAsPublishedAsync(string funPayOfferId, string g2gOfferId)
        {
            _logger.LogInformation("[PureDB] ℹ️ MarkAsPublishedAsync not implemented in PureDatabaseOfferTracker");
        }

        public async Task MarkAsFailedAsync(string funPayOfferId, string reason)
        {
            _logger.LogInformation("[PureDB] ℹ️ MarkAsFailedAsync not implemented in PureDatabaseOfferTracker");
        }

        public async Task MarkAsRejectedAsync(string funPayOfferId, string reason)
        {
            _logger.LogInformation("[PureDB] ℹ️ MarkAsRejectedAsync not implemented in PureDatabaseOfferTracker");
        }

        public async Task MarkAsLimitReachedAsync(string funPayOfferId)
        {
            _logger.LogInformation("[PureDB] ℹ️ MarkAsLimitReachedAsync not implemented in PureDatabaseOfferTracker");
        }

        public async Task<Dictionary<string, int>> GetStatusCountsAsync()
        {
            var offers = await _repository.Offers.GetAllAsync();
            return offers.GroupBy(o => o.Status).ToDictionary(g => g.Key, g => g.Count());
        }

        public void ClearSessionCache()
        {
            _logger.LogInformation("[PureDB] ℹ️ No session cache in PureDatabaseOfferTracker");
        }

        // Synchronous methods (backward compatibility - delegate to async)
        public bool WasOfferProcessed(string funPayOfferId)
        {
            try
            {
                return WasOfferProcessedAsync(funPayOfferId).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[PureDB] ❌ Error in sync WasOfferProcessed for {OfferId}", funPayOfferId);
                throw;
            }
        }

        public void AddProcessedOffer(string funPayOfferId)
        {
            try
            {
                AddProcessedOfferAsync(funPayOfferId, "Unknown").GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[PureDB] ❌ Error in sync AddProcessedOffer for {OfferId}", funPayOfferId);
                throw;
            }
        }

        public List<string> GetAllOfferIds()
        {
            try
            {
                return GetAllOfferIdsAsync().GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[PureDB] ❌ Error in sync GetAllOfferIds");
                throw;
            }
        }

        public DateTime GetLastProcessingTime()
        {
            try
            {
                return GetLastProcessingTimeAsync().GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[PureDB] ❌ Error in sync GetLastProcessingTime");
                throw;
            }
        }

        public void SynchronizeWithActiveOffers(ISet<string> verifiedOffers)
        {
            try
            {
                SynchronizeWithActiveOffersAsync(verifiedOffers).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[PureDB] ❌ Error in sync SynchronizeWithActiveOffers");
                throw;
            }
        }

        public void Dispose()
        {
            _semaphore?.Dispose();
        }
    }
}
