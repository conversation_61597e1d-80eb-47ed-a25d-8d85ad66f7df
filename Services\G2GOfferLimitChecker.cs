using System;
using System.Threading.Tasks;
using Microsoft.Playwright;
using System.Text.RegularExpressions;

namespace ShopBot.Services
{
    public class G2GOfferLimitChecker
    {
        private const int MAX_OFFERS_LIMIT = 1000; // TYMCZASOWO ZWIĘKSZONE DLA TESTU
        private readonly IBrowser _browser;
        private readonly IBrowserContext _context;
        private bool _isLimitReached = false;

        public G2GOfferLimitChecker(IBrowser browser, IBrowserContext context)
        {
            _browser = browser;
            _context = context;
        }

        public bool IsLimitReached => _isLimitReached;

        public async Task<bool> CheckOfferLimit()
        {
            try
            {
                // Sprawdź liczbę ofert z lokalnego pliku
                var g2gOffers = G2GOfferTracker.LoadAllOffers();
                var currentOffersCount = g2gOffers.Count;

                if (currentOffersCount >= MAX_OFFERS_LIMIT)
                {
                    _isLimitReached = true;
                    Console.WriteLine($"[G2G] Osiągnięto limit ofert: {currentOffersCount}/{MAX_OFFERS_LIMIT}");
                    return true;
                }

                // Spróbuj dodać testową ofertę przez API, aby zweryfikować limit
                var page = await _context.NewPageAsync();
                try
                {
                    await page.GotoAsync("https://www.g2g.com/order/sellNow");
                    var content = await page.ContentAsync();
                    
                    if (content.Contains("You have exceeded the maximum number"))
                    {
                        _isLimitReached = true;
                        Console.WriteLine($"[G2G] API potwierdza przekroczenie limitu ofert");
                        return true;
                    }

                    _isLimitReached = false;
                    Console.WriteLine($"[G2G] Aktualna liczba ofert: {currentOffersCount}/{MAX_OFFERS_LIMIT}");
                    return false;
                }
                finally
                {
                    await page.CloseAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[G2G] Błąd podczas sprawdzania limitu ofert: {ex.Message}");
                // W przypadku błędu zakładamy, że limit nie został osiągnięty
                return false;
            }
        }
    }
} 