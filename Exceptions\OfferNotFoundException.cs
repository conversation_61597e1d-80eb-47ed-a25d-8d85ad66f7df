using System;

namespace ShopBot.Exceptions
{
    /// <summary>
    /// Wyjątek rzucany gdy oferta nie została znaleziona na FunPay (została usunięta, wygasła lub nigdy nie istniała).
    /// </summary>
    public class OfferNotFoundException : Exception
    {
        public string? OfferId { get; }

        public OfferNotFoundException() : base()
        {
        }

        public OfferNotFoundException(string message) : base(message)
        {
        }

        public OfferNotFoundException(string message, Exception innerException) : base(message, innerException)
        {
        }

        public OfferNotFoundException(string message, string offerId) : base(message)
        {
            OfferId = offerId;
        }
    }
}
