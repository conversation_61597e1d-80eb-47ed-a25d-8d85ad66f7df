#nullable disable
using System;
using System.IO;
using Newtonsoft.Json;

namespace ShopBot.Config
{
    public class AppSettings
    {
        public ApplicationSettings Application { get; set; } = new ApplicationSettings();
        public TimeoutSettings Timeouts { get; set; } = new TimeoutSettings();
        public RetrySettings Retry { get; set; } = new RetrySettings();
        public LoggingSettings Logging { get; set; } = new LoggingSettings();
        public FeatureSettings Features { get; set; } = new FeatureSettings();
        public PricingSettings Pricing { get; set; } = new PricingSettings();
        public DropboxSettings Dropbox { get; set; } = new DropboxSettings();
        public ImgurSettings Imgur { get; set; } = new ImgurSettings();
        public DatabaseSettings Database { get; set; } = new DatabaseSettings();

        private static AppSettings? _instance;
        private static readonly object _lock = new object();

        public static AppSettings Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = LoadFromFile();
                        }
                    }
                }
                return _instance;
            }
        }

        public static AppSettings LoadFromFile(string filePath = "appsettings.json")
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    Console.WriteLine($"[AppSettings] ⚠️ Plik {filePath} nie istnieje, używam domyślnych ustawień");
                    return new AppSettings();
                }

                var json = File.ReadAllText(filePath);
                var settings = JsonConvert.DeserializeObject<AppSettings>(json) ?? new AppSettings();
                
                Console.WriteLine($"[AppSettings] ✅ Załadowano ustawienia z {filePath}");
                return settings;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[AppSettings] ❌ Błąd podczas ładowania {filePath}: {ex.Message}");
                Console.WriteLine("[AppSettings] Używam domyślnych ustawień");
                return new AppSettings();
            }
        }

        public void SaveToFile(string filePath = "appsettings.json")
        {
            try
            {
                var json = JsonConvert.SerializeObject(this, Formatting.Indented);
                File.WriteAllText(filePath, json);
                Console.WriteLine($"[AppSettings] ✅ Zapisano ustawienia do {filePath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[AppSettings] ❌ Błąd podczas zapisywania {filePath}: {ex.Message}");
            }
        }
    }

    public class ApplicationSettings
    {
        public string BrowserId { get; set; } = "7bd614cc-90c2-4607-902a-66cedded3494";
        public int SyncIntervalMinutes { get; set; } = 15;
        public int CleanupIntervalHours { get; set; } = 24;
        public int MaxWorkerThreads { get; set; } = 20;
        public int MaxSyncWorkerThreads { get; set; } = 3;
    }

    public class TimeoutSettings
    {
        public int DeleteOfferTimeoutSeconds { get; set; } = 90;
        public int DeleteOfferCheckIntervalSeconds { get; set; } = 3;
        public int PageLoadTimeoutSeconds { get; set; } = 30;
        public int ElementWaitTimeoutSeconds { get; set; } = 15;
        public int PublishOfferTimeoutSeconds { get; set; } = 30;
        public int GameSelectionTimeoutSeconds { get; set; } = 30;
        public int FormFillDelayMs { get; set; } = 200;
        public int GameProcessDelayMs { get; set; } = 8000;
        public int DropdownSelectionDelayMs { get; set; } = 500;
        public int PriceSetDelayMs { get; set; } = 300;
        public int KeyboardDelayMs { get; set; } = 100;
        public int FunPayDetailsIntervalMs { get; set; } = 500;
    }

    public class RetrySettings
    {
        public int MaxRetryAttempts { get; set; } = 3;
        public int RetryDelaySeconds { get; set; } = 5;
        public bool ExponentialBackoff { get; set; } = true;
    }

    public class LoggingSettings
    {
        public LogLevelSettings LogLevel { get; set; } = new LogLevelSettings();
        public ConsoleSettings Console { get; set; } = new ConsoleSettings();
    }

    public class LogLevelSettings
    {
        public string Default { get; set; } = "Information";
        public string ShopBot { get; set; } = "Debug";
        public string Microsoft { get; set; } = "Warning";
    }

    public class ConsoleSettings
    {
        public bool IncludeScopes { get; set; } = false;
        public string TimestampFormat { get; set; } = "HH:mm:ss";
    }

    public class FeatureSettings
    {
        public bool EnableDetailedLogging { get; set; } = true;
        public bool EnablePerformanceMetrics { get; set; } = false;
        public bool EnableConfigValidation { get; set; } = true;
        public bool EnableServiceValidation { get; set; } = true;
        public bool EnableAutoRetry { get; set; } = true;
        public bool EnableStartupTests { get; set; } = false;
    }

    public class PricingSettings
    {
        public decimal DefaultMarginPercent { get; set; } = 25.0m;
        public decimal MinimumPrice { get; set; } = 1.0m;
        public decimal MaximumPrice { get; set; } = 1000.0m;
        public ProgressiveMarginsSettings ProgressiveMargins { get; set; } = new ProgressiveMarginsSettings();
    }

    public class ProgressiveMarginsSettings
    {
        public decimal Under30 { get; set; } = 40.0m;
        public decimal From30To50 { get; set; } = 35.0m;
        public decimal From50To300 { get; set; } = 30.0m;
        public decimal Above300 { get; set; } = 25.0m;
    }

    public class DropboxSettings
    {
        public string ClientId { get; set; } = string.Empty;
        public string ClientSecret { get; set; } = string.Empty;
        public string AccessToken { get; set; } = string.Empty;
        public string RefreshToken { get; set; } = string.Empty;
        public bool Enabled { get; set; } = true;
    }

    public class ImgurSettings
    {
        public string ClientId { get; set; } = "your_imgur_client_id_here";
        public string ClientSecret { get; set; } = "your_imgur_client_secret_here";
        public string AccessToken { get; set; } = string.Empty;
        public bool Enabled { get; set; } = false;
    }

    public class DatabaseSettings
    {
        public string ConnectionString { get; set; } = "Server=localhost;Database=shopbotdb;Uid=root;Pwd=**********;";
        public bool EnableMigrations { get; set; } = true;
        public int CommandTimeoutSeconds { get; set; } = 30;
    }
}
