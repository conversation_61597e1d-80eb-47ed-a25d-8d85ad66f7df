{"TotalOffers": 550, "AcceptedOffers": 69, "RejectedOffers": 481, "RejectionReasons": {"Cena powyzej maximum (352,73 > 99)": 2, "Cena ponizej minimum (1,14 < 15)": 4, "Cena ponizej minimum (0,28 < 15)": 1, "Cena ponizej minimum (0,10 < 15)": 2, "Cena ponizej minimum (5,88 < 15)": 3, "Cena ponizej minimum (5,32 < 15)": 3, "Cena ponizej minimum (1,04 < 15)": 1, "Cena ponizej minimum (0,34 < 15)": 1, "Cena ponizej minimum (8,66 < 15)": 3, "Ocena ponizej minimum (0 < 5)": 35, "Cena ponizej minimum (0,29 < 15)": 1, "Cena ponizej minimum (0,46 < 15)": 4, "Cena powyzej maximum (1136,59 > 99)": 1, "Cena powyzej maximum (195,96 > 99)": 6, "Cena ponizej minimum (1,31 < 15)": 14, "Cena ponizej minimum (3,92 < 15)": 7, "Cena powyzej maximum (182,90 > 99)": 8, "Cena ponizej minimum (3,27 < 15)": 4, "Cena powyzej maximum (113,52 > 99)": 1, "Cena ponizej minimum (1,12 < 15)": 1, "Cena ponizej minimum (1,32 < 15)": 1, "Cena ponizej minimum (1,45 < 15)": 1, "Cena ponizej minimum (0,26 < 15)": 3, "Cena ponizej minimum (0,20 < 15)": 4, "Cena powyzej maximum (209,03 > 99)": 4, "Ocena ponizej minimum (4 < 5)": 30, "Cena ponizej minimum (0,57 < 15)": 15, "Cena ponizej minimum (11,76 < 15)": 4, "Cena ponizej minimum (10,45 < 15)": 6, "Cena ponizej minimum (8,49 < 15)": 2, "Cena ponizej minimum (11,10 < 15)": 1, "Cena powyzej maximum (130,64 > 99)": 6, "Cena ponizej minimum (9,65 < 15)": 1, "Cena ponizej minimum (0,23 < 15)": 1, "Cena ponizej minimum (0,22 < 15)": 2, "Cena ponizej minimum (0,21 < 15)": 2, "Duplikat oferty": 32, "Cena ponizej minimum (0,19 < 15)": 1, "Cena ponizej minimum (0,18 < 15)": 1, "Cena ponizej minimum (0,17 < 15)": 1, "Cena ponizej minimum (0,16 < 15)": 1, "Cena ponizej minimum (0,15 < 15)": 1, "Cena ponizej minimum (0,14 < 15)": 1, "Cena ponizej minimum (0,12 < 15)": 1, "Cena ponizej minimum (0,11 < 15)": 1, "Cena ponizej minimum (0,13 < 15)": 1, "Cena ponizej minimum (11,71 < 15)": 1, "Cena ponizej minimum (11,72 < 15)": 1, "Cena ponizej minimum (11,73 < 15)": 1, "Cena ponizej minimum (11,74 < 15)": 1, "Cena ponizej minimum (1,16 < 15)": 1, "Cena ponizej minimum (0,91 < 15)": 2, "Cena ponizej minimum (0,65 < 15)": 2, "Cena ponizej minimum (1,05 < 15)": 8, "Cena ponizej minimum (1,08 < 15)": 1, "Cena powyzej maximum (457,25 > 99)": 1, "Cena ponizej minimum (1,63 < 15)": 2, "Cena ponizej minimum (13,06 < 15)": 10, "Cena ponizej minimum (14,51 < 15)": 2, "Cena ponizej minimum (13,05 < 15)": 1, "Cena powyzej maximum (104,51 > 99)": 4, "Cena powyzej maximum (261,28 > 99)": 5, "Cena powyzej maximum (193,00 > 99)": 1, "Cena powyzej maximum (326,61 > 99)": 4, "Cena ponizej minimum (1,28 < 15)": 1, "Cena ponizej minimum (0,07 < 15)": 1, "Cena ponizej minimum (6,53 < 15)": 6, "Cena powyzej maximum (102,18 > 99)": 1, "Cena powyzej maximum (190,31 > 99)": 1, "Cena ponizej minimum (0,32 < 15)": 1, "Cena ponizej minimum (2,47 < 15)": 3, "Cena powyzej maximum (135,27 > 99)": 1, "Cena powyzej maximum (112,72 > 99)": 1, "Cena ponizej minimum (0,47 < 15)": 3, "Cena ponizej minimum (5,23 < 15)": 4, "Cena ponizej minimum (2,45 < 15)": 1, "Cena ponizej minimum (3,07 < 15)": 1, "Cena ponizej minimum (0,98 < 15)": 5, "Cena ponizej minimum (1,97 < 15)": 1, "Cena ponizej minimum (0,01 < 15)": 2, "Cena powyzej maximum (206,41 > 99)": 1, "Cena ponizej minimum (4,54 < 15)": 1, "Cena ponizej minimum (4,57 < 15)": 2, "Cena ponizej minimum (1,83 < 15)": 1, "Cena ponizej minimum (4,44 < 15)": 2, "Cena ponizej minimum (1,57 < 15)": 1, "Cena ponizej minimum (2,84 < 15)": 1, "Cena ponizej minimum (2,87 < 15)": 1, "Cena powyzej maximum (103,21 > 99)": 2, "Cena powyzej maximum (124,11 > 99)": 1, "Cena powyzej maximum (222,09 > 99)": 1, "Cena ponizej minimum (10,91 < 15)": 1, "Cena ponizej minimum (5,49 < 15)": 1, "Cena powyzej maximum (587,89 > 99)": 2, "Cena powyzej maximum (105,47 > 99)": 1, "Cena powyzej maximum (104,57 > 99)": 1, "Cena powyzej maximum (105,17 > 99)": 2, "Cena powyzej maximum (225,96 > 99)": 1, "Cena powyzej maximum (365,63 > 99)": 1, "Cena powyzej maximum (103,36 > 99)": 1, "Cena powyzej maximum (970,18 > 99)": 1, "Cena powyzej maximum (103,06 > 99)": 2, "Cena powyzej maximum (105,78 > 99)": 2, "Cena powyzej maximum (102,42 > 99)": 1, "Cena powyzej maximum (101,49 > 99)": 1, "Cena powyzej maximum (103,34 > 99)": 1, "Cena powyzej maximum (117,58 > 99)": 2, "Cena powyzej maximum (165,44 > 99)": 1, "Cena powyzej maximum (319,42 > 99)": 2, "Cena ponizej minimum (14,37 < 15)": 1, "Cena powyzej maximum (585,61 > 99)": 1, "Cena ponizej minimum (7,19 < 15)": 3, "Cena powyzej maximum (920,21 > 99)": 1, "Cena powyzej maximum (292,13 > 99)": 1, "Cena powyzej maximum (262,92 > 99)": 2, "Cena powyzej maximum (394,37 > 99)": 1, "Cena powyzej maximum (236,62 > 99)": 3, "Cena powyzej maximum (146,06 > 99)": 1, "Cena powyzej maximum (354,94 > 99)": 1, "Cena powyzej maximum (223,48 > 99)": 2, "Cena powyzej maximum (328,65 > 99)": 1, "Cena powyzej maximum (302,35 > 99)": 1, "Cena powyzej maximum (335,94 > 99)": 1, "Cena powyzej maximum (604,71 > 99)": 1, "Cena powyzej maximum (483,38 > 99)": 1, "Cena powyzej maximum (300,48 > 99)": 1, "Cena powyzej maximum (158,65 > 99)": 1, "Cena ponizej minimum (0,90 < 15)": 1, "Cena ponizej minimum (9,14 < 15)": 3, "Cena powyzej maximum (244,89 > 99)": 1, "Cena powyzej maximum (404,60 > 99)": 1, "Cena ponizej minimum (10,65 < 15)": 2, "Cena ponizej minimum (4,26 < 15)": 2, "Cena powyzej maximum (163,30 > 99)": 1, "Cena powyzej maximum (653,21 > 99)": 3, "Cena ponizej minimum (7,84 < 15)": 1, "Cena powyzej maximum (202,50 > 99)": 2, "Cena ponizej minimum (1,85 < 15)": 1, "Cena ponizej minimum (1,96 < 15)": 1, "Cena powyzej maximum (122,74 > 99)": 1, "Cena powyzej maximum (391,93 > 99)": 6, "Cena powyzej maximum (1306,42 > 99)": 3, "Cena powyzej maximum (340,60 > 99)": 1, "Cena ponizej minimum (1,03 < 15)": 2, "Cena ponizej minimum (2,60 < 15)": 2, "Cena powyzej maximum (143,71 > 99)": 2, "Cena powyzej maximum (1175,78 > 99)": 1, "Cena powyzej maximum (106,47 > 99)": 1, "Cena powyzej maximum (169,83 > 99)": 2, "Cena powyzej maximum (248,22 > 99)": 2, "Cena powyzej maximum (212,95 > 99)": 1, "Cena powyzej maximum (287,25 > 99)": 1, "Cena ponizej minimum (0,33 < 15)": 1, "Cena powyzej maximum (339,67 > 99)": 1, "Ocena ponizej minimum (1 < 5)": 1, "Cena ponizej minimum (4,91 < 15)": 1, "Cena ponizej minimum (8,59 < 15)": 1, "Cena ponizej minimum (6,14 < 15)": 1, "Cena powyzej maximum (211,88 > 99)": 1, "Cena powyzej maximum (254,47 > 99)": 1, "Cena powyzej maximum (339,65 > 99)": 1, "Cena powyzej maximum (392,89 > 99)": 1, "Cena powyzej maximum (228,92 > 99)": 1, "Cena powyzej maximum (424,83 > 99)": 1, "Cena powyzej maximum (190,59 > 99)": 1, "Cena powyzej maximum (275,77 > 99)": 1, "Cena powyzej maximum (137,35 > 99)": 1, "Cena powyzej maximum (126,70 > 99)": 1, "Cena powyzej maximum (175,68 > 99)": 2, "Cena powyzej maximum (148,00 > 99)": 1, "Cena powyzej maximum (318,36 > 99)": 1, "Cena powyzej maximum (532,37 > 99)": 1, "Cena powyzej maximum (286,42 > 99)": 1, "Cena powyzej maximum (165,03 > 99)": 1, "Cena powyzej maximum (169,29 > 99)": 1, "Cena powyzej maximum (329,00 > 99)": 1, "Cena powyzej maximum (431,12 > 99)": 1, "Cena powyzej maximum (111,05 > 99)": 1, "Cena powyzej maximum (338,36 > 99)": 1, "Cena powyzej maximum (313,54 > 99)": 1, "Cena powyzej maximum (849,17 > 99)": 1, "Cena ponizej minimum (0,39 < 15)": 1, "Cena powyzej maximum (266,19 > 99)": 1, "Cena powyzej maximum (532,36 > 99)": 1, "Cena powyzej maximum (291,87 > 99)": 1, "Cena powyzej maximum (522,57 > 99)": 2, "Cena powyzej maximum (613,69 > 99)": 1, "Cena powyzej maximum (212,05 > 99)": 1, "Cena ponizej minimum (12,41 < 15)": 1, "Cena powyzej maximum (600,95 > 99)": 1, "Cena powyzej maximum (393,12 > 99)": 1, "Cena powyzej maximum (150,24 > 99)": 1, "Cena powyzej maximum (231,15 > 99)": 1, "Cena ponizej minimum (2,61 < 15)": 1, "Cena ponizej minimum (4,31 < 15)": 1, "Cena powyzej maximum (149,06 > 99)": 1, "Cena ponizej minimum (5,52 < 15)": 1, "Cena powyzej maximum (561,76 > 99)": 1, "Cena powyzej maximum (141,15 > 99)": 1, "Cena ponizej minimum (0,78 < 15)": 1, "Cena powyzej maximum (783,85 > 99)": 3, "Cena ponizej minimum (0,02 < 15)": 2, "Cena powyzej maximum (159,71 > 99)": 1, "Cena powyzej maximum (258,67 > 99)": 1, "Cena powyzej maximum (306,85 > 99)": 1, "Cena powyzej maximum (418,05 > 99)": 1, "Ocena ponizej minimum (3 < 5)": 1, "Cena powyzej maximum (692,43 > 99)": 1, "Cena powyzej maximum (1045,14 > 99)": 1, "Cena ponizej minimum (9,80 < 15)": 1, "Cena ponizej minimum (1,71 < 15)": 1, "Cena powyzej maximum (235,16 > 99)": 1, "Cena powyzej maximum (249,02 > 99)": 1, "Cena powyzej maximum (914,50 > 99)": 1, "Cena ponizej minimum (7,96 < 15)": 1}, "GenerationTime": "2025-06-17T17:14:27.296212+02:00"}