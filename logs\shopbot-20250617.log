[2025-06-17 12:03:09.961 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-17 12:03:09.990 +02:00 INF] ShopBot.Services.CliCommandHandler: 🧪 Testowanie OfferContentChangeDetector...
[2025-06-17 12:03:09.992 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 🧪 Rozpoczynam testy OfferContentChangeDetector
[2025-06-17 12:03:09.999 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test1
[2025-06-17 12:03:10.002 +02:00 WRN] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚠️ Nie można sparsować nowej ceny: 200.00
[2025-06-17 12:03:10.010 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:03:10.014 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test1
[2025-06-17 12:03:10.017 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test2
[2025-06-17 12:03:10.018 +02:00 WRN] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚠️ Nie można sparsować nowej ceny: 105.00
[2025-06-17 12:03:10.019 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:03:10.021 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test2
[2025-06-17 12:03:10.022 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test3
[2025-06-17 12:03:10.023 +02:00 WRN] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚠️ Nie można sparsować nowej ceny: 100.00
[2025-06-17 12:03:10.025 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo tytułów: 23.53 % (próg: 70.00 %)
[2025-06-17 12:03:10.026 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Wykryto zmianę tytułu w ofercie test3: podobieństwo 23.53 %
[2025-06-17 12:03:10.028 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test4
[2025-06-17 12:03:10.029 +02:00 WRN] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚠️ Nie można sparsować nowej ceny: 100.00
[2025-06-17 12:03:10.030 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo tytułów: 73.91 % (próg: 70.00 %)
[2025-06-17 12:03:10.032 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test4
[2025-06-17 12:03:10.033 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test5
[2025-06-17 12:03:10.034 +02:00 WRN] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚠️ Nie można sparsować nowej ceny: 100.00
[2025-06-17 12:03:10.035 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:03:10.036 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test5
[2025-06-17 12:03:10.037 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test6
[2025-06-17 12:03:10.038 +02:00 WRN] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚠️ Nie można sparsować nowej ceny: invalid_price
[2025-06-17 12:03:10.039 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:03:10.041 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test6
[2025-06-17 12:03:10.044 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 📊 Wyniki testów: 6/7 testów przeszło pomyślnie
[2025-06-17 12:03:10.046 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ❌ Znacząca zmiana ceny: Oczekiwano: True, Otrzymano: False (100% wzrost ceny)
[2025-06-17 12:03:10.047 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana ceny: Oczekiwano: False, Otrzymano: False (5% wzrost ceny)
[2025-06-17 12:03:10.049 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Znacząca zmiana tytułu: Oczekiwano: True, Otrzymano: True (WoW → Dota 2)
[2025-06-17 12:03:10.050 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana tytułu: Oczekiwano: False, Otrzymano: False (dodano 'with extras')
[2025-06-17 12:03:10.052 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Brak zmian: Oczekiwano: False, Otrzymano: False (identyczne dane)
[2025-06-17 12:03:10.053 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieprawidłowa cena: Oczekiwano: False, Otrzymano: False (graceful handling)
[2025-06-17 12:03:10.054 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Progi algorytmu: Próg ceny: 0,15 (oczekiwano: 0,15), Próg tytułu: 0,7 (oczekiwano: 0,7)
[2025-06-17 12:03:10.056 +02:00 WRN] ShopBot.Services.OfferContentChangeDetectorTestRunner: ⚠️ 1 testów nie powiodło się
[2025-06-17 12:03:10.057 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Test change detector completed successfully
[2025-06-17 12:03:46.002 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-17 12:03:46.023 +02:00 INF] ShopBot.Services.CliCommandHandler: 🧪 Testowanie OfferContentChangeDetector...
[2025-06-17 12:03:46.025 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 🧪 Rozpoczynam testy OfferContentChangeDetector
[2025-06-17 12:03:46.031 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test1
[2025-06-17 12:03:46.034 +02:00 WRN] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚠️ Nie można sparsować nowej ceny: 200.00
[2025-06-17 12:03:46.039 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:03:46.042 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test1
[2025-06-17 12:03:46.043 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test2
[2025-06-17 12:03:46.044 +02:00 WRN] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚠️ Nie można sparsować nowej ceny: 105.00
[2025-06-17 12:03:46.045 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:03:46.047 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test2
[2025-06-17 12:03:46.048 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test3
[2025-06-17 12:03:46.049 +02:00 WRN] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚠️ Nie można sparsować nowej ceny: 100.00
[2025-06-17 12:03:46.051 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo tytułów: 23.53 % (próg: 70.00 %)
[2025-06-17 12:03:46.052 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Wykryto zmianę tytułu w ofercie test3: podobieństwo 23.53 %
[2025-06-17 12:03:46.054 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test4
[2025-06-17 12:03:46.055 +02:00 WRN] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚠️ Nie można sparsować nowej ceny: 100.00
[2025-06-17 12:03:46.056 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo tytułów: 73.91 % (próg: 70.00 %)
[2025-06-17 12:03:46.057 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test4
[2025-06-17 12:03:46.058 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test5
[2025-06-17 12:03:46.060 +02:00 WRN] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚠️ Nie można sparsować nowej ceny: 100.00
[2025-06-17 12:03:46.061 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:03:46.062 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test5
[2025-06-17 12:03:46.063 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test6
[2025-06-17 12:03:46.065 +02:00 WRN] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚠️ Nie można sparsować nowej ceny: invalid_price
[2025-06-17 12:03:46.066 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:03:46.067 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test6
[2025-06-17 12:03:46.070 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 📊 Wyniki testów: 6/7 testów przeszło pomyślnie
[2025-06-17 12:03:46.072 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ❌ Znacząca zmiana ceny: Oczekiwano: True, Otrzymano: False (100% wzrost ceny)
[2025-06-17 12:03:46.074 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana ceny: Oczekiwano: False, Otrzymano: False (5% wzrost ceny)
[2025-06-17 12:03:46.075 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Znacząca zmiana tytułu: Oczekiwano: True, Otrzymano: True (WoW → Dota 2)
[2025-06-17 12:03:46.077 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana tytułu: Oczekiwano: False, Otrzymano: False (dodano 'with extras')
[2025-06-17 12:03:46.078 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Brak zmian: Oczekiwano: False, Otrzymano: False (identyczne dane)
[2025-06-17 12:03:46.080 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieprawidłowa cena: Oczekiwano: False, Otrzymano: False (graceful handling)
[2025-06-17 12:03:46.081 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Progi algorytmu: Próg ceny: 0,15 (oczekiwano: 0,15), Próg tytułu: 0,7 (oczekiwano: 0,7)
[2025-06-17 12:03:46.083 +02:00 WRN] ShopBot.Services.OfferContentChangeDetectorTestRunner: ⚠️ 1 testów nie powiodło się
[2025-06-17 12:03:46.084 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Test change detector completed successfully
[2025-06-17 12:05:08.255 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-17 12:05:08.277 +02:00 INF] ShopBot.Services.CliCommandHandler: 🧪 Testowanie OfferContentChangeDetector...
[2025-06-17 12:05:08.280 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 🧪 Rozpoczynam testy OfferContentChangeDetector
[2025-06-17 12:05:08.285 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test1
[2025-06-17 12:05:08.291 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 200.00 (zmiana: 100.00 %)
[2025-06-17 12:05:08.296 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 💰 Wykryto zmianę ceny w ofercie test1: $100 → $200.00 (zmiana: 100.00 %)
[2025-06-17 12:05:08.298 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test2
[2025-06-17 12:05:08.299 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 105.00 (zmiana: 5.00 %)
[2025-06-17 12:05:08.302 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:05:08.304 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test2
[2025-06-17 12:05:08.305 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test3
[2025-06-17 12:05:08.307 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:05:08.308 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo tytułów: 23.53 % (próg: 70.00 %)
[2025-06-17 12:05:08.310 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Wykryto zmianę tytułu w ofercie test3: podobieństwo 23.53 %
[2025-06-17 12:05:08.312 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test4
[2025-06-17 12:05:08.313 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:05:08.315 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo tytułów: 73.91 % (próg: 70.00 %)
[2025-06-17 12:05:08.316 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test4
[2025-06-17 12:05:08.317 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test5
[2025-06-17 12:05:08.319 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:05:08.321 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:05:08.322 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test5
[2025-06-17 12:05:08.324 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test6
[2025-06-17 12:05:08.325 +02:00 WRN] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚠️ Nie można sparsować nowej ceny: 'invalid_price' → ''
[2025-06-17 12:05:08.327 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:05:08.328 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test6
[2025-06-17 12:05:08.331 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 📊 Wyniki testów: 7/7 testów przeszło pomyślnie
[2025-06-17 12:05:08.333 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Znacząca zmiana ceny: Oczekiwano: True, Otrzymano: True (100% wzrost ceny)
[2025-06-17 12:05:08.334 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana ceny: Oczekiwano: False, Otrzymano: False (5% wzrost ceny)
[2025-06-17 12:05:08.336 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Znacząca zmiana tytułu: Oczekiwano: True, Otrzymano: True (WoW → Dota 2)
[2025-06-17 12:05:08.337 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana tytułu: Oczekiwano: False, Otrzymano: False (dodano 'with extras')
[2025-06-17 12:05:08.339 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Brak zmian: Oczekiwano: False, Otrzymano: False (identyczne dane)
[2025-06-17 12:05:08.341 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieprawidłowa cena: Oczekiwano: False, Otrzymano: False (graceful handling)
[2025-06-17 12:05:08.342 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Progi algorytmu: Próg ceny: 0,15 (oczekiwano: 0,15), Próg tytułu: 0,7 (oczekiwano: 0,7)
[2025-06-17 12:05:08.344 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 🎉 Wszystkie testy przeszły pomyślnie!
[2025-06-17 12:05:08.345 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Test change detector completed successfully
[2025-06-17 12:09:05.453 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-17 12:09:05.473 +02:00 INF] ShopBot.Services.CliCommandHandler: 🧪 Testowanie OfferContentChangeDetector...
[2025-06-17 12:09:05.475 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 🧪 Rozpoczynam testy OfferContentChangeDetector
[2025-06-17 12:09:05.480 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test1
[2025-06-17 12:09:05.486 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 200.00 (zmiana: 100.00 %)
[2025-06-17 12:09:05.490 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 💰 Wykryto zmianę ceny w ofercie test1: $100 → $200.00 (zmiana: 100.00 %)
[2025-06-17 12:09:05.492 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test2
[2025-06-17 12:09:05.493 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 105.00 (zmiana: 5.00 %)
[2025-06-17 12:09:05.495 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:09:05.497 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test2
[2025-06-17 12:09:05.498 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test3
[2025-06-17 12:09:05.500 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:09:05.502 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo tytułów: 23.53 % (próg: 70.00 %)
[2025-06-17 12:09:05.503 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Wykryto zmianę tytułu w ofercie test3: podobieństwo 23.53 %
[2025-06-17 12:09:05.505 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test4
[2025-06-17 12:09:05.506 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:09:05.508 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo tytułów: 73.91 % (próg: 70.00 %)
[2025-06-17 12:09:05.509 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test4
[2025-06-17 12:09:05.511 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test5
[2025-06-17 12:09:05.512 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:09:05.514 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:09:05.515 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test5
[2025-06-17 12:09:05.516 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test6
[2025-06-17 12:09:05.518 +02:00 WRN] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚠️ Nie można sparsować nowej ceny: 'invalid_price' → ''
[2025-06-17 12:09:05.519 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:09:05.520 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test6
[2025-06-17 12:09:05.523 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 📊 Wyniki testów: 7/7 testów przeszło pomyślnie
[2025-06-17 12:09:05.525 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Znacząca zmiana ceny: Oczekiwano: True, Otrzymano: True (100% wzrost ceny)
[2025-06-17 12:09:05.526 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana ceny: Oczekiwano: False, Otrzymano: False (5% wzrost ceny)
[2025-06-17 12:09:05.528 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Znacząca zmiana tytułu: Oczekiwano: True, Otrzymano: True (WoW → Dota 2)
[2025-06-17 12:09:05.529 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana tytułu: Oczekiwano: False, Otrzymano: False (dodano 'with extras')
[2025-06-17 12:09:05.530 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Brak zmian: Oczekiwano: False, Otrzymano: False (identyczne dane)
[2025-06-17 12:09:05.532 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieprawidłowa cena: Oczekiwano: False, Otrzymano: False (graceful handling)
[2025-06-17 12:09:05.533 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Progi algorytmu: Próg ceny: 0,15 (oczekiwano: 0,15), Próg tytułu: 0,7 (oczekiwano: 0,7)
[2025-06-17 12:09:05.535 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 🎉 Wszystkie testy przeszły pomyślnie!
[2025-06-17 12:09:07.788 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 6
[2025-06-17 12:09:07.790 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Epic champions = 22
[2025-06-17 12:09:07.791 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Rare champions = 20
[2025-06-17 12:09:07.793 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 51
[2025-06-17 12:09:07.794 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 12:09:07.795 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 12:09:07.796 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 12:09:07.800 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 12:09:07.801 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 12:09:07.829 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 12:09:07.830 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 12:09:07.833 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 12:09:07.834 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 12:09:07.835 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 12:09:07.836 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 12:09:07.837 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 12:09:07.838 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 12:09:07.840 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 12:09:07.841 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 12:09:07.842 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 12:09:07.843 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 12:09:07.844 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 12:09:07.846 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 12:09:07.847 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 52 $</span></span>
[2025-06-17 12:09:07.848 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>57.15 $</span></span>
[2025-06-17 12:09:07.850 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>57.15 $</span></span>
[2025-06-17 12:09:07.851 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>57.15 $</span></span>
[2025-06-17 12:09:07.852 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>52 USDT</span></span>
[2025-06-17 12:09:07.853 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 52
[2025-06-17 12:09:07.854 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 52 USDT
[2025-06-17 12:09:07.859 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 12:09:07.860 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 12:09:07.861 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 12:09:07.862 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 12:09:07.863 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 12:09:07.864 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-17 12:09:07.865 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-17 12:09:07.867 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty ********
[2025-06-17 12:09:07.868 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 53.24 → 52 (zmiana: 2.33 %)
[2025-06-17 12:09:07.869 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚡ Wczesne przerwanie - duża różnica długości: 194 vs 74
[2025-06-17 12:09:07.871 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo tytułów: 0.00 % (próg: 70.00 %)
[2025-06-17 12:09:07.872 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Wykryto zmianę tytułu w ofercie ********: podobieństwo 0.00 %
[2025-06-17 12:09:07.876 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Test change detector completed successfully
[2025-06-17 12:26:39.433 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-17 12:26:39.454 +02:00 INF] ShopBot.Services.CliCommandHandler: 🧪 Testowanie OfferContentChangeDetector...
[2025-06-17 12:26:39.456 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 🧪 Rozpoczynam testy OfferContentChangeDetector
[2025-06-17 12:26:39.462 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test1
[2025-06-17 12:26:39.469 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 200.00 (zmiana: 100.00 %)
[2025-06-17 12:26:39.474 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 💰 Wykryto zmianę ceny w ofercie test1: $100 → $200.00 (zmiana: 100.00 %)
[2025-06-17 12:26:39.476 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test2
[2025-06-17 12:26:39.478 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 108.00 (zmiana: 8.00 %)
[2025-06-17 12:26:39.480 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: 
[2025-06-17 12:26:39.482 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 12:26:39.483 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: 
[2025-06-17 12:26:39.484 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 12:26:39.486 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:26:39.487 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 12:26:39.488 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: got questions? contact me on g2g chat before buying! offers like this go fast – reserve your ideal account now!
[2025-06-17 12:26:39.489 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 12:26:39.490 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: got questions? contact me on g2g chat before buying! offers like this go fast – reserve your ideal account now!
[2025-06-17 12:26:39.491 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:26:39.492 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test2
[2025-06-17 12:26:39.494 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test3
[2025-06-17 12:26:39.495 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:26:39.497 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: 
[2025-06-17 12:26:39.498 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 12:26:39.499 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: 
[2025-06-17 12:26:39.500 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 12:26:39.501 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:26:39.502 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: World of Warcraft Account Level 60
[2025-06-17 12:26:39.503 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: got questions? contact me on g2g chat before buying! offers like this go fast – reserve your ideal account now!
[2025-06-17 12:26:39.504 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Dota 2 Account with Rare Items
[2025-06-17 12:26:39.505 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: got questions? contact me on g2g chat before buying! offers like this go fast – reserve your ideal account now!
[2025-06-17 12:26:39.506 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:26:39.508 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test3
[2025-06-17 12:26:39.510 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test4
[2025-06-17 12:26:39.511 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:26:39.512 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: 
[2025-06-17 12:26:39.513 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 12:26:39.514 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: 
[2025-06-17 12:26:39.516 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 12:26:39.517 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:26:39.517 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: World of Warcraft Account Level 60
[2025-06-17 12:26:39.518 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: got questions? contact me on g2g chat before buying! offers like this go fast – reserve your ideal account now!
[2025-06-17 12:26:39.520 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: World of Warcraft Account Level 60 with extras
[2025-06-17 12:26:39.521 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: got questions? contact me on g2g chat before buying! offers like this go fast – reserve your ideal account now!
[2025-06-17 12:26:39.522 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:26:39.523 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test4
[2025-06-17 12:26:39.525 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test5
[2025-06-17 12:26:39.526 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:26:39.528 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: 
[2025-06-17 12:26:39.529 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 12:26:39.530 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: 
[2025-06-17 12:26:39.531 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 12:26:39.532 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:26:39.533 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 12:26:39.534 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: got questions? contact me on g2g chat before buying! offers like this go fast – reserve your ideal account now!
[2025-06-17 12:26:39.535 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 12:26:39.536 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: got questions? contact me on g2g chat before buying! offers like this go fast – reserve your ideal account now!
[2025-06-17 12:26:39.538 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:26:39.539 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test5
[2025-06-17 12:26:39.540 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test6
[2025-06-17 12:26:39.542 +02:00 WRN] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚠️ Nie można sparsować nowej ceny: 'invalid_price' → ''
[2025-06-17 12:26:39.543 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: 
[2025-06-17 12:26:39.544 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 12:26:39.545 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: 
[2025-06-17 12:26:39.546 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 12:26:39.547 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:26:39.548 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 12:26:39.549 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: got questions? contact me on g2g chat before buying! offers like this go fast – reserve your ideal account now!
[2025-06-17 12:26:39.550 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 12:26:39.551 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: got questions? contact me on g2g chat before buying! offers like this go fast – reserve your ideal account now!
[2025-06-17 12:26:39.552 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:26:39.553 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test6
[2025-06-17 12:26:39.556 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 📊 Wyniki testów: 6/7 testów przeszło pomyślnie
[2025-06-17 12:26:39.557 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Znacząca zmiana ceny: Oczekiwano: True, Otrzymano: True (100% wzrost ceny)
[2025-06-17 12:26:39.559 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana ceny: Oczekiwano: False, Otrzymano: False (8% wzrost ceny)
[2025-06-17 12:26:39.560 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ❌ Znacząca zmiana tytułu: Oczekiwano: True, Otrzymano: False (WoW → Dota 2)
[2025-06-17 12:26:39.562 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana tytułu: Oczekiwano: False, Otrzymano: False (dodano 'with extras')
[2025-06-17 12:26:39.563 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Brak zmian: Oczekiwano: False, Otrzymano: False (identyczne dane)
[2025-06-17 12:26:39.565 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieprawidłowa cena: Oczekiwano: False, Otrzymano: False (graceful handling)
[2025-06-17 12:26:39.566 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Progi algorytmu: Próg ceny: 0,10 (oczekiwano: 0,10), Próg tytułu: 0,7 (oczekiwano: 0,7)
[2025-06-17 12:26:39.568 +02:00 WRN] ShopBot.Services.OfferContentChangeDetectorTestRunner: ⚠️ 1 testów nie powiodło się
[2025-06-17 12:26:39.569 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Test change detector completed successfully
[2025-06-17 12:27:32.881 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-17 12:27:32.907 +02:00 INF] ShopBot.Services.CliCommandHandler: 🧪 Testowanie OfferContentChangeDetector...
[2025-06-17 12:27:32.910 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 🧪 Rozpoczynam testy OfferContentChangeDetector
[2025-06-17 12:27:32.916 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test1
[2025-06-17 12:27:32.924 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 200.00 (zmiana: 100.00 %)
[2025-06-17 12:27:32.929 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 💰 Wykryto zmianę ceny w ofercie test1: $100 → $200.00 (zmiana: 100.00 %)
[2025-06-17 12:27:32.932 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test2
[2025-06-17 12:27:32.933 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 108.00 (zmiana: 8.00 %)
[2025-06-17 12:27:32.937 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:27:32.937 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 12:27:32.938 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: 
[2025-06-17 12:27:32.939 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 12:27:32.941 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: 
[2025-06-17 12:27:32.942 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:27:32.944 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test2
[2025-06-17 12:27:32.945 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test3
[2025-06-17 12:27:32.946 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:27:32.948 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:27:32.949 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: World of Warcraft Account Level 60
[2025-06-17 12:27:32.950 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: 
[2025-06-17 12:27:32.951 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Dota 2 Account with Rare Items
[2025-06-17 12:27:32.952 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: 
[2025-06-17 12:27:32.953 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:27:32.954 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test3
[2025-06-17 12:27:32.955 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test4
[2025-06-17 12:27:32.956 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:27:32.958 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:27:32.959 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: World of Warcraft Account Level 60
[2025-06-17 12:27:32.960 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: 
[2025-06-17 12:27:32.961 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: World of Warcraft Account Level 60 with extras
[2025-06-17 12:27:32.962 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: 
[2025-06-17 12:27:32.963 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:27:32.964 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test4
[2025-06-17 12:27:32.965 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test5
[2025-06-17 12:27:32.967 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:27:32.968 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:27:32.969 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 12:27:32.970 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: 
[2025-06-17 12:27:32.971 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 12:27:32.972 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: 
[2025-06-17 12:27:32.973 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:27:32.974 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test5
[2025-06-17 12:27:32.975 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test6
[2025-06-17 12:27:32.977 +02:00 WRN] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚠️ Nie można sparsować nowej ceny: 'invalid_price' → ''
[2025-06-17 12:27:32.978 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:27:32.979 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 12:27:32.980 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: 
[2025-06-17 12:27:32.981 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 12:27:32.982 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: 
[2025-06-17 12:27:32.983 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:27:32.985 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test6
[2025-06-17 12:27:32.987 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 📊 Wyniki testów: 6/7 testów przeszło pomyślnie
[2025-06-17 12:27:32.988 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Znacząca zmiana ceny: Oczekiwano: True, Otrzymano: True (100% wzrost ceny)
[2025-06-17 12:27:32.989 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana ceny: Oczekiwano: False, Otrzymano: False (8% wzrost ceny)
[2025-06-17 12:27:32.991 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ❌ Znacząca zmiana tytułu: Oczekiwano: True, Otrzymano: False (WoW → Dota 2)
[2025-06-17 12:27:32.992 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana tytułu: Oczekiwano: False, Otrzymano: False (dodano 'with extras')
[2025-06-17 12:27:32.994 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Brak zmian: Oczekiwano: False, Otrzymano: False (identyczne dane)
[2025-06-17 12:27:32.995 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieprawidłowa cena: Oczekiwano: False, Otrzymano: False (graceful handling)
[2025-06-17 12:27:32.997 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Progi algorytmu: Próg ceny: 0,10 (oczekiwano: 0,10), Próg tytułu: 0,7 (oczekiwano: 0,7)
[2025-06-17 12:27:32.998 +02:00 WRN] ShopBot.Services.OfferContentChangeDetectorTestRunner: ⚠️ 1 testów nie powiodło się
[2025-06-17 12:27:32.999 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Test change detector completed successfully
[2025-06-17 12:28:25.958 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-17 12:28:25.980 +02:00 INF] ShopBot.Services.CliCommandHandler: 🧪 Testowanie OfferContentChangeDetector...
[2025-06-17 12:28:25.983 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 🧪 Rozpoczynam testy OfferContentChangeDetector
[2025-06-17 12:28:25.989 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test1
[2025-06-17 12:28:25.996 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 200.00 (zmiana: 100.00 %)
[2025-06-17 12:28:26.002 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 💰 Wykryto zmianę ceny w ofercie test1: $100 → $200.00 (zmiana: 100.00 %)
[2025-06-17 12:28:26.005 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test2
[2025-06-17 12:28:26.006 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 108.00 (zmiana: 8.00 %)
[2025-06-17 12:28:26.009 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:28:26.010 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 12:28:26.011 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: 
[2025-06-17 12:28:26.012 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 12:28:26.013 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: 
[2025-06-17 12:28:26.015 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:28:26.016 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test2
[2025-06-17 12:28:26.018 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test3
[2025-06-17 12:28:26.019 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:28:26.021 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:28:26.022 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: World of Warcraft Account Level 60
[2025-06-17 12:28:26.023 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: 
[2025-06-17 12:28:26.024 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Dota 2 Account with Rare Items
[2025-06-17 12:28:26.025 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: 
[2025-06-17 12:28:26.026 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:28:26.028 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test3
[2025-06-17 12:28:26.029 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test4
[2025-06-17 12:28:26.031 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:28:26.032 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:28:26.034 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: World of Warcraft Account Level 60
[2025-06-17 12:28:26.035 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: 
[2025-06-17 12:28:26.036 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: World of Warcraft Account Level 60 with extras
[2025-06-17 12:28:26.037 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: 
[2025-06-17 12:28:26.038 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:28:26.040 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test4
[2025-06-17 12:28:26.041 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test5
[2025-06-17 12:28:26.042 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:28:26.044 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:28:26.045 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 12:28:26.046 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: 
[2025-06-17 12:28:26.047 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 12:28:26.048 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: 
[2025-06-17 12:28:26.049 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:28:26.051 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test5
[2025-06-17 12:28:26.052 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test6
[2025-06-17 12:28:26.053 +02:00 WRN] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚠️ Nie można sparsować nowej ceny: 'invalid_price' → ''
[2025-06-17 12:28:26.055 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:28:26.056 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 12:28:26.057 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: 
[2025-06-17 12:28:26.058 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 12:28:26.059 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: 
[2025-06-17 12:28:26.060 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:28:26.062 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test6
[2025-06-17 12:28:26.064 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 📊 Wyniki testów: 6/7 testów przeszło pomyślnie
[2025-06-17 12:28:26.065 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Znacząca zmiana ceny: Oczekiwano: True, Otrzymano: True (100% wzrost ceny)
[2025-06-17 12:28:26.067 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana ceny: Oczekiwano: False, Otrzymano: False (8% wzrost ceny)
[2025-06-17 12:28:26.068 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ❌ Znacząca zmiana tytułu: Oczekiwano: True, Otrzymano: False (WoW → Dota 2)
[2025-06-17 12:28:26.070 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana tytułu: Oczekiwano: False, Otrzymano: False (dodano 'with extras')
[2025-06-17 12:28:26.071 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Brak zmian: Oczekiwano: False, Otrzymano: False (identyczne dane)
[2025-06-17 12:28:26.073 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieprawidłowa cena: Oczekiwano: False, Otrzymano: False (graceful handling)
[2025-06-17 12:28:26.074 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Progi algorytmu: Próg ceny: 0,10 (oczekiwano: 0,10), Próg tytułu: 0,7 (oczekiwano: 0,7)
[2025-06-17 12:28:26.076 +02:00 WRN] ShopBot.Services.OfferContentChangeDetectorTestRunner: ⚠️ 1 testów nie powiodło się
[2025-06-17 12:28:28.212 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 6
[2025-06-17 12:28:28.213 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Epic champions = 22
[2025-06-17 12:28:28.215 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Rare champions = 20
[2025-06-17 12:28:28.216 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 51
[2025-06-17 12:28:28.217 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 12:28:28.219 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 12:28:28.220 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 12:28:28.225 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 12:28:28.227 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 12:28:28.251 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 12:28:28.253 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 12:28:28.257 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 12:28:28.258 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 12:28:28.259 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 12:28:28.260 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 12:28:28.262 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 12:28:28.263 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 12:28:28.264 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 12:28:28.265 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 12:28:28.266 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 12:28:28.268 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 12:28:28.269 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 12:28:28.272 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 12:28:28.273 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 52 $</span></span>
[2025-06-17 12:28:28.275 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>57.15 $</span></span>
[2025-06-17 12:28:28.276 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>57.15 $</span></span>
[2025-06-17 12:28:28.278 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>57.15 $</span></span>
[2025-06-17 12:28:28.279 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>52 USDT</span></span>
[2025-06-17 12:28:28.281 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 52
[2025-06-17 12:28:28.284 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 52 USDT
[2025-06-17 12:28:28.291 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 12:28:28.293 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 12:28:28.294 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 12:28:28.295 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 12:28:28.296 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 12:28:28.297 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-17 12:28:28.298 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-17 12:28:28.300 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty ********
[2025-06-17 12:28:28.301 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 53.24 → 52 (zmiana: 2.33 %)
[2025-06-17 12:28:28.303 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:28:28.304 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: (⁠ ⁠˘⁠ ⁠³⁠˘⁠)❤️【𝐋𝐨𝐧𝐚𝐒𝐡𝐨𝐩】📌Selling ᴀᴄᴄᴏᴜɴᴛ💖PRINCE KAYMER + 5 OTHERS⭐️💖⭐️SELECTED IN THE SCREENSHOTS💖, Legendary champions: 6, Epic champions: 22, Rare champions: 20, Account level: 51
[2025-06-17 12:28:28.305 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: ( ) +
[2025-06-17 12:28:28.306 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: ( | | ) | selling | prince kaymer + 5 others | selected in the screenshots
[2025-06-17 12:28:28.307 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: ( ) +
[2025-06-17 12:28:28.308 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:28:28.310 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty ********
[2025-06-17 12:28:28.314 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Test change detector completed successfully
[2025-06-17 12:29:43.386 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-17 12:29:43.407 +02:00 INF] ShopBot.Services.CliCommandHandler: 🧪 Testowanie OfferContentChangeDetector...
[2025-06-17 12:29:43.410 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 🧪 Rozpoczynam testy OfferContentChangeDetector
[2025-06-17 12:29:43.415 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test1
[2025-06-17 12:29:43.421 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 200.00 (zmiana: 100.00 %)
[2025-06-17 12:29:43.425 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 💰 Wykryto zmianę ceny w ofercie test1: $100 → $200.00 (zmiana: 100.00 %)
[2025-06-17 12:29:43.427 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test2
[2025-06-17 12:29:43.428 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 108.00 (zmiana: 8.00 %)
[2025-06-17 12:29:43.431 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:29:43.432 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 12:29:43.433 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: 
[2025-06-17 12:29:43.434 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 12:29:43.436 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: 
[2025-06-17 12:29:43.437 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:29:43.438 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test2
[2025-06-17 12:29:43.440 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test3
[2025-06-17 12:29:43.441 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:29:43.442 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:29:43.443 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: World of Warcraft Epic Account Level 60 Legendary Gear
[2025-06-17 12:29:43.444 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: 
[2025-06-17 12:29:43.446 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Mobile Legends Bang Bang Account High Rank Mythic
[2025-06-17 12:29:43.447 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: 
[2025-06-17 12:29:43.448 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:29:43.449 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test3
[2025-06-17 12:29:43.451 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test4
[2025-06-17 12:29:43.452 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:29:43.453 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:29:43.454 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: World of Warcraft Account Level 60
[2025-06-17 12:29:43.456 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: 
[2025-06-17 12:29:43.457 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: World of Warcraft Account Level 60 with extras
[2025-06-17 12:29:43.458 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: 
[2025-06-17 12:29:43.459 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:29:43.460 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test4
[2025-06-17 12:29:43.462 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test5
[2025-06-17 12:29:43.463 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:29:43.464 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:29:43.465 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 12:29:43.467 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: 
[2025-06-17 12:29:43.468 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 12:29:43.469 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: 
[2025-06-17 12:29:43.470 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:29:43.471 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test5
[2025-06-17 12:29:43.473 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test6
[2025-06-17 12:29:43.474 +02:00 WRN] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚠️ Nie można sparsować nowej ceny: 'invalid_price' → ''
[2025-06-17 12:29:43.475 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:29:43.476 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 12:29:43.477 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: 
[2025-06-17 12:29:43.478 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 12:29:43.479 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: 
[2025-06-17 12:29:43.480 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:29:43.481 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test6
[2025-06-17 12:29:43.484 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 📊 Wyniki testów: 6/7 testów przeszło pomyślnie
[2025-06-17 12:29:43.485 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Znacząca zmiana ceny: Oczekiwano: True, Otrzymano: True (100% wzrost ceny)
[2025-06-17 12:29:43.487 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana ceny: Oczekiwano: False, Otrzymano: False (8% wzrost ceny)
[2025-06-17 12:29:43.488 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ❌ Znacząca zmiana tytułu: Oczekiwano: True, Otrzymano: False (WoW Epic → Mobile Legends)
[2025-06-17 12:29:43.489 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana tytułu: Oczekiwano: False, Otrzymano: False (dodano 'with extras')
[2025-06-17 12:29:43.491 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Brak zmian: Oczekiwano: False, Otrzymano: False (identyczne dane)
[2025-06-17 12:29:43.492 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieprawidłowa cena: Oczekiwano: False, Otrzymano: False (graceful handling)
[2025-06-17 12:29:43.494 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Progi algorytmu: Próg ceny: 0,10 (oczekiwano: 0,10), Próg tytułu: 0,7 (oczekiwano: 0,7)
[2025-06-17 12:29:43.495 +02:00 WRN] ShopBot.Services.OfferContentChangeDetectorTestRunner: ⚠️ 1 testów nie powiodło się
[2025-06-17 12:29:43.496 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Test change detector completed successfully
[2025-06-17 12:30:32.617 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-17 12:30:32.638 +02:00 INF] ShopBot.Services.CliCommandHandler: 🧪 Testowanie OfferContentChangeDetector...
[2025-06-17 12:30:32.641 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 🧪 Rozpoczynam testy OfferContentChangeDetector
[2025-06-17 12:30:32.645 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test1
[2025-06-17 12:30:32.652 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 200.00 (zmiana: 100.00 %)
[2025-06-17 12:30:32.656 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 💰 Wykryto zmianę ceny w ofercie test1: $100 → $200.00 (zmiana: 100.00 %)
[2025-06-17 12:30:32.659 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test2
[2025-06-17 12:30:32.660 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 108.00 (zmiana: 8.00 %)
[2025-06-17 12:30:32.662 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Czyszczenie tekstu: 'Test Account'
[2025-06-17 12:30:32.664 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po usunięciu emoji: '            '
[2025-06-17 12:30:32.665 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po usunięciu znaków specjalnych: '            '
[2025-06-17 12:30:32.666 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po czyszczeniu: '            '
[2025-06-17 12:30:32.667 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Wynik końcowy: ''
[2025-06-17 12:30:32.669 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Czyszczenie tekstu: 'Test Account'
[2025-06-17 12:30:32.670 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po usunięciu emoji: '            '
[2025-06-17 12:30:32.671 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po usunięciu znaków specjalnych: '            '
[2025-06-17 12:30:32.673 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po czyszczeniu: '            '
[2025-06-17 12:30:32.674 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Wynik końcowy: ''
[2025-06-17 12:30:32.675 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:30:32.676 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 12:30:32.677 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: 
[2025-06-17 12:30:32.678 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 12:30:32.679 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: 
[2025-06-17 12:30:32.681 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:30:32.682 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test2
[2025-06-17 12:30:32.683 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test3
[2025-06-17 12:30:32.685 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:30:32.687 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Czyszczenie tekstu: 'World of Warcraft Epic Account Level 60 Legendary Gear'
[2025-06-17 12:30:32.688 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po usunięciu emoji: '                                                      '
[2025-06-17 12:30:32.689 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po usunięciu znaków specjalnych: '                                                      '
[2025-06-17 12:30:32.690 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po czyszczeniu: '                                                      '
[2025-06-17 12:30:32.692 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Wynik końcowy: ''
[2025-06-17 12:30:32.693 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Czyszczenie tekstu: 'Mobile Legends Bang Bang Account High Rank Mythic'
[2025-06-17 12:30:32.694 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po usunięciu emoji: '                                                 '
[2025-06-17 12:30:32.696 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po usunięciu znaków specjalnych: '                                                 '
[2025-06-17 12:30:32.697 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po czyszczeniu: '                                                 '
[2025-06-17 12:30:32.698 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Wynik końcowy: ''
[2025-06-17 12:30:32.699 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:30:32.700 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: World of Warcraft Epic Account Level 60 Legendary Gear
[2025-06-17 12:30:32.701 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: 
[2025-06-17 12:30:32.702 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Mobile Legends Bang Bang Account High Rank Mythic
[2025-06-17 12:30:32.703 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: 
[2025-06-17 12:30:32.704 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:30:32.706 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test3
[2025-06-17 12:30:32.709 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test4
[2025-06-17 12:30:32.710 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:30:32.712 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Czyszczenie tekstu: 'World of Warcraft Account Level 60'
[2025-06-17 12:30:32.713 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po usunięciu emoji: '                                  '
[2025-06-17 12:30:32.714 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po usunięciu znaków specjalnych: '                                  '
[2025-06-17 12:30:32.716 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po czyszczeniu: '                                  '
[2025-06-17 12:30:32.717 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Wynik końcowy: ''
[2025-06-17 12:30:32.718 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Czyszczenie tekstu: 'World of Warcraft Account Level 60 with extras'
[2025-06-17 12:30:32.719 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po usunięciu emoji: '                                              '
[2025-06-17 12:30:32.721 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po usunięciu znaków specjalnych: '                                              '
[2025-06-17 12:30:32.722 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po czyszczeniu: '                                              '
[2025-06-17 12:30:32.723 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Wynik końcowy: ''
[2025-06-17 12:30:32.724 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:30:32.725 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: World of Warcraft Account Level 60
[2025-06-17 12:30:32.726 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: 
[2025-06-17 12:30:32.727 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: World of Warcraft Account Level 60 with extras
[2025-06-17 12:30:32.728 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: 
[2025-06-17 12:30:32.729 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:30:32.731 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test4
[2025-06-17 12:30:32.732 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test5
[2025-06-17 12:30:32.733 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:30:32.734 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Czyszczenie tekstu: 'Test Account'
[2025-06-17 12:30:32.735 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po usunięciu emoji: '            '
[2025-06-17 12:30:32.737 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po usunięciu znaków specjalnych: '            '
[2025-06-17 12:30:32.738 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po czyszczeniu: '            '
[2025-06-17 12:30:32.739 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Wynik końcowy: ''
[2025-06-17 12:30:32.740 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Czyszczenie tekstu: 'Test Account'
[2025-06-17 12:30:32.741 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po usunięciu emoji: '            '
[2025-06-17 12:30:32.742 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po usunięciu znaków specjalnych: '            '
[2025-06-17 12:30:32.743 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po czyszczeniu: '            '
[2025-06-17 12:30:32.745 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Wynik końcowy: ''
[2025-06-17 12:30:32.746 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:30:32.747 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 12:30:32.748 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: 
[2025-06-17 12:30:32.749 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 12:30:32.750 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: 
[2025-06-17 12:30:32.751 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:30:32.752 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test5
[2025-06-17 12:30:32.753 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test6
[2025-06-17 12:30:32.754 +02:00 WRN] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚠️ Nie można sparsować nowej ceny: 'invalid_price' → ''
[2025-06-17 12:30:32.756 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Czyszczenie tekstu: 'Test Account'
[2025-06-17 12:30:32.757 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po usunięciu emoji: '            '
[2025-06-17 12:30:32.758 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po usunięciu znaków specjalnych: '            '
[2025-06-17 12:30:32.759 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po czyszczeniu: '            '
[2025-06-17 12:30:32.760 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Wynik końcowy: ''
[2025-06-17 12:30:32.761 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Czyszczenie tekstu: 'Test Account'
[2025-06-17 12:30:32.762 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po usunięciu emoji: '            '
[2025-06-17 12:30:32.764 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po usunięciu znaków specjalnych: '            '
[2025-06-17 12:30:32.765 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Po czyszczeniu: '            '
[2025-06-17 12:30:32.766 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🧹 Wynik końcowy: ''
[2025-06-17 12:30:32.767 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:30:32.768 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 12:30:32.769 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: 
[2025-06-17 12:30:32.770 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 12:30:32.771 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: 
[2025-06-17 12:30:32.772 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:30:32.773 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test6
[2025-06-17 12:30:32.776 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 📊 Wyniki testów: 6/7 testów przeszło pomyślnie
[2025-06-17 12:30:32.777 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Znacząca zmiana ceny: Oczekiwano: True, Otrzymano: True (100% wzrost ceny)
[2025-06-17 12:30:32.778 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana ceny: Oczekiwano: False, Otrzymano: False (8% wzrost ceny)
[2025-06-17 12:30:32.780 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ❌ Znacząca zmiana tytułu: Oczekiwano: True, Otrzymano: False (WoW Epic → Mobile Legends)
[2025-06-17 12:30:32.781 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana tytułu: Oczekiwano: False, Otrzymano: False (dodano 'with extras')
[2025-06-17 12:30:32.782 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Brak zmian: Oczekiwano: False, Otrzymano: False (identyczne dane)
[2025-06-17 12:30:32.784 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieprawidłowa cena: Oczekiwano: False, Otrzymano: False (graceful handling)
[2025-06-17 12:30:32.785 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Progi algorytmu: Próg ceny: 0,10 (oczekiwano: 0,10), Próg tytułu: 0,7 (oczekiwano: 0,7)
[2025-06-17 12:30:32.787 +02:00 WRN] ShopBot.Services.OfferContentChangeDetectorTestRunner: ⚠️ 1 testów nie powiodło się
[2025-06-17 12:30:32.788 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Test change detector completed successfully
[2025-06-17 12:31:34.651 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-17 12:31:34.671 +02:00 INF] ShopBot.Services.CliCommandHandler: 🧪 Testowanie OfferContentChangeDetector...
[2025-06-17 12:31:34.673 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 🧪 Rozpoczynam testy OfferContentChangeDetector
[2025-06-17 12:31:34.677 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test1
[2025-06-17 12:31:34.685 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 200.00 (zmiana: 100.00 %)
[2025-06-17 12:31:34.689 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 💰 Wykryto zmianę ceny w ofercie test1: $100 → $200.00 (zmiana: 100.00 %)
[2025-06-17 12:31:34.692 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test2
[2025-06-17 12:31:34.693 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 108.00 (zmiana: 8.00 %)
[2025-06-17 12:31:34.696 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:31:34.697 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 12:31:34.698 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: 
[2025-06-17 12:31:34.700 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 12:31:34.701 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: 
[2025-06-17 12:31:34.702 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:31:34.703 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test2
[2025-06-17 12:31:34.705 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test3
[2025-06-17 12:31:34.707 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:31:34.708 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:31:34.710 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: World of Warcraft Epic Account Level 60 Legendary Gear
[2025-06-17 12:31:34.711 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: 
[2025-06-17 12:31:34.712 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Mobile Legends Bang Bang Account High Rank Mythic
[2025-06-17 12:31:34.713 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: 
[2025-06-17 12:31:34.714 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:31:34.716 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test3
[2025-06-17 12:31:34.717 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test4
[2025-06-17 12:31:34.718 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:31:34.720 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:31:34.721 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: World of Warcraft Account Level 60
[2025-06-17 12:31:34.722 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: 
[2025-06-17 12:31:34.723 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: World of Warcraft Account Level 60 with extras
[2025-06-17 12:31:34.724 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: 
[2025-06-17 12:31:34.726 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:31:34.728 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test4
[2025-06-17 12:31:34.729 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test5
[2025-06-17 12:31:34.730 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:31:34.732 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:31:34.733 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 12:31:34.734 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: 
[2025-06-17 12:31:34.735 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 12:31:34.736 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: 
[2025-06-17 12:31:34.737 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:31:34.739 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test5
[2025-06-17 12:31:34.740 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test6
[2025-06-17 12:31:34.741 +02:00 WRN] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚠️ Nie można sparsować nowej ceny: 'invalid_price' → ''
[2025-06-17 12:31:34.742 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Formatowanie tytułów:
[2025-06-17 12:31:34.743 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 12:31:34.744 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary sformatowany: 
[2025-06-17 12:31:34.745 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 12:31:34.746 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy sformatowany: 
[2025-06-17 12:31:34.747 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo sformatowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:31:34.749 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test6
[2025-06-17 12:31:34.751 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 📊 Wyniki testów: 6/7 testów przeszło pomyślnie
[2025-06-17 12:31:34.752 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Znacząca zmiana ceny: Oczekiwano: True, Otrzymano: True (100% wzrost ceny)
[2025-06-17 12:31:34.754 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana ceny: Oczekiwano: False, Otrzymano: False (8% wzrost ceny)
[2025-06-17 12:31:34.755 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ❌ Znacząca zmiana tytułu: Oczekiwano: True, Otrzymano: False (WoW Epic → Mobile Legends)
[2025-06-17 12:31:34.757 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana tytułu: Oczekiwano: False, Otrzymano: False (dodano 'with extras')
[2025-06-17 12:31:34.758 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Brak zmian: Oczekiwano: False, Otrzymano: False (identyczne dane)
[2025-06-17 12:31:34.759 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieprawidłowa cena: Oczekiwano: False, Otrzymano: False (graceful handling)
[2025-06-17 12:31:34.761 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Progi algorytmu: Próg ceny: 0,10 (oczekiwano: 0,10), Próg tytułu: 0,7 (oczekiwano: 0,7)
[2025-06-17 12:31:34.762 +02:00 WRN] ShopBot.Services.OfferContentChangeDetectorTestRunner: ⚠️ 1 testów nie powiodło się
[2025-06-17 12:31:34.763 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Test change detector completed successfully
[2025-06-17 12:32:56.281 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-17 12:32:56.303 +02:00 INF] ShopBot.Services.CliCommandHandler: 🧪 Testowanie OfferContentChangeDetector...
[2025-06-17 12:32:56.306 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 🧪 Rozpoczynam testy OfferContentChangeDetector
[2025-06-17 12:32:56.311 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test1
[2025-06-17 12:32:56.317 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 200.00 (zmiana: 100.00 %)
[2025-06-17 12:32:56.321 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 💰 Wykryto zmianę ceny w ofercie test1: $100 → $200.00 (zmiana: 100.00 %)
[2025-06-17 12:32:56.323 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test2
[2025-06-17 12:32:56.325 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 108.00 (zmiana: 8.00 %)
[2025-06-17 12:32:56.327 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Normalizacja tytułów:
[2025-06-17 12:32:56.328 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 12:32:56.329 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: test account
[2025-06-17 12:32:56.330 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 12:32:56.331 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: test account
[2025-06-17 12:32:56.333 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo znormalizowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:32:56.334 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test2
[2025-06-17 12:32:56.336 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test3
[2025-06-17 12:32:56.337 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:32:56.339 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Normalizacja tytułów:
[2025-06-17 12:32:56.340 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: World of Warcraft Epic Account Level 60 Legendary Gear
[2025-06-17 12:32:56.341 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: world of warcraft epic account level 60 legendary gear
[2025-06-17 12:32:56.342 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Mobile Legends Bang Bang Account High Rank Mythic
[2025-06-17 12:32:56.343 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: mobile legends bang bang account high rank mythic
[2025-06-17 12:32:56.344 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo znormalizowanych tytułów: 27.78 % (próg: 70.00 %)
[2025-06-17 12:32:56.346 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Wykryto zmianę tytułu w ofercie test3: podobieństwo 27.78 %
[2025-06-17 12:32:56.348 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test4
[2025-06-17 12:32:56.349 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:32:56.351 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Normalizacja tytułów:
[2025-06-17 12:32:56.352 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: World of Warcraft Account Level 60
[2025-06-17 12:32:56.353 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: world of warcraft account level 60
[2025-06-17 12:32:56.354 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: World of Warcraft Account Level 60 with extras
[2025-06-17 12:32:56.355 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: world of warcraft account level 60 with extras
[2025-06-17 12:32:56.356 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo znormalizowanych tytułów: 73.91 % (próg: 70.00 %)
[2025-06-17 12:32:56.357 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test4
[2025-06-17 12:32:56.359 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test5
[2025-06-17 12:32:56.360 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:32:56.362 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Normalizacja tytułów:
[2025-06-17 12:32:56.363 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 12:32:56.364 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: test account
[2025-06-17 12:32:56.365 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 12:32:56.366 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: test account
[2025-06-17 12:32:56.366 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo znormalizowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:32:56.368 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test5
[2025-06-17 12:32:56.369 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test6
[2025-06-17 12:32:56.371 +02:00 WRN] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚠️ Nie można sparsować nowej ceny: 'invalid_price' → ''
[2025-06-17 12:32:56.372 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Normalizacja tytułów:
[2025-06-17 12:32:56.373 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 12:32:56.374 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: test account
[2025-06-17 12:32:56.375 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 12:32:56.376 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: test account
[2025-06-17 12:32:56.377 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo znormalizowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:32:56.378 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test6
[2025-06-17 12:32:56.381 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 📊 Wyniki testów: 7/7 testów przeszło pomyślnie
[2025-06-17 12:32:56.383 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Znacząca zmiana ceny: Oczekiwano: True, Otrzymano: True (100% wzrost ceny)
[2025-06-17 12:32:56.384 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana ceny: Oczekiwano: False, Otrzymano: False (8% wzrost ceny)
[2025-06-17 12:32:56.386 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Znacząca zmiana tytułu: Oczekiwano: True, Otrzymano: True (WoW Epic → Mobile Legends)
[2025-06-17 12:32:56.387 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana tytułu: Oczekiwano: False, Otrzymano: False (dodano 'with extras')
[2025-06-17 12:32:56.388 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Brak zmian: Oczekiwano: False, Otrzymano: False (identyczne dane)
[2025-06-17 12:32:56.390 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieprawidłowa cena: Oczekiwano: False, Otrzymano: False (graceful handling)
[2025-06-17 12:32:56.391 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Progi algorytmu: Próg ceny: 0,10 (oczekiwano: 0,10), Próg tytułu: 0,7 (oczekiwano: 0,7)
[2025-06-17 12:32:56.393 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 🎉 Wszystkie testy przeszły pomyślnie!
[2025-06-17 12:32:56.394 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Test change detector completed successfully
[2025-06-17 12:33:09.284 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-17 12:33:09.306 +02:00 INF] ShopBot.Services.CliCommandHandler: 🧪 Testowanie OfferContentChangeDetector...
[2025-06-17 12:33:09.309 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 🧪 Rozpoczynam testy OfferContentChangeDetector
[2025-06-17 12:33:09.314 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test1
[2025-06-17 12:33:09.321 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 200.00 (zmiana: 100.00 %)
[2025-06-17 12:33:09.324 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 💰 Wykryto zmianę ceny w ofercie test1: $100 → $200.00 (zmiana: 100.00 %)
[2025-06-17 12:33:09.327 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test2
[2025-06-17 12:33:09.328 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 108.00 (zmiana: 8.00 %)
[2025-06-17 12:33:09.329 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Normalizacja tytułów:
[2025-06-17 12:33:09.330 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 12:33:09.331 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: test account
[2025-06-17 12:33:09.332 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 12:33:09.333 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: test account
[2025-06-17 12:33:09.335 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo znormalizowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:33:09.336 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test2
[2025-06-17 12:33:09.337 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test3
[2025-06-17 12:33:09.339 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:33:09.340 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Normalizacja tytułów:
[2025-06-17 12:33:09.341 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: World of Warcraft Epic Account Level 60 Legendary Gear
[2025-06-17 12:33:09.342 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: world of warcraft epic account level 60 legendary gear
[2025-06-17 12:33:09.344 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Mobile Legends Bang Bang Account High Rank Mythic
[2025-06-17 12:33:09.345 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: mobile legends bang bang account high rank mythic
[2025-06-17 12:33:09.346 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo znormalizowanych tytułów: 27.78 % (próg: 70.00 %)
[2025-06-17 12:33:09.349 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Wykryto zmianę tytułu w ofercie test3: podobieństwo 27.78 %
[2025-06-17 12:33:09.350 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test4
[2025-06-17 12:33:09.351 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:33:09.353 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Normalizacja tytułów:
[2025-06-17 12:33:09.354 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: World of Warcraft Account Level 60
[2025-06-17 12:33:09.355 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: world of warcraft account level 60
[2025-06-17 12:33:09.356 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: World of Warcraft Account Level 60 with extras
[2025-06-17 12:33:09.357 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: world of warcraft account level 60 with extras
[2025-06-17 12:33:09.359 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo znormalizowanych tytułów: 73.91 % (próg: 70.00 %)
[2025-06-17 12:33:09.360 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test4
[2025-06-17 12:33:09.361 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test5
[2025-06-17 12:33:09.363 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 12:33:09.364 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Normalizacja tytułów:
[2025-06-17 12:33:09.365 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 12:33:09.366 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: test account
[2025-06-17 12:33:09.367 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 12:33:09.369 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: test account
[2025-06-17 12:33:09.370 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo znormalizowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:33:09.371 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test5
[2025-06-17 12:33:09.373 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test6
[2025-06-17 12:33:09.374 +02:00 WRN] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚠️ Nie można sparsować nowej ceny: 'invalid_price' → ''
[2025-06-17 12:33:09.376 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Normalizacja tytułów:
[2025-06-17 12:33:09.377 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 12:33:09.378 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: test account
[2025-06-17 12:33:09.379 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 12:33:09.380 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: test account
[2025-06-17 12:33:09.381 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo znormalizowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:33:09.382 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test6
[2025-06-17 12:33:09.384 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 📊 Wyniki testów: 7/7 testów przeszło pomyślnie
[2025-06-17 12:33:09.386 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Znacząca zmiana ceny: Oczekiwano: True, Otrzymano: True (100% wzrost ceny)
[2025-06-17 12:33:09.387 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana ceny: Oczekiwano: False, Otrzymano: False (8% wzrost ceny)
[2025-06-17 12:33:09.389 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Znacząca zmiana tytułu: Oczekiwano: True, Otrzymano: True (WoW Epic → Mobile Legends)
[2025-06-17 12:33:09.390 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana tytułu: Oczekiwano: False, Otrzymano: False (dodano 'with extras')
[2025-06-17 12:33:09.392 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Brak zmian: Oczekiwano: False, Otrzymano: False (identyczne dane)
[2025-06-17 12:33:09.393 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieprawidłowa cena: Oczekiwano: False, Otrzymano: False (graceful handling)
[2025-06-17 12:33:09.394 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Progi algorytmu: Próg ceny: 0,10 (oczekiwano: 0,10), Próg tytułu: 0,7 (oczekiwano: 0,7)
[2025-06-17 12:33:09.396 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 🎉 Wszystkie testy przeszły pomyślnie!
[2025-06-17 12:33:11.498 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 6
[2025-06-17 12:33:11.499 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Epic champions = 22
[2025-06-17 12:33:11.501 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Rare champions = 20
[2025-06-17 12:33:11.502 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 51
[2025-06-17 12:33:11.503 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 12:33:11.504 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 12:33:11.506 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 12:33:11.509 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 12:33:11.510 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 12:33:11.529 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 12:33:11.530 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 12:33:11.534 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 12:33:11.535 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 12:33:11.536 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 12:33:11.537 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 12:33:11.538 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 12:33:11.539 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 12:33:11.540 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 12:33:11.541 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 12:33:11.542 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 12:33:11.543 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 12:33:11.544 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 12:33:11.546 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 12:33:11.547 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 52 $</span></span>
[2025-06-17 12:33:11.548 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>57.15 $</span></span>
[2025-06-17 12:33:11.549 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>57.15 $</span></span>
[2025-06-17 12:33:11.551 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>57.15 $</span></span>
[2025-06-17 12:33:11.552 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>52 USDT</span></span>
[2025-06-17 12:33:11.553 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 52
[2025-06-17 12:33:11.554 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 52 USDT
[2025-06-17 12:33:11.559 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 12:33:11.560 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 12:33:11.561 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 12:33:11.562 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 12:33:11.563 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 12:33:11.564 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-17 12:33:11.565 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-17 12:33:11.566 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty ********
[2025-06-17 12:33:11.567 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 53.24 → 52 (zmiana: 2.33 %)
[2025-06-17 12:33:11.569 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Normalizacja tytułów:
[2025-06-17 12:33:11.570 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: (⁠ ⁠˘⁠ ⁠³⁠˘⁠)❤️【𝐋𝐨𝐧𝐚𝐒𝐡𝐨𝐩】📌Selling ᴀᴄᴄᴏᴜɴᴛ💖PRINCE KAYMER + 5 OTHERS⭐️💖⭐️SELECTED IN THE SCREENSHOTS💖, Legendary champions: 6, Epic champions: 22, Rare champions: 20, Account level: 51
[2025-06-17 12:33:11.571 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: (⁠ ⁠˘⁠ ⁠³⁠˘⁠)❤️【𝐋𝐨𝐧𝐚𝐒𝐡𝐨𝐩】📌selling ᴀᴄᴄᴏᴜɴᴛ💖prince kaymer + 5 others⭐️💖⭐️selected in the screenshots💖, legendary champions: 6, epic champions: 22, rare champions: 20, account level: 51
[2025-06-17 12:33:11.572 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: ( | | ) | selling | prince kaymer + 5 others | selected in the screenshots
[2025-06-17 12:33:11.573 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: ( | | ) | selling | prince kaymer + 5 others | selected in the screenshots
[2025-06-17 12:33:11.574 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚡ Wczesne przerwanie - duża różnica długości: 194 vs 74
[2025-06-17 12:33:11.575 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo znormalizowanych tytułów: 0.00 % (próg: 70.00 %)
[2025-06-17 12:33:11.576 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Wykryto zmianę tytułu w ofercie ********: podobieństwo 0.00 %
[2025-06-17 12:33:11.580 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Test change detector completed successfully
[2025-06-17 12:51:40.107 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-17 12:51:40.129 +02:00 INF] ShopBot.Services.CliCommandHandler: 🧪 Testowanie OfferContentChangeDetector na wszystkich ofertach...
[2025-06-17 12:51:42.256 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 12:51:42.258 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 12:51:42.259 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 12:51:42.266 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 12:51:42.267 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 12:51:42.276 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 12:51:42.277 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 12:51:42.280 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 12:51:42.281 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 12:51:42.282 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 12:51:42.283 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 12:51:42.283 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 12:51:42.284 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 12:51:42.285 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 12:51:42.286 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 12:51:42.287 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 12:51:42.288 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 12:51:42.289 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 12:51:42.291 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 12:51:42.293 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 384.09 $</span></span>
[2025-06-17 12:51:42.294 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>422.08 $</span></span>
[2025-06-17 12:51:42.295 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>422.08 $</span></span>
[2025-06-17 12:51:42.297 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>422.08 $</span></span>
[2025-06-17 12:51:42.298 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>384.09 USDT</span></span>
[2025-06-17 12:51:42.299 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 384.09
[2025-06-17 12:51:42.301 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 384.09 USDT
[2025-06-17 12:51:42.306 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 12:51:42.307 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 12:51:42.308 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 12:51:42.309 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 12:51:42.310 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 12:51:42.311 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-17 12:51:42.312 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-17 12:51:42.314 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty ********
[2025-06-17 12:51:42.316 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 392.54 → 384.09 (zmiana: 2.15 %)
[2025-06-17 12:51:42.319 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Normalizacja tytułów:
[2025-06-17 12:51:42.320 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: 🪑👑👹Ingrid a3 Pretus a3, Valderon a3!!! Top account ❤️‍🔥❤️‍🔥❤️‍🔥
[2025-06-17 12:51:42.321 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: 🪑👑👹ingrid a3 pretus a3, valderon a3!!! top account ❤️‍🔥❤️‍🔥❤️‍🔥
[2025-06-17 12:51:42.322 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: ingrid a3 pretus a3 | valderon a3!!! top account
[2025-06-17 12:51:42.323 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: ingrid a3 pretus a3 | valderon a3!!! top account
[2025-06-17 12:51:42.324 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo znormalizowanych tytułów: 65.22 % (próg: 70.00 %)
[2025-06-17 12:51:42.326 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Wykryto zmianę tytułu w ofercie ********: podobieństwo 65.22 %
[2025-06-17 12:51:43.319 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 60
[2025-06-17 12:51:43.320 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 12:51:43.321 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 12:51:43.322 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 12:51:43.324 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 12:51:43.325 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 12:51:43.329 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 12:51:43.330 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 12:51:43.332 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 12:51:43.333 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 12:51:43.334 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 12:51:43.335 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 12:51:43.336 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 12:51:43.337 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 12:51:43.338 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 12:51:43.339 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 12:51:43.340 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 12:51:43.341 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 12:51:43.342 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 12:51:43.343 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 12:51:43.344 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 44.81 $</span></span>
[2025-06-17 12:51:43.346 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>49.25 $</span></span>
[2025-06-17 12:51:43.347 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>49.25 $</span></span>
[2025-06-17 12:51:43.348 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>49.25 $</span></span>
[2025-06-17 12:51:43.350 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>44.81 USDT</span></span>
[2025-06-17 12:51:43.351 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 44.81
[2025-06-17 12:51:43.352 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 44.81 USDT
[2025-06-17 12:51:43.354 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 12:51:43.355 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 12:51:43.356 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 12:51:43.357 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 12:51:43.358 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 12:51:43.359 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-17 12:51:43.360 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-17 12:51:43.361 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty ********
[2025-06-17 12:51:43.362 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 45.80 → 44.81 (zmiana: 2.16 %)
[2025-06-17 12:51:43.364 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Normalizacja tytułów:
[2025-06-17 12:51:43.365 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Viksvel kaimer vizig 5+6 kb 1 klush🔥, Account level: 60
[2025-06-17 12:51:43.366 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: viksvel kaimer vizig 5+6 kb 1 klush🔥, account level: 60
[2025-06-17 12:51:43.367 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: viksvel kaimer vizig 5+6 kb 1 klush
[2025-06-17 12:51:43.368 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: viksvel kaimer vizig 5+6 kb 1 klush
[2025-06-17 12:51:43.369 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo znormalizowanych tytułów: 62.50 % (próg: 70.00 %)
[2025-06-17 12:51:43.370 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Wykryto zmianę tytułu w ofercie ********: podobieństwo 62.50 %
[2025-06-17 12:51:44.429 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Server = Europe (Amsterdam)
[2025-06-17 12:51:44.430 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Weapon type = Melee weapons
[2025-06-17 12:51:44.432 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: PvE Fame (m) = 53
[2025-06-17 12:51:44.433 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 12:51:44.434 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 12:51:44.435 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 12:51:44.437 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 12:51:44.438 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 12:51:44.441 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 12:51:44.442 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 12:51:44.444 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 12:51:44.445 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 12:51:44.446 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 12:51:44.447 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 12:51:44.448 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 12:51:44.449 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 12:51:44.449 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 12:51:44.450 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 12:51:44.452 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 12:51:44.452 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 12:51:44.454 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 12:51:44.455 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 89.62 $</span></span>
[2025-06-17 12:51:44.456 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>98.49 $</span></span>
[2025-06-17 12:51:44.457 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>98.49 $</span></span>
[2025-06-17 12:51:44.458 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>98.49 $</span></span>
[2025-06-17 12:51:44.460 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>89.62 USDT</span></span>
[2025-06-17 12:51:44.461 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 89.62
[2025-06-17 12:51:44.462 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 89.62 USDT
[2025-06-17 12:51:44.465 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 12:51:44.466 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 12:51:44.467 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 12:51:44.468 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 12:51:44.469 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-17 12:51:44.471 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty ********
[2025-06-17 12:51:44.472 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 91.59 → 89.62 (zmiana: 2.15 %)
[2025-06-17 12:51:44.473 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Normalizacja tytułów:
[2025-06-17 12:51:44.474 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Personal account ⛏T8 Miner, 💰About 24+Silver🔥 More details in PM 🔥, Melee weapons, Fame 53m
[2025-06-17 12:51:44.475 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: personal account ⛏t8 miner, 💰about 24+silver🔥 more details in pm 🔥, melee weapons, fame 53m
[2025-06-17 12:51:44.476 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: personal account | t8 miner | about 24+silver | more details in pm
[2025-06-17 12:51:44.477 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: personal account | t8 miner | about 24+silver | more details in pm
[2025-06-17 12:51:44.478 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo znormalizowanych tytułów: 62.77 % (próg: 70.00 %)
[2025-06-17 12:51:44.480 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Wykryto zmianę tytułu w ofercie ********: podobieństwo 62.77 %
[2025-06-17 12:51:45.382 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 12:51:45.384 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 12:51:45.386 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 12:51:45.388 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 12:51:45.389 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 12:51:45.410 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 12:51:45.411 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 12:51:45.412 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 12:51:45.413 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 12:51:45.414 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 12:51:45.415 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 12:51:45.416 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 12:51:45.417 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 12:51:45.418 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 12:51:45.419 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 12:51:45.420 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 12:51:45.421 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 12:51:45.422 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 12:51:45.423 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 12:51:45.424 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 38.41 $</span></span>
[2025-06-17 12:51:45.425 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>42.21 $</span></span>
[2025-06-17 12:51:45.427 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>42.21 $</span></span>
[2025-06-17 12:51:45.428 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>42.21 $</span></span>
[2025-06-17 12:51:45.429 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>38.41 USDT</span></span>
[2025-06-17 12:51:45.430 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 38.41
[2025-06-17 12:51:45.431 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 38.41 USDT
[2025-06-17 12:51:45.433 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 12:51:45.433 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 12:51:45.435 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 12:51:45.436 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 12:51:45.437 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 12:51:45.438 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-17 12:51:45.438 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-17 12:51:45.439 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty ********
[2025-06-17 12:51:45.440 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 39.25 → 38.41 (zmiana: 2.14 %)
[2025-06-17 12:51:45.442 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Normalizacja tytułów:
[2025-06-17 12:51:45.443 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Ingrid + Pretus The Pioneers server
[2025-06-17 12:51:45.444 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: ingrid + pretus the pioneers server
[2025-06-17 12:51:45.444 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: ingrid + pretus the pioneers server
[2025-06-17 12:51:45.445 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: ingrid + pretus the pioneers server
[2025-06-17 12:51:45.446 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo znormalizowanych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 12:51:45.448 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty ********
[2025-06-17 12:51:46.337 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 36
[2025-06-17 12:51:46.338 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Epic champions = 77
[2025-06-17 12:51:46.340 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Rare champions = 100500
[2025-06-17 12:51:46.341 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 85
[2025-06-17 12:51:46.342 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 12:51:46.343 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 12:51:46.344 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 12:51:46.346 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 12:51:46.347 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 12:51:46.348 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 12:51:46.349 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 12:51:46.351 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 12:51:46.351 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 12:51:46.352 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 12:51:46.353 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 12:51:46.354 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 12:51:46.355 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 12:51:46.356 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 12:51:46.357 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 12:51:46.358 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 12:51:46.359 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 12:51:46.360 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 12:51:46.361 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 12:51:46.362 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 57.62 $</span></span>
[2025-06-17 12:51:46.363 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>63.32 $</span></span>
[2025-06-17 12:51:46.364 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>63.32 $</span></span>
[2025-06-17 12:51:46.365 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>63.32 $</span></span>
[2025-06-17 12:51:46.366 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>57.62 USDT</span></span>
[2025-06-17 12:51:46.368 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 57.62
[2025-06-17 12:51:46.369 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 57.62 USDT
[2025-06-17 12:51:46.370 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 12:51:46.371 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 12:51:46.372 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 12:51:46.373 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 12:51:46.374 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 12:51:46.375 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-17 12:51:46.376 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-17 12:51:46.376 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty ********
[2025-06-17 12:51:46.377 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 58.88 → 57.62 (zmiana: 2.14 %)
[2025-06-17 12:51:46.379 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Normalizacja tytułów:
[2025-06-17 12:51:46.380 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: All tops, 66 prems, read the description  All tops, 66 prems, read the description, Legendary champions: 36, Epic champions: 77, Rare champions: 100500, Account level: 85
[2025-06-17 12:51:46.381 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: all tops, 66 prems, read the description all tops, 66 prems, read the description, legendary champions: 36, epic champions: 77, rare champions: 100500, account level: 85
[2025-06-17 12:51:46.382 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: all tops | 66 prems | read the description all tops | 66 prems | read the description
[2025-06-17 12:51:46.382 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: all tops | 66 prems | read the description all tops | 66 prems | read the description
[2025-06-17 12:51:46.384 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo znormalizowanych tytułów: 43.20 % (próg: 70.00 %)
[2025-06-17 12:51:46.385 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Wykryto zmianę tytułu w ofercie ********: podobieństwo 43.20 %
[2025-06-17 12:51:47.272 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Server = America (Washington)
[2025-06-17 12:51:47.273 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Weapon type = Melee weapons
[2025-06-17 12:51:47.274 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: PvE Fame (m) = 70
[2025-06-17 12:51:47.276 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 12:51:47.276 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 12:51:47.278 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 12:51:47.279 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 12:51:47.280 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 12:51:47.283 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 12:51:47.283 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 12:51:47.285 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 12:51:47.286 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 12:51:47.287 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 12:51:47.287 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 12:51:47.288 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 12:51:47.289 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 12:51:47.290 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 12:51:47.291 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 12:51:47.292 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 12:51:47.293 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 12:51:47.294 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 12:51:47.295 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 51.22 $</span></span>
[2025-06-17 12:51:47.296 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>56.29 $</span></span>
[2025-06-17 12:51:47.297 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>56.29 $</span></span>
[2025-06-17 12:51:47.298 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>56.29 $</span></span>
[2025-06-17 12:51:47.300 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>51.22 USDT</span></span>
[2025-06-17 12:51:47.301 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 51.22
[2025-06-17 12:51:47.302 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 51.22 USDT
[2025-06-17 12:51:47.303 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 12:51:47.304 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 12:51:47.305 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 12:51:47.306 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 12:51:47.307 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-17 12:51:47.308 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty ********
[2025-06-17 12:51:47.309 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 52.34 → 51.22 (zmiana: 2.14 %)
[2025-06-17 12:51:47.310 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Normalizacja tytułów:
[2025-06-17 12:51:47.311 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Cool old account, Melee weapons, Fame 70m
[2025-06-17 12:51:47.312 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: cool old account, melee weapons, fame 70m
[2025-06-17 12:51:47.313 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: cool old account
[2025-06-17 12:51:47.314 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: cool old account
[2025-06-17 12:51:47.315 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚡ Wczesne przerwanie - duża różnica długości: 41 vs 16
[2025-06-17 12:51:47.316 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo znormalizowanych tytułów: 0.00 % (próg: 70.00 %)
[2025-06-17 12:51:47.318 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Wykryto zmianę tytułu w ofercie ********: podobieństwo 0.00 %
[2025-06-17 12:51:48.251 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Server = Europe (Amsterdam)
[2025-06-17 12:51:48.252 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Weapon type = Melee weapons
[2025-06-17 12:51:48.253 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: PvE Fame (m) = 98
[2025-06-17 12:51:48.254 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 12:51:48.255 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 12:51:48.257 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 12:51:48.258 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 12:51:48.259 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 12:51:48.261 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 12:51:48.261 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 12:51:48.263 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 12:51:48.264 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 12:51:48.265 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 12:51:48.266 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 12:51:48.267 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 12:51:48.268 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 12:51:48.269 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 12:51:48.269 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 12:51:48.270 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 12:51:48.271 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 12:51:48.272 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 12:51:48.273 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 95.68 $</span></span>
[2025-06-17 12:51:48.274 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>105.15 $</span></span>
[2025-06-17 12:51:48.276 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>105.15 $</span></span>
[2025-06-17 12:51:48.277 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>105.15 $</span></span>
[2025-06-17 12:51:48.278 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>95.68 USDT</span></span>
[2025-06-17 12:51:48.279 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 95.68
[2025-06-17 12:51:48.280 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 95.68 USDT
[2025-06-17 12:51:48.282 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 12:51:48.283 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 12:51:48.284 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 12:51:48.285 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 12:51:48.285 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: 35936102
[2025-06-17 12:51:48.286 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty 35936102
[2025-06-17 12:51:48.287 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 97.96 → 95.68 (zmiana: 2.33 %)
[2025-06-17 12:51:48.289 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Normalizacja tytułów:
[2025-06-17 12:51:48.289 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: EU + TOTAL FAME 98M + SILVER 50M + Gathering T4 +TANK / HEALER / SC, Melee weapons, Fame 98m
[2025-06-17 12:51:48.290 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: eu + total fame 98m + silver 50m + gathering t4 +tank / healer / sc, melee weapons, fame 98m
[2025-06-17 12:51:48.291 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: eu + total fame 98m + silver 50m + gathering t4 +tank / healer / sc
[2025-06-17 12:51:48.292 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: eu + total fame 98m + silver 50m + gathering t4 +tank / healer / sc
[2025-06-17 12:51:48.293 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo znormalizowanych tytułów: 72.83 % (próg: 70.00 %)
[2025-06-17 12:51:48.295 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty 35936102
[2025-06-17 12:51:49.151 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Server = Europe (Amsterdam)
[2025-06-17 12:51:49.152 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Weapon type = Ranged weapons
[2025-06-17 12:51:49.153 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: PvE Fame (m) = 1
[2025-06-17 12:51:49.155 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 2 pcs.
[2025-06-17 12:51:49.156 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 12:51:49.158 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 12:51:49.159 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 12:51:49.160 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 12:51:49.163 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 12:51:49.163 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 12:51:49.165 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 12:51:49.166 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 12:51:49.167 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 12:51:49.168 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 12:51:49.169 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 12:51:49.170 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 12:51:49.171 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 12:51:49.172 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 12:51:49.173 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 12:51:49.174 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 12:51:49.176 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 12:51:49.177 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 15.37 $</span></span>
[2025-06-17 12:51:49.178 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>16.90 $</span></span>
[2025-06-17 12:51:49.179 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>16.90 $</span></span>
[2025-06-17 12:51:49.181 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>16.90 $</span></span>
[2025-06-17 12:51:49.182 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>15.37 USDT</span></span>
[2025-06-17 12:51:49.183 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 15.37
[2025-06-17 12:51:49.184 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 15.37 USDT
[2025-06-17 12:51:49.186 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 12:51:49.187 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 12:51:49.188 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 12:51:49.189 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 12:51:49.190 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-17 12:51:49.191 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty ********
[2025-06-17 12:51:49.192 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 15.70 → 15.37 (zmiana: 2.10 %)
[2025-06-17 12:51:49.193 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Normalizacja tytułów:
[2025-06-17 12:51:49.194 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: I will create an account for you with a premium for 33 days, Ranged weapons, Fame 1m
[2025-06-17 12:51:49.195 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: i will create an account for you with a premium for 33 days, ranged weapons, fame 1m
[2025-06-17 12:51:49.196 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: i will create an account for you with a premium for 33 days
[2025-06-17 12:51:49.197 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: i will create an account for you with a premium for 33 days
[2025-06-17 12:51:49.199 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo znormalizowanych tytułów: 70.24 % (próg: 70.00 %)
[2025-06-17 12:51:49.200 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty ********
[2025-06-17 12:51:50.166 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Server = Europe (Amsterdam)
[2025-06-17 12:51:50.167 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Weapon type = Ranged weapons
[2025-06-17 12:51:50.169 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: PvE Fame (m) = 4
[2025-06-17 12:51:50.170 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 12:51:50.171 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 12:51:50.172 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 12:51:50.173 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 12:51:50.174 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 12:51:50.176 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 12:51:50.177 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 12:51:50.179 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 12:51:50.179 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 12:51:50.180 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 12:51:50.181 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 12:51:50.182 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 12:51:50.183 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 12:51:50.184 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 12:51:50.185 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 12:51:50.186 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 12:51:50.187 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 12:51:50.188 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 12:51:50.189 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 17.93 $</span></span>
[2025-06-17 12:51:50.190 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>19.71 $</span></span>
[2025-06-17 12:51:50.191 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>19.71 $</span></span>
[2025-06-17 12:51:50.193 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>19.71 $</span></span>
[2025-06-17 12:51:50.194 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>17.93 USDT</span></span>
[2025-06-17 12:51:50.195 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 17.93
[2025-06-17 12:51:50.197 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 17.93 USDT
[2025-06-17 12:51:50.198 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 12:51:50.200 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 12:51:50.201 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 12:51:50.202 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 12:51:50.203 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: 41518288
[2025-06-17 12:51:50.204 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty 41518288
[2025-06-17 12:51:50.205 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 18.32 → 17.93 (zmiana: 2.13 %)
[2025-06-17 12:51:50.206 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Normalizacja tytułów:
[2025-06-17 12:51:50.207 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: T6 Miner, Badon's Bow has been upgraded, Dagger shot, a little kingmaker, Ranged weapons, Fame 4m
[2025-06-17 12:51:50.208 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: t6 miner, badon's bow has been upgraded, dagger shot, a little kingmaker, ranged weapons, fame 4m
[2025-06-17 12:51:50.209 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: t6 miner | badon's bow has been upgraded | dagger shot | a little kingmaker
[2025-06-17 12:51:50.210 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: t6 miner | badon's bow has been upgraded | dagger shot | a little kingmaker
[2025-06-17 12:51:50.211 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo znormalizowanych tytułów: 68.04 % (próg: 70.00 %)
[2025-06-17 12:51:50.212 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Wykryto zmianę tytułu w ofercie 41518288: podobieństwo 68.04 %
[2025-06-17 12:51:51.121 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Server = Europe (Amsterdam)
[2025-06-17 12:51:51.122 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Weapon type = Magical weapons
[2025-06-17 12:51:51.124 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: PvE Fame (m) = 38
[2025-06-17 12:51:51.125 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 12:51:51.126 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 12:51:51.127 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 12:51:51.129 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 12:51:51.130 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 12:51:51.132 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 12:51:51.133 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 12:51:51.134 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 12:51:51.135 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 12:51:51.136 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 12:51:51.137 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 12:51:51.138 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 12:51:51.139 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 12:51:51.140 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 12:51:51.141 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 12:51:51.142 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 12:51:51.143 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 12:51:51.145 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 12:51:51.146 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 64.02 $</span></span>
[2025-06-17 12:51:51.147 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>70.36 $</span></span>
[2025-06-17 12:51:51.148 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>70.36 $</span></span>
[2025-06-17 12:51:51.149 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>70.36 $</span></span>
[2025-06-17 12:51:51.151 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>64.02 USDT</span></span>
[2025-06-17 12:51:51.152 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 64.02
[2025-06-17 12:51:51.153 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 64.02 USDT
[2025-06-17 12:51:51.155 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 12:51:51.156 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 12:51:51.157 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 12:51:51.158 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 12:51:51.159 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: 30184133
[2025-06-17 12:51:51.160 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty 30184133
[2025-06-17 12:51:51.161 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 65.43 → 64.02 (zmiana: 2.15 %)
[2025-06-17 12:51:51.163 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Normalizacja tytułów:
[2025-06-17 12:51:51.163 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: fire mage staff  8.2 set, Magical weapons, Fame 38m
[2025-06-17 12:51:51.164 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: fire mage staff 8.2 set, magical weapons, fame 38m
[2025-06-17 12:51:51.166 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: fire mage staff 8.2 set
[2025-06-17 12:51:51.167 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: fire mage staff 8.2 set
[2025-06-17 12:51:51.168 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚡ Wczesne przerwanie - duża różnica długości: 50 vs 23
[2025-06-17 12:51:51.169 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo znormalizowanych tytułów: 0.00 % (próg: 70.00 %)
[2025-06-17 12:51:51.170 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Wykryto zmianę tytułu w ofercie 30184133: podobieństwo 0.00 %
[2025-06-17 12:51:51.676 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Mass change detector test completed successfully
[2025-06-17 13:08:43.538 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-17 13:08:43.560 +02:00 INF] ShopBot.Services.CliCommandHandler: 🧪 Testowanie OfferContentChangeDetector...
[2025-06-17 13:08:43.563 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 🧪 Rozpoczynam testy OfferContentChangeDetector
[2025-06-17 13:08:43.567 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test1
[2025-06-17 13:08:43.574 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 200.00 (zmiana: 100.00 %)
[2025-06-17 13:08:43.579 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 💰 Wykryto zmianę ceny w ofercie test1: $100 → $200.00 (zmiana: 100.00 %)
[2025-06-17 13:08:43.582 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test2
[2025-06-17 13:08:43.583 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 108.00 (zmiana: 8.00 %)
[2025-06-17 13:08:43.588 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Czyszczenie i normalizacja tytułów:
[2025-06-17 13:08:43.589 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 13:08:43.591 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: Test Account
[2025-06-17 13:08:43.592 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: test account
[2025-06-17 13:08:43.593 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 13:08:43.594 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: Test Account
[2025-06-17 13:08:43.595 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: test account
[2025-06-17 13:08:43.597 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo oczyszczonych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 13:08:43.598 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test2
[2025-06-17 13:08:43.599 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test3
[2025-06-17 13:08:43.600 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 13:08:43.604 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Czyszczenie i normalizacja tytułów:
[2025-06-17 13:08:43.605 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: World of Warcraft Epic Account Level 60 Legendary Gear
[2025-06-17 13:08:43.606 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: World of Warcraft Epic Account Level 60 Legendary Gear
[2025-06-17 13:08:43.607 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: world of warcraft epic account level 60 legendary gear
[2025-06-17 13:08:43.609 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Mobile Legends Bang Bang Account High Rank Mythic
[2025-06-17 13:08:43.610 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: Mobile Legends Bang Bang Account High Rank Mythic
[2025-06-17 13:08:43.612 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: mobile legends bang bang account high rank mythic
[2025-06-17 13:08:43.614 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo oczyszczonych tytułów: 27.78 % (próg: 70.00 %)
[2025-06-17 13:08:43.616 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Wykryto zmianę tytułu w ofercie test3: podobieństwo 27.78 %
[2025-06-17 13:08:43.618 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test4
[2025-06-17 13:08:43.619 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 13:08:43.622 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Czyszczenie i normalizacja tytułów:
[2025-06-17 13:08:43.624 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: World of Warcraft Account Level 60
[2025-06-17 13:08:43.625 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: World of Warcraft Account Level 60
[2025-06-17 13:08:43.626 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: world of warcraft account level 60
[2025-06-17 13:08:43.627 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: World of Warcraft Account Level 60 with extras
[2025-06-17 13:08:43.628 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: World of Warcraft Account Level 60 with extras
[2025-06-17 13:08:43.630 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: world of warcraft account level 60 with extras
[2025-06-17 13:08:43.631 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo oczyszczonych tytułów: 73.91 % (próg: 70.00 %)
[2025-06-17 13:08:43.632 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test4
[2025-06-17 13:08:43.634 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test5
[2025-06-17 13:08:43.635 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 13:08:43.638 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Czyszczenie i normalizacja tytułów:
[2025-06-17 13:08:43.639 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 13:08:43.640 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: Test Account
[2025-06-17 13:08:43.641 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: test account
[2025-06-17 13:08:43.642 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 13:08:43.643 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: Test Account
[2025-06-17 13:08:43.644 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: test account
[2025-06-17 13:08:43.645 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo oczyszczonych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 13:08:43.647 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test5
[2025-06-17 13:08:43.648 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test6
[2025-06-17 13:08:43.650 +02:00 WRN] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚠️ Nie można sparsować nowej ceny: 'invalid_price' → ''
[2025-06-17 13:08:43.652 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Czyszczenie i normalizacja tytułów:
[2025-06-17 13:08:43.653 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 13:08:43.654 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: Test Account
[2025-06-17 13:08:43.655 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: test account
[2025-06-17 13:08:43.656 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 13:08:43.657 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: Test Account
[2025-06-17 13:08:43.658 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: test account
[2025-06-17 13:08:43.660 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo oczyszczonych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 13:08:43.661 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test6
[2025-06-17 13:08:43.664 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 📊 Wyniki testów: 7/7 testów przeszło pomyślnie
[2025-06-17 13:08:43.665 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Znacząca zmiana ceny: Oczekiwano: True, Otrzymano: True (100% wzrost ceny)
[2025-06-17 13:08:43.667 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana ceny: Oczekiwano: False, Otrzymano: False (8% wzrost ceny)
[2025-06-17 13:08:43.668 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Znacząca zmiana tytułu: Oczekiwano: True, Otrzymano: True (WoW Epic → Mobile Legends)
[2025-06-17 13:08:43.669 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana tytułu: Oczekiwano: False, Otrzymano: False (dodano 'with extras')
[2025-06-17 13:08:43.671 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Brak zmian: Oczekiwano: False, Otrzymano: False (identyczne dane)
[2025-06-17 13:08:43.672 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieprawidłowa cena: Oczekiwano: False, Otrzymano: False (graceful handling)
[2025-06-17 13:08:43.673 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Progi algorytmu: Próg ceny: 0,10 (oczekiwano: 0,10), Próg tytułu: 0,7 (oczekiwano: 0,7)
[2025-06-17 13:08:43.675 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 🎉 Wszystkie testy przeszły pomyślnie!
[2025-06-17 13:08:43.676 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Test change detector completed successfully
[2025-06-17 13:08:56.499 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-17 13:08:56.524 +02:00 INF] ShopBot.Services.CliCommandHandler: 🧪 Testowanie OfferContentChangeDetector na wszystkich ofertach...
[2025-06-17 13:08:58.644 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 13:08:58.647 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 13:08:58.648 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 13:08:58.655 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:08:58.657 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:08:58.668 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:08:58.669 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:08:58.672 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 13:08:58.673 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 13:08:58.674 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:08:58.675 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:08:58.677 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:08:58.678 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:08:58.679 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 13:08:58.680 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 13:08:58.681 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 13:08:58.682 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 13:08:58.683 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 13:08:58.686 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 13:08:58.687 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 383.09 $</span></span>
[2025-06-17 13:08:58.689 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>420.97 $</span></span>
[2025-06-17 13:08:58.690 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>420.97 $</span></span>
[2025-06-17 13:08:58.692 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>420.97 $</span></span>
[2025-06-17 13:08:58.693 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>383.08 USDT</span></span>
[2025-06-17 13:08:58.695 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 383.08
[2025-06-17 13:08:58.696 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 383.08 USDT
[2025-06-17 13:08:58.701 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 13:08:58.702 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:08:58.703 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:08:58.704 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:08:58.706 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:08:58.707 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-17 13:08:58.708 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-17 13:08:58.709 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty ********
[2025-06-17 13:08:58.711 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 392.54 → 383.08 (zmiana: 2.41 %)
[2025-06-17 13:08:58.716 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Czyszczenie i normalizacja tytułów:
[2025-06-17 13:08:58.717 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: 🪑👑👹Ingrid a3 Pretus a3, Valderon a3!!! Top account ❤️‍🔥❤️‍🔥❤️‍🔥
[2025-06-17 13:08:58.718 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: 🪑👑👹Ingrid a3 Pretus a3, Valderon a3!!! Top account ❤️‍🔥❤️‍🔥❤️‍🔥
[2025-06-17 13:08:58.719 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: 🪑👑👹ingrid a3 pretus a3, valderon a3!!! top account ❤️‍🔥❤️‍🔥❤️‍🔥
[2025-06-17 13:08:58.720 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: ingrid a3 pretus a3 | valderon a3!!! top account
[2025-06-17 13:08:58.721 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: ingrid a3 pretus a3 | valderon a3!!! top account
[2025-06-17 13:08:58.722 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: ingrid a3 pretus a3 | valderon a3!!! top account
[2025-06-17 13:08:58.724 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo oczyszczonych tytułów: 65.22 % (próg: 70.00 %)
[2025-06-17 13:08:58.725 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Wykryto zmianę tytułu w ofercie ********: podobieństwo 65.22 %
[2025-06-17 13:08:59.741 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 60
[2025-06-17 13:08:59.742 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 13:08:59.743 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 13:08:59.744 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 13:08:59.746 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:08:59.747 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:08:59.749 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:08:59.751 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:08:59.753 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 13:08:59.754 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 13:08:59.755 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:08:59.756 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:08:59.757 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:08:59.758 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:08:59.759 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 13:08:59.759 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 13:08:59.760 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 13:08:59.761 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 13:08:59.762 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 13:08:59.763 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 13:08:59.764 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 44.71 $</span></span>
[2025-06-17 13:08:59.766 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>49.13 $</span></span>
[2025-06-17 13:08:59.767 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>49.13 $</span></span>
[2025-06-17 13:08:59.768 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>49.13 $</span></span>
[2025-06-17 13:08:59.769 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>44.70 USDT</span></span>
[2025-06-17 13:08:59.771 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 44.70
[2025-06-17 13:08:59.772 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 44.70 USDT
[2025-06-17 13:08:59.774 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 13:08:59.775 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:08:59.776 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:08:59.777 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:08:59.778 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:08:59.779 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-17 13:08:59.780 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-17 13:08:59.781 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty ********
[2025-06-17 13:08:59.782 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 45.80 → 44.70 (zmiana: 2.40 %)
[2025-06-17 13:08:59.784 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Czyszczenie i normalizacja tytułów:
[2025-06-17 13:08:59.784 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Viksvel kaimer vizig 5+6 kb 1 klush🔥, Account level: 60
[2025-06-17 13:08:59.786 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: Viksvel kaimer vizig 5+6 kb 1 klush🔥
[2025-06-17 13:08:59.786 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: viksvel kaimer vizig 5+6 kb 1 klush🔥
[2025-06-17 13:08:59.788 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: viksvel kaimer vizig 5+6 kb 1 klush
[2025-06-17 13:08:59.788 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: viksvel kaimer vizig 5+6 kb 1 klush
[2025-06-17 13:08:59.789 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: viksvel kaimer vizig 5+6 kb 1 klush
[2025-06-17 13:08:59.790 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo oczyszczonych tytułów: 94.59 % (próg: 70.00 %)
[2025-06-17 13:08:59.792 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty ********
[2025-06-17 13:09:00.808 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Server = Europe (Amsterdam)
[2025-06-17 13:09:00.809 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Weapon type = Melee weapons
[2025-06-17 13:09:00.811 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: PvE Fame (m) = 53
[2025-06-17 13:09:00.812 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 13:09:00.813 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 13:09:00.814 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 13:09:00.816 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:09:00.817 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:09:00.820 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:09:00.821 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:09:00.823 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 13:09:00.824 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:09:00.825 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:09:00.825 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:09:00.826 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:09:00.827 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 13:09:00.828 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 13:09:00.829 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 13:09:00.830 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 13:09:00.831 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 13:09:00.832 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 13:09:00.833 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 89.40 $</span></span>
[2025-06-17 13:09:00.834 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>98.24 $</span></span>
[2025-06-17 13:09:00.835 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>98.24 $</span></span>
[2025-06-17 13:09:00.837 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>98.24 $</span></span>
[2025-06-17 13:09:00.838 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>89.39 USDT</span></span>
[2025-06-17 13:09:00.839 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 89.39
[2025-06-17 13:09:00.840 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 89.39 USDT
[2025-06-17 13:09:00.843 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:09:00.843 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:09:00.844 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:09:00.845 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:09:00.847 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-17 13:09:00.848 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty ********
[2025-06-17 13:09:00.849 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 91.59 → 89.39 (zmiana: 2.40 %)
[2025-06-17 13:09:00.851 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Czyszczenie i normalizacja tytułów:
[2025-06-17 13:09:00.851 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Personal account ⛏T8 Miner, 💰About 24+Silver🔥 More details in PM 🔥, Melee weapons, Fame 53m
[2025-06-17 13:09:00.852 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: Personal account ⛏T8 Miner, 💰About 24+Silver🔥 More details in PM 🔥
[2025-06-17 13:09:00.853 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: personal account ⛏t8 miner, 💰about 24+silver🔥 more details in pm 🔥
[2025-06-17 13:09:00.854 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: personal account | t8 miner | about 24+silver | more details in pm
[2025-06-17 13:09:00.855 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: personal account | t8 miner | about 24+silver | more details in pm
[2025-06-17 13:09:00.856 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: personal account | t8 miner | about 24+silver | more details in pm
[2025-06-17 13:09:00.858 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo oczyszczonych tytułów: 85.51 % (próg: 70.00 %)
[2025-06-17 13:09:00.859 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty ********
[2025-06-17 13:09:01.743 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 13:09:01.744 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 13:09:01.745 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 13:09:01.747 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:09:01.748 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:09:01.749 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:09:01.750 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:09:01.752 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 13:09:01.753 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 13:09:01.754 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:09:01.755 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:09:01.756 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:09:01.757 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:09:01.757 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 13:09:01.758 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 13:09:01.759 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 13:09:01.760 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 13:09:01.761 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 13:09:01.762 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 13:09:01.763 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 38.32 $</span></span>
[2025-06-17 13:09:01.764 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>42.10 $</span></span>
[2025-06-17 13:09:01.765 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>42.10 $</span></span>
[2025-06-17 13:09:01.766 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>42.10 $</span></span>
[2025-06-17 13:09:01.767 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>38.31 USDT</span></span>
[2025-06-17 13:09:01.769 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 38.31
[2025-06-17 13:09:01.770 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 38.31 USDT
[2025-06-17 13:09:01.771 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 13:09:01.772 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:09:01.773 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:09:01.774 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:09:01.775 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:09:01.775 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-17 13:09:01.776 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-17 13:09:01.777 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty ********
[2025-06-17 13:09:01.778 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 39.25 → 38.31 (zmiana: 2.39 %)
[2025-06-17 13:09:01.780 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Czyszczenie i normalizacja tytułów:
[2025-06-17 13:09:01.781 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Ingrid + Pretus The Pioneers server
[2025-06-17 13:09:01.782 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: Ingrid + Pretus The Pioneers server
[2025-06-17 13:09:01.783 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: ingrid + pretus the pioneers server
[2025-06-17 13:09:01.784 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: ingrid + pretus the pioneers server
[2025-06-17 13:09:01.784 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: ingrid + pretus the pioneers server
[2025-06-17 13:09:01.785 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: ingrid + pretus the pioneers server
[2025-06-17 13:09:01.786 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo oczyszczonych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 13:09:01.787 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty ********
[2025-06-17 13:09:02.669 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 36
[2025-06-17 13:09:02.671 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Epic champions = 77
[2025-06-17 13:09:02.672 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Rare champions = 100500
[2025-06-17 13:09:02.673 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 85
[2025-06-17 13:09:02.675 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 13:09:02.676 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 13:09:02.677 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 13:09:02.679 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:09:02.680 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:09:02.682 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:09:02.683 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:09:02.684 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 13:09:02.685 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 13:09:02.686 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:09:02.687 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:09:02.688 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:09:02.689 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:09:02.690 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 13:09:02.691 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 13:09:02.692 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 13:09:02.693 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 13:09:02.694 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 13:09:02.695 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 13:09:02.696 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 57.48 $</span></span>
[2025-06-17 13:09:02.697 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>63.16 $</span></span>
[2025-06-17 13:09:02.698 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>63.16 $</span></span>
[2025-06-17 13:09:02.700 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>63.16 $</span></span>
[2025-06-17 13:09:02.701 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>57.47 USDT</span></span>
[2025-06-17 13:09:02.702 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 57.47
[2025-06-17 13:09:02.703 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 57.47 USDT
[2025-06-17 13:09:02.704 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 13:09:02.706 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:09:02.707 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:09:02.708 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:09:02.709 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:09:02.710 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-17 13:09:02.711 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-17 13:09:02.712 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty ********
[2025-06-17 13:09:02.713 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 58.88 → 57.47 (zmiana: 2.39 %)
[2025-06-17 13:09:02.715 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Czyszczenie i normalizacja tytułów:
[2025-06-17 13:09:02.716 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: All tops, 66 prems, read the description  All tops, 66 prems, read the description, Legendary champions: 36, Epic champions: 77, Rare champions: 100500, Account level: 85
[2025-06-17 13:09:02.717 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: All tops, 66 prems, read the description  All tops, 66 prems, read the description
[2025-06-17 13:09:02.718 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: all tops, 66 prems, read the description all tops, 66 prems, read the description
[2025-06-17 13:09:02.719 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: all tops | 66 prems | read the description all tops | 66 prems | read the description
[2025-06-17 13:09:02.720 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: all tops | 66 prems | read the description all tops | 66 prems | read the description
[2025-06-17 13:09:02.721 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: all tops | 66 prems | read the description all tops | 66 prems | read the description
[2025-06-17 13:09:02.722 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo oczyszczonych tytułów: 90.59 % (próg: 70.00 %)
[2025-06-17 13:09:02.723 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty ********
[2025-06-17 13:09:03.230 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Mass change detector test completed successfully
[2025-06-17 13:24:05.838 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-17 13:24:05.860 +02:00 INF] ShopBot.Services.CliCommandHandler: 🧪 Testowanie OfferContentChangeDetector...
[2025-06-17 13:24:05.863 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 🧪 Rozpoczynam testy OfferContentChangeDetector
[2025-06-17 13:24:05.868 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test1
[2025-06-17 13:24:05.875 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 200.00 (zmiana: 100.00 %)
[2025-06-17 13:24:05.879 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 💰 Wykryto zmianę ceny w ofercie test1: $100 → $200.00 (zmiana: 100.00 %)
[2025-06-17 13:24:05.882 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test2
[2025-06-17 13:24:05.883 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 108.00 (zmiana: 8.00 %)
[2025-06-17 13:24:05.888 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Pełne przetwarzanie tytułów:
[2025-06-17 13:24:05.889 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 13:24:05.890 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: Test Account
[2025-06-17 13:24:05.891 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary przetworzony: 
[2025-06-17 13:24:05.892 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: 
[2025-06-17 13:24:05.893 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 13:24:05.894 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: Test Account
[2025-06-17 13:24:05.895 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy przetworzony: 
[2025-06-17 13:24:05.896 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: 
[2025-06-17 13:24:05.897 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo przetworzonych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 13:24:05.899 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test2
[2025-06-17 13:24:05.900 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test3
[2025-06-17 13:24:05.901 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 13:24:05.904 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Pełne przetwarzanie tytułów:
[2025-06-17 13:24:05.905 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: World of Warcraft Epic Account Level 60 Legendary Gear
[2025-06-17 13:24:05.906 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: World of Warcraft Epic Account Level 60 Legendary Gear
[2025-06-17 13:24:05.907 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary przetworzony: 
[2025-06-17 13:24:05.908 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: 
[2025-06-17 13:24:05.909 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Mobile Legends Bang Bang Account High Rank Mythic
[2025-06-17 13:24:05.910 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: Mobile Legends Bang Bang Account High Rank Mythic
[2025-06-17 13:24:05.912 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy przetworzony: 
[2025-06-17 13:24:05.913 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: 
[2025-06-17 13:24:05.914 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo przetworzonych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 13:24:05.915 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test3
[2025-06-17 13:24:05.916 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test4
[2025-06-17 13:24:05.917 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 13:24:05.920 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Pełne przetwarzanie tytułów:
[2025-06-17 13:24:05.921 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: World of Warcraft Account Level 60
[2025-06-17 13:24:05.922 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: World of Warcraft Account Level 60
[2025-06-17 13:24:05.923 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary przetworzony: 
[2025-06-17 13:24:05.924 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: 
[2025-06-17 13:24:05.925 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: World of Warcraft Account Level 60 with extras
[2025-06-17 13:24:05.926 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: World of Warcraft Account Level 60 with extras
[2025-06-17 13:24:05.927 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy przetworzony: 
[2025-06-17 13:24:05.928 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: 
[2025-06-17 13:24:05.929 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo przetworzonych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 13:24:05.930 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test4
[2025-06-17 13:24:05.932 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test5
[2025-06-17 13:24:05.933 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 13:24:05.935 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Pełne przetwarzanie tytułów:
[2025-06-17 13:24:05.936 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 13:24:05.937 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: Test Account
[2025-06-17 13:24:05.938 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary przetworzony: 
[2025-06-17 13:24:05.939 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: 
[2025-06-17 13:24:05.940 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 13:24:05.941 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: Test Account
[2025-06-17 13:24:05.942 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy przetworzony: 
[2025-06-17 13:24:05.943 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: 
[2025-06-17 13:24:05.944 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo przetworzonych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 13:24:05.945 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test5
[2025-06-17 13:24:05.946 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test6
[2025-06-17 13:24:05.947 +02:00 WRN] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚠️ Nie można sparsować nowej ceny: 'invalid_price' → ''
[2025-06-17 13:24:05.949 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Pełne przetwarzanie tytułów:
[2025-06-17 13:24:05.950 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 13:24:05.951 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: Test Account
[2025-06-17 13:24:05.953 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary przetworzony: 
[2025-06-17 13:24:05.953 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: 
[2025-06-17 13:24:05.955 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 13:24:05.956 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: Test Account
[2025-06-17 13:24:05.957 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy przetworzony: 
[2025-06-17 13:24:05.958 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: 
[2025-06-17 13:24:05.959 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo przetworzonych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 13:24:05.960 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test6
[2025-06-17 13:24:05.963 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 📊 Wyniki testów: 6/7 testów przeszło pomyślnie
[2025-06-17 13:24:05.964 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Znacząca zmiana ceny: Oczekiwano: True, Otrzymano: True (100% wzrost ceny)
[2025-06-17 13:24:05.966 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana ceny: Oczekiwano: False, Otrzymano: False (8% wzrost ceny)
[2025-06-17 13:24:05.967 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ❌ Znacząca zmiana tytułu: Oczekiwano: True, Otrzymano: False (WoW Epic → Mobile Legends)
[2025-06-17 13:24:05.969 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana tytułu: Oczekiwano: False, Otrzymano: False (dodano 'with extras')
[2025-06-17 13:24:05.970 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Brak zmian: Oczekiwano: False, Otrzymano: False (identyczne dane)
[2025-06-17 13:24:05.972 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieprawidłowa cena: Oczekiwano: False, Otrzymano: False (graceful handling)
[2025-06-17 13:24:05.973 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Progi algorytmu: Próg ceny: 0,10 (oczekiwano: 0,10), Próg tytułu: 0,7 (oczekiwano: 0,7)
[2025-06-17 13:24:05.974 +02:00 WRN] ShopBot.Services.OfferContentChangeDetectorTestRunner: ⚠️ 1 testów nie powiodło się
[2025-06-17 13:24:05.976 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Test change detector completed successfully
[2025-06-17 13:37:38.305 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-17 13:37:38.326 +02:00 INF] ShopBot.Services.CliCommandHandler: 🧪 Testowanie OfferContentChangeDetector...
[2025-06-17 13:37:38.328 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 🧪 Rozpoczynam testy OfferContentChangeDetector
[2025-06-17 13:37:38.333 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test1
[2025-06-17 13:37:38.341 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 200.00 (zmiana: 100.00 %)
[2025-06-17 13:37:38.346 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 💰 Wykryto zmianę ceny w ofercie test1: $100 → $200.00 (zmiana: 100.00 %)
[2025-06-17 13:37:38.350 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test2
[2025-06-17 13:37:38.351 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 108.00 (zmiana: 8.00 %)
[2025-06-17 13:37:38.358 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Pełne przetwarzanie tytułów:
[2025-06-17 13:37:38.360 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 13:37:38.361 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: Test Account
[2025-06-17 13:37:38.363 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary przetworzony: 
[2025-06-17 13:37:38.364 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: 
[2025-06-17 13:37:38.366 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 13:37:38.367 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: Test Account
[2025-06-17 13:37:38.369 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy przetworzony: 
[2025-06-17 13:37:38.370 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: 
[2025-06-17 13:37:38.372 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo przetworzonych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 13:37:38.375 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test2
[2025-06-17 13:37:38.376 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test3
[2025-06-17 13:37:38.378 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 13:37:38.382 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Pełne przetwarzanie tytułów:
[2025-06-17 13:37:38.384 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: World of Warcraft Epic Account Level 60 Legendary Gear
[2025-06-17 13:37:38.385 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: World of Warcraft Epic Account Level 60 Legendary Gear
[2025-06-17 13:37:38.387 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary przetworzony: 
[2025-06-17 13:37:38.388 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: 
[2025-06-17 13:37:38.389 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Mobile Legends Bang Bang Account High Rank Mythic
[2025-06-17 13:37:38.390 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: Mobile Legends Bang Bang Account High Rank Mythic
[2025-06-17 13:37:38.391 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy przetworzony: 
[2025-06-17 13:37:38.393 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: 
[2025-06-17 13:37:38.394 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo przetworzonych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 13:37:38.396 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test3
[2025-06-17 13:37:38.398 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test4
[2025-06-17 13:37:38.399 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 13:37:38.403 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Pełne przetwarzanie tytułów:
[2025-06-17 13:37:38.404 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: World of Warcraft Account Level 60
[2025-06-17 13:37:38.407 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: World of Warcraft Account Level 60
[2025-06-17 13:37:38.409 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary przetworzony: 
[2025-06-17 13:37:38.411 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: 
[2025-06-17 13:37:38.412 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: World of Warcraft Account Level 60 with extras
[2025-06-17 13:37:38.413 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: World of Warcraft Account Level 60 with extras
[2025-06-17 13:37:38.414 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy przetworzony: 
[2025-06-17 13:37:38.415 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: 
[2025-06-17 13:37:38.416 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo przetworzonych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 13:37:38.418 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test4
[2025-06-17 13:37:38.419 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test5
[2025-06-17 13:37:38.420 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 100 → 100.00 (zmiana: 0.00 %)
[2025-06-17 13:37:38.423 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Pełne przetwarzanie tytułów:
[2025-06-17 13:37:38.424 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 13:37:38.426 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: Test Account
[2025-06-17 13:37:38.427 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary przetworzony: 
[2025-06-17 13:37:38.428 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: 
[2025-06-17 13:37:38.429 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 13:37:38.430 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: Test Account
[2025-06-17 13:37:38.431 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy przetworzony: 
[2025-06-17 13:37:38.432 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: 
[2025-06-17 13:37:38.433 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo przetworzonych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 13:37:38.435 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test5
[2025-06-17 13:37:38.436 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty test6
[2025-06-17 13:37:38.438 +02:00 WRN] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚠️ Nie można sparsować nowej ceny: 'invalid_price' → ''
[2025-06-17 13:37:38.440 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Pełne przetwarzanie tytułów:
[2025-06-17 13:37:38.441 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Test Account
[2025-06-17 13:37:38.443 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: Test Account
[2025-06-17 13:37:38.444 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary przetworzony: 
[2025-06-17 13:37:38.445 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: 
[2025-06-17 13:37:38.446 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: Test Account
[2025-06-17 13:37:38.447 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: Test Account
[2025-06-17 13:37:38.448 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy przetworzony: 
[2025-06-17 13:37:38.449 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: 
[2025-06-17 13:37:38.450 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo przetworzonych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 13:37:38.451 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty test6
[2025-06-17 13:37:38.453 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: 📊 Wyniki testów: 6/7 testów przeszło pomyślnie
[2025-06-17 13:37:38.455 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Znacząca zmiana ceny: Oczekiwano: True, Otrzymano: True (100% wzrost ceny)
[2025-06-17 13:37:38.456 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana ceny: Oczekiwano: False, Otrzymano: False (8% wzrost ceny)
[2025-06-17 13:37:38.458 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ❌ Znacząca zmiana tytułu: Oczekiwano: True, Otrzymano: False (WoW Epic → Mobile Legends)
[2025-06-17 13:37:38.459 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieznaczna zmiana tytułu: Oczekiwano: False, Otrzymano: False (dodano 'with extras')
[2025-06-17 13:37:38.460 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Brak zmian: Oczekiwano: False, Otrzymano: False (identyczne dane)
[2025-06-17 13:37:38.462 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Nieprawidłowa cena: Oczekiwano: False, Otrzymano: False (graceful handling)
[2025-06-17 13:37:38.463 +02:00 INF] ShopBot.Services.OfferContentChangeDetectorTestRunner: ✅ Progi algorytmu: Próg ceny: 0,10 (oczekiwano: 0,10), Próg tytułu: 0,7 (oczekiwano: 0,7)
[2025-06-17 13:37:38.464 +02:00 WRN] ShopBot.Services.OfferContentChangeDetectorTestRunner: ⚠️ 1 testów nie powiodło się
[2025-06-17 13:37:38.466 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Test change detector completed successfully
[2025-06-17 13:38:24.995 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-17 13:38:25.018 +02:00 INF] ShopBot.Services.CliCommandHandler: 🧪 Testowanie OfferContentChangeDetector na wszystkich ofertach...
[2025-06-17 13:38:27.417 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 13:38:27.420 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 13:38:27.421 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 13:38:27.426 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:38:27.428 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:38:27.438 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:38:27.439 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:38:27.442 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 13:38:27.443 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 13:38:27.444 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:38:27.445 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:38:27.446 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:38:27.447 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:38:27.448 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 13:38:27.450 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 13:38:27.450 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 13:38:27.451 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 13:38:27.452 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 13:38:27.455 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 13:38:27.456 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 383.09 $</span></span>
[2025-06-17 13:38:27.458 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>420.97 $</span></span>
[2025-06-17 13:38:27.459 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>420.97 $</span></span>
[2025-06-17 13:38:27.460 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>420.97 $</span></span>
[2025-06-17 13:38:27.462 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>383.08 USDT</span></span>
[2025-06-17 13:38:27.463 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 383.08
[2025-06-17 13:38:27.465 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 383.08 USDT
[2025-06-17 13:38:27.470 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 13:38:27.471 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:38:27.472 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:38:27.474 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:38:27.475 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:38:27.476 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-17 13:38:27.477 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-17 13:38:27.478 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty ********
[2025-06-17 13:38:27.480 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 392.54 → 383.08 (zmiana: 2.41 %)
[2025-06-17 13:38:27.484 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Pełne przetwarzanie tytułów:
[2025-06-17 13:38:27.485 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: 🪑👑👹Ingrid a3 Pretus a3, Valderon a3!!! Top account ❤️‍🔥❤️‍🔥❤️‍🔥
[2025-06-17 13:38:27.486 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: 🪑👑👹Ingrid a3 Pretus a3, Valderon a3!!! Top account ❤️‍🔥❤️‍🔥❤️‍🔥
[2025-06-17 13:38:27.488 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary przetworzony: | !!! ️‍ ️‍ ️‍
[2025-06-17 13:38:27.489 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: | !!! ️‍ ️‍ ️‍
[2025-06-17 13:38:27.490 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: ingrid a3 pretus a3 | valderon a3!!! top account
[2025-06-17 13:38:27.491 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: ingrid a3 pretus a3 | valderon a3!!! top account
[2025-06-17 13:38:27.492 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy przetworzony: !!!
[2025-06-17 13:38:27.493 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: !!!
[2025-06-17 13:38:27.494 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ⚡ Wczesne przerwanie - duża różnica długości: 14 vs 3
[2025-06-17 13:38:27.496 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo przetworzonych tytułów: 0.00 % (próg: 70.00 %)
[2025-06-17 13:38:27.497 +02:00 INF] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Wykryto zmianę tytułu w ofercie ********: podobieństwo 0.00 %
[2025-06-17 13:38:28.007 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Mass change detector test completed successfully
[2025-06-17 13:39:10.719 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-17 13:39:10.745 +02:00 INF] ShopBot.Services.CliCommandHandler: 🧪 Testowanie OfferContentChangeDetector na wszystkich ofertach...
[2025-06-17 13:39:12.815 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 13:39:12.818 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 13:39:12.819 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 13:39:12.825 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:39:12.827 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:39:12.836 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:39:12.837 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:39:12.840 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 13:39:12.841 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 13:39:12.842 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:39:12.843 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:39:12.844 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:39:12.845 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:39:12.846 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 13:39:12.847 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 13:39:12.848 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 13:39:12.849 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 13:39:12.850 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 13:39:12.853 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 13:39:12.854 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 383.09 $</span></span>
[2025-06-17 13:39:12.856 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>420.97 $</span></span>
[2025-06-17 13:39:12.857 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>420.97 $</span></span>
[2025-06-17 13:39:12.859 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>420.97 $</span></span>
[2025-06-17 13:39:12.860 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>383.08 USDT</span></span>
[2025-06-17 13:39:12.862 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 383.08
[2025-06-17 13:39:12.863 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 383.08 USDT
[2025-06-17 13:39:12.868 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 13:39:12.869 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:39:12.870 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:39:12.871 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:39:12.872 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:39:12.873 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-17 13:39:12.874 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-17 13:39:12.875 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty ********
[2025-06-17 13:39:12.877 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 392.54 → 383.08 (zmiana: 2.41 %)
[2025-06-17 13:39:12.881 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Pełne przetwarzanie tytułów:
[2025-06-17 13:39:12.882 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: 🪑👑👹Ingrid a3 Pretus a3, Valderon a3!!! Top account ❤️‍🔥❤️‍🔥❤️‍🔥
[2025-06-17 13:39:12.883 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: 🪑👑👹Ingrid a3 Pretus a3, Valderon a3!!! Top account ❤️‍🔥❤️‍🔥❤️‍🔥
[2025-06-17 13:39:12.884 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary przetworzony: Ingrid a3 Pretus a3 | Valderon a3!!! Top account ‍ ‍ ‍
[2025-06-17 13:39:12.884 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: ingrid a3 pretus a3 | valderon a3!!! top account ‍ ‍ ‍
[2025-06-17 13:39:12.885 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: ingrid a3 pretus a3 | valderon a3!!! top account
[2025-06-17 13:39:12.887 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: ingrid a3 pretus a3 | valderon a3!!! top account
[2025-06-17 13:39:12.888 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy przetworzony: ingrid a3 pretus a3 | valderon a3!!! top account
[2025-06-17 13:39:12.889 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: ingrid a3 pretus a3 | valderon a3!!! top account
[2025-06-17 13:39:12.890 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo przetworzonych tytułów: 88.89 % (próg: 70.00 %)
[2025-06-17 13:39:12.892 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty ********
[2025-06-17 13:39:13.395 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Mass change detector test completed successfully
[2025-06-17 13:39:40.617 +02:00 INF] ShopBot.Services.DropboxService: [Dropbox] Using OAuth refresh token system
[2025-06-17 13:39:40.640 +02:00 INF] ShopBot.Services.CliCommandHandler: 🧪 Testowanie OfferContentChangeDetector na wszystkich ofertach...
[2025-06-17 13:39:42.745 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 13:39:42.747 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 13:39:42.749 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 13:39:42.755 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:39:42.757 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:39:42.765 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:39:42.766 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:39:42.770 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 13:39:42.771 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 13:39:42.772 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:39:42.772 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:39:42.773 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:39:42.774 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:39:42.775 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 13:39:42.776 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 13:39:42.777 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 13:39:42.778 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 13:39:42.779 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 13:39:42.781 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 13:39:42.783 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 383.09 $</span></span>
[2025-06-17 13:39:42.784 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>420.97 $</span></span>
[2025-06-17 13:39:42.786 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>420.97 $</span></span>
[2025-06-17 13:39:42.787 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>420.97 $</span></span>
[2025-06-17 13:39:42.789 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>383.08 USDT</span></span>
[2025-06-17 13:39:42.790 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 383.08
[2025-06-17 13:39:42.791 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 383.08 USDT
[2025-06-17 13:39:42.798 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 13:39:42.799 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:39:42.800 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:39:42.801 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:39:42.802 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:39:42.804 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-17 13:39:42.805 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-17 13:39:42.807 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty ********
[2025-06-17 13:39:42.809 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 392.54 → 383.08 (zmiana: 2.41 %)
[2025-06-17 13:39:42.813 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Pełne przetwarzanie tytułów:
[2025-06-17 13:39:42.814 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: 🪑👑👹Ingrid a3 Pretus a3, Valderon a3!!! Top account ❤️‍🔥❤️‍🔥❤️‍🔥
[2025-06-17 13:39:42.815 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: 🪑👑👹Ingrid a3 Pretus a3, Valderon a3!!! Top account ❤️‍🔥❤️‍🔥❤️‍🔥
[2025-06-17 13:39:42.816 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary przetworzony: Ingrid a3 Pretus a3 | Valderon a3!!! Top account ‍ ‍ ‍
[2025-06-17 13:39:42.817 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: ingrid a3 pretus a3 | valderon a3!!! top account ‍ ‍ ‍
[2025-06-17 13:39:42.818 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: ingrid a3 pretus a3 | valderon a3!!! top account
[2025-06-17 13:39:42.819 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: ingrid a3 pretus a3 | valderon a3!!! top account
[2025-06-17 13:39:42.821 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy przetworzony: ingrid a3 pretus a3 | valderon a3!!! top account
[2025-06-17 13:39:42.822 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: ingrid a3 pretus a3 | valderon a3!!! top account
[2025-06-17 13:39:42.823 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo przetworzonych tytułów: 88.89 % (próg: 70.00 %)
[2025-06-17 13:39:42.825 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty ********
[2025-06-17 13:39:43.816 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 60
[2025-06-17 13:39:43.817 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 13:39:43.819 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 13:39:43.820 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 13:39:43.822 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:39:43.823 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:39:43.826 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:39:43.827 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:39:43.829 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 13:39:43.830 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 13:39:43.831 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:39:43.832 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:39:43.833 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:39:43.834 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:39:43.835 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 13:39:43.836 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 13:39:43.837 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 13:39:43.838 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 13:39:43.839 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 13:39:43.840 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 13:39:43.842 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 44.71 $</span></span>
[2025-06-17 13:39:43.843 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>49.13 $</span></span>
[2025-06-17 13:39:43.844 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>49.13 $</span></span>
[2025-06-17 13:39:43.846 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>49.13 $</span></span>
[2025-06-17 13:39:43.847 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>44.70 USDT</span></span>
[2025-06-17 13:39:43.849 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 44.70
[2025-06-17 13:39:43.850 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 44.70 USDT
[2025-06-17 13:39:43.854 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 13:39:43.855 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:39:43.856 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:39:43.857 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:39:43.858 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:39:43.859 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-17 13:39:43.860 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-17 13:39:43.861 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty ********
[2025-06-17 13:39:43.862 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 45.80 → 44.70 (zmiana: 2.40 %)
[2025-06-17 13:39:43.864 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Pełne przetwarzanie tytułów:
[2025-06-17 13:39:43.865 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Viksvel kaimer vizig 5+6 kb 1 klush🔥, Account level: 60
[2025-06-17 13:39:43.866 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: Viksvel kaimer vizig 5+6 kb 1 klush🔥
[2025-06-17 13:39:43.867 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary przetworzony: Viksvel kaimer vizig 5+6 kb 1 klush
[2025-06-17 13:39:43.868 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: viksvel kaimer vizig 5+6 kb 1 klush
[2025-06-17 13:39:43.869 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: viksvel kaimer vizig 5+6 kb 1 klush
[2025-06-17 13:39:43.870 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: viksvel kaimer vizig 5+6 kb 1 klush
[2025-06-17 13:39:43.871 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy przetworzony: viksvel kaimer vizig 5+6 kb 1 klush
[2025-06-17 13:39:43.872 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: viksvel kaimer vizig 5+6 kb 1 klush
[2025-06-17 13:39:43.873 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo przetworzonych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 13:39:43.875 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty ********
[2025-06-17 13:39:44.880 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Server = Europe (Amsterdam)
[2025-06-17 13:39:44.882 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Weapon type = Melee weapons
[2025-06-17 13:39:44.883 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: PvE Fame (m) = 53
[2025-06-17 13:39:44.884 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 13:39:44.886 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 13:39:44.887 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 13:39:44.889 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:39:44.891 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:39:44.895 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:39:44.896 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:39:44.898 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 13:39:44.898 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:39:44.899 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:39:44.900 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:39:44.901 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:39:44.902 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 13:39:44.903 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 13:39:44.904 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 13:39:44.905 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 13:39:44.906 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 13:39:44.907 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 13:39:44.909 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 89.40 $</span></span>
[2025-06-17 13:39:44.910 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>98.24 $</span></span>
[2025-06-17 13:39:44.911 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>98.24 $</span></span>
[2025-06-17 13:39:44.912 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>98.24 $</span></span>
[2025-06-17 13:39:44.913 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>89.39 USDT</span></span>
[2025-06-17 13:39:44.915 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 89.39
[2025-06-17 13:39:44.916 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 89.39 USDT
[2025-06-17 13:39:44.919 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:39:44.920 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:39:44.921 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:39:44.922 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:39:44.923 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-17 13:39:44.924 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty ********
[2025-06-17 13:39:44.925 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 91.59 → 89.39 (zmiana: 2.40 %)
[2025-06-17 13:39:44.926 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Pełne przetwarzanie tytułów:
[2025-06-17 13:39:44.927 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Personal account ⛏T8 Miner, 💰About 24+Silver🔥 More details in PM 🔥, Melee weapons, Fame 53m
[2025-06-17 13:39:44.928 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: Personal account ⛏T8 Miner, 💰About 24+Silver🔥 More details in PM 🔥
[2025-06-17 13:39:44.929 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary przetworzony: Personal account T8 Miner | About 24+Silver More details in PM
[2025-06-17 13:39:44.930 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: personal account t8 miner | about 24+silver more details in pm
[2025-06-17 13:39:44.931 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: personal account | t8 miner | about 24+silver | more details in pm
[2025-06-17 13:39:44.932 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: personal account | t8 miner | about 24+silver | more details in pm
[2025-06-17 13:39:44.933 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy przetworzony: personal account | t8 miner | about 24+silver | more details in pm
[2025-06-17 13:39:44.934 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: personal account | t8 miner | about 24+silver | more details in pm
[2025-06-17 13:39:44.935 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo przetworzonych tytułów: 93.94 % (próg: 70.00 %)
[2025-06-17 13:39:44.937 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty ********
[2025-06-17 13:39:45.791 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 13:39:45.792 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 13:39:45.793 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 13:39:45.795 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:39:45.795 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:39:45.797 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:39:45.798 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:39:45.800 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 13:39:45.801 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 13:39:45.801 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:39:45.802 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:39:45.803 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:39:45.805 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:39:45.805 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 13:39:45.806 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 13:39:45.807 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 13:39:45.808 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 13:39:45.809 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 13:39:45.810 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 13:39:45.811 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 38.32 $</span></span>
[2025-06-17 13:39:45.813 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>42.10 $</span></span>
[2025-06-17 13:39:45.814 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>42.10 $</span></span>
[2025-06-17 13:39:45.815 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>42.10 $</span></span>
[2025-06-17 13:39:45.816 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>38.31 USDT</span></span>
[2025-06-17 13:39:45.817 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 38.31
[2025-06-17 13:39:45.819 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 38.31 USDT
[2025-06-17 13:39:45.820 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 13:39:45.821 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:39:45.822 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:39:45.823 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:39:45.824 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:39:45.824 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-17 13:39:45.825 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-17 13:39:45.826 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty ********
[2025-06-17 13:39:45.827 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 39.25 → 38.31 (zmiana: 2.39 %)
[2025-06-17 13:39:45.829 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Pełne przetwarzanie tytułów:
[2025-06-17 13:39:45.830 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Ingrid + Pretus The Pioneers server
[2025-06-17 13:39:45.831 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: Ingrid + Pretus The Pioneers server
[2025-06-17 13:39:45.832 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary przetworzony: Ingrid + Pretus The Pioneers server
[2025-06-17 13:39:45.833 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: ingrid + pretus the pioneers server
[2025-06-17 13:39:45.834 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: ingrid + pretus the pioneers server
[2025-06-17 13:39:45.834 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: ingrid + pretus the pioneers server
[2025-06-17 13:39:45.835 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy przetworzony: ingrid + pretus the pioneers server
[2025-06-17 13:39:45.836 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: ingrid + pretus the pioneers server
[2025-06-17 13:39:45.837 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo przetworzonych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 13:39:45.839 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty ********
[2025-06-17 13:39:46.745 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Legendary champions = 36
[2025-06-17 13:39:46.747 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Epic champions = 77
[2025-06-17 13:39:46.748 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Rare champions = 100500
[2025-06-17 13:39:46.749 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Account level = 85
[2025-06-17 13:39:46.750 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 13:39:46.752 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 13:39:46.753 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 13:39:46.754 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:39:46.755 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:39:46.757 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:39:46.758 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:39:46.760 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 13:39:46.761 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 13:39:46.762 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:39:46.763 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:39:46.764 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:39:46.765 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:39:46.766 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 13:39:46.767 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 13:39:46.768 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 13:39:46.769 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 13:39:46.770 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 13:39:46.772 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 13:39:46.773 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 57.48 $</span></span>
[2025-06-17 13:39:46.774 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>63.16 $</span></span>
[2025-06-17 13:39:46.775 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>63.16 $</span></span>
[2025-06-17 13:39:46.776 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>63.16 $</span></span>
[2025-06-17 13:39:46.777 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>57.47 USDT</span></span>
[2025-06-17 13:39:46.779 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 57.47
[2025-06-17 13:39:46.780 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 57.47 USDT
[2025-06-17 13:39:46.781 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Server
[2025-06-17 13:39:46.782 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:39:46.783 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:39:46.784 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:39:46.785 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:39:46.786 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: EUW
[2025-06-17 13:39:46.786 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-17 13:39:46.787 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty ********
[2025-06-17 13:39:46.788 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 58.88 → 57.47 (zmiana: 2.39 %)
[2025-06-17 13:39:46.790 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Pełne przetwarzanie tytułów:
[2025-06-17 13:39:46.791 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: All tops, 66 prems, read the description  All tops, 66 prems, read the description, Legendary champions: 36, Epic champions: 77, Rare champions: 100500, Account level: 85
[2025-06-17 13:39:46.792 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: All tops, 66 prems, read the description  All tops, 66 prems, read the description
[2025-06-17 13:39:46.793 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary przetworzony: All tops | 66 prems | read the description All tops | 66 prems | read the description
[2025-06-17 13:39:46.794 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: all tops | 66 prems | read the description all tops | 66 prems | read the description
[2025-06-17 13:39:46.795 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: all tops | 66 prems | read the description all tops | 66 prems | read the description
[2025-06-17 13:39:46.796 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: all tops | 66 prems | read the description all tops | 66 prems | read the description
[2025-06-17 13:39:46.797 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy przetworzony: all tops | 66 prems | read the description all tops | 66 prems | read the description
[2025-06-17 13:39:46.798 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: all tops | 66 prems | read the description all tops | 66 prems | read the description
[2025-06-17 13:39:46.799 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo przetworzonych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 13:39:46.800 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty ********
[2025-06-17 13:39:47.649 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Server = America (Washington)
[2025-06-17 13:39:47.650 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Weapon type = Melee weapons
[2025-06-17 13:39:47.652 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: PvE Fame (m) = 70
[2025-06-17 13:39:47.653 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 13:39:47.654 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 13:39:47.655 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 13:39:47.657 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:39:47.658 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:39:47.660 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:39:47.660 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:39:47.662 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 13:39:47.663 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:39:47.664 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:39:47.665 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:39:47.666 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:39:47.667 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 13:39:47.668 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 13:39:47.668 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 13:39:47.669 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 13:39:47.670 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 13:39:47.671 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 13:39:47.673 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 51.09 $</span></span>
[2025-06-17 13:39:47.674 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>56.14 $</span></span>
[2025-06-17 13:39:47.675 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>56.14 $</span></span>
[2025-06-17 13:39:47.676 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>56.14 $</span></span>
[2025-06-17 13:39:47.677 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>51.08 USDT</span></span>
[2025-06-17 13:39:47.678 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 51.08
[2025-06-17 13:39:47.679 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 51.08 USDT
[2025-06-17 13:39:47.681 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:39:47.682 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:39:47.682 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:39:47.683 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:39:47.685 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-17 13:39:47.686 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty ********
[2025-06-17 13:39:47.686 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 52.34 → 51.08 (zmiana: 2.41 %)
[2025-06-17 13:39:47.688 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Pełne przetwarzanie tytułów:
[2025-06-17 13:39:47.689 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: Cool old account, Melee weapons, Fame 70m
[2025-06-17 13:39:47.690 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: Cool old account
[2025-06-17 13:39:47.691 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary przetworzony: Cool old account
[2025-06-17 13:39:47.692 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: cool old account
[2025-06-17 13:39:47.693 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: cool old account
[2025-06-17 13:39:47.694 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: cool old account
[2025-06-17 13:39:47.695 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy przetworzony: cool old account
[2025-06-17 13:39:47.696 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: cool old account
[2025-06-17 13:39:47.697 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo przetworzonych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 13:39:47.698 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty ********
[2025-06-17 13:39:48.563 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Server = Europe (Amsterdam)
[2025-06-17 13:39:48.565 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Weapon type = Melee weapons
[2025-06-17 13:39:48.566 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: PvE Fame (m) = 98
[2025-06-17 13:39:48.567 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 13:39:48.568 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 13:39:48.570 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 13:39:48.571 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:39:48.572 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:39:48.574 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:39:48.575 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:39:48.577 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 13:39:48.578 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:39:48.579 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:39:48.580 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:39:48.581 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:39:48.582 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 13:39:48.582 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 13:39:48.583 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 13:39:48.584 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 13:39:48.585 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 13:39:48.587 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 13:39:48.588 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 95.69 $</span></span>
[2025-06-17 13:39:48.589 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>105.15 $</span></span>
[2025-06-17 13:39:48.591 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>105.15 $</span></span>
[2025-06-17 13:39:48.592 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>105.15 $</span></span>
[2025-06-17 13:39:48.593 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>95.68 USDT</span></span>
[2025-06-17 13:39:48.594 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 95.68
[2025-06-17 13:39:48.596 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 95.68 USDT
[2025-06-17 13:39:48.597 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:39:48.598 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:39:48.599 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:39:48.600 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:39:48.601 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: 35936102
[2025-06-17 13:39:48.602 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty 35936102
[2025-06-17 13:39:48.603 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 97.96 → 95.68 (zmiana: 2.33 %)
[2025-06-17 13:39:48.607 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Pełne przetwarzanie tytułów:
[2025-06-17 13:39:48.608 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: EU + TOTAL FAME 98M + SILVER 50M + Gathering T4 +TANK / HEALER / SC, Melee weapons, Fame 98m
[2025-06-17 13:39:48.609 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: EU + TOTAL FAME 98M + SILVER 50M + Gathering T4 +TANK / HEALER / SC
[2025-06-17 13:39:48.610 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary przetworzony: EU + TOTAL FAME 98M + SILVER 50M + Gathering T4 +TANK / HEALER / SC
[2025-06-17 13:39:48.611 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: eu + total fame 98m + silver 50m + gathering t4 +tank / healer / sc
[2025-06-17 13:39:48.612 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: eu + total fame 98m + silver 50m + gathering t4 +tank / healer / sc
[2025-06-17 13:39:48.613 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: eu + total fame 98m + silver 50m + gathering t4 +tank / healer / sc
[2025-06-17 13:39:48.614 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy przetworzony: eu + total fame 98m + silver 50m + gathering t4 +tank / healer / sc
[2025-06-17 13:39:48.615 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: eu + total fame 98m + silver 50m + gathering t4 +tank / healer / sc
[2025-06-17 13:39:48.616 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo przetworzonych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 13:39:48.618 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty 35936102
[2025-06-17 13:39:49.439 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Server = Europe (Amsterdam)
[2025-06-17 13:39:49.441 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Weapon type = Ranged weapons
[2025-06-17 13:39:49.442 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: PvE Fame (m) = 1
[2025-06-17 13:39:49.443 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 2 pcs.
[2025-06-17 13:39:49.445 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 13:39:49.446 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 13:39:49.447 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:39:49.448 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:39:49.450 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:39:49.451 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:39:49.452 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 13:39:49.453 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:39:49.454 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:39:49.455 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:39:49.456 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:39:49.457 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 13:39:49.458 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 13:39:49.459 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 13:39:49.460 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 13:39:49.461 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 13:39:49.462 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 13:39:49.463 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 15.34 $</span></span>
[2025-06-17 13:39:49.464 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>16.85 $</span></span>
[2025-06-17 13:39:49.465 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>16.85 $</span></span>
[2025-06-17 13:39:49.467 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>16.85 $</span></span>
[2025-06-17 13:39:49.468 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>15.33 USDT</span></span>
[2025-06-17 13:39:49.469 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 15.33
[2025-06-17 13:39:49.470 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 15.33 USDT
[2025-06-17 13:39:49.471 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:39:49.472 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:39:49.473 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:39:49.474 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:39:49.475 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: ********
[2025-06-17 13:39:49.476 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty ********
[2025-06-17 13:39:49.477 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 15.70 → 15.33 (zmiana: 2.36 %)
[2025-06-17 13:39:49.479 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Pełne przetwarzanie tytułów:
[2025-06-17 13:39:49.480 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: I will create an account for you with a premium for 33 days, Ranged weapons, Fame 1m
[2025-06-17 13:39:49.481 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: I will create an account for you with a premium for 33 days
[2025-06-17 13:39:49.482 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary przetworzony: I will create an account for you with a premium for 33 days
[2025-06-17 13:39:49.484 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: i will create an account for you with a premium for 33 days
[2025-06-17 13:39:49.485 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: i will create an account for you with a premium for 33 days
[2025-06-17 13:39:49.485 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: i will create an account for you with a premium for 33 days
[2025-06-17 13:39:49.486 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy przetworzony: i will create an account for you with a premium for 33 days
[2025-06-17 13:39:49.487 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: i will create an account for you with a premium for 33 days
[2025-06-17 13:39:49.488 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo przetworzonych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 13:39:49.490 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty ********
[2025-06-17 13:39:50.413 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Server = Europe (Amsterdam)
[2025-06-17 13:39:50.414 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Weapon type = Ranged weapons
[2025-06-17 13:39:50.415 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: PvE Fame (m) = 4
[2025-06-17 13:39:50.416 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 13:39:50.417 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 13:39:50.419 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 13:39:50.420 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:39:50.421 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:39:50.423 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:39:50.424 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:39:50.426 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 13:39:50.427 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:39:50.427 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:39:50.428 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:39:50.429 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:39:50.430 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 13:39:50.431 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 13:39:50.432 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 13:39:50.433 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 13:39:50.434 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 13:39:50.435 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 13:39:50.436 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 17.89 $</span></span>
[2025-06-17 13:39:50.438 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>19.65 $</span></span>
[2025-06-17 13:39:50.439 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>19.65 $</span></span>
[2025-06-17 13:39:50.441 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>19.65 $</span></span>
[2025-06-17 13:39:50.442 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>17.88 USDT</span></span>
[2025-06-17 13:39:50.443 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 17.88
[2025-06-17 13:39:50.444 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 17.88 USDT
[2025-06-17 13:39:50.446 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:39:50.447 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:39:50.448 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:39:50.449 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:39:50.450 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: 41518288
[2025-06-17 13:39:50.451 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty 41518288
[2025-06-17 13:39:50.451 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 18.32 → 17.88 (zmiana: 2.40 %)
[2025-06-17 13:39:50.453 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Pełne przetwarzanie tytułów:
[2025-06-17 13:39:50.454 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: T6 Miner, Badon's Bow has been upgraded, Dagger shot, a little kingmaker, Ranged weapons, Fame 4m
[2025-06-17 13:39:50.455 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: T6 Miner, Badon's Bow has been upgraded, Dagger shot, a little kingmaker
[2025-06-17 13:39:50.456 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary przetworzony: T6 Miner | Badon's Bow has been upgraded | Dagger shot | a little kingmaker
[2025-06-17 13:39:50.457 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: t6 miner | badon's bow has been upgraded | dagger shot | a little kingmaker
[2025-06-17 13:39:50.458 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: t6 miner | badon's bow has been upgraded | dagger shot | a little kingmaker
[2025-06-17 13:39:50.459 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: t6 miner | badon's bow has been upgraded | dagger shot | a little kingmaker
[2025-06-17 13:39:50.460 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy przetworzony: t6 miner | badon's bow has been upgraded | dagger shot | a little kingmaker
[2025-06-17 13:39:50.461 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: t6 miner | badon's bow has been upgraded | dagger shot | a little kingmaker
[2025-06-17 13:39:50.462 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo przetworzonych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 13:39:50.463 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty 41518288
[2025-06-17 13:39:51.314 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Server = Europe (Amsterdam)
[2025-06-17 13:39:51.316 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Weapon type = Magical weapons
[2025-06-17 13:39:51.317 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: PvE Fame (m) = 38
[2025-06-17 13:39:51.318 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: In&nbsp;stock = 1 pcs.
[2025-06-17 13:39:51.319 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Your account will be charged for&nbsp; = 0&nbsp; from&nbsp;0&nbsp;
[2025-06-17 13:39:51.321 +02:00 DBG] ShopBot.Services.ParserService: [Parser] Znaleziono parametr: Remaining price = 0&nbsp;
[2025-06-17 13:39:51.322 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:39:51.323 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:39:51.325 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Getting formatter for game: null
[2025-06-17 13:39:51.326 +02:00 INF] ShopBot.Services.Formatters.DescriptionFormatterFactory: Using default formatter for null game name
[2025-06-17 13:39:51.328 +02:00 WRN] ShopBot.Services.Formatters.DefaultDescriptionFormatter: No ID found in parameters!
[2025-06-17 13:39:51.329 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:39:51.329 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:39:51.330 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:39:51.331 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:39:51.332 +02:00 INF] ShopBot.Services.ParserService: [Parser] FC 25 Details:
[2025-06-17 13:39:51.333 +02:00 INF] ShopBot.Services.ParserService:   Squad Price: Not found
[2025-06-17 13:39:51.334 +02:00 INF] ShopBot.Services.ParserService:   Coins Balance: Not found
[2025-06-17 13:39:51.335 +02:00 INF] ShopBot.Services.ParserService:   Web App: Not found
[2025-06-17 13:39:51.336 +02:00 INF] ShopBot.Services.ParserService:   Rating FC 25: Not found
[2025-06-17 13:39:51.337 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💳 Znaleziono 6 opcji płatności
[2025-06-17 13:39:51.338 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: &nbsp; | Data: <span class=&quot;payment&quot;><span class=&quot;payment-title&quot;>Not selected</span><span class=&quot;payment-value&quot;>from 63.86 $</span></span>
[2025-06-17 13:39:51.339 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Apple Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-20&quot;></span><span class=&quot;payment-title&quot;>Apple Pay</span><span class=&quot;payment-value&quot;>70.17 $</span></span>
[2025-06-17 13:39:51.340 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Google Pay | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-18&quot;></span><span class=&quot;payment-title&quot;>Google Pay</span><span class=&quot;payment-value&quot;>70.17 $</span></span>
[2025-06-17 13:39:51.341 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: Bank card | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-35&quot;></span><span class=&quot;payment-title&quot;>Bank card</span><span class=&quot;payment-value&quot;>70.17 $</span></span>
[2025-06-17 13:39:51.343 +02:00 DBG] ShopBot.Services.ParserService: [Parser] 🔍 Opcja płatności: USDT TRC20 | Data: <span class=&quot;payment&quot;><span class=&quot;payment-logo payment-method-41&quot;></span><span class=&quot;payment-title&quot;>USDT TRC20</span><span class=&quot;payment-value&quot;>63.85 USDT</span></span>
[2025-06-17 13:39:51.344 +02:00 INF] ShopBot.Services.ParserService: [Parser] 💰 Znaleziono cenę USDT: 63.85
[2025-06-17 13:39:51.345 +02:00 INF] ShopBot.Services.ParserService: [Parser] 🎯 Finalna cena: 63.85 USDT
[2025-06-17 13:39:51.346 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Level
[2025-06-17 13:39:51.347 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Champions
[2025-06-17 13:39:51.348 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Skins
[2025-06-17 13:39:51.349 +02:00 WRN] ShopBot.Services.ParserService: [Parser] ⚠️ Nie znaleziono parametru: Rank
[2025-06-17 13:39:51.350 +02:00 INF] ShopBot.Services.ParserService: [Parser] ✅ Utworzono OfferDetails z ID: 30184133
[2025-06-17 13:39:51.351 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔍 Sprawdzam zmiany dla oferty 30184133
[2025-06-17 13:39:51.352 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📊 Zmiana ceny: 65.43 → 63.85 (zmiana: 2.41 %)
[2025-06-17 13:39:51.353 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 🔄 Pełne przetwarzanie tytułów:
[2025-06-17 13:39:51.355 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary surowy: fire mage staff  8.2 set, Magical weapons, Fame 38m
[2025-06-17 13:39:51.356 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary oczyszczony: fire mage staff  8.2 set
[2025-06-17 13:39:51.356 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary przetworzony: fire mage staff 8.2 set
[2025-06-17 13:39:51.357 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Stary znormalizowany: fire mage staff 8.2 set
[2025-06-17 13:39:51.358 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy surowy: fire mage staff 8.2 set
[2025-06-17 13:39:51.359 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy oczyszczony: fire mage staff 8.2 set
[2025-06-17 13:39:51.360 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy przetworzony: fire mage staff 8.2 set
[2025-06-17 13:39:51.361 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector]   Nowy znormalizowany: fire mage staff 8.2 set
[2025-06-17 13:39:51.362 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] 📝 Podobieństwo przetworzonych tytułów: 100.00 % (próg: 70.00 %)
[2025-06-17 13:39:51.363 +02:00 DBG] ShopBot.Services.OfferContentChangeDetector: [ChangeDetector] ✅ Brak znaczących zmian dla oferty 30184133
[2025-06-17 13:39:51.878 +02:00 INF] ShopBot.Services.CliCommandHandler: ✅ Mass change detector test completed successfully
