using System;
using System.Text.Json;
using System.Web;
using System.Threading.Tasks;
using Microsoft.Playwright;

namespace ShopBot
{
    public class Browser
    {
        private static string host = "localhost:8848";
        private static string apiKey = "3c8feb21-76eb-4bb0-9c9b-ff7e3c085d07";

        public static async Task<IBrowser> ConnectToExistingBrowserAsync(string profileId, bool headless = false, bool autoClose = false)
        {
            var config = new
            {
                headless = headless,
                autoClose = autoClose
            };

            var query = HttpUtility.ParseQueryString(string.Empty);
            query["x-api-key"] = apiKey;
            query["config"] = Uri.EscapeDataString(JsonSerializer.Serialize(config));

            string browserWSEndpoint = $"ws://{host}/devtool/launch/{profileId}?{query}";

            return await ConnectPlaywrightAsync(browserWSEndpoint);
        }

        private static async Task<IBrowser> ConnectPlaywrightAsync(string browserWSEndpoint)
        {
            try
            {
                var playwright = await Playwright.CreateAsync();
                return await playwright.Chromium.ConnectOverCDPAsync(browserWSEndpoint);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Błąd podczas łączenia z przeglądarką: {ex.Message}");
                throw;
            }
        }

        public static async Task<IBrowserContext> GetOrCreateContextAsync(IBrowser browser)
        {
            var existingContexts = browser.Contexts;
            if (existingContexts.Count > 0)
            {
                return existingContexts[0];
            }
            else
            {
                return await browser.NewContextAsync();
            }
        }

        public static async Task<IPage> CreatePageAsync(IBrowserContext context, string url)
        {
            var page = await context.NewPageAsync();
            await page.GotoAsync(url);
            return page;
        }
    }
}
