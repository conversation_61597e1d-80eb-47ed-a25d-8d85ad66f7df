using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Microsoft.EntityFrameworkCore;
using Microsoft.Playwright;
using Serilog;
using ShopBot.Config;
using ShopBot.Data;
using ShopBot.Services.Formatters;
using ShopBot.Services.Interfaces;
using ShopBot.Services.Factories;
using System;

namespace ShopBot.Services
{
    /// <summary>
    /// Ulepszona konfiguracja Dependency Injection z pełnym wsparciem dla nowoczesnej architektury
    /// </summary>
    public static class DependencyConfig
    {
        /// <summary>
        /// Konfiguruje wszystkie serwisy aplikacji z pełnym DI
        /// </summary>
        public static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
        {
            // 1. Konfiguracja podstawowa
            ConfigureLogging(services, configuration);
            ConfigureConfiguration(services, configuration);

            // 2. <PERSON><PERSON><PERSON> podstawowe
            ConfigureCoreServices(services);

            // 3. Serwisy biznesowe
            ConfigureBusinessServices(services);

            // 4. Serwisy zewnętrzne
            ConfigureExternalServices(services);

            // 5. Parsery i formattery
            ConfigureParsersAndFormatters(services);

            // 6. Hosted Services
            ConfigureHostedServices(services);
        }

        /// <summary>
        /// Konfiguruje system logowania z Serilog
        /// </summary>
        private static void ConfigureLogging(IServiceCollection services, IConfiguration configuration)
        {
            // Konfiguracja Serilog z appsettings.json
            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(configuration)
                .Enrich.FromLogContext()
                .Enrich.WithProperty("Application", "ShopBot")
                .CreateLogger();

            // Rejestracja logowania
            services.AddLogging(builder =>
            {
                builder.ClearProviders();
                builder.AddSerilog(dispose: true);
            });

            // Rejestracja nowoczesnego serwisu logowania
            services.AddSingleton<ModernLoggingService>();

            // Zachowaj stary LoggerService dla kompatybilności wstecznej (bez TaskScheduler)
            services.AddSingleton<LoggerService>(provider => new LoggerService());

        }

        /// <summary>
        /// Konfiguruje system konfiguracji z walidacją
        /// </summary>
        private static void ConfigureConfiguration(IServiceCollection services, IConfiguration configuration)
        {
            // Rejestracja konfiguracji z walidacją
            services.Configure<AppSettings>(configuration);
            services.AddSingleton(provider => provider.GetRequiredService<IOptions<AppSettings>>().Value);

            // Configuration Repository - unified access do wszystkich konfiguracji
            services.AddSingleton<IConfigurationRepository, ConfigurationRepository>();

            // Database Configuration
            var appSettings = configuration.Get<AppSettings>() ?? new AppSettings();
            services.AddDbContext<ShopBotDbContext>(options =>
            {
                options.UseMySql(appSettings.Database.ConnectionString,
                    ServerVersion.AutoDetect(appSettings.Database.ConnectionString),
                    mySqlOptions =>
                    {
                        mySqlOptions.CommandTimeout(appSettings.Database.CommandTimeoutSeconds);
                        mySqlOptions.EnableRetryOnFailure(maxRetryCount: 3);
                    });

                // Enable sensitive data logging in development
                if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development")
                {
                    options.EnableSensitiveDataLogging();
                    options.EnableDetailedErrors();
                }
            });

            // Repository Pattern - Database Access Layer
            services.AddScoped<IOfferRepository, OfferRepository>();
            services.AddScoped<IProcessingStatsRepository, ProcessingStatsRepository>();
            services.AddScoped<IImageUploadRepository, ImageUploadRepository>();
            services.AddScoped<IErrorLogRepository, ErrorLogRepository>();
            services.AddScoped<IShopBotRepository, ShopBotRepository>();

            // Database Migration Service
            services.AddScoped<IDatabaseMigrationService, DatabaseMigrationService>();

            // Walidator konfiguracji (legacy - będzie zastąpiony przez Repository)
            services.AddSingleton<IConfigurationValidator, ConfigurationValidator>();

            // Startup validation
            services.AddOptions<AppSettings>()
                .Bind(configuration)
                .ValidateOnStart();
        }

        /// <summary>
        /// Konfiguruje podstawowe serwisy systemowe
        /// </summary>
        private static void ConfigureCoreServices(IServiceCollection services)
        {
            // Task Scheduler i Worker Pool
            services.AddSingleton<ITaskScheduler>(provider =>
            {
                var logger = provider.GetRequiredService<LoggerService>();
                var appSettings = provider.GetRequiredService<AppSettings>();
                return new ShopBot.Tasks.TaskScheduler(logger, appSettings.Application.MaxWorkerThreads);
            });

            // Browser Service
            services.AddSingleton<IBrowserService, BrowserService>();

            // Playwright Services - Factory pattern for browser instances
            services.AddSingleton<IBrowser>(provider =>
            {
                var browserService = provider.GetRequiredService<IBrowserService>();
                return browserService.InitializeBrowserAsync().GetAwaiter().GetResult();
            });

            services.AddScoped<IBrowserContext>(provider =>
            {
                var browserService = provider.GetRequiredService<IBrowserService>();
                return browserService.GetBrowserContextAsync().GetAwaiter().GetResult();
            });

            services.AddScoped<IPage>(provider =>
            {
                var browserService = provider.GetRequiredService<IBrowserService>();
                return browserService.CreateNewPageAsync().GetAwaiter().GetResult();
            });

            // Processed Offers Tracker (legacy - kept for backward compatibility)
            services.AddSingleton<ProcessedOffersTracker>();

            // Processing Session Cache - In-memory cache for current session
            services.AddSingleton<IProcessingSessionCache, ProcessingSessionCache>();

            // Primary Offer Tracker - Smart Hybrid Mode (Enhanced Status + Session Cache)
            services.AddScoped<IOfferTracker, SmartOfferTracker>();

            // Legacy trackers (kept for reference/emergency)
            // services.AddScoped<IOfferTracker, PureDatabaseOfferTracker>();  // Pure database mode
            // services.AddScoped<IOfferTracker, HybridOfferTracker>();  // Hybrid mode
        }

        /// <summary>
        /// Konfiguruje serwisy biznesowe
        /// </summary>
        private static void ConfigureBusinessServices(IServiceCollection services)
        {
            // Parser Service - nowy zrefaktoryzowany z kompozycją
            services.AddScoped<IParserService, RefactoredParserService>();

            // Legacy ParserService dla kompatybilności wstecznej
            services.AddScoped<ParserService>(); // Concrete class for legacy dependencies

            // FunPay Service
            services.AddScoped<IFunPayService, FunPayService>();
            services.AddScoped<FunPayService>(); // Concrete class for legacy dependencies

            // FunPay Details Service (dedykowana zakładka z kontrolowanymi opóźnieniami)
            services.AddSingleton<IFunPayDetailsService, FunPayDetailsService>();

            // Factory dla FunPayService (dla legacy code)
            services.AddScoped<Func<IPage, FunPayService>>(provider => page =>
            {
                var parserService = provider.GetRequiredService<ParserService>();
                var browser = provider.GetRequiredService<IBrowser>();
                var context = provider.GetRequiredService<IBrowserContext>();
                var tracker = provider.GetRequiredService<ProcessedOffersTracker>();
                var logger = provider.GetRequiredService<ILogger<FunPayService>>();
                return new FunPayService(parserService, browser, context, page, tracker, logger);
            });

            // G2G Services
            services.AddScoped<IG2GPublisherService, G2GPublisherService>();
            services.AddSingleton<G2GPublisherService>(); // Legacy - do usunięcia
            services.AddScoped<G2GOfferManager>();
            services.AddScoped<G2GOfferLimitChecker>();
            services.AddScoped<G2GOfferVerificationService>();

            // Delete Service
            services.AddScoped<DeleteService>();

            // Offer Analysis
            services.AddScoped<OfferDescriptionAnalyzer>();

            // Filter
            services.AddSingleton<OfferFilter>();

            // CLI Command Handler
            services.AddScoped<ICliCommandHandler, CliCommandHandler>();

            // Dropbox Test Service
            services.AddScoped<DropboxTestService>();

            // Task Factory - eliminuje ręczne tworzenie Tasks
            services.AddScoped<ITaskFactory, Factories.TaskFactory>();

            // Database Offer Tracker - replacement for ProcessedOffersTracker
            services.AddScoped<IDatabaseOfferTracker, DatabaseOfferTracker>();

            // Hybrid Offer Tracker - przejściowy system Database + JSON
            services.AddScoped<HybridOfferTracker>();

            // Analytics Service - wykorzystuje moc bazy danych
            services.AddScoped<IAnalyticsService, AnalyticsService>();
        }

        /// <summary>
        /// Konfiguruje serwisy zewnętrzne (Imgur, Dropbox)
        /// </summary>
        private static void ConfigureExternalServices(IServiceCollection services)
        {
            // Image Upload Services
            services.AddScoped<IImageUploadService, ImageUploadService>();
            services.AddSingleton<ImageUploadService>(provider =>
            {
                var imgurService = provider.GetRequiredService<ImgurUploader.ImgurService>();
                var tokenManager = provider.GetRequiredService<ImgurUploader.ImgurTokenManager>();
                var appSettings = provider.GetRequiredService<AppSettings>();
                var dropboxService = provider.GetRequiredService<DropboxService>();
                var logger = provider.GetRequiredService<ILogger<ImageUploadService>>();
                return new ImageUploadService(imgurService, tokenManager, appSettings, dropboxService, logger);
            });

            // Imgur Services
            services.AddSingleton<ImgurUploader.ImgurService>(provider =>
            {
                var appSettings = provider.GetRequiredService<AppSettings>();
                return new ImgurUploader.ImgurService(appSettings.Imgur.ClientId);
            });
            services.AddSingleton<ImgurUploader.ImgurAuthService>(provider =>
            {
                var appSettings = provider.GetRequiredService<AppSettings>();
                return new ImgurUploader.ImgurAuthService(appSettings.Imgur.ClientId, appSettings.Imgur.ClientSecret);
            });
            services.AddSingleton<ImgurUploader.ImgurTokenManager>(provider =>
            {
                var authService = provider.GetRequiredService<ImgurUploader.ImgurAuthService>();
                return new ImgurUploader.ImgurTokenManager(authService, "appsettings.json");
            });

            // Dropbox Services - Always register all components
            services.AddScoped<DropboxAuthService>(provider =>
            {
                var appSettings = provider.GetRequiredService<AppSettings>();
                var logger = provider.GetRequiredService<ILogger<DropboxAuthService>>();
                return new DropboxAuthService(appSettings.Dropbox.ClientId, appSettings.Dropbox.ClientSecret, logger);
            });

            services.AddSingleton<DropboxTokenManager>(provider =>
            {
                var authService = provider.GetRequiredService<DropboxAuthService>();
                var logger = provider.GetRequiredService<ILogger<DropboxTokenManager>>();
                return new DropboxTokenManager(authService, logger);
            });

            services.AddScoped<DropboxService>(provider =>
            {
                var appSettings = provider.GetRequiredService<AppSettings>();
                var logger = provider.GetRequiredService<ILogger<DropboxService>>();

                // Priority 1: Use static access token if available (legacy mode)
                if (!string.IsNullOrEmpty(appSettings.Dropbox.AccessToken))
                {
                    logger.LogInformation("[Dropbox] Using static access token (legacy mode)");
                    return new DropboxService(appSettings.Dropbox.AccessToken, logger);
                }
                // Priority 2: Use OAuth system if credentials are configured
                else if (!string.IsNullOrEmpty(appSettings.Dropbox.ClientId) &&
                         appSettings.Dropbox.ClientId != "your_dropbox_app_key_here" &&
                         !string.IsNullOrEmpty(appSettings.Dropbox.ClientSecret) &&
                         appSettings.Dropbox.ClientSecret != "your_dropbox_app_secret_here")
                {
                    logger.LogInformation("[Dropbox] Using OAuth refresh token system");
                    var tokenManager = provider.GetRequiredService<DropboxTokenManager>();
                    return new DropboxService(tokenManager, logger);
                }
                else
                {
                    logger.LogError("[Dropbox] No valid credentials found - configure either static AccessToken or OAuth (ClientId/ClientSecret)");
                    throw new InvalidOperationException("Dropbox credentials not configured");
                }
            });
        }

        /// <summary>
        /// Konfiguruje parsery i formattery
        /// </summary>
        private static void ConfigureParsersAndFormatters(IServiceCollection services)
        {
            // Nowe zrefaktoryzowane parsery
            services.AddScoped<IDataExtractor, ShopBot.Services.Parsers.DataExtractor>();
            services.AddScoped<IUrlValidator, ShopBot.Services.Parsers.UrlValidator>();
            services.AddScoped<IImageParser, ShopBot.Services.Parsers.ImageParser>();
            services.AddScoped<IOfferDetailsParser, ShopBot.Services.Parsers.OfferDetailsParser>();
            services.AddScoped<IOfferListParser, ShopBot.Services.Parsers.OfferListParser>();

            // Formattery opisów
            services.AddSingleton<DefaultDescriptionFormatter>();
            services.AddSingleton<FC25DescriptionFormatter>();
            services.AddSingleton<MobileLegendsDescriptionFormatter>();
            services.AddSingleton<RaidShadowLegendsDescriptionFormatter>();
            // services.AddSingleton<AlbionDescriptionFormatter>();
            // services.AddSingleton<WatcherOfRealmsDescriptionFormatter>();
            services.AddSingleton<DescriptionFormatterFactory>();
        }

        /// <summary>
        /// Konfiguruje Hosted Services dla zarządzania cyklem życia aplikacji
        /// </summary>
        private static void ConfigureHostedServices(IServiceCollection services)
        {
            // Główny Hosted Service aplikacji
            services.AddHostedService<ShopBotHostedService>();
        }
    }
} 