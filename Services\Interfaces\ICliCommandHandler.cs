using System.Threading.Tasks;

namespace ShopBot.Services
{
    /// <summary>
    /// Interface dla obsługi poleceń CLI
    /// </summary>
    public interface ICliCommandHandler
    {
        /// <summary>
        /// Obsługuje polecenie CLI na podstawie argumentów
        /// </summary>
        /// <param name="args">Argumenty wiersza poleceń</param>
        Task HandleCommandAsync(string[] args);
    }
}
