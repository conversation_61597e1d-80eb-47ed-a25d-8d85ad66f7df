using Microsoft.Extensions.Logging;
using System;
using System.Diagnostics;
using System.Collections.Generic;
using System.Linq;

namespace ShopBot.Services
{
    /// <summary>
    /// Nowoczesny serwis logowania używający Microsoft.Extensions.Logging
    /// Zastępuje stary LoggerService z Console.WriteLine i dodaje strukturalne logowanie
    /// </summary>
    public class ModernLoggingService
    {
        private readonly ILogger<ModernLoggingService> _logger;
        private readonly ShopBot.Tasks.TaskScheduler? _taskScheduler;
        private readonly Dictionary<string, Stopwatch> _operationTimers = new();

        public ModernLoggingService(ILogger<ModernLoggingService> logger, ShopBot.Tasks.TaskScheduler? taskScheduler = null)
        {
            _logger = logger;
            _taskScheduler = taskScheduler;
        }

        /// <summary>
        /// Loguje wiadomość informacyjną
        /// </summary>
        public void LogInfo(string message, params object[] args)
        {
            _logger.LogInformation(message, args);
        }

        /// <summary>
        /// Loguje wiadomość debugową
        /// </summary>
        public void LogDebug(string message, params object[] args)
        {
            _logger.LogDebug(message, args);
        }

        /// <summary>
        /// Loguje ostrzeżenie
        /// </summary>
        public void LogWarning(string message, params object[] args)
        {
            _logger.LogWarning(message, args);
        }

        /// <summary>
        /// Loguje błąd
        /// </summary>
        public void LogError(string message, params object[] args)
        {
            _logger.LogError(message, args);
        }

        /// <summary>
        /// Loguje błąd z wyjątkiem
        /// </summary>
        public void LogError(Exception exception, string message, params object[] args)
        {
            _logger.LogError(exception, message, args);
        }

        /// <summary>
        /// Loguje informacje o stanie zadań (tylko w trybie debug)
        /// </summary>
        public void LogTaskStatus()
        {
            if (_taskScheduler == null) return;

            var (activeWorkers, queueLength, activeSyncWorkers, syncQueueLength) = _taskScheduler.GetStatus();
            
            _logger.LogDebug("Stan zadań - Aktywni workerzy: {ActiveWorkers}, Zadania w kolejce: {QueueLength}, " +
                           "Aktywne zadania synchronizacji: {ActiveSyncWorkers}, Zadania synchronizacji w kolejce: {SyncQueueLength}",
                           activeWorkers, queueLength, activeSyncWorkers, syncQueueLength);
        }

        /// <summary>
        /// Loguje rozpoczęcie operacji
        /// </summary>
        public void LogOperationStart(string operationName, string? details = null)
        {
            if (string.IsNullOrEmpty(details))
            {
                _logger.LogInformation("🚀 Rozpoczynam operację: {OperationName}", operationName);
            }
            else
            {
                _logger.LogInformation("🚀 Rozpoczynam operację: {OperationName} - {Details}", operationName, details);
            }
        }

        /// <summary>
        /// Loguje zakończenie operacji
        /// </summary>
        public void LogOperationComplete(string operationName, string? details = null)
        {
            if (string.IsNullOrEmpty(details))
            {
                _logger.LogInformation("✅ Zakończono operację: {OperationName}", operationName);
            }
            else
            {
                _logger.LogInformation("✅ Zakończono operację: {OperationName} - {Details}", operationName, details);
            }
        }

        /// <summary>
        /// Loguje niepowodzenie operacji
        /// </summary>
        public void LogOperationFailed(string operationName, string reason, Exception? exception = null)
        {
            if (exception != null)
            {
                _logger.LogError(exception, "❌ Operacja nieudana: {OperationName} - {Reason}", operationName, reason);
            }
            else
            {
                _logger.LogError("❌ Operacja nieudana: {OperationName} - {Reason}", operationName, reason);
            }
        }

        /// <summary>
        /// Loguje metryki wydajności
        /// </summary>
        public void LogPerformanceMetric(string metricName, double value, string unit = "ms")
        {
            _logger.LogDebug("📊 Metryka: {MetricName} = {Value} {Unit}", metricName, value, unit);
        }

        /// <summary>
        /// Loguje upload obrazu
        /// </summary>
        public void LogImageUpload(string service, string imageUrl, bool success, string? resultUrl = null, string? error = null)
        {
            if (success && !string.IsNullOrEmpty(resultUrl))
            {
                _logger.LogInformation("📸 Upload obrazu do {Service} - SUCCESS: {ImageUrl} -> {ResultUrl}", 
                                     service, imageUrl, resultUrl);
            }
            else if (!success && !string.IsNullOrEmpty(error))
            {
                _logger.LogError("📸 Upload obrazu do {Service} - FAILED: {ImageUrl} - {Error}", 
                               service, imageUrl, error);
            }
        }

        /// <summary>
        /// Loguje operacje na ofercie
        /// </summary>
        public void LogOfferOperation(string operation, string offerId, string gameName, bool success, string? details = null)
        {
            if (success)
            {
                _logger.LogInformation("🎮 {Operation} oferty {OfferId} ({GameName}) - SUCCESS{Details}", 
                                     operation, offerId, gameName, 
                                     string.IsNullOrEmpty(details) ? "" : $" - {details}");
            }
            else
            {
                _logger.LogWarning("🎮 {Operation} oferty {OfferId} ({GameName}) - FAILED{Details}", 
                                 operation, offerId, gameName, 
                                 string.IsNullOrEmpty(details) ? "" : $" - {details}");
            }
        }

        /// <summary>
        /// Loguje operacje na G2G
        /// </summary>
        public void LogG2GOperation(string operation, bool success, string? details = null)
        {
            if (success)
            {
                _logger.LogInformation("🌐 G2G {Operation} - SUCCESS{Details}",
                                     operation, string.IsNullOrEmpty(details) ? "" : $" - {details}");
            }
            else
            {
                _logger.LogError("🌐 G2G {Operation} - FAILED{Details}",
                               operation, string.IsNullOrEmpty(details) ? "" : $" - {details}");
            }
        }

        /// <summary>
        /// Rozpoczyna pomiar czasu operacji z automatycznym ID
        /// </summary>
        public string StartTimedOperation(string operationName, string? details = null)
        {
            var operationId = $"{operationName}_{DateTime.Now.Ticks}";
            _operationTimers[operationId] = Stopwatch.StartNew();

            if (string.IsNullOrEmpty(details))
            {
                _logger.LogInformation("🚀 Rozpoczynam operację: {OperationName} (ID: {OperationId})", operationName, operationId);
            }
            else
            {
                _logger.LogInformation("🚀 Rozpoczynam operację: {OperationName} - {Details} (ID: {OperationId})",
                                     operationName, details, operationId);
            }

            return operationId;
        }

        /// <summary>
        /// Kończy pomiar czasu operacji po ID
        /// </summary>
        public void CompleteTimedOperation(string operationId, bool success = true, string? details = null)
        {
            if (_operationTimers.TryGetValue(operationId, out var stopwatch))
            {
                stopwatch.Stop();
                _operationTimers.Remove(operationId);

                var operationName = operationId.Split('_')[0];
                var emoji = success ? "✅" : "❌";
                var status = success ? "SUCCESS" : "FAILED";

                if (string.IsNullOrEmpty(details))
                {
                    _logger.LogInformation("{Emoji} Operacja {OperationName} - {Status} ({Duration}ms)",
                                         emoji, operationName, status, stopwatch.ElapsedMilliseconds);
                }
                else
                {
                    _logger.LogInformation("{Emoji} Operacja {OperationName} - {Status} - {Details} ({Duration}ms)",
                                         emoji, operationName, status, details, stopwatch.ElapsedMilliseconds);
                }

                // Loguj metrykę wydajności
                LogPerformanceMetric(operationName, stopwatch.ElapsedMilliseconds);
            }
        }

        /// <summary>
        /// Loguje operację z automatycznym pomiarem czasu (using pattern)
        /// </summary>
        public IDisposable LogOperation(string operationName, string? details = null)
        {
            return new TimedOperation(this, operationName, details);
        }

        /// <summary>
        /// Klasa pomocnicza dla automatycznego pomiaru czasu operacji
        /// </summary>
        private class TimedOperation : IDisposable
        {
            private readonly ModernLoggingService _loggingService;
            private readonly string _operationId;
            private bool _disposed = false;

            public TimedOperation(ModernLoggingService loggingService, string operationName, string? details)
            {
                _loggingService = loggingService;
                _operationId = _loggingService.StartTimedOperation(operationName, details);
            }

            public void Dispose()
            {
                if (!_disposed)
                {
                    _loggingService.CompleteTimedOperation(_operationId, success: true);
                    _disposed = true;
                }
            }

            public void MarkAsFailed(string? reason = null)
            {
                if (!_disposed)
                {
                    _loggingService.CompleteTimedOperation(_operationId, success: false, details: reason);
                    _disposed = true;
                }
            }
        }
    }
}
