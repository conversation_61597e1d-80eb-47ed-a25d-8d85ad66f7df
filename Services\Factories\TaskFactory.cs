using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Playwright;
using ShopBot.Tasks;
using ShopBot.Services.Formatters;
using ShopBot.Services.Interfaces;
using System;

namespace ShopBot.Services.Factories
{
    /// <summary>
    /// Factory dla tworzenia Tasks z proper Dependency Injection
    /// Eliminuje ręczne tworzenie Tasks z wieloma parametrami
    /// </summary>
    public interface ITaskFactory
    {
        AddOfferTask CreateAddOfferTask(OfferListItem offer, string game);
        ScanFunPayTask CreateScanFunPayTask();
        UpdatePricesTask CreateUpdatePricesTask();
        AnalyzeOffersTask CreateAnalyzeOffersTask();
        ScanAndSyncOffersTask CreateScanAndSyncOffersTask();
    }

    public class TaskFactory : ITaskFactory
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IBrowser _browser;
        private readonly IBrowserContext _context;
        private readonly ProcessedOffersTracker _processedOffersTracker;
        private readonly ParserService _parserService;
        private readonly ImageUploadService _imageUploadService;

        public TaskFactory(
            IServiceProvider serviceProvider,
            IBrowser browser,
            IBrowserContext context,
            ProcessedOffersTracker processedOffersTracker,
            ParserService parserService,
            ImageUploadService imageUploadService)
        {
            _serviceProvider = serviceProvider;
            _browser = browser;
            _context = context;
            _processedOffersTracker = processedOffersTracker;
            _parserService = parserService;
            _imageUploadService = imageUploadService;
        }

        public AddOfferTask CreateAddOfferTask(OfferListItem offer, string game)
        {
            return new AddOfferTask(
                offer,
                game,
                _context,
                _processedOffersTracker,
                _parserService,
                _imageUploadService,
                _serviceProvider.GetRequiredService<DescriptionFormatterFactory>(),
                _serviceProvider.GetRequiredService<IFunPayDetailsService>(),
                _serviceProvider.GetRequiredService<IOfferRepository>(),
                _serviceProvider.GetRequiredService<ILogger<AddOfferTask>>(),
                _serviceProvider.GetRequiredService<G2GPublisherService>(),
                _serviceProvider.GetRequiredService<G2GOfferManager>()
            );
        }

        public ScanFunPayTask CreateScanFunPayTask()
        {
            return new ScanFunPayTask(
                _serviceProvider.GetRequiredService<FunPayService>(),
                (ShopBot.Tasks.TaskScheduler)_serviceProvider.GetRequiredService<ITaskScheduler>(),
                _processedOffersTracker,
                _parserService,
                _browser,
                _context,
                _imageUploadService,
                _serviceProvider,
                _serviceProvider.GetRequiredService<ILogger<ScanFunPayTask>>()
            );
        }

        public UpdatePricesTask CreateUpdatePricesTask()
        {
            return new UpdatePricesTask(
                _browser,
                _context,
                _processedOffersTracker,
                _parserService,
                _serviceProvider,
                _serviceProvider.GetRequiredService<ILogger<UpdatePricesTask>>()
            );
        }

        public AnalyzeOffersTask CreateAnalyzeOffersTask()
        {
            return new AnalyzeOffersTask(
                _serviceProvider.GetRequiredService<FunPayService>(),
                _parserService,
                _browser,
                _context,
                _serviceProvider.GetRequiredService<OfferDescriptionAnalyzer>(),
                _serviceProvider,
                _serviceProvider.GetRequiredService<ILogger<AnalyzeOffersTask>>()
            );
        }

        public ScanAndSyncOffersTask CreateScanAndSyncOffersTask()
        {
            return new ScanAndSyncOffersTask(
                _serviceProvider.GetRequiredService<FunPayService>(),
                (ShopBot.Tasks.TaskScheduler)_serviceProvider.GetRequiredService<ITaskScheduler>(),
                _processedOffersTracker,
                _parserService,
                _browser,
                _context,
                _imageUploadService,
                _serviceProvider,
                _serviceProvider.GetRequiredService<IConfigurationRepository>(),
                _serviceProvider.GetRequiredService<IShopBotRepository>(),
                _serviceProvider.GetRequiredService<ILogger<ScanAndSyncOffersTask>>(),
                _serviceProvider.GetRequiredService<OfferContentChangeDetector>()
            );
        }
    }
}
