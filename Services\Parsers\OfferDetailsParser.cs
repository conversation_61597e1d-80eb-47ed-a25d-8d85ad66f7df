using HtmlAgilityPack;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;

namespace ShopBot.Services.Parsers
{
    /// <summary>
    /// Parser odpowiedzialny za wyciąganie szczegółów oferty z HTML
    /// </summary>
    public class OfferDetailsParser : IOfferDetailsParser
    {
        private readonly ILogger<OfferDetailsParser> _logger;
        private readonly IDataExtractor _dataExtractor;
        private readonly IImageParser _imageParser;

        public OfferDetailsParser(
            ILogger<OfferDetailsParser> logger,
            IDataExtractor dataExtractor,
            IImageParser imageParser)
        {
            _logger = logger;
            _dataExtractor = dataExtractor;
            _imageParser = imageParser;
        }

        public OfferDetails ParseOfferDetails(HtmlDocument document, string url)
        {
            try
            {
                _logger.LogDebug("🔍 Rozpoczynam parsowanie szczegółów oferty z URL: {Url}", url);

                var offer = new OfferDetails
                {
                    Id = ExtractOfferIdFromUrl(url),
                    Url = url,
                    ShortDescription = ParseShortDescription(document),
                    DetailedDescription = ParseDetailedDescription(document),
                    Price = ParsePrice(document),
                    Images = _imageParser.ParseImages(document),
                    Parameters = ParseParameters(document)
                };

                // Parsowanie specyficznych pól na podstawie parametrów
                var parameters = ParseParameters(document);
            offer.Parameters = parameters;
            ParseSpecificFields(offer, parameters);

                _logger.LogDebug("✅ Pomyślnie sparsowano ofertę {OfferId}", offer.Id);
                return offer;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Błąd podczas parsowania szczegółów oferty z URL: {Url}", url);
                throw;
            }
        }

        public Dictionary<string, string> ParseParameters(HtmlDocument document)
        {
            var parameters = new Dictionary<string, string>();

            try
            {
                // Parsowanie parametrów z sekcji "param-item"
                var paramNodes = document.DocumentNode.SelectNodes("//div[contains(@class, 'param-item')]");
                if (paramNodes != null)
                {
                    foreach (var paramNode in paramNodes)
                    {
                        var labelNode = paramNode.SelectSingleNode(".//h5");
                        var valueNode = paramNode.SelectSingleNode(".//div[contains(@class, 'param-value')]");

                        if (labelNode != null && valueNode != null)
                        {
                            var label = _dataExtractor.ExtractText(labelNode, "").Trim();
                            var value = _dataExtractor.ExtractText(valueNode, "").Trim();

                            if (!string.IsNullOrEmpty(label) && !string.IsNullOrEmpty(value))
                            {
                                parameters[label] = value;
                            }
                        }
                    }
                }

                _logger.LogDebug("📋 Znaleziono {Count} parametrów", parameters.Count);
                return parameters;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "⚠️ Błąd podczas parsowania parametrów");
                return parameters;
            }
        }

        public decimal ParsePrice(HtmlDocument document)
        {
            try
            {
                // Różne selektory dla ceny w zależności od struktury strony
                var priceSelectors = new[]
                {
                    "//div[contains(@class, 'offer-price')]//span[contains(@class, 'price')]",
                    "//span[contains(@class, 'offer-price')]",
                    "//div[contains(@class, 'price')]//span",
                    "//span[contains(text(), '$')]"
                };

                foreach (var selector in priceSelectors)
                {
                    var priceNode = document.DocumentNode.SelectSingleNode(selector);
                    if (priceNode != null)
                    {
                        var priceText = _dataExtractor.ExtractText(priceNode, "");
                        var price = ExtractPriceFromText(priceText);
                        
                        if (price > 0)
                        {
                            _logger.LogDebug("💰 Znaleziono cenę: ${Price:F2}", price);
                            return price;
                        }
                    }
                }

                _logger.LogWarning("⚠️ Nie znaleziono ceny w ofercie");
                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Błąd podczas parsowania ceny");
                return 0;
            }
        }

        public string ParseDescription(HtmlDocument document)
        {
            return ParseDetailedDescription(document);
        }

        private string ParseShortDescription(HtmlDocument document)
        {
            var selectors = new[]
            {
                "//h1[contains(@class, 'offer-title')]",
                "//div[contains(@class, 'offer-header')]//h1",
                "//title"
            };

            foreach (var selector in selectors)
            {
                var node = document.DocumentNode.SelectSingleNode(selector);
                if (node != null)
                {
                    var description = _dataExtractor.ExtractText(node, "").Trim();
                    if (!string.IsNullOrEmpty(description))
                    {
                        return CleanDescription(description);
                    }
                }
            }

            return "Brak opisu";
        }

        private string ParseDetailedDescription(HtmlDocument document)
        {
            var selectors = new[]
            {
                "//div[contains(@class, 'offer-description')]",
                "//div[contains(@class, 'description')]//p",
                "//div[contains(@class, 'content')]//p"
            };

            foreach (var selector in selectors)
            {
                var nodes = document.DocumentNode.SelectNodes(selector);
                if (nodes != null)
                {
                    var description = string.Join("\n", nodes.Select(n => _dataExtractor.ExtractText(n, "").Trim()))
                                           .Trim();
                    if (!string.IsNullOrEmpty(description))
                    {
                        return CleanDescription(description);
                    }
                }
            }

            return "Brak szczegółowego opisu";
        }

        private void ParseSpecificFields(OfferDetails offer, Dictionary<string, string> parameters)
        {
            // Parsowanie pól specyficznych dla różnych gier
            offer.Level = (int)_dataExtractor.ExtractNumber(parameters.GetValueOrDefault("Level", "0"));
            offer.Heroes = (int)_dataExtractor.ExtractNumber(parameters.GetValueOrDefault("Heroes", "0"));
            offer.Power = (int)_dataExtractor.ExtractNumber(parameters.GetValueOrDefault("Power", "0"));

            // Dodatkowe pola
            offer.MythicHeroes = (int)_dataExtractor.ExtractNumber(parameters.GetValueOrDefault("Mythic Heroes", "0"));
            offer.LegendaryHeroes = (int)_dataExtractor.ExtractNumber(parameters.GetValueOrDefault("Legendary Heroes", "0"));
            offer.EpicHeroes = (int)_dataExtractor.ExtractNumber(parameters.GetValueOrDefault("Epic Heroes", "0"));
            offer.RareHeroes = (int)_dataExtractor.ExtractNumber(parameters.GetValueOrDefault("Rare Heroes", "0"));

            // Pola dla innych gier
            offer.Brawlers = (int)_dataExtractor.ExtractNumber(parameters.GetValueOrDefault("Brawlers", "0"));
            offer.SkinsQuantity = (int)_dataExtractor.ExtractNumber(parameters.GetValueOrDefault("Skins quantity", "0"));
            offer.AgentsQuantity = (int)_dataExtractor.ExtractNumber(parameters.GetValueOrDefault("Agents quantity", "0"));
            
            // Dodatkowe informacje
            offer.WeaponType = parameters.GetValueOrDefault("Weapon type", "");
            offer.BattlegroundsPlus = parameters.ContainsKey("BATTLEGROUNDS Plus") && 
                                    parameters["BATTLEGROUNDS Plus"].ToUpper() == "YES" ? "Yes" : "No";
            
            // Określenie edycji gry
            offer.Edition = GameSpecificHelper.DetermineGameEdition(
                parameters.GetValueOrDefault("Detailed description", "") + 
                parameters.GetValueOrDefault("Short description", ""));
        }

        private decimal ExtractPriceFromText(string priceText)
        {
            if (string.IsNullOrEmpty(priceText)) return 0;

            Console.WriteLine($"[PriceParser] 🔍 Parsowanie ceny z tekstu: '{priceText}'");

            // Sprawdź czy to USDT i konwertuj na USD
            if (priceText.Contains("USDT"))
            {
                Console.WriteLine($"[PriceParser] 💰 Wykryto USDT: {priceText}");

                // Wyciągnij liczbę z USDT
                var usdtMatch = Regex.Match(priceText, @"([\d.,]+)\s*USDT");
                if (usdtMatch.Success)
                {
                    var usdtValue = usdtMatch.Groups[1].Value;
                    Console.WriteLine($"[PriceParser] 🔄 USDT wartość: {usdtValue}");

                    if (decimal.TryParse(usdtValue, NumberStyles.Any, CultureInfo.InvariantCulture, out decimal usdtPrice))
                    {
                        // USDT ≈ USD (1:1 ratio), ale dodajmy małą konwersję dla bezpieczeństwa
                        var usdPrice = usdtPrice * 1.0m; // 1:1 conversion
                        Console.WriteLine($"[PriceParser] ✅ USDT {usdtPrice} → USD {usdPrice}");
                        return usdPrice;
                    }
                }
            }

            // Usuń wszystkie znaki oprócz cyfr, kropek i przecinków
            var cleanPrice = Regex.Replace(priceText, @"[^\d.,]", "");
            Console.WriteLine($"[PriceParser] 🧹 Oczyszczony tekst: '{cleanPrice}'");

            // Spróbuj sparsować jako decimal
            if (decimal.TryParse(cleanPrice, NumberStyles.Any, CultureInfo.InvariantCulture, out decimal price))
            {
                Console.WriteLine($"[PriceParser] ✅ Sparsowano cenę: {price}");
                return price;
            }

            // Spróbuj z polskim formatem
            if (decimal.TryParse(cleanPrice, NumberStyles.Any, new CultureInfo("pl-PL"), out price))
            {
                Console.WriteLine($"[PriceParser] ✅ Sparsowano cenę (PL format): {price}");
                return price;
            }

            Console.WriteLine($"[PriceParser] ❌ Nie udało się sparsować ceny z: '{priceText}'");
            return 0;
        }

        private string ExtractOfferIdFromUrl(string url)
        {
            if (string.IsNullOrEmpty(url)) return "";

            // Wzorzec dla ID w URL FunPay
            var match = Regex.Match(url, @"id=(\d+)");
            if (match.Success)
            {
                return match.Groups[1].Value;
            }

            // Alternatywny wzorzec
            match = Regex.Match(url, @"/lots/(\d+)/");
            if (match.Success)
            {
                return match.Groups[1].Value;
            }

            return "";
        }

        private string CleanDescription(string description)
        {
            if (string.IsNullOrEmpty(description)) return "";

            // Usuń nadmiarowe białe znaki
            description = Regex.Replace(description, @"\s+", " ");
            
            // Usuń znaki specjalne na początku i końcu
            description = description.Trim(' ', '\n', '\r', '\t');

            return description;
        }
    }
}
