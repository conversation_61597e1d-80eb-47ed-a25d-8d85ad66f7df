using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Newtonsoft.Json;
using ShopBot.Config;

namespace ShopBot.Services
{
    /// <summary>
    /// Interfejs dla walidatora konfiguracji
    /// </summary>
    public interface IConfigurationValidator
    {
        ConfigurationValidator.ValidationResult ValidateConfiguration();
    }

    public class ConfigurationValidator : IConfigurationValidator
    {
        private readonly List<string> _errors = new List<string>();
        private readonly List<string> _warnings = new List<string>();

        public class ValidationResult
        {
            public bool IsValid { get; set; }
            public List<string> Errors { get; set; } = new List<string>();
            public List<string> Warnings { get; set; } = new List<string>();
        }

        public ValidationResult ValidateConfiguration()
        {
            Console.WriteLine("[Config] 🔍 Rozpoczynam walidację konfiguracji...");

            ValidateConfigFiles();
            ValidateGamesConfiguration();
            ValidateImgurConfiguration();

            var result = new ValidationResult
            {
                IsValid = _errors.Count == 0,
                Errors = _errors,
                Warnings = _warnings
            };

            LogValidationResults(result);
            return result;
        }

        private void ValidateConfigFiles()
        {
            // Sprawdź czy pliki konfiguracyjne istnieją
            var requiredFiles = new[]
            {
                "config.json",
                "gamesconfig.json",
                "deleteconfig.json"
            };

            foreach (var file in requiredFiles)
            {
                if (!File.Exists(file))
                {
                    _errors.Add($"Brak wymaganego pliku konfiguracyjnego: {file}");
                }
                else
                {
                    try
                    {
                        var content = File.ReadAllText(file);
                        JsonConvert.DeserializeObject(content);
                        Console.WriteLine($"[Config] ✅ Plik {file} jest poprawny");
                    }
                    catch (Exception ex)
                    {
                        _errors.Add($"Błąd w pliku {file}: {ex.Message}");
                    }
                }
            }
        }

        private void ValidateGamesConfiguration()
        {
            try
            {
                var gamesConfigContent = File.ReadAllText("gamesconfig.json");
                var gamesConfig = JsonConvert.DeserializeObject<dynamic>(gamesConfigContent);

                // Sprawdź ActiveGames
                if (gamesConfig.ActiveGames == null)
                {
                    _errors.Add("Brak sekcji 'ActiveGames' w gamesconfig.json");
                    return;
                }

                var activeGames = ((Newtonsoft.Json.Linq.JArray)gamesConfig.ActiveGames).ToObject<string[]>();
                if (activeGames == null || activeGames.Length == 0)
                {
                    _warnings.Add("Lista 'ActiveGames' jest pusta");
                }

                // Sprawdź GamesData
                if (gamesConfig.GamesData == null)
                {
                    _errors.Add("Brak sekcji 'GamesData' w gamesconfig.json");
                    return;
                }

                var gamesData = (Newtonsoft.Json.Linq.JObject)gamesConfig.GamesData;
                
                foreach (var game in activeGames ?? new string[0])
                {
                    if (!gamesData.ContainsKey(game))
                    {
                        _errors.Add($"Gra '{game}' z ActiveGames nie ma konfiguracji w GamesData");
                        continue;
                    }

                    var gameConfig = gamesData[game];
                    
                    // Sprawdź wymagane pola
                    if (gameConfig["id"] == null)
                    {
                        _errors.Add($"Gra '{game}' nie ma zdefiniowanego 'id'");
                    }

                    if (gameConfig["priceRange"] == null)
                    {
                        _errors.Add($"Gra '{game}' nie ma zdefiniowanego 'priceRange'");
                    }
                    else
                    {
                        var priceRange = gameConfig["priceRange"];
                        if (priceRange["min"] == null || priceRange["max"] == null)
                        {
                            _errors.Add($"Gra '{game}' ma niepełny 'priceRange' (brak min lub max)");
                        }
                        else
                        {
                            var min = (decimal)priceRange["min"];
                            var max = (decimal)priceRange["max"];
                            if (min >= max)
                            {
                                _errors.Add($"Gra '{game}' ma nieprawidłowy zakres cen (min >= max)");
                            }
                            if (min < 0 || max < 0)
                            {
                                _errors.Add($"Gra '{game}' ma ujemne ceny");
                            }
                        }
                    }

                    if (gameConfig["listAttributes"] == null)
                    {
                        _warnings.Add($"Gra '{game}' nie ma zdefiniowanych 'listAttributes'");
                    }
                    else
                    {
                        var attributes = ((Newtonsoft.Json.Linq.JArray)gameConfig["listAttributes"]).ToObject<string[]>();
                        if (attributes == null || attributes.Length == 0)
                        {
                            _warnings.Add($"Gra '{game}' ma pustą listę 'listAttributes'");
                        }
                    }

                    Console.WriteLine($"[Config] ✅ Konfiguracja gry '{game}' jest poprawna");
                }

                // Sprawdź czy są nieużywane gry w GamesData
                var allGamesInData = gamesData.Properties().Select(p => p.Name).ToArray();
                var unusedGames = allGamesInData.Except(activeGames ?? new string[0]).ToArray();

                if (unusedGames.Length > 0)
                {
                    Console.WriteLine($"[Config] 💤 Znaleziono {unusedGames.Length} nieaktywnych gier: {string.Join(", ", unusedGames)}");
                    Console.WriteLine("[Config] 💡 Aby włączyć grę, dodaj jej nazwę do ActiveGames w gamesconfig.json");
                }
            }
            catch (Exception ex)
            {
                _errors.Add($"Błąd podczas walidacji gamesconfig.json: {ex.Message}");
            }
        }

        private void ValidateImgurConfiguration()
        {
            try
            {
                var (filter, imgurConfig, _) = FilterConfig.LoadFromFile();

                if (string.IsNullOrEmpty(imgurConfig.ClientId))
                {
                    _errors.Add("Brak ClientId dla Imgur w config.json");
                }

                if (string.IsNullOrEmpty(imgurConfig.ClientSecret))
                {
                    _errors.Add("Brak ClientSecret dla Imgur w config.json");
                }

                // RefreshToken nie jest częścią ImgurConfig w tym projekcie
                // _warnings.Add("Brak RefreshToken dla Imgur - może być wymagany do odświeżania tokenów");

                // Sprawdź filtry
                if (filter.MinPrice <= 0)
                {
                    _warnings.Add($"MinPrice jest <= 0: {filter.MinPrice}");
                }

                if (filter.MaxPrice <= filter.MinPrice)
                {
                    _errors.Add($"MaxPrice ({filter.MaxPrice}) jest <= MinPrice ({filter.MinPrice})");
                }

                Console.WriteLine("[Config] ✅ Konfiguracja Imgur jest poprawna");
            }
            catch (Exception ex)
            {
                _errors.Add($"Błąd podczas walidacji konfiguracji Imgur: {ex.Message}");
            }
        }

        private void LogValidationResults(ValidationResult result)
        {
            Console.WriteLine("\n[Config] 📋 Wyniki walidacji konfiguracji:");
            
            if (result.Errors.Count > 0)
            {
                Console.WriteLine($"[Config] ❌ Znaleziono {result.Errors.Count} błędów:");
                foreach (var error in result.Errors)
                {
                    Console.WriteLine($"[Config]   • {error}");
                }
            }

            if (result.Warnings.Count > 0)
            {
                Console.WriteLine($"[Config] ⚠️ Znaleziono {result.Warnings.Count} ostrzeżeń:");
                foreach (var warning in result.Warnings)
                {
                    Console.WriteLine($"[Config]   • {warning}");
                }
            }

            if (result.IsValid)
            {
                Console.WriteLine("[Config] ✅ Konfiguracja jest poprawna!");
            }
            else
            {
                Console.WriteLine("[Config] ❌ Konfiguracja zawiera błędy - aplikacja może nie działać poprawnie!");
            }
        }
    }
}
