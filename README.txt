🤖 SHOPBOT - SYSTEM AUTOMATYCZNEJ SYNCHRONIZACJI OFERT 🤖

=== GŁÓWNE FUNKCJE ===

1. 🔄 Synchronizacja Ofert
   - Skanowanie ofert na FunPay
   - Porównywanie z ofertami na G2G
   - Automatyczne dodawanie nowych ofert
   - Usuwanie nieaktywnych ofert
   - Limit ofert na G2G: 1000

2. 🎮 Obsługiwane Gry
   - Raid Shadow Legends
   - League of Legends
   - Valorant
   - PUBG Mobile
   - Albion
   - Dota 2
   - Fortnite
   - Apex Legends
   - PUBG
   - Rust
   - Genshin Impact
   - Call of Duty Warzone
   - Mobile Legends
   - Escape from Tarkov
   - World of Warcraft

3. 💰 System Cen
   - Automatyczne przeliczanie cen z FunPay na G2G
   - Różne marże w zależności od przedziału cenowego
   - Zaokrąglanie cen do .99
   - Zakresy cen dla każdej gry w gamesconfig.json:
     * Minimalna i maksymalna cena per gra
     * Globalne limity z config.json ($10-$200)
     * Priorytet: limity gry > limity globalne

4. 🖼️ Obsługa Zdjęć
   - Automatyczny upload zdjęć na Imgur
   - Zachowywanie oryginalnych URL-i
   - Obsługa wielu zdjęć per oferta

=== SYSTEM ZADAŃ ===

1. 📋 Główne Typy Zadań
   - ScanAndSyncOffersTask (co 5 minut)
   - AddOfferTask (dodawanie nowych ofert)
   - DeleteOfferTask (usuwanie nieaktywnych ofert)
   - CleanupTask (raz dziennie)

2. 👥 Zarządzanie Zadaniami
   - TaskScheduler zarządza kolejką zadań
   - WorkerPool obsługuje współbieżność
   - Automatyczne kolejkowanie zadań
   - Obsługa błędów i ponawianie prób

=== FILTRY I KONFIGURACJA ===

1. 🎯 Filtry Ofert
   - Globalne limity cen w config.json:
     * Minimalna cena: $10
     * Maksymalna cena: $200
   - Minimalny rating: 5
   - Minimalna liczba opinii: 1
   - Ignorowane serwery: RU, Russia, CIS
   - Ignorowane frazy w opisach

2. ⚙️ Konfiguracja
   - Główne ustawienia w config.json
   - Konfiguracja gier w gamesconfig.json:
     * Zakresy cen per gra
     * ID gier
     * Atrybuty specyficzne dla gier
   - Konfiguracja Imgur do zdjęć
   - Możliwość dostosowania filtrów

=== UŻYTKOWANIE ===

1. 🚀 Uruchomienie
   - dotnet run - normalne uruchomienie
   - dotnet run -- delete [ID] - usunięcie pojedynczej oferty

2. 📊 Monitoring
   - Szczegółowe logi z emoji
   - Informacje o każdej operacji
   - Statystyki synchronizacji
   - Podsumowania dla każdej gry

3. 🛑 Zatrzymanie
   - Ctrl+C - bezpieczne zatrzymanie
   - Dokończenie aktualnych zadań
   - Zapisanie stanu

=== BEZPIECZEŃSTWO ===

1. 🔒 Zabezpieczenia
   - Limity prób połączeń
   - Obsługa błędów API
   - Zabezpieczenie przed duplikatami
   - Weryfikacja poprawności danych

2. 💾 Przechowywanie Danych
   - Śledzenie przetworzonych ofert (offers_tracking.json)
   - Zapisywanie konfiguracji (config.json, gamesconfig.json)
   - Backup danych
   - Cache dla optymalizacji

=== WSPARCIE ===

❓ W razie problemów:
   - Sprawdź logi z odpowiednim emoji
   - Zweryfikuj config.json i gamesconfig.json
   - Upewnij się, że przeglądarka jest uruchomiona
   - Sprawdź połączenie z API G2G i FunPay 