using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ShopBot.Data;
using ShopBot.Models;
using ShopBot.Services.Interfaces;

namespace ShopBot.Services
{
    public class ImageUploadRepository : IImageUploadRepository
    {
        private readonly ShopBotDbContext _context;
        private readonly ILogger<ImageUploadRepository> _logger;

        public ImageUploadRepository(ShopBotDbContext context, ILogger<ImageUploadRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<ImageUpload> CreateAsync(ImageUpload imageUpload)
        {
            _logger.LogDebug("[ImageUploadRepo] Creating image upload for offer: {OfferId}", imageUpload.OfferId);
            
            _context.ImageUploads.Add(imageUpload);
            await _context.SaveChangesAsync();
            
            _logger.LogDebug("[ImageUploadRepo] ✅ Created image upload: {Id}", imageUpload.Id);
            return imageUpload;
        }

        public async Task<ImageUpload> UpdateAsync(ImageUpload imageUpload)
        {
            _logger.LogDebug("[ImageUploadRepo] Updating image upload: {Id}", imageUpload.Id);
            
            _context.ImageUploads.Update(imageUpload);
            await _context.SaveChangesAsync();
            
            _logger.LogDebug("[ImageUploadRepo] ✅ Updated image upload: {Id}", imageUpload.Id);
            return imageUpload;
        }

        public async Task<List<ImageUpload>> GetByOfferIdAsync(Guid offerId)
        {
            return await _context.ImageUploads
                .Where(i => i.OfferId == offerId)
                .OrderBy(i => i.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<ImageUpload>> GetFailedUploadsAsync(int limit = 100)
        {
            return await _context.ImageUploads
                .Where(i => i.UploadStatus == "Failed")
                .OrderByDescending(i => i.CreatedAt)
                .Take(limit)
                .ToListAsync();
        }

        public async Task<List<ImageUpload>> GetPendingUploadsAsync(int limit = 50)
        {
            return await _context.ImageUploads
                .Where(i => i.UploadStatus == "Pending")
                .OrderBy(i => i.CreatedAt)
                .Take(limit)
                .ToListAsync();
        }

        public async Task<int> GetUploadCountByStatusAsync(string status)
        {
            return await _context.ImageUploads
                .CountAsync(i => i.UploadStatus == status);
        }
    }
}
