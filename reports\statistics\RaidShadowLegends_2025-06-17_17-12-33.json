{"TotalOffers": 1662, "AcceptedOffers": 482, "RejectedOffers": 1180, "RejectionReasons": {"Ocena ponizej minimum (0 < 5)": 333, "Cena ponizej minimum (5,28 < 15)": 16, "Zawiera zabronioną frazę (hardcoded): to choose from": 13, "Cena ponizej minimum (13,06 < 15)": 60, "Cena ponizej minimum (10,97 < 15)": 3, "Cena ponizej minimum (9,41 < 15)": 7, "Cena ponizej minimum (7,84 < 15)": 21, "Zawiera zabronioną frazę (hardcoded): starter account": 34, "Cena ponizej minimum (9,14 < 15)": 26, "Cena ponizej minimum (6,92 < 15)": 9, "Cena ponizej minimum (4,44 < 15)": 1, "Cena ponizej minimum (12,54 < 15)": 1, "Cena ponizej minimum (4,83 < 15)": 2, "Cena ponizej minimum (11,37 < 15)": 2, "Cena ponizej minimum (4,18 < 15)": 1, "Cena ponizej minimum (3,40 < 15)": 2, "Cena ponizej minimum (3,53 < 15)": 1, "Cena ponizej minimum (6,14 < 15)": 3, "Cena ponizej minimum (11,76 < 15)": 22, "Zawiera zabronioną frazę (hardcoded): with lega of choice": 8, "Cena ponizej minimum (14,37 < 15)": 20, "Cena ponizej minimum (10,45 < 15)": 26, "Cena ponizej minimum (10,32 < 15)": 1, "Cena ponizej minimum (1,54 < 15)": 1, "Cena ponizej minimum (5,23 < 15)": 21, "Cena ponizej minimum (5,10 < 15)": 3, "Cena ponizej minimum (12,67 < 15)": 1, "Cena ponizej minimum (10,06 < 15)": 1, "Cena ponizej minimum (7,45 < 15)": 2, "Cena ponizej minimum (6,53 < 15)": 37, "Zawiera zabronioną frazę (hardcoded): with any mythic": 8, "Zawiera zabronioną frazę (hardcoded): with any": 5, "Cena ponizej minimum (12,41 < 15)": 9, "Cena ponizej minimum (7,64 < 15)": 1, "Zawiera zabronioną frazę (hardcoded): start account": 12, "Cena ponizej minimum (8,52 < 15)": 1, "Cena ponizej minimum (5,32 < 15)": 4, "Zawiera zabronioną frazę (hardcoded): your choice": 7, "Cena ponizej minimum (7,19 < 15)": 11, "Cena ponizej minimum (8,49 < 15)": 8, "Zawiera zabronioną frazę (hardcoded): starting accounts": 2, "Cena ponizej minimum (6,40 < 15)": 2, "Cena ponizej minimum (4,28 < 15)": 5, "Cena ponizej minimum (5,35 < 15)": 2, "Cena ponizej minimum (3,21 < 15)": 1, "Cena ponizej minimum (6,41 < 15)": 3, "Cena ponizej minimum (7,47 < 15)": 2, "Cena ponizej minimum (2,62 < 15)": 1, "Cena ponizej minimum (10,68 < 15)": 1, "Cena ponizej minimum (4,80 < 15)": 1, "Cena ponizej minimum (6,95 < 15)": 1, "Cena ponizej minimum (4,49 < 15)": 1, "Cena ponizej minimum (2,57 < 15)": 1, "Cena ponizej minimum (9,80 < 15)": 8, "Cena ponizej minimum (2,67 < 15)": 1, "Cena ponizej minimum (11,89 < 15)": 1, "Cena ponizej minimum (11,10 < 15)": 4, "Cena ponizej minimum (9,54 < 15)": 5, "Cena ponizej minimum (9,28 < 15)": 1, "Zawiera zabronioną frazę (hardcoded): starting account with a legendary to choose": 1, "Zawiera zabronioną frazę (hardcoded): account with one leg to choose": 2, "Zawiera zabronioną frazę (hardcoded): account with 1 leg to choose": 2, "Cena ponizej minimum (10,56 < 15)": 1, "Cena powyzej maximum (1277,69 > 1000)": 1, "Cena ponizej minimum (0,98 < 15)": 1, "Cena ponizej minimum (2,25 < 15)": 1, "Cena ponizej minimum (6,58 < 15)": 2, "Cena ponizej minimum (8,77 < 15)": 1, "Cena ponizej minimum (7,68 < 15)": 3, "Cena ponizej minimum (9,87 < 15)": 2, "Cena ponizej minimum (13,16 < 15)": 1, "Cena ponizej minimum (14,25 < 15)": 1, "Cena ponizej minimum (5,48 < 15)": 1, "Cena ponizej minimum (0,52 < 15)": 22, "Cena ponizej minimum (2,19 < 15)": 2, "Ocena ponizej minimum (4 < 5)": 53, "Cena ponizej minimum (4,57 < 15)": 20, "Cena ponizej minimum (9,05 < 15)": 1, "Zawiera zabronioną frazę (hardcoded): accounts with": 8, "Zawiera zabronioną frazę (hardcoded): with ankil of your choice": 1, "Zawiera zabronioną frazę (hardcoded): with any login legend": 2, "Cena ponizej minimum (13,05 < 15)": 2, "Cena ponizej minimum (10,15 < 15)": 3, "Cena ponizej minimum (8,88 < 15)": 1, "Cena ponizej minimum (8,70 < 15)": 1, "Cena ponizej minimum (13,21 < 15)": 1, "Cena ponizej minimum (6,39 < 15)": 2, "Cena ponizej minimum (11,71 < 15)": 3, "Zawiera zabronioną frazę (config): START🏆Any LEGENDARY": 1, "Zawiera zabronioną frazę (config): start with MYTHIC": 2, "Cena ponizej minimum (10,69 < 15)": 1, "Cena ponizej minimum (1,96 < 15)": 8, "Cena ponizej minimum (14,97 < 15)": 1, "Cena ponizej minimum (8,55 < 15)": 3, "Cena ponizej minimum (13,89 < 15)": 1, "Cena ponizej minimum (11,75 < 15)": 1, "Cena ponizej minimum (8,54 < 15)": 1, "Cena ponizej minimum (12,83 < 15)": 1, "Cena ponizej minimum (13,90 < 15)": 1, "Cena powyzej maximum (1384,16 > 1000)": 1, "Cena ponizej minimum (5,86 < 15)": 1, "Cena ponizej minimum (4,68 < 15)": 1, "Cena ponizej minimum (7,67 < 15)": 1, "Ocena ponizej minimum (3 < 5)": 2, "Cena ponizej minimum (2,30 < 15)": 1, "Cena ponizej minimum (2,74 < 15)": 5, "Cena ponizej minimum (1,75 < 15)": 1, "Cena ponizej minimum (12,27 < 15)": 4, "Cena ponizej minimum (5,88 < 15)": 12, "Cena ponizej minimum (8,62 < 15)": 3, "Cena ponizej minimum (13,72 < 15)": 2, "Cena powyzej maximum (1136,59 > 1000)": 1, "Cena ponizej minimum (1,31 < 15)": 8, "Cena powyzej maximum (1306,42 > 1000)": 6, "Cena ponizej minimum (5,75 < 15)": 4, "Cena ponizej minimum (3,83 < 15)": 2, "Cena ponizej minimum (4,79 < 15)": 6, "Cena ponizej minimum (6,71 < 15)": 3, "Cena ponizej minimum (7,66 < 15)": 1, "Duplikat oferty": 3, "Cena ponizej minimum (0,32 < 15)": 1, "Cena ponizej minimum (4,96 < 15)": 2, "Cena powyzej maximum (1045,14 > 1000)": 3, "Cena ponizej minimum (2,35 < 15)": 1, "Cena ponizej minimum (12,72 < 15)": 1, "Cena powyzej maximum (1437,06 > 1000)": 1, "Cena ponizej minimum (9,53 < 15)": 1, "Cena ponizej minimum (7,57 < 15)": 1, "Cena ponizej minimum (9,32 < 15)": 1, "Cena powyzej maximum (2612,84 > 1000)": 2, "Cena powyzej maximum (2743,49 > 1000)": 1, "Cena powyzej maximum (3919,27 > 1000)": 4, "Cena ponizej minimum (14,96 < 15)": 1, "Cena ponizej minimum (8,10 < 15)": 2, "Cena ponizej minimum (3,92 < 15)": 24, "Cena ponizej minimum (6,27 < 15)": 1, "Cena ponizej minimum (6,34 < 15)": 2, "Cena ponizej minimum (0,72 < 15)": 1, "Cena ponizej minimum (14,51 < 15)": 2, "Cena powyzej maximum (1863,30 > 1000)": 1, "Cena powyzej maximum (1171,22 > 1000)": 1, "Cena powyzej maximum (1293,36 > 1000)": 1, "Cena ponizej minimum (6,01 < 15)": 1, "Cena ponizej minimum (3,27 < 15)": 20, "Cena ponizej minimum (5,49 < 15)": 1, "Cena powyzej maximum (2220,92 > 1000)": 3, "Cena ponizej minimum (6,19 < 15)": 1, "Cena ponizej minimum (14,76 < 15)": 1, "Cena ponizej minimum (1,07 < 15)": 1, "Cena ponizej minimum (9,67 < 15)": 1, "Cena ponizej minimum (7,71 < 15)": 1, "Cena ponizej minimum (14,44 < 15)": 1, "Cena ponizej minimum (1,03 < 15)": 2, "Cena powyzej maximum (1698,35 > 1000)": 1, "Cena powyzej maximum (1490,64 > 1000)": 1, "Cena ponizej minimum (13,50 < 15)": 1, "Cena ponizej minimum (5,40 < 15)": 1, "Cena ponizej minimum (2,61 < 15)": 9, "Cena powyzej maximum (2482,20 > 1000)": 1, "Cena ponizej minimum (2,04 < 15)": 1, "Cena powyzej maximum (1166,02 > 1000)": 1, "Cena ponizej minimum (6,60 < 15)": 1, "Cena ponizej minimum (0,53 < 15)": 2, "Cena powyzej maximum (1175,78 > 1000)": 4, "Cena ponizej minimum (7,36 < 15)": 1, "Cena ponizej minimum (9,30 < 15)": 1, "Cena ponizej minimum (9,51 < 15)": 1, "Cena ponizej minimum (10,19 < 15)": 1, "Cena ponizej minimum (10,84 < 15)": 1, "Cena ponizej minimum (1,57 < 15)": 1, "Cena ponizej minimum (0,39 < 15)": 1, "Cena ponizej minimum (4,31 < 15)": 2, "Cena ponizej minimum (14,16 < 15)": 1, "Cena ponizej minimum (2,50 < 15)": 1, "Cena ponizej minimum (7,05 < 15)": 1, "Cena ponizej minimum (0,44 < 15)": 1, "Cena ponizej minimum (4,19 < 15)": 1, "Cena ponizej minimum (10,58 < 15)": 1, "Cena ponizej minimum (4,91 < 15)": 3, "Cena powyzej maximum (3292,18 > 1000)": 1, "Cena powyzej maximum (2129,47 > 1000)": 1, "Cena ponizej minimum (3,66 < 15)": 2, "Cena ponizej minimum (0,65 < 15)": 1, "Cena ponizej minimum (2,22 < 15)": 1, "Cena ponizej minimum (5,21 < 15)": 1, "Cena ponizej minimum (5,62 < 15)": 1, "Cena powyzej maximum (1288,76 > 1000)": 1, "Cena ponizej minimum (1,05 < 15)": 1, "Cena ponizej minimum (9,82 < 15)": 2, "Cena ponizej minimum (3,88 < 15)": 1, "Cena ponizej minimum (5,80 < 15)": 1, "Cena ponizej minimum (5,52 < 15)": 1, "Cena ponizej minimum (0,49 < 15)": 1, "Cena ponizej minimum (2,13 < 15)": 2, "Cena ponizej minimum (1,83 < 15)": 1, "Cena powyzej maximum (3068,47 > 1000)": 1, "Cena ponizej minimum (1,18 < 15)": 1, "Cena ponizej minimum (2,87 < 15)": 1, "Cena powyzej maximum (1959,63 > 1000)": 1, "Cena ponizej minimum (2,44 < 15)": 1, "Cena ponizej minimum (8,09 < 15)": 1, "Cena ponizej minimum (13,46 < 15)": 1, "Cena powyzej maximum (1371,74 > 1000)": 1, "Cena ponizej minimum (7,25 < 15)": 1, "Cena powyzej maximum (1358,68 > 1000)": 1, "Cena powyzej maximum (1345,61 > 1000)": 1, "Cena ponizej minimum (3,19 < 15)": 1, "Cena ponizej minimum (4,25 < 15)": 1, "Cena ponizej minimum (6,89 < 15)": 1}, "GenerationTime": "2025-06-17T17:12:33.6449972+02:00"}