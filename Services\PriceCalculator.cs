using System;
using ShopBot.Config;

namespace ShopBot.Services
{
    public static class PriceCalculator
    {
        public static decimal CalculatePrice(decimal originalPrice)
        {
            var appSettings = AppSettings.Instance;
            var margin = appSettings.Pricing.DefaultMarginPercent / 100m;

            // Oblicz cenę z marżą 25%
            var calculatedPrice = originalPrice * (1 + margin);

            // Zaokrąglij do 2 miejsc po przecinku
            return Math.Round(calculatedPrice, 2);
        }

        public static decimal CalculatePriceWithGameLimits(decimal originalPrice, string gameName)
        {
            Console.WriteLine($"[PriceCalculator] 🎯 Rozpoczynam kalkulację dla gry: {gameName}");
            Console.WriteLine($"[PriceCalculator] 💰 Cena wejściowa: ${originalPrice:F2}");

            var appSettings = AppSettings.Instance;
            var margins = appSettings.Pricing.ProgressiveMargins;

            // Progresywny system marż (konfigurowalny)
            decimal margin;
            string marginInfo;

            if (originalPrice < 30m)
            {
                margin = margins.Under30 / 100m;
                marginInfo = $"{margins.Under30}% (< $30)";
            }
            else if (originalPrice < 50m)
            {
                margin = margins.From30To50 / 100m;
                marginInfo = $"{margins.From30To50}% ($30-$50)";
            }
            else if (originalPrice < 300m)
            {
                margin = margins.From50To300 / 100m;
                marginInfo = $"{margins.From50To300}% ($50-$300)";
            }
            else
            {
                margin = margins.Above300 / 100m;
                marginInfo = $"{margins.Above300}% (≥ $300)";
            }

            // Oblicz cenę z progresywną marżą
            var calculatedPrice = originalPrice * (1 + margin);

            Console.WriteLine($"[PriceCalculator] 📊 ${originalPrice:F2} → marża {marginInfo} → ${calculatedPrice:F2}");

            // Pobierz limity cenowe dla gry
            var priceRange = GameConfig.GetGamePriceRange(gameName);
            Console.WriteLine($"[PriceCalculator] 🎮 Limity dla {gameName}: ${priceRange.Min} - ${priceRange.Max}");

            // Zastosuj limity per gra
            if (calculatedPrice < priceRange.Min)
            {
                Console.WriteLine($"[PriceCalculator] ⬆️ Cena {calculatedPrice:F2} poniżej minimum {priceRange.Min} dla {gameName}, ustawiam na minimum");
                calculatedPrice = priceRange.Min;
            }
            else if (calculatedPrice > priceRange.Max)
            {
                Console.WriteLine($"[PriceCalculator] ⬇️ Cena {calculatedPrice:F2} powyżej maksimum {priceRange.Max} dla {gameName}, ustawiam na maksimum");
                calculatedPrice = priceRange.Max;
            }
            else
            {
                Console.WriteLine($"[PriceCalculator] ✅ Cena {calculatedPrice:F2} mieści się w limitach dla {gameName}");
            }

            var finalPrice = Math.Round(calculatedPrice, 2);
            Console.WriteLine($"[PriceCalculator] 🎯 FINALNA CENA: ${finalPrice:F2}");

            return finalPrice;
        }
        
        public static decimal CalculatePriceWithCustomMargin(decimal originalPrice, decimal marginPercent)
        {
            var margin = marginPercent / 100m;
            var calculatedPrice = originalPrice * (1 + margin);
            return Math.Round(calculatedPrice, 2);
        }
    }
}
