using HtmlAgilityPack;
using System.Collections.Generic;

namespace ShopBot.Services
{
    /// <summary>
    /// Główny interfejs dla serwisu parsowania - będzie refaktoryzowany na mniejsze interfejsy
    /// </summary>
    public interface IParserService
    {
        /// <summary>
        /// Parsuje szczegóły oferty z dokumentu HTML
        /// </summary>
        OfferDetails ParseOfferDetails(HtmlDocument document, string url);

        /// <summary>
        /// Parsuje listę ofert z dokumentu HTML
        /// </summary>
        List<OfferListItem> ParseOfferList(HtmlDocument document);

        /// <summary>
        /// Waliduje i naprawia URL
        /// </summary>
        string ValidateAndFixUrl(string url);

        /// <summary>
        /// Wyciąga ID z URL oferty
        /// </summary>
        string ExtractIdFromUrl(string url);
    }

    /// <summary>
    /// Interfejs dla parsowania szczegółów ofert - docelowy po refaktoryzacji
    /// </summary>
    public interface IOfferDetailsParser
    {
        OfferDetails ParseOfferDetails(HtmlDocument document, string url);
        Dictionary<string, string> ParseParameters(HtmlDocument document);
        decimal ParsePrice(HtmlDocument document);
        string ParseDescription(HtmlDocument document);
    }

    /// <summary>
    /// Interfejs dla parsowania list ofert - docelowy po refaktoryzacji
    /// </summary>
    public interface IOfferListParser
    {
        List<OfferListItem> ParseOfferList(HtmlDocument document);
        OfferListItem ParseSingleOfferFromList(HtmlNode offerNode);
    }

    /// <summary>
    /// Interfejs dla parsowania obrazów - docelowy po refaktoryzacji
    /// </summary>
    public interface IImageParser
    {
        List<string> ParseImages(HtmlDocument document);
        string ValidateImageUrl(string imageUrl);
        bool IsValidImageFormat(string imageUrl);
    }

    /// <summary>
    /// Interfejs dla walidacji i manipulacji URL - docelowy po refaktoryzacji
    /// </summary>
    public interface IUrlValidator
    {
        string ValidateAndFixUrl(string url);
        string ExtractIdFromUrl(string url);
        bool IsValidFunPayUrl(string url);
        string NormalizeUrl(string url);
    }

    /// <summary>
    /// Interfejs dla ekstrakcji danych - docelowy po refaktoryzacji
    /// </summary>
    public interface IDataExtractor
    {
        string ExtractText(HtmlNode node, string selector);
        decimal ExtractNumber(string text);
        List<string> ExtractMultipleValues(HtmlDocument document, string selector);
    }
}
