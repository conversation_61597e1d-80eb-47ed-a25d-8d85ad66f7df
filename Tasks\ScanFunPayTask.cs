using System;
using System.Threading;
using System.Threading.Tasks;
using ShopBot.Services;
using ShopBot.Config;
using Microsoft.Playwright;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ShopBot.Services.Formatters;
using ShopBot.Services.Extensions;
using ShopBot.Services.Interfaces;

namespace ShopBot.Tasks
{
    public class ScanFunPayTask : BaseTask
    {
        private readonly FunPayService _funPayService;
        private readonly TaskScheduler _taskScheduler;
        private readonly ProcessedOffersTracker _processedOffersTracker;
        private readonly ParserService _parserService;
        private readonly IBrowser _browser;
        private readonly IBrowserContext _context;
        private readonly ImageUploadService _imageUploadService;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<ScanFunPayTask> _logger;

        public ScanFunPayTask(
            FunPayService funPayService,
            TaskScheduler taskScheduler,
            ProcessedOffersTracker processedOffersTracker,
            ParserService parserService,
            IBrowser browser,
            IBrowserContext context,
            ImageUploadService imageUploadService,
            IServiceProvider serviceProvider,
            ILogger<ScanFunPayTask> logger)
        {
            _funPayService = funPayService;
            _taskScheduler = taskScheduler;
            _processedOffersTracker = processedOffersTracker;
            _parserService = parserService;
            _browser = browser;
            _context = context;
            _imageUploadService = imageUploadService;
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        public override async Task ExecuteAsync(CancellationToken cancellationToken)
        {
            try
            {
                Status = TaskStatus.Running;
                _logger.LogScanStart();

                var (filter, _, _) = FilterConfig.LoadFromFile();
                var gamesToProcess = GameConfig.GetActiveGames();

                foreach (var game in gamesToProcess)
                {
                    if (cancellationToken.IsCancellationRequested)
                        break;

                    try
                    {
                        _logger.LogGameScanStart(game);
                        var page = await _browser.NewPageAsync();
                        var funPayService = _serviceProvider.GetRequiredService<Func<IPage, FunPayService>>()(page);
                        var filteredOffers = await funPayService.GetFilteredOffers(filter, game);
                        await page.CloseAsync();

                        foreach (var offer in filteredOffers)
                        {
                            if (_processedOffersTracker.WasOfferProcessed(offer.Id))
                            {
                                _logger.LogDebug("[Scan] Pomijam już przetworzoną ofertę: {OfferId}", offer.Id);
                                continue;
                            }

                            var addOfferTask = new AddOfferTask(
                                offer,
                                game,
                                _context,
                                _processedOffersTracker,
                                _parserService,
                                _imageUploadService,
                                _serviceProvider.GetRequiredService<DescriptionFormatterFactory>(),
                                _serviceProvider.GetRequiredService<IFunPayDetailsService>(),
                                _serviceProvider.GetRequiredService<IOfferRepository>(),
                                _serviceProvider.GetRequiredService<ILogger<AddOfferTask>>(),
                                _serviceProvider.GetRequiredService<G2GPublisherService>(),
                                _serviceProvider.GetRequiredService<G2GOfferManager>()
                            );
                            await _taskScheduler.ScheduleTask(addOfferTask);
                        }

                        _logger.LogGameScanComplete(game, filteredOffers.Count);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogScanError(game, ex.Message);
                    }
                }

                _logger.LogScanComplete();
                Status = TaskStatus.Completed;
            }
            catch (Exception ex)
            {
                Status = TaskStatus.Failed;
                ErrorMessage = ex.Message;
                _logger.LogError("[Scan] Błąd podczas skanowania FunPay: {Error}", ex.Message);
                throw;
            }
        }
    }
} 