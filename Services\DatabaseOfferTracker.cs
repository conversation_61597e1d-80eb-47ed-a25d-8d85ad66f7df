using Microsoft.Extensions.Logging;
using ShopBot.Services.Interfaces;
using ShopBot.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;

namespace ShopBot.Services
{
    /// <summary>
    /// Database-based offer tracking - replacement for ProcessedOffersTracker
    /// Uses Repository Pattern for persistent storage
    /// </summary>
    public interface IDatabaseOfferTracker
    {
        Task<bool> WasOfferProcessedAsync(string funPayOfferId);
        Task AddProcessedOfferAsync(string funPayOfferId, string gameName);
        Task<List<string>> GetAllOfferIdsAsync();
        Task<DateTime> GetLastProcessingTimeAsync();
        Task<bool> TryProcessOfferAsync(string funPayOfferId, string gameName);
        Task SynchronizeWithActiveOffersAsync(ISet<string> verifiedOffers);
        Task<int> GetProcessedOffersCountAsync();
        Task<Dictionary<string, int>> GetOfferCountsByGameAsync();
    }

    public class DatabaseOfferTracker : IDatabaseOfferTracker
    {
        private readonly IShopBotRepository _repository;
        private readonly ILogger<DatabaseOfferTracker> _logger;
        private readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);

        public DatabaseOfferTracker(
            IShopBotRepository repository,
            ILogger<DatabaseOfferTracker> logger)
        {
            _repository = repository;
            _logger = logger;
        }

        public async Task<bool> WasOfferProcessedAsync(string funPayOfferId)
        {
            try
            {
                var offer = await _repository.Offers.GetByFunPayIdAsync(funPayOfferId);
                var wasProcessed = offer != null;
                
                _logger.LogDebug("[DatabaseTracker] 🔍 Offer {OfferId} processed: {WasProcessed}", 
                    funPayOfferId, wasProcessed);
                
                return wasProcessed;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[DatabaseTracker] ❌ Error checking if offer {OfferId} was processed", funPayOfferId);
                return false;
            }
        }

        public async Task AddProcessedOfferAsync(string funPayOfferId, string gameName)
        {
            try
            {
                // Check if offer already exists
                var existingOffer = await _repository.Offers.GetByFunPayIdAsync(funPayOfferId);
                if (existingOffer != null)
                {
                    _logger.LogDebug("[DatabaseTracker] ⚠️ Offer {OfferId} already exists", funPayOfferId);
                    return;
                }

                // Create new offer record
                var offer = new Offer
                {
                    FunPayOfferId = funPayOfferId,
                    GameName = gameName,
                    Title = "Pending Processing",
                    Status = "Pending",
                    ProcessedAt = DateTime.UtcNow,
                    OriginalPrice = 0,
                    ConvertedPrice = 0,
                    Currency = "USD"
                };

                await _repository.Offers.CreateAsync(offer);
                _logger.LogInformation("[DatabaseTracker] ✅ Added offer {OfferId} to database", funPayOfferId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[DatabaseTracker] ❌ Error adding offer {OfferId}", funPayOfferId);
                throw;
            }
        }

        public async Task<List<string>> GetAllOfferIdsAsync()
        {
            try
            {
                var offers = await _repository.Offers.GetAllAsync();
                var offerIds = offers.Select(o => o.FunPayOfferId).ToList();
                
                _logger.LogDebug("[DatabaseTracker] 📊 Retrieved {Count} offer IDs", offerIds.Count);
                return offerIds;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[DatabaseTracker] ❌ Error retrieving offer IDs");
                return new List<string>();
            }
        }

        public async Task<DateTime> GetLastProcessingTimeAsync()
        {
            try
            {
                var offers = await _repository.Offers.GetAllAsync();
                if (!offers.Any())
                {
                    return DateTime.MinValue;
                }

                var lastProcessingTime = offers.Max(o => o.ProcessedAt);
                _logger.LogDebug("[DatabaseTracker] 📅 Last processing time: {LastTime}", lastProcessingTime);
                
                return lastProcessingTime;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[DatabaseTracker] ❌ Error getting last processing time");
                return DateTime.MinValue;
            }
        }

        public async Task<bool> TryProcessOfferAsync(string funPayOfferId, string gameName)
        {
            await _semaphore.WaitAsync();
            try
            {
                var wasProcessed = await WasOfferProcessedAsync(funPayOfferId);
                if (wasProcessed)
                {
                    _logger.LogDebug("[DatabaseTracker] ⚠️ Offer {OfferId} already processed", funPayOfferId);
                    return false;
                }

                await AddProcessedOfferAsync(funPayOfferId, gameName);
                _logger.LogInformation("[DatabaseTracker] ✅ Successfully processed offer {OfferId}", funPayOfferId);
                return true;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        public async Task SynchronizeWithActiveOffersAsync(ISet<string> verifiedOffers)
        {
            try
            {
                _logger.LogInformation("[DatabaseTracker] 🔄 Synchronizing with {Count} verified offers", verifiedOffers.Count);

                var allOffers = await _repository.Offers.GetAllAsync();
                var currentOfferIds = allOffers.Select(o => o.FunPayOfferId).ToHashSet();

                // Find inactive offers (in database but not in verified set)
                var inactiveOffers = currentOfferIds.Except(verifiedOffers).ToList();
                
                if (inactiveOffers.Any())
                {
                    _logger.LogInformation("[DatabaseTracker] 🗑️ Found {Count} inactive offers to remove", inactiveOffers.Count);
                    
                    foreach (var inactiveOfferId in inactiveOffers)
                    {
                        var offer = await _repository.Offers.GetByFunPayIdAsync(inactiveOfferId);
                        if (offer != null)
                        {
                            await _repository.Offers.DeleteAsync(offer.Id);
                            _logger.LogDebug("[DatabaseTracker] 🗑️ Removed inactive offer: {OfferId}", inactiveOfferId);
                        }
                    }
                }

                // Add new verified offers that aren't in database
                var newOffers = verifiedOffers.Except(currentOfferIds).ToList();
                if (newOffers.Any())
                {
                    _logger.LogInformation("[DatabaseTracker] ➕ Found {Count} new offers to add", newOffers.Count);
                    
                    foreach (var newOfferId in newOffers)
                    {
                        await AddProcessedOfferAsync(newOfferId, "Unknown"); // Game will be updated later
                    }
                }

                await _repository.SaveChangesAsync();
                _logger.LogInformation("[DatabaseTracker] ✅ Synchronization completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[DatabaseTracker] ❌ Error during synchronization");
                throw;
            }
        }

        public async Task<int> GetProcessedOffersCountAsync()
        {
            try
            {
                var offers = await _repository.Offers.GetAllAsync();
                var count = offers.Count;
                
                _logger.LogDebug("[DatabaseTracker] 📊 Total processed offers: {Count}", count);
                return count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[DatabaseTracker] ❌ Error getting processed offers count");
                return 0;
            }
        }

        public async Task<Dictionary<string, int>> GetOfferCountsByGameAsync()
        {
            try
            {
                var offerCounts = await _repository.Offers.GetOfferCountsByGameAsync();
                
                _logger.LogDebug("[DatabaseTracker] 📊 Offer counts by game: {Counts}", 
                    string.Join(", ", offerCounts.Select(kv => $"{kv.Key}: {kv.Value}")));
                
                return offerCounts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[DatabaseTracker] ❌ Error getting offer counts by game");
                return new Dictionary<string, int>();
            }
        }

        public void Dispose()
        {
            _semaphore?.Dispose();
        }
    }
}
