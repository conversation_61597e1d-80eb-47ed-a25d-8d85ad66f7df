using System;
using System.Threading;
using System.Threading.Tasks;
using ShopBot.Services;
using System.Linq;

namespace ShopBot.Tasks
{
    public class DeleteOfferTask : BaseTask
    {
        private readonly string _offerId;
        private readonly DeleteService _deleteService;

        public DeleteOfferTask(string offerId, DeleteService deleteService)
        {
            _offerId = offerId;
            _deleteService = deleteService;
        }

        public override async Task ExecuteAsync(CancellationToken cancellationToken)
        {
            try
            {
                Status = TaskStatus.Running;

                try
                {
                    // Usuń ofertę ze strony G2G niezależnie od tego czy jest w trackerze
                    await _deleteService.DeleteOffer(_offerId);
                    
                    // Je<PERSON><PERSON> oferta była w trackerze, zostanie usunięta przez DeleteService
                    Status = TaskStatus.Completed;
                }
                catch (Exception ex)
                {
                    Status = TaskStatus.Failed;
                    ErrorMessage = ex.Message;
                    Console.WriteLine($"[DeleteTask] ❌ Błąd podczas usuwania oferty {_offerId}: {ex.Message}");
                    throw;
                }
            }
            catch (Exception ex)
            {
                Status = TaskStatus.Failed;
                ErrorMessage = ex.Message;
                Console.WriteLine($"[DeleteTask] ❌ Błąd krytyczny podczas usuwania oferty {_offerId}: {ex.Message}");
                throw;
            }
        }
    }
} 