using HtmlAgilityPack;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;

namespace ShopBot.Services.Parsers
{
    /// <summary>
    /// Extractor odpowiedzialny za wyciąganie danych z HTML
    /// </summary>
    public class DataExtractor : IDataExtractor
    {
        private readonly ILogger<DataExtractor> _logger;

        public DataExtractor(ILogger<DataExtractor> logger)
        {
            _logger = logger;
        }

        public string ExtractText(HtmlNode node, string selector)
        {
            try
            {
                if (node == null)
                    return "";

                HtmlNode targetNode;
                
                if (string.IsNullOrEmpty(selector))
                {
                    targetNode = node;
                }
                else
                {
                    targetNode = node.SelectSingleNode(selector);
                    if (targetNode == null)
                        return "";
                }

                // Wyciągnij tekst i oczyść go
                var text = targetNode.InnerText ?? "";
                text = CleanText(text);

                return text;
            }
            catch (Exception ex)
            {
                _logger.LogTrace(ex, "⚠️ Błąd wyciągania tekstu z selektora: {Selector}", selector);
                return "";
            }
        }

        public decimal ExtractNumber(string text)
        {
            if (string.IsNullOrEmpty(text))
                return 0;

            try
            {
                // Usuń wszystkie znaki oprócz cyfr, kropek, przecinków i minusów
                var cleanText = Regex.Replace(text, @"[^\d.,-]", "");
                
                if (string.IsNullOrEmpty(cleanText))
                    return 0;

                // Spróbuj różne formaty liczbowe
                var formats = new[]
                {
                    CultureInfo.InvariantCulture,
                    new CultureInfo("en-US"),
                    new CultureInfo("pl-PL"),
                    new CultureInfo("de-DE")
                };

                foreach (var format in formats)
                {
                    if (decimal.TryParse(cleanText, NumberStyles.Any, format, out decimal result))
                    {
                        return result;
                    }
                }

                // Ostatnia próba - usuń wszystko oprócz cyfr i spróbuj jako integer
                var digitsOnly = Regex.Replace(cleanText, @"[^\d]", "");
                if (!string.IsNullOrEmpty(digitsOnly) && decimal.TryParse(digitsOnly, out decimal intResult))
                {
                    return intResult;
                }

                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogTrace(ex, "⚠️ Błąd wyciągania liczby z tekstu: {Text}", text);
                return 0;
            }
        }

        public List<string> ExtractMultipleValues(HtmlDocument document, string selector)
        {
            var values = new List<string>();

            try
            {
                if (document?.DocumentNode == null || string.IsNullOrEmpty(selector))
                    return values;

                var nodes = document.DocumentNode.SelectNodes(selector);
                if (nodes == null)
                    return values;

                foreach (var node in nodes)
                {
                    var text = ExtractText(node, "");
                    if (!string.IsNullOrEmpty(text))
                    {
                        values.Add(text);
                    }
                }

                _logger.LogTrace("📋 Wyciągnięto {Count} wartości z selektora: {Selector}", values.Count, selector);
                return values;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "❌ Błąd wyciągania wielu wartości z selektora: {Selector}", selector);
                return values;
            }
        }

        /// <summary>
        /// Wyciąga atrybut z węzła HTML
        /// </summary>
        public string ExtractAttribute(HtmlNode node, string attributeName)
        {
            try
            {
                if (node == null || string.IsNullOrEmpty(attributeName))
                    return "";

                var attributeValue = node.GetAttributeValue(attributeName, "");
                return CleanText(attributeValue);
            }
            catch (Exception ex)
            {
                _logger.LogTrace(ex, "⚠️ Błąd wyciągania atrybutu {Attribute}", attributeName);
                return "";
            }
        }

        /// <summary>
        /// Wyciąga wszystkie atrybuty z węzła
        /// </summary>
        public Dictionary<string, string> ExtractAllAttributes(HtmlNode node)
        {
            var attributes = new Dictionary<string, string>();

            try
            {
                if (node?.Attributes == null)
                    return attributes;

                foreach (var attr in node.Attributes)
                {
                    if (!string.IsNullOrEmpty(attr.Name) && !string.IsNullOrEmpty(attr.Value))
                    {
                        attributes[attr.Name] = CleanText(attr.Value);
                    }
                }

                return attributes;
            }
            catch (Exception ex)
            {
                _logger.LogTrace(ex, "⚠️ Błąd wyciągania wszystkich atrybutów");
                return attributes;
            }
        }

        /// <summary>
        /// Sprawdza czy węzeł zawiera określony tekst
        /// </summary>
        public bool NodeContainsText(HtmlNode node, string searchText, bool ignoreCase = true)
        {
            try
            {
                if (node == null || string.IsNullOrEmpty(searchText))
                    return false;

                var nodeText = ExtractText(node, "");
                
                return ignoreCase 
                    ? nodeText.IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0
                    : nodeText.Contains(searchText);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Wyciąga liczbę z tekstu używając wyrażenia regularnego
        /// </summary>
        public decimal ExtractNumberWithPattern(string text, string pattern)
        {
            try
            {
                if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(pattern))
                    return 0;

                var match = Regex.Match(text, pattern);
                if (match.Success && match.Groups.Count > 1)
                {
                    return ExtractNumber(match.Groups[1].Value);
                }

                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogTrace(ex, "⚠️ Błąd wyciągania liczby wzorcem: {Pattern} z tekstu: {Text}", pattern, text);
                return 0;
            }
        }

        /// <summary>
        /// Czyści tekst z nadmiarowych białych znaków i znaków specjalnych
        /// </summary>
        private string CleanText(string text)
        {
            if (string.IsNullOrEmpty(text))
                return "";

            // Dekoduj encje HTML
            text = HtmlEntity.DeEntitize(text);

            // Usuń nadmiarowe białe znaki
            text = Regex.Replace(text, @"\s+", " ");

            // Usuń białe znaki na początku i końcu
            text = text.Trim();

            return text;
        }
    }

    /// <summary>
    /// Pomocnicza klasa dla operacji specyficznych dla gier
    /// </summary>
    public static class GameSpecificHelper
    {
        public static string DetermineGameEdition(string description)
        {
            if (string.IsNullOrEmpty(description))
                return "";

            var descLower = description.ToLowerInvariant();

            // Sprawdź różne edycje gier
            if (descLower.Contains("standard edition") || descLower.Contains("standard"))
                return "Standard Edition";

            if (descLower.Contains("deluxe edition") || descLower.Contains("deluxe"))
                return "Deluxe Edition";

            if (descLower.Contains("ultimate edition") || descLower.Contains("ultimate"))
                return "Ultimate Edition";

            if (descLower.Contains("premium edition") || descLower.Contains("premium"))
                return "Premium Edition";

            return "";
        }
    }
}
