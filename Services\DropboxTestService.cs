using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace ShopBot.Services
{
    /// <summary>
    /// Serwis do testowania funkcjonalności Dropbox
    /// </summary>
    public class DropboxTestService
    {
        private readonly DropboxService _dropboxService;
        private readonly ILogger _logger;

        public DropboxTestService(DropboxService dropboxService, ILogger logger)
        {
            _dropboxService = dropboxService ?? throw new ArgumentNullException(nameof(dropboxService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Uruchamia test uploadu obrazów do Dropbox
        /// </summary>
        public async Task RunTestAsync()
        {
            _logger.LogInformation("=== 🧪 TEST DROPBOX UPLOAD ===");

            // Test URLs - prawdziwy obrazek z FunPay
            var testImageUrls = new List<string>
            {
                "https://sfunpay.com/s/offer/ap/h4/aph4z04jufw645ri6a1m.jpg"
            };

            _logger.LogInformation("Testuję upload {Count} obrazów do Dropbox...", testImageUrls.Count);

            var results = new List<DropboxUploadResult>();

            for (int i = 0; i < testImageUrls.Count; i++)
            {
                var imageUrl = testImageUrls[i];
                _logger.LogInformation("--- Test {Current}/{Total} ---", i + 1, testImageUrls.Count);
                _logger.LogInformation("URL: {ImageUrl}", imageUrl);

                try
                {
                    var result = await _dropboxService.UploadImageFromUrlAsync(imageUrl, $"test_image_{i + 1}.png");
                    results.Add(result);

                    if (result.Success)
                    {
                        _logger.LogInformation("✅ SUCCESS!");
                        _logger.LogInformation("Dropbox URL: {DropboxUrl}", result.DropboxUrl);
                        _logger.LogInformation("Direct URL: {DirectUrl}", result.DirectUrl);
                    }
                    else
                    {
                        _logger.LogError("❌ FAILED: {ErrorMessage}", result.ErrorMessage);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "❌ EXCEPTION podczas uploadu obrazu {ImageUrl}", imageUrl);
                }

                // Pauza między uploadami
                await Task.Delay(3000);
            }

            // Podsumowanie
            LogTestSummary(results);
        }

        private void LogTestSummary(List<DropboxUploadResult> results)
        {
            _logger.LogInformation("=== 📊 PODSUMOWANIE TESTU ===");
            
            var successful = results.Count(r => r.Success);
            _logger.LogInformation("Pomyślne uploady: {Successful}/{Total}", successful, results.Count);

            if (successful > 0)
            {
                _logger.LogInformation("📸 Linki do zauploadowanych obrazów:");
                foreach (var result in results.Where(r => r.Success))
                {
                    _logger.LogInformation("- {DirectUrl}", result.DirectUrl);
                }

                _logger.LogInformation("🔗 Sprawdź w Dropbox:");
                _logger.LogInformation("1. Idź na https://dropbox.com");
                _logger.LogInformation("2. Sprawdź folder /G2G_Screenshots/");
                _logger.LogInformation("3. Powinny być tam testowe obrazy");
            }

            _logger.LogInformation("Test zakończony!");
        }
    }
}
