using HtmlAgilityPack;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;

namespace ShopBot.Services
{
    /// <summary>
    /// Nowy, zrefaktoryzowany ParserService używający wzorca kompozycji
    /// Deleguje odpowiedzialności do wyspecjalizowanych parserów
    /// </summary>
    public class RefactoredParserService : IParserService
    {
        private readonly ILogger<RefactoredParserService> _logger;
        private readonly IOfferDetailsParser _offerDetailsParser;
        private readonly IOfferListParser _offerListParser;
        private readonly IUrlValidator _urlValidator;

        public RefactoredParserService(
            ILogger<RefactoredParserService> logger,
            IOfferDetailsParser offerDetailsParser,
            IOfferListParser offerListParser,
            IUrlValidator urlValidator)
        {
            _logger = logger;
            _offerDetailsParser = offerDetailsParser;
            _offerListParser = offerListParser;
            _urlValidator = urlValidator;
        }

        /// <summary>
        /// Parsuje szczegóły oferty - deleguje do IOfferDetailsParser
        /// </summary>
        public OfferDetails ParseOfferDetails(HtmlDocument document, string url)
        {
            _logger.LogDebug("🔍 Parsowanie szczegółów oferty z URL: {Url}", url);
            return _offerDetailsParser.ParseOfferDetails(document, url);
        }

        /// <summary>
        /// Parsuje listę ofert - deleguje do IOfferListParser
        /// </summary>
        public List<OfferListItem> ParseOfferList(HtmlDocument document)
        {
            _logger.LogDebug("📋 Parsowanie listy ofert");
            return _offerListParser.ParseOfferList(document);
        }

        /// <summary>
        /// Waliduje i naprawia URL - deleguje do IUrlValidator
        /// </summary>
        public string ValidateAndFixUrl(string url)
        {
            return _urlValidator.ValidateAndFixUrl(url);
        }

        /// <summary>
        /// Wyciąga ID z URL - deleguje do IUrlValidator
        /// </summary>
        public string ExtractIdFromUrl(string url)
        {
            return _urlValidator.ExtractIdFromUrl(url);
        }
    }
}
