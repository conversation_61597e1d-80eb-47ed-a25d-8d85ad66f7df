using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ShopBot.Data;
using ShopBot.Models;
using ShopBot.Services.Interfaces;

namespace ShopBot.Services
{
    public class ProcessingStatsRepository : IProcessingStatsRepository
    {
        private readonly ShopBotDbContext _context;
        private readonly ILogger<ProcessingStatsRepository> _logger;

        public ProcessingStatsRepository(ShopBotDbContext context, ILogger<ProcessingStatsRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<ProcessingStat?> GetByDateAndGameAsync(DateTime date, string gameName)
        {
            var dateOnly = date.Date;
            return await _context.ProcessingStats
                .FirstOrDefaultAsync(s => s.Date.Date == dateOnly && s.GameName == gameName);
        }

        public async Task<ProcessingStat> CreateOrUpdateAsync(ProcessingStat stat)
        {
            var existing = await GetByDateAndGameAsync(stat.Date, stat.GameName);
            
            if (existing != null)
            {
                // Update existing
                existing.OffersScanned = stat.OffersScanned;
                existing.OffersProcessed = stat.OffersProcessed;
                existing.OffersPublished = stat.OffersPublished;
                existing.OffersFailed = stat.OffersFailed;
                existing.OffersSkipped = stat.OffersSkipped;
                existing.AverageProcessingTimeMs = stat.AverageProcessingTimeMs;
                existing.TotalProcessingTimeMs = stat.TotalProcessingTimeMs;
                existing.ErrorCount = stat.ErrorCount;
                existing.LastError = stat.LastError;

                _context.ProcessingStats.Update(existing);
                await _context.SaveChangesAsync();

                _logger.LogDebug("[StatsRepo] ✅ Updated stats for {GameName} on {Date}", stat.GameName, stat.Date.Date);
                return existing;
            }
            else
            {
                // Create new
                _context.ProcessingStats.Add(stat);
                await _context.SaveChangesAsync();

                _logger.LogDebug("[StatsRepo] ✅ Created stats for {GameName} on {Date}", stat.GameName, stat.Date.Date);
                return stat;
            }
        }

        public async Task<List<ProcessingStat>> GetStatsAsync(DateTime from, DateTime to, string? gameName = null)
        {
            var query = _context.ProcessingStats
                .Where(s => s.Date >= from.Date && s.Date <= to.Date);

            if (!string.IsNullOrEmpty(gameName))
            {
                query = query.Where(s => s.GameName == gameName);
            }

            return await query
                .OrderByDescending(s => s.Date)
                .ThenBy(s => s.GameName)
                .ToListAsync();
        }

        public async Task<Dictionary<string, int>> GetTotalsByGameAsync(DateTime from, DateTime to)
        {
            return await _context.ProcessingStats
                .Where(s => s.Date >= from.Date && s.Date <= to.Date)
                .GroupBy(s => s.GameName)
                .Select(g => new { Game = g.Key, Total = g.Sum(s => s.OffersProcessed) })
                .ToDictionaryAsync(x => x.Game, x => x.Total);
        }

        public async Task<List<ProcessingStat>> GetDailyStatsAsync(int days = 30)
        {
            var fromDate = DateTime.UtcNow.Date.AddDays(-days);
            
            return await _context.ProcessingStats
                .Where(s => s.Date >= fromDate)
                .OrderByDescending(s => s.Date)
                .ThenBy(s => s.GameName)
                .ToListAsync();
        }
    }
}
