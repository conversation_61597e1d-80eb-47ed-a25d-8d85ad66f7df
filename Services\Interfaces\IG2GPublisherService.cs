using Microsoft.Playwright;
using System.Threading.Tasks;

namespace ShopBot.Services
{
    /// <summary>
    /// Interfejs dla serwisu publikacji ofert na G2G
    /// </summary>
    public interface IG2GPublisherService
    {
        /// <summary>
        /// Publikuje ofertę na platformie G2G
        /// </summary>
        /// <param name="offer">Szczegóły oferty do publikacji</param>
        /// <param name="gameName">Nazwa gry</param>
        /// <param name="page">Strona przeglądarki do użycia</param>
        /// <returns>ID opublikowanej oferty lub null w przypadku błędu</returns>
        Task<string?> PublishOffer(OfferDetails offer, string gameName, IPage page);

        /// <summary>
        /// Aktualizuje cenę istniejącej oferty
        /// </summary>
        Task<bool> UpdateOfferPrice(string offerId, decimal newPrice);

        /// <summary>
        /// Sprawdza status oferty na G2G
        /// </summary>
        Task<string?> GetOfferStatus(string offerId);

        /// <summary>
        /// Usuwa ofertę z G2G
        /// </summary>
        Task<bool> DeleteOffer(string offerId);

        /// <summary>
        /// Sprawdza dostępność serwisu G2G
        /// </summary>
        Task<bool> IsServiceAvailable();
    }
}
