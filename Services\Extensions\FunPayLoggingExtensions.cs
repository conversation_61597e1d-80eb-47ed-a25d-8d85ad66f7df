using Microsoft.Extensions.Logging;

namespace ShopBot.Services.Extensions
{
    /// <summary>
    /// Extension methods dla FunPayService structured logging
    /// Zastępuje Console.WriteLine z structured logging
    /// </summary>
    public static class FunPayLoggingExtensions
    {
        // ===== OFFER DISCOVERY =====
        public static void LogOffersFound(this ILogger logger, int offerCount, string gameName)
            => logger.LogInformation("[FunPay] 🔍 Znaleziono {OfferCount} ofert dla {GameName}", offerCount, gameName);

        public static void LogOffersFiltered(this ILogger logger, int filteredCount, int totalCount)
            => logger.LogInformation("[FunPay] 🔽 Po filtrowaniu zostało {FilteredCount} ofert z {TotalCount}", filteredCount, totalCount);

        // ===== ERROR HANDLING =====
        public static void LogSaleButtonError(this ILogger logger, string error)
            => logger.LogError("[FunPay] ❌ Błąd podczas szukania/klikania przycisku Sale: {Error}", error);

        public static void LogOfferProcessingError(this ILogger logger, string offerId, string error)
            => logger.LogError("[FunPay] ❌ Błąd podczas przetwarzania oferty {OfferId}: {Error}", offerId, error);

        // ===== RATE LIMITING & RETRY =====
        public static void LogRateLimitHit(this ILogger logger, string offerId, int attempt, int maxAttempts)
            => logger.LogWarning("[FunPay] ⏳ Otrzymano 429 Too Many Requests dla oferty {OfferId}. Próba {Attempt}/{MaxAttempts}. Czekam 10 sekund...", 
                offerId, attempt, maxAttempts);

        public static void LogOfferFetchError(this ILogger logger, string offerId, string error, int attempt, int maxAttempts)
            => logger.LogWarning("[FunPay] ⚠️ Błąd podczas próby pobrania szczegółów oferty {OfferId}: {Error}. Próba {Attempt}/{MaxAttempts}. Czekam 10 sekund...", 
                offerId, error, attempt, maxAttempts);

        // ===== SUCCESS OPERATIONS =====
        public static void LogOfferDetailsSuccess(this ILogger logger, string offerId)
            => logger.LogDebug("[FunPay] ✅ Pomyślnie pobrano szczegóły oferty {OfferId}", offerId);

        public static void LogGameNavigationSuccess(this ILogger logger, string gameName)
            => logger.LogDebug("[FunPay] ✅ Pomyślnie przeszedł do gry {GameName}", gameName);

        // ===== PERFORMANCE METRICS =====
        public static void LogOfferFetchDuration(this ILogger logger, string offerId, double durationMs)
            => logger.LogDebug("[FunPay] ⏱️ Pobrano szczegóły oferty {OfferId} w {DurationMs}ms", offerId, durationMs);

        public static void LogBatchProcessingStart(this ILogger logger, int offerCount)
            => logger.LogInformation("[FunPay] 🚀 Rozpoczynam przetwarzanie {OfferCount} ofert", offerCount);

        public static void LogBatchProcessingEnd(this ILogger logger, int successCount, int totalCount)
            => logger.LogInformation("[FunPay] 📊 Zakończono przetwarzanie: {SuccessCount}/{TotalCount} ofert pomyślnie", successCount, totalCount);
    }
}
