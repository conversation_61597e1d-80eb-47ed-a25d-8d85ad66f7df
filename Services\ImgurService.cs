#nullable disable
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;

namespace ImgurUploader
{
    public class UploadResult
    {
        public bool Success { get; set; }
        public string? OriginalUrl { get; set; }
        public string? ImgurUrl { get; set; }
        public string? ErrorMessage { get; set; }
    }

    public class ImgurService : IDisposable
    {
        private readonly HttpClient _httpClient;
        private string _accessToken;
        private const string ImgurApiUrl = "https://api.imgur.com/3/image";
        private const int DelayBetweenUploads = 5000;
        private const int MaxRetries = 5;
        private const int MaxRetryDelay = 30000;
        private static readonly SemaphoreSlim _uploadSemaphore = new SemaphoreSlim(1, 1);
        private static DateTime _lastUploadTime = DateTime.MinValue;
        private static int _remainingCredits = 1250;
        private static DateTime _nextReset = DateTime.UtcNow.AddHours(1);

        public ImgurService(string clientId)
        {
            _httpClient = new HttpClient
            {
                Timeout = TimeSpan.FromMinutes(5) // 5 minut timeout dla całego requestu
            };
        }

        public void SetAccessToken(string accessToken)
        {
            if (string.IsNullOrEmpty(accessToken))
            {
                throw new ArgumentException("Access token nie może być pusty - wymagana autoryzacja OAuth");
            }

            _accessToken = accessToken;
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {accessToken}");
            Console.WriteLine($"[Imgur Debug] Ustawiono token OAuth: {accessToken.Substring(0, 10)}...");
        }

        private async Task CheckRateLimits(HttpResponseMessage response)
        {
            // Sprawdzamy limity z nagłówków odpowiedzi
            if (response.Headers.TryGetValues("X-RateLimit-UserRemaining", out var userRemaining))
            {
                _remainingCredits = int.Parse(userRemaining.First());
                Console.WriteLine($"[Imgur] Pozostało kredytów użytkownika: {_remainingCredits}");
            }

            if (response.Headers.TryGetValues("X-RateLimit-ClientRemaining", out var clientRemaining))
            {
                var clientCredits = int.Parse(clientRemaining.First());
                Console.WriteLine($"[Imgur] Pozostało kredytów klienta: {clientCredits}");
                _remainingCredits = Math.Min(_remainingCredits, clientCredits);
            }

            if (response.Headers.TryGetValues("X-RateLimit-UserReset", out var userReset))
            {
                var resetTime = int.Parse(userReset.First());
                _nextReset = DateTimeOffset.FromUnixTimeSeconds(resetTime).DateTime;
                Console.WriteLine($"[Imgur] Reset limitów nastąpi o: {_nextReset:HH:mm:ss}");
            }

            // Jeśli zostało mało kredytów, zwiększamy opóźnienie
            if (_remainingCredits < 100)
            {
                var timeToWait = 10000; // 10 sekund
                Console.WriteLine($"[Imgur] Mało kredytów ({_remainingCredits}), czekam {timeToWait/1000} sekund");
                await Task.Delay(timeToWait);
            }

            // Jeśli prawie wyczerpaliśmy limit, czekamy do resetu
            if (_remainingCredits < 20)
            {
                var timeToWait = (_nextReset - DateTime.UtcNow).TotalMilliseconds;
                if (timeToWait > 0)
                {
                    Console.WriteLine($"[Imgur] Krytycznie mało kredytów ({_remainingCredits}), czekam {timeToWait/1000:F1} sekund do resetu");
                    await Task.Delay((int)timeToWait);
                }
            }
        }

        private async Task EnforceRateLimit()
        {
            await _uploadSemaphore.WaitAsync();
            try
            {
                var timeSinceLastUpload = DateTime.Now - _lastUploadTime;
                if (timeSinceLastUpload.TotalMilliseconds < DelayBetweenUploads)
                {
                    var delayTime = DelayBetweenUploads - (int)timeSinceLastUpload.TotalMilliseconds;
                    Console.WriteLine($"[Imgur Debug] Rate limit: Waiting {delayTime}ms before next upload");
                    await Task.Delay(delayTime);
                }
                _lastUploadTime = DateTime.Now;

                // Sprawdź czy mamy wystarczająco kredytów
                if (_remainingCredits < 10) // Upload kosztuje 10 kredytów
                {
                    var timeToWait = (_nextReset - DateTime.UtcNow).TotalMilliseconds;
                    if (timeToWait > 0)
                    {
                        Console.WriteLine($"[Imgur] Brak kredytów, czekam {timeToWait/1000:F1} sekund na reset");
                        await Task.Delay((int)timeToWait);
                    }
                }
            }
            finally
            {
                _uploadSemaphore.Release();
            }
        }

        public async Task<UploadResult> UploadImageFromUrlAsync(string imageUrl)
        {
            var result = new UploadResult
            {
                OriginalUrl = imageUrl,
                Success = false
            };

            var startTime = DateTime.Now;
            const int maxRetries = 5;
            const int baseDelayMs = 60000; // 1 minuta

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    using var cts = new CancellationTokenSource(TimeSpan.FromMinutes(5));
                    await EnforceRateLimit();
                    
                    Console.WriteLine($"[Imgur Debug] Próba {attempt}/{maxRetries} - Upload zdjęcia z URL: {imageUrl}");
                    Console.WriteLine($"[Imgur Debug] Pozostało kredytów: {_remainingCredits}, Reset o: {_nextReset:HH:mm:ss}");
                    
                    var content = new MultipartFormDataContent();
                    content.Add(new StringContent(imageUrl), "image");

                    var response = await _httpClient.PostAsync(ImgurApiUrl, content, cts.Token);
                    await CheckRateLimits(response);
                    
                    var jsonResponse = await response.Content.ReadAsStringAsync(cts.Token);
                    Console.WriteLine($"[Imgur Debug] Status odpowiedzi: {response.StatusCode}");

                    if (response.StatusCode == System.Net.HttpStatusCode.TooManyRequests)
                    {
                        var resetTime = _nextReset - DateTime.UtcNow;
                        Console.WriteLine($"[Imgur Debug] Otrzymano kod 429 (Too Many Requests). Reset limitów za: {resetTime.TotalMinutes:F1} minut");
                        result.ErrorMessage = "429";
                        return result;
                    }
                    
                    using var document = JsonDocument.Parse(jsonResponse);
                    var root = document.RootElement;

                    if (response.IsSuccessStatusCode && root.GetProperty("success").GetBoolean())
                    {
                        result.Success = true;
                        result.ImgurUrl = root.GetProperty("data").GetProperty("link").GetString();
                        var elapsed = DateTime.Now - startTime;
                        Console.WriteLine($"[Imgur Debug] Pomyślnie zauploadowano do: {result.ImgurUrl} (zajęło {elapsed.TotalSeconds:F1}s)");
                        return result;
                    }
                    else if (!response.IsSuccessStatusCode)
                    {
                        result.ErrorMessage = $"HTTP {response.StatusCode}: {jsonResponse}";
                        if (attempt < maxRetries)
                        {
                            using var retrycts = new CancellationTokenSource();
                            await Task.Delay(baseDelayMs * attempt, retrycts.Token);
                            continue;
                        }
                    }
                    else
                    {
                        if (root.TryGetProperty("data", out var data) && 
                            data.TryGetProperty("error", out var errorProperty))
                        {
                            result.ErrorMessage = errorProperty.GetString();
                        }
                        else
                        {
                            result.ErrorMessage = $"Upload nie powiódł się: {jsonResponse}";
                        }
                        Console.WriteLine($"[Imgur Debug] Błąd uploadu: {result.ErrorMessage}");
                    }
                }
                catch (Exception ex)
                {
                    result.ErrorMessage = ex.Message;
                    Console.WriteLine($"[Imgur Debug] Wyjątek podczas uploadu: {ex.Message}");
                    if (ex.InnerException != null)
                    {
                        Console.WriteLine($"[Imgur Debug] Wewnętrzny wyjątek: {ex.InnerException.Message}");
                    }
                    
                    if (attempt < maxRetries)
                    {
                        using var retrycts = new CancellationTokenSource();
                        await Task.Delay(baseDelayMs * attempt, retrycts.Token);
                        continue;
                    }
                }
            }

            var totalTime = DateTime.Now - startTime;
            Console.WriteLine($"[Imgur Debug] Nie udało się zauploadować po {totalTime.TotalSeconds:F1}s i {maxRetries} próbach");
            return result;
        }

        public void Dispose()
        {
            _httpClient.Dispose();
        }
    }
}
