{"TotalOffers": 552, "AcceptedOffers": 66, "RejectedOffers": 486, "RejectionReasons": {"Cena ponizej minimum (1,13 < 15)": 4, "Ocena ponizej minimum (0 < 5)": 34, "Cena ponizej minimum (0,21 < 15)": 2, "Cena powyzej maximum (191,79 > 99)": 1, "Cena powyzej maximum (324,56 > 99)": 1, "Cena powyzej maximum (1129,44 > 99)": 1, "Cena powyzej maximum (194,73 > 99)": 5, "Cena ponizej minimum (0,28 < 15)": 1, "Cena ponizej minimum (0,10 < 15)": 2, "Cena ponizej minimum (3,25 < 15)": 2, "Duplikat oferty": 36, "Cena ponizej minimum (1,27 < 15)": 1, "Cena ponizej minimum (0,26 < 15)": 4, "Cena ponizej minimum (0,06 < 15)": 1, "Cena ponizej minimum (0,47 < 15)": 3, "Cena ponizej minimum (1,12 < 15)": 1, "Cena ponizej minimum (1,31 < 15)": 1, "Cena ponizej minimum (1,44 < 15)": 1, "Cena powyzej maximum (259,64 > 99)": 4, "Cena ponizej minimum (8,44 < 15)": 4, "Cena powyzej maximum (389,46 > 99)": 6, "Cena powyzej maximum (1428,02 > 99)": 1, "Cena ponizej minimum (5,32 < 15)": 12, "Cena ponizej minimum (1,04 < 15)": 6, "Cena ponizej minimum (2,60 < 15)": 2, "Cena ponizej minimum (1,08 < 15)": 1, "Cena ponizej minimum (1,15 < 15)": 1, "Cena ponizej minimum (0,13 < 15)": 1, "Cena ponizej minimum (0,29 < 15)": 1, "Cena ponizej minimum (0,45 < 15)": 3, "Cena ponizej minimum (12,98 < 15)": 8, "Cena ponizej minimum (0,97 < 15)": 1, "Cena ponizej minimum (10,65 < 15)": 10, "Cena ponizej minimum (0,56 < 15)": 15, "Cena ponizej minimum (0,22 < 15)": 1, "Cena powyzej maximum (189,11 > 99)": 1, "Cena powyzej maximum (129,82 > 99)": 10, "Cena ponizej minimum (14,92 < 15)": 9, "Cena ponizej minimum (4,51 < 15)": 1, "Cena ponizej minimum (4,54 < 15)": 4, "Cena ponizej minimum (0,20 < 15)": 1, "Cena ponizej minimum (0,19 < 15)": 4, "Cena ponizej minimum (0,18 < 15)": 1, "Cena ponizej minimum (0,17 < 15)": 1, "Cena ponizej minimum (0,16 < 15)": 1, "Cena ponizej minimum (0,15 < 15)": 1, "Cena ponizej minimum (0,14 < 15)": 1, "Cena ponizej minimum (0,12 < 15)": 1, "Cena ponizej minimum (0,11 < 15)": 2, "Cena ponizej minimum (11,29 < 15)": 1, "Cena powyzej maximum (135,27 > 99)": 1, "Cena powyzej maximum (112,72 > 99)": 1, "Cena powyzej maximum (584,19 > 99)": 2, "Cena ponizej minimum (1,03 < 15)": 1, "Cena ponizej minimum (0,33 < 15)": 1, "Cena powyzej maximum (104,81 > 99)": 1, "Cena powyzej maximum (103,91 > 99)": 1, "Cena powyzej maximum (104,51 > 99)": 1, "Cena powyzej maximum (224,54 > 99)": 1, "Cena powyzej maximum (363,33 > 99)": 1, "Cena powyzej maximum (102,71 > 99)": 1, "Cena powyzej maximum (964,08 > 99)": 1, "Cena powyzej maximum (102,42 > 99)": 2, "Cena powyzej maximum (105,11 > 99)": 2, "Cena powyzej maximum (101,78 > 99)": 1, "Cena powyzej maximum (100,85 > 99)": 1, "Cena powyzej maximum (102,69 > 99)": 1, "Cena powyzej maximum (101,54 > 99)": 1, "Cena ponizej minimum (10,84 < 15)": 1, "Cena ponizej minimum (5,45 < 15)": 1, "Cena powyzej maximum (116,84 > 99)": 2, "Cena ponizej minimum (1,30 < 15)": 17, "Cena powyzej maximum (181,75 > 99)": 4, "Ocena ponizej minimum (4 < 5)": 24, "Cena ponizej minimum (14,42 < 15)": 1, "Cena ponizej minimum (12,97 < 15)": 2, "Cena ponizej minimum (3,89 < 15)": 5, "Cena ponizej minimum (10,39 < 15)": 4, "Cena ponizej minimum (1,62 < 15)": 3, "Cena powyzej maximum (1168,38 > 99)": 1, "Cena ponizej minimum (11,68 < 15)": 2, "Cena ponizej minimum (11,03 < 15)": 2, "Cena ponizej minimum (9,74 < 15)": 2, "Cena ponizej minimum (13,84 < 15)": 1, "Cena ponizej minimum (0,91 < 15)": 2, "Cena ponizej minimum (8,66 < 15)": 3, "Cena powyzej maximum (207,71 > 99)": 4, "Cena powyzej maximum (112,81 > 99)": 1, "Cena ponizej minimum (0,65 < 15)": 3, "Cena powyzej maximum (123,33 > 99)": 1, "Cena ponizej minimum (7,14 < 15)": 1, "Cena ponizej minimum (5,84 < 15)": 3, "Cena ponizej minimum (7,99 < 15)": 1, "Cena ponizej minimum (4,26 < 15)": 1, "Cena powyzej maximum (133,09 > 99)": 1, "Cena ponizej minimum (6,49 < 15)": 5, "Cena ponizej minimum (7,79 < 15)": 2, "Cena powyzej maximum (201,22 > 99)": 1, "Cena ponizej minimum (1,84 < 15)": 1, "Cena powyzej maximum (161,50 > 99)": 1, "Cena powyzej maximum (103,86 > 99)": 3, "Cena ponizej minimum (0,46 < 15)": 1, "Cena powyzej maximum (168,77 > 99)": 4, "Cena ponizej minimum (1,97 < 15)": 1, "Cena ponizej minimum (2,46 < 15)": 1, "Cena powyzej maximum (233,68 > 99)": 4, "Cena ponizej minimum (9,59 < 15)": 1, "Cena ponizej minimum (12,33 < 15)": 2, "Cena ponizej minimum (2,86 < 15)": 1, "Cena ponizej minimum (1,69 < 15)": 1, "Cena ponizej minimum (4,67 < 15)": 1, "Cena ponizej minimum (2,47 < 15)": 1, "Cena powyzej maximum (1298,20 > 99)": 3, "Cena powyzej maximum (338,45 > 99)": 1, "Cena powyzej maximum (480,33 > 99)": 1, "Cena powyzej maximum (319,42 > 99)": 3, "Cena powyzej maximum (649,10 > 99)": 2, "Cena ponizej minimum (9,82 < 15)": 1, "Cena powyzej maximum (205,12 > 99)": 1, "Cena powyzej maximum (585,61 > 99)": 1, "Cena ponizej minimum (0,32 < 15)": 2, "Cena powyzej maximum (298,59 > 99)": 1, "Cena powyzej maximum (155,90 > 99)": 1, "Cena powyzej maximum (350,51 > 99)": 2, "Cena ponizej minimum (1,95 < 15)": 1, "Cena powyzej maximum (272,62 > 99)": 1, "Cena powyzej maximum (155,78 > 99)": 5, "Cena ponizej minimum (5,19 < 15)": 3, "Cena powyzej maximum (148,00 > 99)": 2, "Zawiera zabronioną frazę: your choice": 3, "Cena powyzej maximum (111,65 > 99)": 1, "Cena ponizej minimum (10,09 < 15)": 1, "Cena ponizej minimum (8,65 < 15)": 1, "Cena powyzej maximum (214,76 > 99)": 1, "Cena powyzej maximum (324,55 > 99)": 3, "Cena ponizej minimum (3,86 < 15)": 1, "Cena ponizej minimum (0,08 < 15)": 1, "Ocena ponizej minimum (1 < 5)": 1, "Cena powyzej maximum (162,28 > 99)": 1, "Cena powyzej maximum (142,80 > 99)": 4, "Cena powyzej maximum (165,44 > 99)": 1, "Cena powyzej maximum (368,27 > 99)": 1, "Cena ponizej minimum (14,93 < 15)": 1, "Cena ponizej minimum (9,09 < 15)": 4, "Cena powyzej maximum (714,01 > 99)": 1, "Cena ponizej minimum (4,91 < 15)": 1, "Cena ponizej minimum (8,59 < 15)": 1, "Cena ponizej minimum (6,14 < 15)": 1, "Cena powyzej maximum (211,88 > 99)": 1, "Cena powyzej maximum (254,47 > 99)": 1, "Cena powyzej maximum (339,65 > 99)": 1, "Cena powyzej maximum (392,89 > 99)": 1, "Cena powyzej maximum (228,92 > 99)": 1, "Cena powyzej maximum (424,83 > 99)": 1, "Cena powyzej maximum (190,59 > 99)": 1, "Cena powyzej maximum (275,77 > 99)": 1, "Cena powyzej maximum (137,35 > 99)": 1, "Cena powyzej maximum (126,70 > 99)": 1, "Cena powyzej maximum (175,68 > 99)": 2, "Cena powyzej maximum (318,36 > 99)": 1, "Cena powyzej maximum (532,37 > 99)": 1, "Cena powyzej maximum (286,42 > 99)": 1, "Cena powyzej maximum (165,03 > 99)": 1, "Cena powyzej maximum (109,05 > 99)": 1, "Cena powyzej maximum (244,89 > 99)": 1, "Cena powyzej maximum (372,66 > 99)": 1, "Cena powyzej maximum (519,28 > 99)": 5, "Cena powyzej maximum (336,23 > 99)": 1, "Cena ponizej minimum (0,99 < 15)": 1, "Cena powyzej maximum (311,57 > 99)": 1, "Cena powyzej maximum (102,56 > 99)": 1, "Cena powyzej maximum (843,83 > 99)": 1, "Cena powyzej maximum (532,36 > 99)": 1, "Cena powyzej maximum (291,87 > 99)": 1, "Cena powyzej maximum (613,78 > 99)": 1, "Cena powyzej maximum (173,55 > 99)": 1, "Cena ponizej minimum (0,09 < 15)": 1, "Cena powyzej maximum (597,17 > 99)": 1, "Cena powyzej maximum (390,64 > 99)": 1, "Cena ponizej minimum (4,28 < 15)": 1, "Cena powyzej maximum (428,41 > 99)": 2, "Cena powyzej maximum (149,06 > 99)": 1, "Cena ponizej minimum (5,52 < 15)": 1, "Cena powyzej maximum (168,75 > 99)": 1, "Cena powyzej maximum (558,23 > 99)": 1, "Cena powyzej maximum (165,72 > 99)": 1, "Cena ponizej minimum (0,78 < 15)": 1, "Cena powyzej maximum (111,80 > 99)": 1, "Cena powyzej maximum (778,92 > 99)": 2, "Cena ponizej minimum (0,02 < 15)": 2, "Cena ponizej minimum (0,01 < 15)": 1, "Cena powyzej maximum (191,65 > 99)": 1, "Cena powyzej maximum (1233,29 > 99)": 1, "Ocena ponizej minimum (3 < 5)": 1, "Cena powyzej maximum (688,07 > 99)": 1, "Cena powyzej maximum (1038,56 > 99)": 2, "Cena ponizej minimum (1,70 < 15)": 1, "Cena powyzej maximum (106,47 > 99)": 1, "Cena ponizej minimum (2,21 < 15)": 1, "Cena powyzej maximum (249,06 > 99)": 1, "Cena powyzej maximum (533,89 > 99)": 1, "Cena powyzej maximum (537,29 > 99)": 1, "Cena powyzej maximum (908,74 > 99)": 1}, "GenerationTime": "2025-06-13T12:06:23.6305783+02:00"}