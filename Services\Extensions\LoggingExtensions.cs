using Microsoft.Extensions.Logging;

namespace ShopBot.Services.Extensions
{
    /// <summary>
    /// Extension methods dla unified logging w całej aplikacji
    /// Zastępuje Console.WriteLine z structured logging
    /// </summary>
    public static class LoggingExtensions
    {
        // Parser Service Logging
        public static void LogOfferParsing(this ILogger logger, string offerId, string action)
            => logger.LogInformation("[Parser] {Action} oferty {OfferId}", action, offerId);

        public static void LogParameterFound(this ILogger logger, string key, string value)
            => logger.LogDebug("[Parser] Znaleziono parametr: {Key} = {Value}", key, value);

        public static void LogParameterMissing(this ILogger logger, string parameter)
            => logger.LogWarning("[Parser] ⚠️ Nie znaleziono parametru: {Parameter}", parameter);

        public static void LogPriceFound(this ILogger logger, string priceType, string price)
            => logger.LogInformation("[Parser] 💰 Znaleziono cenę {PriceType}: {Price}", priceType, price);

        public static void LogPriceError(this ILogger logger, string priceType, string error)
            => logger.LogWarning("[Parser] ⚠️ Błąd podczas parsowania ceny {PriceType}: {Error}", priceType, error);

        public static void LogPaymentOptions(this ILogger logger, int count)
            => logger.LogInformation("[Parser] 💳 Znaleziono {Count} opcji płatności", count);

        public static void LogPaymentOption(this ILogger logger, string optionText, string dataContent)
            => logger.LogDebug("[Parser] 🔍 Opcja płatności: {OptionText} | Data: {DataContent}", optionText, dataContent);

        public static void LogFinalPrice(this ILogger logger, string price)
            => logger.LogInformation("[Parser] 🎯 Finalna cena: {Price}", price);

        public static void LogOfferCreated(this ILogger logger, string offerId)
            => logger.LogInformation("[Parser] ✅ Utworzono OfferDetails z ID: {OfferId}", offerId);

        public static void LogServerMapping(this ILogger logger, string originalServer, string mappedServer)
            => logger.LogDebug("[Parser] 🌍 Mapowanie serwera: {Original} → {Mapped}", originalServer, mappedServer);

        public static void LogDefaultServer(this ILogger logger, string defaultServer)
            => logger.LogInformation("[Parser] 🌍 Nie znaleziono serwera, ustawiam domyślnie: {DefaultServer}", defaultServer);

        public static void LogRatingFound(this ILogger logger, string rating)
            => logger.LogInformation("[Parser] ✅ Znaleziono Rating konta FC 25: {Rating}", rating);

        public static void LogFC25Details(this ILogger logger, string squadPrice, string coinsBalance, string webApp, string rating)
        {
            logger.LogInformation("[Parser] FC 25 Details:");
            logger.LogInformation("  Squad Price: {SquadPrice}", squadPrice);
            logger.LogInformation("  Coins Balance: {CoinsBalance}", coinsBalance);
            logger.LogInformation("  Web App: {WebApp}", webApp);
            logger.LogInformation("  Rating FC 25: {Rating}", rating);
        }

        // Image Upload Service Logging
        public static void LogImageUploadStart(this ILogger logger, int count, bool useDropbox)
            => logger.LogInformation("[Image Upload] 🚀 Rozpoczynam PARALLEL upload {Count} obrazów (Dropbox: {UseDropbox})", count, useDropbox);

        public static void LogImageUploadProgress(this ILogger logger, string imageUrl)
            => logger.LogInformation("[Image Upload] ⬆️ Rozpoczynam upload: {ImageUrl}", imageUrl);

        public static void LogImageUploadSuccess(this ILogger logger, string imageUrl)
            => logger.LogInformation("[Image Upload] ✅ Upload pomyślny: {ImageUrl}", imageUrl);

        public static void LogImageUploadFailure(this ILogger logger, string imageUrl, string error)
            => logger.LogError("[Image Upload] ❌ Upload nieudany: {ImageUrl} - {Error}", imageUrl, error);

        public static void LogImageUploadSummary(this ILogger logger, int successful, int total)
            => logger.LogInformation("[Image Upload] 📊 Upload zakończony: {Successful}/{Total} pomyślnych", successful, total);

        // Dropbox Service Logging
        public static void LogDropboxDownload(this ILogger logger, string imageUrl)
            => logger.LogInformation("[Dropbox] ⬇️ Pobieram obraz z URL: {ImageUrl}", imageUrl);

        public static void LogDropboxDownloadComplete(this ILogger logger, int sizeKB, double timeSeconds)
            => logger.LogInformation("[Dropbox] ⬇️ Pobrano {SizeKB}KB w {TimeSeconds:F1}s", sizeKB, timeSeconds);

        public static void LogDropboxUploadSuccess(this ILogger logger, double timeSeconds, string directUrl)
            => logger.LogInformation("[Dropbox] ✅ Upload pomyślny w {TimeSeconds:F1}s: {DirectUrl}", timeSeconds, directUrl);

        public static void LogDropboxUploadError(this ILogger logger, string error)
            => logger.LogError("[Dropbox] ❌ Błąd uploadu: {Error}", error);

        // Price Calculator Logging
        public static void LogPriceCalculationStart(this ILogger logger, string gameName, decimal originalPrice)
            => logger.LogInformation("[PriceCalculator] 🎯 Rozpoczynam kalkulację dla gry: {GameName}, cena wejściowa: ${OriginalPrice:F2}", gameName, originalPrice);

        public static void LogPriceMargin(this ILogger logger, decimal originalPrice, string marginInfo, decimal calculatedPrice)
            => logger.LogInformation("[PriceCalculator] 📊 ${OriginalPrice:F2} → marża {MarginInfo} → ${CalculatedPrice:F2}", originalPrice, marginInfo, calculatedPrice);

        public static void LogPriceLimits(this ILogger logger, string gameName, decimal min, decimal max)
            => logger.LogInformation("[PriceCalculator] 🎮 Limity dla {GameName}: ${Min} - ${Max}", gameName, min, max);

        public static void LogPriceAdjustment(this ILogger logger, decimal originalPrice, decimal adjustedPrice, string reason)
            => logger.LogInformation("[PriceCalculator] ⚖️ Cena {OriginalPrice:F2} → {AdjustedPrice:F2} ({Reason})", originalPrice, adjustedPrice, reason);

        public static void LogFinalCalculatedPrice(this ILogger logger, decimal finalPrice)
            => logger.LogInformation("[PriceCalculator] 🎯 FINALNA CENA: ${FinalPrice:F2}", finalPrice);

        // General Error Logging
        public static void LogUnexpectedError(this ILogger logger, string operation, string error)
            => logger.LogError("[Error] 💥 Nieoczekiwany błąd podczas {Operation}: {Error}", operation, error);

        public static void LogOperationStart(this ILogger logger, string operation)
            => logger.LogInformation("[Operation] 🚀 Rozpoczynam: {Operation}", operation);

        public static void LogOperationComplete(this ILogger logger, string operation, double timeSeconds)
            => logger.LogInformation("[Operation] ✅ Zakończono: {Operation} w {TimeSeconds:F1}s", operation, timeSeconds);
    }
}
