{"TotalOffers": 2001, "AcceptedOffers": 458, "RejectedOffers": 1543, "RejectionReasons": {"Cena powyzej maximum (262,24 > 100)": 2, "Cena powyzej maximum (324,55 > 100)": 20, "Ocena ponizej minimum (0 < 5)": 319, "Duplikat oferty": 6, "Cena powyzej maximum (486,83 > 100)": 2, "Cena powyzej maximum (264,83 > 100)": 1, "Ocena ponizej minimum (4 < 5)": 29, "Cena powyzej maximum (194,73 > 100)": 24, "Cena powyzej maximum (454,37 > 100)": 8, "Cena powyzej maximum (116,84 > 100)": 28, "Cena ponizej minimum (0,28 < 10)": 2, "Cena powyzej maximum (389,46 > 100)": 23, "Cena powyzej maximum (259,64 > 100)": 24, "Cena powyzej maximum (160,98 > 100)": 2, "Oferta typu wynajem": 117, "Cena powyzej maximum (110,35 > 100)": 9, "Cena powyzej maximum (642,61 > 100)": 1, "Cena powyzej maximum (982,05 > 100)": 2, "Cena ponizej minimum (0,50 < 10)": 12, "Cena ponizej minimum (0,09 < 10)": 2, "Cena powyzej maximum (620,79 > 100)": 1, "Cena powyzej maximum (299,31 > 100)": 1, "Cena powyzej maximum (168,77 > 100)": 15, "Cena powyzej maximum (233,68 > 100)": 12, "Cena ponizej minimum (0,45 < 10)": 8, "Cena ponizej minimum (0,32 < 10)": 4, "Cena ponizej minimum (0,19 < 10)": 2, "Cena ponizej minimum (0,04 < 10)": 3, "Cena ponizej minimum (9,09 < 10)": 12, "Cena powyzej maximum (181,75 > 100)": 14, "Cena ponizej minimum (5,84 < 10)": 10, "Cena ponizej minimum (6,49 < 10)": 22, "Cena ponizej minimum (7,14 < 10)": 6, "Cena powyzej maximum (307,71 > 100)": 1, "Cena powyzej maximum (324,75 > 100)": 1, "Cena powyzej maximum (649,10 > 100)": 9, "Cena ponizej minimum (1,30 < 10)": 6, "Cena ponizej minimum (1,10 < 10)": 2, "Cena powyzej maximum (245,51 > 100)": 2, "Cena powyzej maximum (256,35 > 100)": 1, "Cena ponizej minimum (8,96 < 10)": 1, "Cena powyzej maximum (246,66 > 100)": 4, "Cena powyzej maximum (129,82 > 100)": 30, "Cena powyzej maximum (155,78 > 100)": 23, "Cena ponizej minimum (0,38 < 10)": 10, "Cena powyzej maximum (106,47 > 100)": 5, "Cena powyzej maximum (102,56 > 100)": 3, "Cena powyzej maximum (279,11 > 100)": 3, "Cena powyzej maximum (459,98 > 100)": 1, "Cena powyzej maximum (694,54 > 100)": 1, "Cena powyzej maximum (328,45 > 100)": 2, "Cena powyzej maximum (830,85 > 100)": 1, "Cena powyzej maximum (397,25 > 100)": 1, "Cena powyzej maximum (107,75 > 100)": 1, "Cena powyzej maximum (106,45 > 100)": 3, "Cena powyzej maximum (636,12 > 100)": 1, "Cena powyzej maximum (171,36 > 100)": 1, "Cena powyzej maximum (1518,90 > 100)": 1, "Cena powyzej maximum (142,80 > 100)": 18, "Cena powyzej maximum (198,62 > 100)": 1, "Cena powyzej maximum (2856,04 > 100)": 1, "Cena powyzej maximum (402,44 > 100)": 1, "Cena powyzej maximum (1921,34 > 100)": 1, "Cena powyzej maximum (103,86 > 100)": 17, "Cena powyzej maximum (191,65 > 100)": 2, "Cena ponizej minimum (5,19 < 10)": 23, "Cena ponizej minimum (5,71 < 10)": 4, "Cena ponizej minimum (6,17 < 10)": 1, "Cena ponizej minimum (5,32 < 10)": 1, "Cena ponizej minimum (1,11 < 10)": 2, "Cena ponizej minimum (0,91 < 10)": 5, "Cena powyzej maximum (428,41 > 100)": 2, "Cena ponizej minimum (2,03 < 10)": 1, "Cena powyzej maximum (584,19 > 100)": 11, "Cena powyzej maximum (1168,38 > 100)": 3, "Cena ponizej minimum (3,89 < 10)": 9, "Cena powyzej maximum (147,31 > 100)": 2, "Zawiera zabronioną frazę: with any": 1, "Cena ponizej minimum (9,07 < 10)": 1, "Cena ponizej minimum (4,36 < 10)": 1, "Zawiera zabronioną frazę: your choice": 14, "Cena ponizej minimum (2,60 < 10)": 6, "Cena ponizej minimum (4,93 < 10)": 4, "Cena ponizej minimum (6,23 < 10)": 4, "Cena ponizej minimum (5,06 < 10)": 2, "Cena ponizej minimum (6,56 < 10)": 1, "Cena ponizej minimum (5,97 < 10)": 1, "Cena ponizej minimum (6,75 < 10)": 3, "Cena ponizej minimum (4,28 < 10)": 2, "Cena ponizej minimum (6,65 < 10)": 2, "Cena ponizej minimum (3,33 < 10)": 1, "Cena ponizej minimum (0,55 < 10)": 3, "Cena ponizej minimum (0,67 < 10)": 2, "Cena ponizej minimum (0,65 < 10)": 19, "Cena ponizej minimum (1,32 < 10)": 7, "Cena powyzej maximum (162,28 > 100)": 4, "Cena powyzej maximum (175,26 > 100)": 1, "Cena powyzej maximum (148,00 > 100)": 1, "Cena powyzej maximum (493,32 > 100)": 1, "Cena powyzej maximum (344,02 > 100)": 1, "Cena powyzej maximum (415,42 > 100)": 3, "Cena powyzej maximum (662,08 > 100)": 1, "Cena powyzej maximum (778,92 > 100)": 6, "Cena powyzej maximum (597,17 > 100)": 1, "Cena powyzej maximum (2700,26 > 100)": 1, "Cena ponizej minimum (2,34 < 10)": 3, "Cena ponizej minimum (4,54 < 10)": 9, "Cena ponizej minimum (1,04 < 10)": 7, "Cena powyzej maximum (337,53 > 100)": 1, "Cena ponizej minimum (0,39 < 10)": 16, "Cena ponizej minimum (0,06 < 10)": 1, "Ocena ponizej minimum (3 < 5)": 15, "Cena ponizej minimum (0,98 < 10)": 1, "Cena powyzej maximum (218,51 > 100)": 1, "Cena powyzej maximum (135,03 > 100)": 1, "Cena powyzej maximum (174,31 > 100)": 1, "Cena powyzej maximum (101,27 > 100)": 1, "Cena ponizej minimum (7,79 < 10)": 8, "Cena powyzej maximum (220,69 > 100)": 5, "Cena powyzej maximum (1428,02 > 100)": 1, "Cena ponizej minimum (7,21 < 10)": 2, "Cena powyzej maximum (119,25 > 100)": 1, "Cena powyzej maximum (163,97 > 100)": 1, "Cena powyzej maximum (203,37 > 100)": 1, "Cena powyzej maximum (215,08 > 100)": 1, "Cena powyzej maximum (172,49 > 100)": 1, "Cena powyzej maximum (220,40 > 100)": 1, "Cena powyzej maximum (226,79 > 100)": 1, "Cena powyzej maximum (362,01 > 100)": 1, "Cena powyzej maximum (157,58 > 100)": 1, "Cena powyzej maximum (138,42 > 100)": 1, "Cena powyzej maximum (214,01 > 100)": 1, "Cena powyzej maximum (175,68 > 100)": 1, "Cena powyzej maximum (141,61 > 100)": 1, "Cena powyzej maximum (319,42 > 100)": 2, "Cena powyzej maximum (143,74 > 100)": 1, "Cena powyzej maximum (519,28 > 100)": 10, "Cena ponizej minimum (5,17 < 10)": 1, "Cena ponizej minimum (4,49 < 10)": 1, "Cena ponizej minimum (4,20 < 10)": 1, "Cena ponizej minimum (8,53 < 10)": 1, "Cena powyzej maximum (980,14 > 100)": 1, "Cena ponizej minimum (1,66 < 10)": 18, "Cena powyzej maximum (144,24 > 100)": 1, "Cena powyzej maximum (434,90 > 100)": 1, "Cena ponizej minimum (1,08 < 10)": 1, "Cena ponizej minimum (1,18 < 10)": 1, "Cena ponizej minimum (0,80 < 10)": 1, "Cena ponizej minimum (1,13 < 10)": 1, "Cena ponizej minimum (2,08 < 10)": 4, "Cena ponizej minimum (0,79 < 10)": 1, "Cena ponizej minimum (1,49 < 10)": 1, "Cena ponizej minimum (1,48 < 10)": 1, "Cena ponizej minimum (1,23 < 10)": 4, "Cena ponizej minimum (1,19 < 10)": 1, "Cena ponizej minimum (4,61 < 10)": 1, "Cena ponizej minimum (2,27 < 10)": 1, "Cena ponizej minimum (3,86 < 10)": 1, "Cena ponizej minimum (1,47 < 10)": 1, "Cena ponizej minimum (1,21 < 10)": 1, "Cena ponizej minimum (1,17 < 10)": 1, "Cena ponizej minimum (0,33 < 10)": 1, "Cena ponizej minimum (0,52 < 10)": 18, "Cena powyzej maximum (110,86 > 100)": 6, "Cena powyzej maximum (116,40 > 100)": 4, "Zawiera zabronioną frazę: accounts with": 1, "Cena powyzej maximum (332,57 > 100)": 2, "Cena ponizej minimum (9,42 < 10)": 1, "Cena ponizej minimum (7,76 < 10)": 4, "Cena ponizej minimum (9,98 < 10)": 4, "Cena ponizej minimum (8,31 < 10)": 4, "Cena ponizej minimum (8,87 < 10)": 1, "Cena ponizej minimum (1,62 < 10)": 2, "Cena powyzej maximum (1108,55 > 100)": 1, "Cena powyzej maximum (121,94 > 100)": 2, "Cena powyzej maximum (283,01 > 100)": 1, "Cena powyzej maximum (127,48 > 100)": 4, "Cena powyzej maximum (144,11 > 100)": 2, "Cena powyzej maximum (177,37 > 100)": 2, "Cena powyzej maximum (155,20 > 100)": 1, "Cena powyzej maximum (1038,56 > 100)": 5, "Cena ponizej minimum (0,78 < 10)": 3, "Cena powyzej maximum (775,97 > 100)": 1, "Cena powyzej maximum (108,64 > 100)": 1, "Cena powyzej maximum (442,31 > 100)": 1, "Cena ponizej minimum (9,74 < 10)": 4, "Cena ponizej minimum (8,44 < 10)": 6, "Cena powyzej maximum (390,20 > 100)": 1, "Cena powyzej maximum (1817,48 > 100)": 3, "Cena powyzej maximum (2336,76 > 100)": 1, "Cena powyzej maximum (272,62 > 100)": 5, "Cena ponizej minimum (2,22 < 10)": 3, "Cena ponizej minimum (6,39 < 10)": 2, "Cena powyzej maximum (188,24 > 100)": 3, "Cena powyzej maximum (285,60 > 100)": 4, "Cena powyzej maximum (664,91 > 100)": 1, "Cena powyzej maximum (1363,11 > 100)": 1, "Cena powyzej maximum (1557,84 > 100)": 2, "Cena powyzej maximum (610,15 > 100)": 1, "Cena powyzej maximum (1298,20 > 100)": 3, "Cena powyzej maximum (363,50 > 100)": 2, "Cena powyzej maximum (369,99 > 100)": 1, "Cena powyzej maximum (635,15 > 100)": 1, "Cena powyzej maximum (601,72 > 100)": 2, "Cena powyzej maximum (521,49 > 100)": 2, "Cena powyzej maximum (641,83 > 100)": 1, "Cena powyzej maximum (528,17 > 100)": 1, "Cena powyzej maximum (1190,06 > 100)": 1, "Cena powyzej maximum (488,06 > 100)": 1, "Cena powyzej maximum (762,17 > 100)": 1, "Cena powyzej maximum (381,09 > 100)": 1, "Cena powyzej maximum (300,86 > 100)": 1, "Cena powyzej maximum (788,92 > 100)": 1, "Cena powyzej maximum (655,20 > 100)": 1, "Cena powyzej maximum (735,43 > 100)": 2, "Cena powyzej maximum (147,09 > 100)": 1, "Cena powyzej maximum (394,46 > 100)": 1, "Cena ponizej minimum (1,80 < 10)": 1, "Cena ponizej minimum (5,54 < 10)": 1, "Cena powyzej maximum (350,51 > 100)": 3, "Cena powyzej maximum (227,19 > 100)": 1, "Cena powyzej maximum (107,10 > 100)": 1, "Cena powyzej maximum (3245,50 > 100)": 1, "Cena powyzej maximum (3894,61 > 100)": 2, "Cena powyzej maximum (201,22 > 100)": 3, "Cena powyzej maximum (311,57 > 100)": 1, "Cena powyzej maximum (387,99 > 100)": 1, "Cena powyzej maximum (443,42 > 100)": 1, "Cena powyzej maximum (306,89 > 100)": 3, "Cena powyzej maximum (266,19 > 100)": 1, "Cena powyzej maximum (159,71 > 100)": 2, "Cena ponizej minimum (6,44 < 10)": 1, "Cena ponizej minimum (3,51 < 10)": 2, "Cena ponizej minimum (6,10 < 10)": 1, "Cena ponizej minimum (4,80 < 10)": 1, "Cena ponizej minimum (5,58 < 10)": 1, "Cena ponizej minimum (7,40 < 10)": 1, "Cena ponizej minimum (7,53 < 10)": 2, "Cena ponizej minimum (4,43 < 10)": 1, "Cena ponizej minimum (1,95 < 10)": 6, "Cena ponizej minimum (1,06 < 10)": 1, "Cena ponizej minimum (5,22 < 10)": 1, "Cena ponizej minimum (3,88 < 10)": 1, "Cena powyzej maximum (908,74 > 100)": 5, "Cena powyzej maximum (108,18 > 100)": 1, "Cena powyzej maximum (399,08 > 100)": 1, "Cena powyzej maximum (188,10 > 100)": 1, "Cena powyzej maximum (133,02 > 100)": 1, "Cena ponizej minimum (7,39 < 10)": 1, "Cena ponizej minimum (4,79 < 10)": 1, "Cena powyzej maximum (122,03 > 100)": 2, "Cena powyzej maximum (146,70 > 100)": 1, "Cena powyzej maximum (145,40 > 100)": 1, "Cena powyzej maximum (110,87 > 100)": 1, "Cena powyzej maximum (138,91 > 100)": 1, "Cena ponizej minimum (2,21 < 10)": 1, "Cena powyzej maximum (1298,19 > 100)": 2, "Cena powyzej maximum (101,15 > 100)": 1, "Cena powyzej maximum (245,36 > 100)": 2, "Cena powyzej maximum (424,73 > 100)": 1, "Cena ponizej minimum (0,30 < 10)": 1, "Cena powyzej maximum (149,29 > 100)": 2, "Cena powyzej maximum (104,25 > 100)": 1, "Cena powyzej maximum (1155,40 > 100)": 1, "Cena powyzej maximum (207,71 > 100)": 5, "Cena powyzej maximum (372,66 > 100)": 1, "Cena ponizej minimum (3,25 < 10)": 4, "Cena powyzej maximum (109,05 > 100)": 1, "Cena powyzej maximum (266,13 > 100)": 1, "Cena powyzej maximum (159,58 > 100)": 1, "Cena powyzej maximum (214,20 > 100)": 2, "Cena powyzej maximum (1687,66 > 100)": 1, "Cena powyzej maximum (272,61 > 100)": 1, "Cena powyzej maximum (545,24 > 100)": 2, "Cena powyzej maximum (123,33 > 100)": 1, "Cena ponizej minimum (7,27 < 10)": 1, "Cena powyzej maximum (234,24 > 100)": 1, "Cena ponizej minimum (9,82 < 10)": 1, "Cena powyzej maximum (133,03 > 100)": 1, "Cena ponizej minimum (6,43 < 10)": 1, "Cena powyzej maximum (110,85 > 100)": 1, "Cena ponizej minimum (0,01 < 10)": 1, "Cena powyzej maximum (1947,30 > 100)": 2, "Cena ponizej minimum (4,02 < 10)": 1, "Cena powyzej maximum (1020,39 > 100)": 1, "Cena powyzej maximum (179,15 > 100)": 1, "Cena powyzej maximum (127,22 > 100)": 1, "Cena powyzej maximum (101,26 > 100)": 3, "Cena ponizej minimum (1,56 < 10)": 2, "Cena ponizej minimum (1,43 < 10)": 1, "Cena ponizej minimum (2,86 < 10)": 2, "Cena ponizej minimum (3,63 < 10)": 1, "Cena ponizej minimum (2,47 < 10)": 2, "Cena ponizej minimum (3,12 < 10)": 1, "Cena ponizej minimum (2,99 < 10)": 2, "Cena ponizej minimum (1,82 < 10)": 2, "Cena powyzej maximum (843,83 > 100)": 2, "Cena powyzej maximum (254,41 > 100)": 1, "Zawiera zabronioną frazę: to choose from": 1, "Cena ponizej minimum (0,49 < 10)": 1, "Cena powyzej maximum (107,54 > 100)": 1, "Cena powyzej maximum (714,01 > 100)": 2, "Cena ponizej minimum (0,64 < 10)": 1, "Cena ponizej minimum (0,13 < 10)": 3, "Cena powyzej maximum (480,33 > 100)": 1, "Cena powyzej maximum (3437,18 > 100)": 1, "Cena ponizej minimum (3,56 < 10)": 1, "Cena powyzej maximum (299,30 > 100)": 1, "Cena powyzej maximum (155,19 > 100)": 1, "Cena powyzej maximum (133,01 > 100)": 3, "Cena powyzej maximum (221,60 > 100)": 1, "Cena powyzej maximum (246,35 > 100)": 1, "Cena powyzej maximum (208,69 > 100)": 1, "Cena powyzej maximum (168,75 > 100)": 2, "Cena powyzej maximum (2466,58 > 100)": 3, "Cena powyzej maximum (1595,83 > 100)": 1, "Cena powyzej maximum (104,67 > 100)": 1, "Cena powyzej maximum (303,78 > 100)": 1, "Cena powyzej maximum (116,83 > 100)": 1, "Cena powyzej maximum (343,72 > 100)": 1, "Cena ponizej minimum (0,43 < 10)": 1, "Cena powyzej maximum (745,32 > 100)": 1, "Cena ponizej minimum (0,71 < 10)": 3, "Cena powyzej maximum (292,10 > 100)": 1, "Cena powyzej maximum (136,31 > 100)": 2, "Cena powyzej maximum (147,88 > 100)": 1, "Cena powyzej maximum (113,35 > 100)": 1, "Cena powyzej maximum (146,68 > 100)": 1, "Cena ponizej minimum (9,23 < 10)": 1, "Cena powyzej maximum (1597,11 > 100)": 1, "Cena powyzej maximum (196,41 > 100)": 1, "Cena powyzej maximum (834,74 > 100)": 1, "Cena ponizej minimum (0,56 < 10)": 2, "Cena ponizej minimum (0,57 < 10)": 2, "Cena ponizej minimum (0,60 < 10)": 1, "Cena ponizej minimum (0,68 < 10)": 2, "Cena ponizej minimum (0,58 < 10)": 2, "Cena ponizej minimum (0,66 < 10)": 1, "Cena ponizej minimum (0,69 < 10)": 1, "Cena powyzej maximum (804,89 > 100)": 1, "Cena powyzej maximum (122,76 > 100)": 2, "Cena ponizej minimum (0,26 < 10)": 1, "Cena powyzej maximum (589,38 > 100)": 1, "Cena ponizej minimum (0,27 < 10)": 1, "Ocena ponizej minimum (1 < 5)": 1, "Cena ponizej minimum (0,84 < 10)": 1, "Cena powyzej maximum (452,27 > 100)": 1, "Cena ponizej minimum (5,52 < 10)": 1, "Cena ponizej minimum (9,61 < 10)": 1, "Cena powyzej maximum (117,12 > 100)": 2, "Cena powyzej maximum (395,95 > 100)": 1, "Cena powyzej maximum (181,62 > 100)": 1, "Cena powyzej maximum (536,22 > 100)": 1, "Cena ponizej minimum (3,05 < 10)": 1, "Cena powyzej maximum (425,90 > 100)": 1, "Cena powyzej maximum (490,42 > 100)": 1, "Cena powyzej maximum (151,89 > 100)": 1, "Cena powyzej maximum (127,77 > 100)": 1, "Cena powyzej maximum (223,58 > 100)": 1, "Cena ponizej minimum (5,53 < 10)": 1, "Cena ponizej minimum (8,86 < 10)": 1, "Cena ponizej minimum (0,54 < 10)": 2, "Cena ponizej minimum (0,17 < 10)": 2, "Cena powyzej maximum (115,38 > 100)": 1, "Cena ponizej minimum (7,50 < 10)": 1}, "GenerationTime": "2025-06-13T12:06:30.6042614+02:00"}