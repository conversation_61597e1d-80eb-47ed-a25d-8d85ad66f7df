using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Playwright;
using ShopBot.Config;
using ShopBot.Exceptions;
using ShopBot.Models;
using ShopBot.Services.Interfaces;

namespace ShopBot.Services
{
    /// <summary>
    /// Serwis do pobierania szczegółów ofert z FunPay z użyciem dedykowanej zakładki
    /// Implementuje kontrolowane opóźnienia i queue system
    /// </summary>
    public class FunPayDetailsService : IFunPayDetailsService
    {
        private readonly IBrowserContext _context;
        private readonly ParserService _parserService;
        private readonly ILogger<FunPayDetailsService> _logger;
        private readonly AppSettings _appSettings;
        
        private IPage? _dedicatedPage;
        private Timer? _processingTimer;
        private readonly ConcurrentQueue<OfferDetailsRequest> _requestQueue = new();
        private readonly CancellationTokenSource _cancellationTokenSource = new();
        
        // Statystyki
        private int _totalRequests = 0;
        private int _successfulRequests = 0;
        private int _failedRequests = 0;
        private int _timeoutRequests = 0;
        private DateTime _lastRequestAt = DateTime.MinValue;
        private readonly ConcurrentQueue<TimeSpan> _processingTimes = new();
        
        public bool IsRunning { get; private set; }
        public int PendingRequestsCount => _requestQueue.Count;
        
        public FunPayDetailsService(
            IBrowserContext context,
            ParserService parserService,
            ILogger<FunPayDetailsService> logger)
        {
            _context = context;
            _parserService = parserService;
            _logger = logger;
            _appSettings = AppSettings.Instance;
        }
        
        public async Task StartAsync()
        {
            if (IsRunning)
            {
                _logger.LogWarning("[FunPayDetails] Serwis już jest uruchomiony");
                return;
            }
            
            try
            {
                _logger.LogInformation("[FunPayDetails] 🚀 Uruchamianie serwisu...");
                
                // Utwórz dedykowaną zakładkę dla FunPay
                _dedicatedPage = await _context.NewPageAsync();
                _logger.LogInformation("[FunPayDetails] ✅ Utworzono dedykowaną zakładkę FunPay");
                
                // Uruchom timer przetwarzania (co 0.5s domyślnie)
                var interval = TimeSpan.FromMilliseconds(_appSettings.Timeouts.FunPayDetailsIntervalMs);
                _processingTimer = new Timer(ProcessQueue, null, TimeSpan.Zero, interval);
                
                IsRunning = true;
                _logger.LogInformation("[FunPayDetails] ✅ Serwis uruchomiony (interwał: {Interval}ms)", 
                    _appSettings.Timeouts.FunPayDetailsIntervalMs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[FunPayDetails] ❌ Błąd podczas uruchamiania serwisu");
                throw;
            }
        }
        
        public async Task StopAsync()
        {
            if (!IsRunning)
            {
                return;
            }
            
            _logger.LogInformation("[FunPayDetails] 🛑 Zatrzymywanie serwisu...");
            
            IsRunning = false;
            _cancellationTokenSource.Cancel();
            
            // Zatrzymaj timer
            _processingTimer?.Dispose();
            _processingTimer = null;
            
            // Zamknij dedykowaną zakładkę
            if (_dedicatedPage != null)
            {
                await _dedicatedPage.CloseAsync();
                _dedicatedPage = null;
            }
            
            // Anuluj wszystkie oczekujące żądania
            while (_requestQueue.TryDequeue(out var request))
            {
                request.CompletionSource.TrySetCanceled();
            }
            
            _logger.LogInformation("[FunPayDetails] ✅ Serwis zatrzymany");
        }
        
        public async Task<OfferDetails> GetOfferDetailsAsync(string offerId, string offerUrl, string gameName)
        {
            if (!IsRunning)
            {
                throw new InvalidOperationException("FunPayDetailsService nie jest uruchomiony. Wywołaj StartAsync() najpierw.");
            }
            
            var request = new OfferDetailsRequest
            {
                OfferId = offerId,
                OfferUrl = offerUrl,
                GameName = gameName
            };
            
            _requestQueue.Enqueue(request);
            Interlocked.Increment(ref _totalRequests);
            
            _logger.LogDebug("[FunPayDetails] 📝 Dodano żądanie do kolejki: {OfferId} (kolejka: {QueueSize})", 
                offerId, _requestQueue.Count);
            
            return await request.CompletionSource.Task;
        }
        
        private async void ProcessQueue(object? state)
        {
            if (!IsRunning || _cancellationTokenSource.Token.IsCancellationRequested)
            {
                return;
            }
            
            if (!_requestQueue.TryDequeue(out var request))
            {
                return; // Brak żądań w kolejce
            }
            
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                _logger.LogDebug("[FunPayDetails] 🔄 Przetwarzam żądanie: {OfferId}", request.OfferId);
                
                var offerDetails = await ProcessSingleRequest(request);
                
                stopwatch.Stop();
                _processingTimes.Enqueue(stopwatch.Elapsed);
                
                // Ogranicz historię czasów do ostatnich 100 żądań
                while (_processingTimes.Count > 100)
                {
                    _processingTimes.TryDequeue(out _);
                }
                
                request.CompletionSource.TrySetResult(offerDetails);
                Interlocked.Increment(ref _successfulRequests);
                _lastRequestAt = DateTime.UtcNow;
                
                _logger.LogDebug("[FunPayDetails] ✅ Pobrano szczegóły: {OfferId} ({Duration}ms)", 
                    request.OfferId, stopwatch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                
                request.AttemptCount++;
                
                if (request.CanRetry)
                {
                    _logger.LogWarning("[FunPayDetails] ⚠️ Błąd żądania {OfferId}, ponawiam próbę ({Attempt}/{MaxAttempts}): {Error}", 
                        request.OfferId, request.AttemptCount, request.MaxAttempts, ex.Message);
                    
                    // Dodaj z powrotem do kolejki
                    _requestQueue.Enqueue(request);
                }
                else
                {
                    _logger.LogError(ex, "[FunPayDetails] ❌ Błąd żądania {OfferId} po {Attempts} próbach", 
                        request.OfferId, request.AttemptCount);
                    
                    if (request.IsExpired)
                    {
                        Interlocked.Increment(ref _timeoutRequests);
                        request.CompletionSource.TrySetException(new TimeoutException($"Żądanie {request.OfferId} wygasło"));
                    }
                    else
                    {
                        Interlocked.Increment(ref _failedRequests);
                        request.CompletionSource.TrySetException(ex);
                    }
                }
            }
        }
        
        private async Task<OfferDetails> ProcessSingleRequest(OfferDetailsRequest request)
        {
            if (_dedicatedPage == null)
            {
                throw new InvalidOperationException("Dedykowana zakładka nie jest dostępna");
            }

            var id = _parserService.ExtractIdFromUrl(request.OfferUrl);
            var processUrl = $"https://funpay.com/en/lots/offer?id={id}";

            await _dedicatedPage.GotoAsync(processUrl);
            string content = await _dedicatedPage.ContentAsync();

            // Sprawdź czy oferta została usunięta
            if (content.Contains("Offer not found") ||
                content.Contains("The offer has expired, been deleted, or never existed"))
            {
                _logger.LogWarning("[FunPayDetails] 🗑️ Oferta {OfferId} została usunięta z FunPay", request.OfferId);
                throw new OfferNotFoundException($"Oferta {request.OfferId} została usunięta z FunPay");
            }

            if (content.Contains("429 Too Many Requests"))
            {
                throw new Exception("Rate limit hit - 429 Too Many Requests");
            }

            await _dedicatedPage.WaitForSelectorAsync("div.param-item", new() { Timeout = 10000 });

            var parameters = new Dictionary<string, string>
            {
                { "Id", id },
                { "OriginalUrl", request.OfferUrl }
            };

            return _parserService.ParseOfferHtml(content, parameters);
        }
        
        public FunPayDetailsServiceStats GetStats()
        {
            var processingTimesArray = _processingTimes.ToArray();
            var averageTime = processingTimesArray.Length > 0 
                ? TimeSpan.FromMilliseconds(processingTimesArray.Average(t => t.TotalMilliseconds))
                : TimeSpan.Zero;
            
            return new FunPayDetailsServiceStats
            {
                TotalRequests = _totalRequests,
                SuccessfulRequests = _successfulRequests,
                FailedRequests = _failedRequests,
                TimeoutRequests = _timeoutRequests,
                PendingRequests = _requestQueue.Count,
                LastRequestAt = _lastRequestAt,
                AverageProcessingTime = averageTime
            };
        }
        
        public async ValueTask DisposeAsync()
        {
            await StopAsync();
            _cancellationTokenSource.Dispose();
        }
    }
}
