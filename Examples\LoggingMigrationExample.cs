using Microsoft.Extensions.Logging;
using System;

namespace ShopBot.Examples
{
    /// <summary>
    /// Przykład migracji z Console.WriteLine na ILogger
    /// </summary>
    public class LoggingMigrationExample
    {
        private readonly ILogger<LoggingMigrationExample> _logger;

        public LoggingMigrationExample(ILogger<LoggingMigrationExample> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// PRZED - stary sposób z Console.WriteLine
        /// </summary>
        public void OldLoggingExample()
        {
            // ❌ STARY SPOSÓB - nie rób tak!
            Console.WriteLine("[Service] Rozpoczynam operację...");
            Console.WriteLine($"[Service] Przetwarzam element: item123");
            Console.WriteLine($"[Service] ⚠️ Ostrzeżenie: coś może być nie tak");
            Console.WriteLine($"[Service] ❌ Błąd: operacja nieudana - szczegóły błędu");
            Console.WriteLine("[Service] ✅ Operacja zakończona pomyślnie");
        }

        /// <summary>
        /// PO - nowy sposób z ILogger
        /// </summary>
        public void NewLoggingExample()
        {
            // ✅ NOWY SPOSÓB - strukturalne logowanie
            _logger.LogInformation("🚀 Rozpoczynam operację");
            _logger.LogDebug("Przetwarzam element: {ItemId}", "item123");
            _logger.LogWarning("⚠️ Ostrzeżenie: {WarningMessage}", "coś może być nie tak");
            _logger.LogError("❌ Błąd: {ErrorMessage} - {ErrorDetails}", "operacja nieudana", "szczegóły błędu");
            _logger.LogInformation("✅ Operacja zakończona pomyślnie");
        }

        /// <summary>
        /// Przykład logowania z wyjątkami
        /// </summary>
        public void ExceptionLoggingExample()
        {
            try
            {
                // Symulacja błędu
                throw new InvalidOperationException("Coś poszło nie tak");
            }
            catch (Exception ex)
            {
                // ❌ STARY SPOSÓB
                // Console.WriteLine($"Błąd: {ex.Message}");
                // Console.WriteLine($"Stack trace: {ex.StackTrace}");

                // ✅ NOWY SPOSÓB - automatyczne logowanie stack trace
                _logger.LogError(ex, "Wystąpił błąd podczas operacji: {Operation}", "przykładowa operacja");
            }
        }

        /// <summary>
        /// Przykład logowania wydajności
        /// </summary>
        public void PerformanceLoggingExample()
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            // Symulacja operacji
            System.Threading.Thread.Sleep(100);
            
            stopwatch.Stop();

            // ❌ STARY SPOSÓB
            // Console.WriteLine($"Operacja zajęła: {stopwatch.ElapsedMilliseconds}ms");

            // ✅ NOWY SPOSÓB - strukturalne logowanie metryk
            _logger.LogDebug("📊 Operacja {OperationName} zajęła {ElapsedMs}ms", 
                           "przykładowa operacja", stopwatch.ElapsedMilliseconds);
        }

        /// <summary>
        /// Przykład logowania z różnymi poziomami
        /// </summary>
        public void LogLevelsExample()
        {
            // Różne poziomy logowania
            _logger.LogTrace("🔍 Bardzo szczegółowe informacje debugowe");
            _logger.LogDebug("🐛 Informacje debugowe");
            _logger.LogInformation("ℹ️ Normalne informacje o działaniu aplikacji");
            _logger.LogWarning("⚠️ Ostrzeżenie - coś może wymagać uwagi");
            _logger.LogError("❌ Błąd - coś poszło nie tak");
            _logger.LogCritical("💥 Krytyczny błąd - aplikacja może przestać działać");
        }

        /// <summary>
        /// Przykład logowania operacji biznesowych
        /// </summary>
        public void BusinessOperationLoggingExample(string offerId, string gameName)
        {
            // ❌ STARY SPOSÓB
            // Console.WriteLine($"[G2G] Rozpoczynam publikację oferty {offerId} dla gry {gameName}");

            // ✅ NOWY SPOSÓB - strukturalne logowanie z kontekstem
            using (_logger.BeginScope("OfferId: {OfferId}, Game: {GameName}", offerId, gameName))
            {
                _logger.LogInformation("🚀 Rozpoczynam publikację oferty");
                _logger.LogDebug("Walidacja danych oferty");
                _logger.LogDebug("Upload obrazów do Dropbox");
                _logger.LogInformation("✅ Oferta opublikowana pomyślnie");
            }
        }
    }

    /// <summary>
    /// Przewodnik migracji Console.WriteLine -> ILogger
    /// </summary>
    public static class LoggingMigrationGuide
    {
        public static void PrintMigrationGuide()
        {
            Console.WriteLine(@"
=== PRZEWODNIK MIGRACJI LOGOWANIA ===

1. ZAMIEŃ Console.WriteLine NA ILogger:
   ❌ Console.WriteLine($""[Service] Operacja: {value}"");
   ✅ _logger.LogInformation(""Operacja: {Value}"", value);

2. UŻYWAJ ODPOWIEDNICH POZIOMÓW:
   - LogTrace: Bardzo szczegółowe debugowanie
   - LogDebug: Informacje debugowe
   - LogInformation: Normalne operacje
   - LogWarning: Potencjalne problemy
   - LogError: Błędy
   - LogCritical: Krytyczne błędy

3. STRUKTURALNE LOGOWANIE:
   ❌ _logger.LogInfo($""User {userId} logged in"");
   ✅ _logger.LogInformation(""User {UserId} logged in"", userId);

4. LOGOWANIE WYJĄTKÓW:
   ❌ _logger.LogError($""Error: {ex.Message}"");
   ✅ _logger.LogError(ex, ""Operation failed: {Operation}"", operationName);

5. UŻYWAJ SCOPES DLA KONTEKSTU:
   using (_logger.BeginScope(""OfferId: {OfferId}"", offerId))
   {
       _logger.LogInformation(""Processing offer"");
   }

6. KORZYŚCI:
   ✅ Automatyczne timestampy
   ✅ Poziomy logowania
   ✅ Strukturalne dane
   ✅ Filtrowanie w konfiguracji
   ✅ Różne outputy (console, file, etc.)
   ✅ Lepsze performance
   ✅ Standardowe formatowanie
");
        }
    }
}
