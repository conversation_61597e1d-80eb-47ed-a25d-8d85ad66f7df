using System;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Playwright;
using ShopBot.Services;
using ShopBot.Exceptions;
using ShopBot.Services.Formatters;
using ShopBot.Services.Extensions;
using ShopBot.Services.Interfaces;
using ShopBot.Models;
using Microsoft.Extensions.Logging;

namespace ShopBot.Tasks
{
    public class AddOfferTask : BaseTask
    {
        private readonly OfferListItem _offer;
        private readonly string _game;
        private readonly IBrowserContext _context;
        private readonly ProcessedOffersTracker _processedOffersTracker;
        private readonly ParserService _parserService;
        private readonly ImageUploadService _imageUploadService;
        private readonly G2GPublisherService _g2gPublisherService;
        private readonly DescriptionFormatterFactory _formatterFactory;
        private readonly G2GOfferManager _offerManager;
        private readonly IFunPayDetailsService _funPayDetailsService;
        private readonly IOfferRepository _offerRepository;
        private readonly ILogger<AddOfferTask> _logger;

        public AddOfferTask(
            OfferListItem offer,
            string game,
            IBrowserContext context,
            ProcessedOffersTracker processedOffersTracker,
            ParserService parserService,
            ImageUploadService imageUploadService,
            DescriptionFormatterFactory formatterFactory,
            IFunPayDetailsService funPayDetailsService,
            IOfferRepository offerRepository,
            ILogger<AddOfferTask> logger,
            G2GPublisherService g2gPublisherService,
            G2GOfferManager offerManager)
        {
            _offer = offer;
            _game = game;
            _context = context;
            _processedOffersTracker = processedOffersTracker;
            _parserService = parserService;
            _imageUploadService = imageUploadService;
            _g2gPublisherService = g2gPublisherService;
            _formatterFactory = formatterFactory;
            _funPayDetailsService = funPayDetailsService;
            _offerRepository = offerRepository;
            _logger = logger;
            _offerManager = offerManager;
        }

        public override async Task ExecuteAsync(CancellationToken cancellationToken)
        {
            IPage page = null;
            try
            {
                // Sprawdź czy możemy dodać ofertę
                if (!await _offerManager.CanAddOffer())
                {
                    _logger.LogOfferLimitReached(_offer.Id);
                    Status = TaskStatus.Cancelled;
                    return;
                }

                _logger.LogOfferAddStart(_offer.Id);
                Status = TaskStatus.Running;

                // Pobierz szczegóły oferty z dedykowanego serwisu (bez tworzenia nowej zakładki)
                var offerDetails = await _funPayDetailsService.GetOfferDetailsAsync(_offer.Id, _offer.Url, _game);

                // Utwórz zakładkę tylko dla G2G (bez opóźnień FunPay)
                page = await _context.NewPageAsync();

                // Oblicz cenę - obsługa różnych walut (USD, USDT, EUR)
                var priceString = offerDetails.ActualOfferPrice;
                decimal originalPrice = 0;

                // Sprawdź USDT
                if (priceString.Contains("USDT"))
                {
                    var usdtMatch = System.Text.RegularExpressions.Regex.Match(priceString, @"([\d.,]+)\s*USDT");
                    if (usdtMatch.Success && decimal.TryParse(usdtMatch.Groups[1].Value, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out originalPrice))
                    {
                        _logger.LogInformation("[AddOffer] 💰 Parsed USDT price: {Price} USDT → {USDPrice} USD", usdtMatch.Groups[1].Value, originalPrice);
                    }
                    else
                    {
                        throw new Exception($"Nie udało się sparsować ceny USDT: '{priceString}'");
                    }
                }
                // Sprawdź USD/EUR/inne waluty
                else
                {
                    var cleanPrice = System.Text.RegularExpressions.Regex.Replace(priceString, @"[^\d.,]", "");
                    if (!decimal.TryParse(cleanPrice, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out originalPrice))
                    {
                        throw new Exception($"Nie udało się sparsować ceny: '{priceString}' (cleaned: '{cleanPrice}')");
                    }
                    _logger.LogInformation("[AddOffer] 💰 Parsed price: {OriginalPrice} → {CleanPrice} USD", priceString, originalPrice);
                }

                // Publikuj na G2G
                try 
                {
                    var publishedOfferId = await _g2gPublisherService.PublishOffer(offerDetails, _game, page);

                    if (string.IsNullOrEmpty(publishedOfferId))
                    {
                        throw new Exception("Otrzymano puste ID opublikowanej oferty");
                    }

                    // Dodaj do trackera
                    G2GOfferTracker.AddOrUpdateOffer(new G2GOfferTracker.TrackedOffer
                    {
                        FunPayId = _offer.Id,
                        G2GOfferId = publishedOfferId,
                        GameName = _game
                    });

                    _processedOffersTracker.AddProcessedOffer(_offer.Id);

                    // Zapisz do bazy danych
                    await SaveOfferToDatabaseAsync(offerDetails, publishedOfferId, originalPrice);

                    // Wymuś odświeżenie licznika po udanym dodaniu
                    await _offerManager.ForceRefreshOfferCount();

                    _logger.LogOfferAddSuccess(_offer.Id, publishedOfferId);

                    Status = TaskStatus.Completed;
                }
                catch (OfferLimitExceededException ex)
                {
                    _logger.LogOfferLimitExceeded(ex.Message);
                    Status = TaskStatus.Failed;
                    ErrorMessage = "Przekroczono limit ofert na G2G";
                    
                    // Wymuś odświeżenie licznika przy błędzie limitu
                    await _offerManager.ForceRefreshOfferCount();
                    
                    // Zapisz informację o przekroczeniu limitu do pliku
                    var limitFile = "g2g_limit_reached.txt";
                    File.WriteAllText(limitFile, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                    
                    throw;
                }
            }
            catch (OfferLimitExceededException)
            {
                throw;
            }
            catch (OfferNotFoundException ex)
            {
                _logger.LogWarning("[AddOffer] 🗑️ Oferta {OfferId} została usunięta z FunPay - pomijam", _offer.Id);
                Status = TaskStatus.Cancelled;
                ErrorMessage = "Oferta została usunięta z FunPay";
                // Nie rzucamy wyjątku - to nie jest błąd, tylko naturalna sytuacja
                return;
            }
            catch (Exception ex)
            {
                Status = TaskStatus.Failed;
                ErrorMessage = ex.Message;
                _logger.LogOfferAddError(_offer.Id, ex.Message);
                throw;
            }
            finally
            {
                if (page != null)
                {
                    await page.CloseAsync();
                }
            }
        }

        private async Task SaveOfferToDatabaseAsync(OfferDetails offerDetails, string g2gOfferId, decimal originalPrice)
        {
            try
            {
                // Sprawdź czy oferta już istnieje
                var existingOffer = await _offerRepository.GetByFunPayIdAsync(_offer.Id);
                if (existingOffer != null)
                {
                    // Aktualizuj istniejącą ofertę
                    existingOffer.G2GOfferId = g2gOfferId;
                    existingOffer.Status = "Published";
                    existingOffer.PublishedAt = DateTime.UtcNow;
                    existingOffer.UpdatedAt = DateTime.UtcNow;

                    await _offerRepository.UpdateAsync(existingOffer);
                    _logger.LogInformation("[AddOffer] 📝 Zaktualizowano ofertę {OfferId} w bazie danych", _offer.Id);
                }
                else
                {
                    // Utwórz nową ofertę
                    var offer = new Offer
                    {
                        FunPayOfferId = _offer.Id,
                        GameName = _game,
                        Title = offerDetails.ShortDescription ?? "No title",
                        Description = offerDetails.DetailedDescription,
                        OriginalPrice = originalPrice,
                        ConvertedPrice = originalPrice, // Będzie zaktualizowane przez pricing service
                        Currency = "USDT", // FunPay default
                        Status = "Published",
                        ProcessedAt = DateTime.UtcNow,
                        PublishedAt = DateTime.UtcNow,
                        G2GOfferId = g2gOfferId,
                        SourceUrl = _offer.Url,
                        ImageCount = offerDetails.Images?.Count ?? 0,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };

                    // Serialize image URLs if available
                    if (offerDetails.Images != null && offerDetails.Images.Any())
                    {
                        offer.ImageUrls = System.Text.Json.JsonSerializer.Serialize(offerDetails.Images);
                    }

                    await _offerRepository.CreateAsync(offer);
                    _logger.LogInformation("[AddOffer] ✅ Zapisano ofertę {OfferId} do bazy danych (G2G: {G2GId})", _offer.Id, g2gOfferId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[AddOffer] ❌ Błąd zapisywania oferty {OfferId} do bazy danych", _offer.Id);
                // Nie rzucamy wyjątku - oferta została opublikowana, więc nie chcemy anulować całego procesu
            }
        }
    }
}