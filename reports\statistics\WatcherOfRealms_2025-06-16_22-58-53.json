{"TotalOffers": 602, "AcceptedOffers": 200, "RejectedOffers": 402, "RejectionReasons": {"Ocena ponizej minimum (0 < 5)": 149, "Cena ponizej minimum (6,54 < 15)": 21, "Cena ponizej minimum (5,89 < 15)": 3, "Cena ponizej minimum (3,93 < 15)": 11, "Cena ponizej minimum (10,47 < 15)": 16, "Cena ponizej minimum (5,23 < 15)": 9, "Cena ponizej minimum (13,09 < 15)": 32, "Cena ponizej minimum (9,16 < 15)": 20, "Cena ponizej minimum (3,27 < 15)": 6, "Cena ponizej minimum (7,20 < 15)": 4, "Cena ponizej minimum (2,84 < 15)": 1, "Cena ponizej minimum (4,55 < 15)": 2, "Cena ponizej minimum (7,85 < 15)": 10, "Cena ponizej minimum (3,41 < 15)": 3, "Cena ponizej minimum (9,81 < 15)": 7, "Cena ponizej minimum (2,27 < 15)": 1, "Cena ponizej minimum (11,78 < 15)": 14, "Cena ponizej minimum (11,12 < 15)": 4, "Cena ponizej minimum (5,58 < 15)": 1, "Cena ponizej minimum (5,69 < 15)": 3, "Cena ponizej minimum (4,58 < 15)": 6, "Cena ponizej minimum (7,69 < 15)": 1, "Cena ponizej minimum (6,82 < 15)": 3, "Cena ponizej minimum (7,96 < 15)": 2, "Cena ponizej minimum (13,65 < 15)": 1, "Zawiera zabronioną frazę (hardcoded): your choice": 3, "Cena ponizej minimum (12,85 < 15)": 2, "Cena ponizej minimum (14,02 < 15)": 2, "Cena ponizej minimum (11,68 < 15)": 2, "Cena ponizej minimum (8,18 < 15)": 1, "Ocena ponizej minimum (4 < 5)": 7, "Cena powyzej maximum (3258,24 > 1000)": 1, "Cena ponizej minimum (5,12 < 15)": 1, "Cena ponizej minimum (1,36 < 15)": 6, "Cena ponizej minimum (1,71 < 15)": 1, "Duplikat oferty": 1, "Cena ponizej minimum (3,66 < 15)": 1, "Cena ponizej minimum (2,62 < 15)": 2, "Cena ponizej minimum (4,32 < 15)": 1, "Ocena ponizej minimum (3 < 5)": 1, "Cena ponizej minimum (6,81 < 15)": 1, "Cena ponizej minimum (11,36 < 15)": 1, "Cena ponizej minimum (1,14 < 15)": 1, "Cena ponizej minimum (1,44 < 15)": 1, "Cena ponizej minimum (0,03 < 15)": 1, "Cena ponizej minimum (13,07 < 15)": 1, "Cena ponizej minimum (10,65 < 15)": 1, "Cena ponizej minimum (7,59 < 15)": 1, "Cena ponizej minimum (6,28 < 15)": 1, "Cena ponizej minimum (2,36 < 15)": 1, "Cena ponizej minimum (14,39 < 15)": 2, "Cena ponizej minimum (1,31 < 15)": 1, "Cena ponizej minimum (0,79 < 15)": 2, "Cena ponizej minimum (8,51 < 15)": 2, "Cena ponizej minimum (5,63 < 15)": 1, "Cena ponizej minimum (2,77 < 15)": 1, "Cena ponizej minimum (1,11 < 15)": 1, "Cena ponizej minimum (5,18 < 15)": 1, "Cena ponizej minimum (6,49 < 15)": 1, "Cena ponizej minimum (9,26 < 15)": 1, "Cena ponizej minimum (4,37 < 15)": 1, "Cena ponizej minimum (5,85 < 15)": 1, "Cena ponizej minimum (7,48 < 15)": 1, "Cena ponizej minimum (6,94 < 15)": 1, "Ocena ponizej minimum (1 < 5)": 1, "Cena ponizej minimum (6,16 < 15)": 1, "Cena ponizej minimum (3,19 < 15)": 4, "Cena ponizej minimum (5,32 < 15)": 1, "Cena powyzej maximum (1570,24 > 1000)": 1, "Cena ponizej minimum (0,01 < 15)": 1, "Ocena ponizej minimum (2 < 5)": 1, "Cena ponizej minimum (1,16 < 15)": 1, "Cena ponizej minimum (0,92 < 15)": 1, "Cena ponizej minimum (3,51 < 15)": 1}, "GenerationTime": "2025-06-16T22:58:53.114111+02:00"}