using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Playwright;
using ShopBot.Services;
using ShopBot.Services.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection;

namespace ShopBot.Tasks
{
    public class G2GOfferVerificationTask : BaseTask
    {
        private readonly IBrowser _browser;
        private readonly IBrowserContext _context;
        private readonly ProcessedOffersTracker _processedOffersTracker;
        private readonly IServiceProvider _serviceProvider;
        private readonly TaskScheduler _taskScheduler;
        private readonly DeleteService _deleteService;

        public G2GOfferVerificationTask(
            IBrowser browser,
            IBrowserContext context,
            ProcessedOffersTracker processedOffersTracker,
            IServiceProvider serviceProvider,
            TaskScheduler taskScheduler)
        {
            _browser = browser;
            _context = context;
            _processedOffersTracker = processedOffersTracker;
            _serviceProvider = serviceProvider;
            _taskScheduler = taskScheduler;
            _deleteService = new DeleteService(browser, context, processedOffersTracker);
        }

        public override async Task ExecuteAsync(CancellationToken cancellationToken)
        {
            try
            {
                Status = TaskStatus.Running;
                Console.WriteLine("\n=== Rozpoczynam pełną weryfikację ofert G2G ===");

                // Używamy DI do pobrania serwisu weryfikacji
                var verificationService = _serviceProvider.GetRequiredService<G2GOfferVerificationService>();

                await verificationService.VerifyOffers();

                Status = TaskStatus.Completed;
                Console.WriteLine("=== Zakończono pełną weryfikację ofert G2G ===\n");
            }
            catch (Exception ex)
            {
                Status = TaskStatus.Failed;
                ErrorMessage = ex.Message;
                Console.WriteLine($"[Verify] ❌ Błąd podczas weryfikacji: {ex.Message}");
                throw;
            }
        }
    }
} 