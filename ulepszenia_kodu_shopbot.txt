# Prompty dla AI dotyczące ulepszeń kodu ShopBot

## Refaktoryzacja dużych klas
- Podziel klasę ParserService na mniejsze klasy zgodnie z zasadą Single Responsibility Pattern. Utwórz <PERSON>y <PERSON>l<PERSON>er, OfferDetailsParser, OfferListParser i TextFormatter.
- Zastosuj wzorzec strategii dla G2GPublisherService, tworząc osobne implementacje IGameProcessingStrategy dla każdej gry.
- Wyodrębnij logikę formatowania opisów z DescriptionFormatter na osobne, specjalizowane formatterzy dla każdej gry.

## Poprawa systemu logowania
- Zamień wszystkie wystąpienia Console.WriteLine na odpowiednie wywołania ILogger z prawidłową kategoryzacją poziomów (Information, Warning, Error, Debug).
- Dodaj ustrukturyzowane logowanie z użyciem template message pattern zamiast konkatenacji stringów.
- Zaimplementuj mechanizm filtrowania logów na podstawie poziomu ważności i kategorii.

## Zastosowanie wstrzykiwania zależności (DI)
- Przebuduj Program.cs, aby używał IHostBuilder i wbudowanego kontenera DI .NET.
- Zarejestruj wszystkie serwisy w kontenerze DI z odpowiednim czasem życia (Singleton, Scoped, Transient).
- Usuń ręczne tworzenie instancji klas i zastąp je wstrzykiwaniem przez konstruktor.

## Zarządzanie zasobami
- Zaimplementuj poprawnie interfejs IAsyncDisposable we wszystkich klasach używających zasobów Playwright.
- Dodaj mechanizm poolingu dla stron przeglądarki, aby uniknąć ciągłego tworzenia i zamykania instancji.
- Zastosuj wzorzec using dla wszystkich zasobów unmanaged.

## Ujednolicona obsługa błędów
- Zaprojektuj hierarchię wyjątków specyficznych dla aplikacji (ShopBotException jako bazowy).
- Zaimplementuj globalne middleware do przechwytywania i logowania wyjątków.
- Ujednolicić strategię obsługi błędów w całej aplikacji, eliminując "ciche" błędy.

## Externalizacja konfiguracji 
- Przenieś wszystkie wartości "hardcoded" do plików konfiguracyjnych (appsettings.json).
- Zaimplementuj system walidacji konfiguracji przy starcie aplikacji.
- Utwórz silnie typowane klasy konfiguracyjne dla każdego komponentu systemu.

## Bezpieczeństwo
- Przenieś dane uwierzytelniające (ClientId, ClientSecret) do User Secrets lub zmiennych środowiskowych.
- Dodaj mechanizm szyfrowania wrażliwych danych przechowywanych lokalnie.
- Zaimplementuj zarządzanie tokenami OAuth2 z bezpiecznym odświeżaniem.

## Concurrent programming
- Dodaj mechanizmy blokad (locks) dla operacji współdzielonych na danych.
- Zaimplementuj wzorzec producent-konsument dla przetwarzania ofert.
- Zastosuj asynchroniczną kolejkę zadań dla przetwarzania ofert w tle.

## Optymalizacja wydajności
- Zaimplementuj mechanizm cache'owania wyników parsowania i filtrowania ofert.
- Dodaj paginację do pobierania dużych list ofert.
- Zoptymalizuj parsowanie HTML przez selektywne ładowanie tylko potrzebnych fragmentów.

## Testowanie
- Utwórz testy jednostkowe dla kluczowych komponentów (parsery, formatterzy, filtry).
- Dodaj testy integracyjne dla przepływów pracy (end-to-end).
- Zaimplementuj mocks dla zewnętrznych zależności (Playwright, API zewnętrzne).

## Możliwości rozszerzania
- Utwórz system wtyczek dla łatwego dodawania obsługi nowych gier.
- Dodaj możliwość definiowania niestandardowych filtrów ofert przez użytkownika.
- Zaimplementuj interfejs API REST dla zdalnego sterowania aplikacją.

## Funkcjonalności antyfraudowe
- Dodaj mechanizm wykrywania podejrzanych ofert na podstawie analizy tekstu.
- Zaimplementuj system scoringu ofert na podstawie ceny, opisu i parametrów.
- Dodaj automatyczną walidację URL i obrazów pod kątem bezpieczeństwa.

## Integracja z innymi platformami
- Dodaj obsługę innych platform sprzedażowych oprócz FunPay i G2G.
- Zaimplementuj możliwość porównywania cen między platformami.
- Dodaj integrację z Discord/Telegram do powiadamiania o znalezionych ofertach.

## Poprawa UI
- Dodaj interfejs webowy dla zarządzania botem.
- Zaimplementuj dashboard z wizualizacją statystyk.
- Dodaj możliwość ręcznego zatwierdzania ofert przed publikacją. 