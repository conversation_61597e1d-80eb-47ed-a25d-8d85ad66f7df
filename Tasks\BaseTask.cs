using System;
using System.Threading;
using System.Threading.Tasks;

namespace ShopBot.Tasks
{
    public abstract class BaseTask
    {
        public string Id { get; } = Guid.NewGuid().ToString();
        public DateTime CreatedAt { get; } = DateTime.UtcNow;
        public TaskStatus Status { get; protected set; } = TaskStatus.Pending;
        public string ErrorMessage { get; protected set; }
        
        public abstract Task ExecuteAsync(CancellationToken cancellationToken);
    }

    public enum TaskStatus
    {
        Pending,
        Running,
        Completed,
        Failed,
        Cancelled
    }
} 