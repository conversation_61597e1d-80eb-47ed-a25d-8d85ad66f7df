var Z={exports:{}};(function(J,te){(function(Y,G){J.exports=G()})(self,function(){return(()=>{var Y={4567:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.AccessibilityManager=void 0;const h=a(9042),f=a(6114),d=a(9924),_=a(3656),g=a(844),v=a(5596),c=a(9631);class e extends g.Disposable{constructor(n,t){super(),this._terminal=n,this._renderService=t,this._liveRegionLineCount=0,this._charsToConsume=[],this._charsToAnnounce="",this._accessibilityTreeRoot=document.createElement("div"),this._accessibilityTreeRoot.classList.add("xterm-accessibility"),this._accessibilityTreeRoot.tabIndex=0,this._rowContainer=document.createElement("div"),this._rowContainer.setAttribute("role","list"),this._rowContainer.classList.add("xterm-accessibility-tree"),this._rowElements=[];for(let r=0;r<this._terminal.rows;r++)this._rowElements[r]=this._createAccessibilityTreeNode(),this._rowContainer.appendChild(this._rowElements[r]);if(this._topBoundaryFocusListener=r=>this._handleBoundaryFocus(r,0),this._bottomBoundaryFocusListener=r=>this._handleBoundaryFocus(r,1),this._rowElements[0].addEventListener("focus",this._topBoundaryFocusListener),this._rowElements[this._rowElements.length-1].addEventListener("focus",this._bottomBoundaryFocusListener),this._refreshRowsDimensions(),this._accessibilityTreeRoot.appendChild(this._rowContainer),this._renderRowsDebouncer=new d.TimeBasedDebouncer(this._renderRows.bind(this)),this._refreshRows(),this._liveRegion=document.createElement("div"),this._liveRegion.classList.add("live-region"),this._liveRegion.setAttribute("aria-live","assertive"),this._accessibilityTreeRoot.appendChild(this._liveRegion),!this._terminal.element)throw new Error("Cannot enable accessibility before Terminal.open");this._terminal.element.insertAdjacentElement("afterbegin",this._accessibilityTreeRoot),this.register(this._renderRowsDebouncer),this.register(this._terminal.onResize(r=>this._handleResize(r.rows))),this.register(this._terminal.onRender(r=>this._refreshRows(r.start,r.end))),this.register(this._terminal.onScroll(()=>this._refreshRows())),this.register(this._terminal.onA11yChar(r=>this._handleChar(r))),this.register(this._terminal.onLineFeed(()=>this._handleChar(`
`))),this.register(this._terminal.onA11yTab(r=>this._handleTab(r))),this.register(this._terminal.onKey(r=>this._handleKey(r.key))),this.register(this._terminal.onBlur(()=>this._clearLiveRegion())),this.register(this._renderService.onDimensionsChange(()=>this._refreshRowsDimensions())),this._screenDprMonitor=new v.ScreenDprMonitor(window),this.register(this._screenDprMonitor),this._screenDprMonitor.setListener(()=>this._refreshRowsDimensions()),this.register((0,_.addDisposableDomListener)(window,"resize",()=>this._refreshRowsDimensions())),this.register((0,g.toDisposable)(()=>{(0,c.removeElementFromParent)(this._accessibilityTreeRoot),this._rowElements.length=0}))}_handleBoundaryFocus(n,t){const r=n.target,l=this._rowElements[t===0?1:this._rowElements.length-2];if(r.getAttribute("aria-posinset")===(t===0?"1":`${this._terminal.buffer.lines.length}`)||n.relatedTarget!==l)return;let u,m;if(t===0?(u=r,m=this._rowElements.pop(),this._rowContainer.removeChild(m)):(u=this._rowElements.shift(),m=r,this._rowContainer.removeChild(u)),u.removeEventListener("focus",this._topBoundaryFocusListener),m.removeEventListener("focus",this._bottomBoundaryFocusListener),t===0){const o=this._createAccessibilityTreeNode();this._rowElements.unshift(o),this._rowContainer.insertAdjacentElement("afterbegin",o)}else{const o=this._createAccessibilityTreeNode();this._rowElements.push(o),this._rowContainer.appendChild(o)}this._rowElements[0].addEventListener("focus",this._topBoundaryFocusListener),this._rowElements[this._rowElements.length-1].addEventListener("focus",this._bottomBoundaryFocusListener),this._terminal.scrollLines(t===0?-1:1),this._rowElements[t===0?1:this._rowElements.length-2].focus(),n.preventDefault(),n.stopImmediatePropagation()}_handleResize(n){this._rowElements[this._rowElements.length-1].removeEventListener("focus",this._bottomBoundaryFocusListener);for(let t=this._rowContainer.children.length;t<this._terminal.rows;t++)this._rowElements[t]=this._createAccessibilityTreeNode(),this._rowContainer.appendChild(this._rowElements[t]);for(;this._rowElements.length>n;)this._rowContainer.removeChild(this._rowElements.pop());this._rowElements[this._rowElements.length-1].addEventListener("focus",this._bottomBoundaryFocusListener),this._refreshRowsDimensions()}_createAccessibilityTreeNode(){const n=document.createElement("div");return n.setAttribute("role","listitem"),n.tabIndex=-1,this._refreshRowDimensions(n),n}_handleTab(n){for(let t=0;t<n;t++)this._handleChar(" ")}_handleChar(n){this._liveRegionLineCount<21&&(this._charsToConsume.length>0?this._charsToConsume.shift()!==n&&(this._charsToAnnounce+=n):this._charsToAnnounce+=n,n===`
`&&(this._liveRegionLineCount++,this._liveRegionLineCount===21&&(this._liveRegion.textContent+=h.tooMuchOutput)),f.isMac&&this._liveRegion.textContent&&this._liveRegion.textContent.length>0&&!this._liveRegion.parentNode&&setTimeout(()=>{this._accessibilityTreeRoot.appendChild(this._liveRegion)},0))}_clearLiveRegion(){this._liveRegion.textContent="",this._liveRegionLineCount=0,f.isMac&&(0,c.removeElementFromParent)(this._liveRegion)}_handleKey(n){this._clearLiveRegion(),/\p{Control}/u.test(n)||this._charsToConsume.push(n)}_refreshRows(n,t){this._renderRowsDebouncer.refresh(n,t,this._terminal.rows)}_renderRows(n,t){const r=this._terminal.buffer,l=r.lines.length.toString();for(let u=n;u<=t;u++){const m=r.translateBufferLineToString(r.ydisp+u,!0),o=(r.ydisp+u+1).toString(),p=this._rowElements[u];p&&(m.length===0?p.innerText=" ":p.textContent=m,p.setAttribute("aria-posinset",o),p.setAttribute("aria-setsize",l))}this._announceCharacters()}_refreshRowsDimensions(){if(this._renderService.dimensions.css.cell.height){this._accessibilityTreeRoot.style.width=`${this._renderService.dimensions.css.canvas.width}px`,this._rowElements.length!==this._terminal.rows&&this._handleResize(this._terminal.rows);for(let n=0;n<this._terminal.rows;n++)this._refreshRowDimensions(this._rowElements[n])}}_refreshRowDimensions(n){n.style.height=`${this._renderService.dimensions.css.cell.height}px`}_announceCharacters(){this._charsToAnnounce.length!==0&&(this._liveRegion.textContent+=this._charsToAnnounce,this._charsToAnnounce="")}}i.AccessibilityManager=e},3614:(T,i)=>{function a(_){return _.replace(/\r?\n/g,"\r")}function h(_,g){return g?"\x1B[200~"+_+"\x1B[201~":_}function f(_,g,v){_=h(_=a(_),v.decPrivateModes.bracketedPasteMode),v.triggerDataEvent(_,!0),g.value=""}function d(_,g,v){const c=v.getBoundingClientRect(),e=_.clientX-c.left-10,s=_.clientY-c.top-10;g.style.width="20px",g.style.height="20px",g.style.left=`${e}px`,g.style.top=`${s}px`,g.style.zIndex="1000",g.focus()}Object.defineProperty(i,"__esModule",{value:!0}),i.rightClickHandler=i.moveTextAreaUnderMouseCursor=i.paste=i.handlePasteEvent=i.copyHandler=i.bracketTextForPaste=i.prepareTextForTerminal=void 0,i.prepareTextForTerminal=a,i.bracketTextForPaste=h,i.copyHandler=function(_,g){_.clipboardData&&_.clipboardData.setData("text/plain",g.selectionText),_.preventDefault()},i.handlePasteEvent=function(_,g,v){_.stopPropagation(),_.clipboardData&&f(_.clipboardData.getData("text/plain"),g,v)},i.paste=f,i.moveTextAreaUnderMouseCursor=d,i.rightClickHandler=function(_,g,v,c,e){d(_,g,v),e&&c.rightClickSelect(_),g.value=c.selectionText,g.select()}},7239:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.ColorContrastCache=void 0;const h=a(1505);i.ColorContrastCache=class{constructor(){this._color=new h.TwoKeyMap,this._css=new h.TwoKeyMap}setCss(f,d,_){this._css.set(f,d,_)}getCss(f,d){return this._css.get(f,d)}setColor(f,d,_){this._color.set(f,d,_)}getColor(f,d){return this._color.get(f,d)}clear(){this._color.clear(),this._css.clear()}}},9631:(T,i)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.removeElementFromParent=void 0,i.removeElementFromParent=function(...a){var h;for(const f of a)(h=f==null?void 0:f.parentElement)===null||h===void 0||h.removeChild(f)}},3656:(T,i)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.addDisposableDomListener=void 0,i.addDisposableDomListener=function(a,h,f,d){a.addEventListener(h,f,d);let _=!1;return{dispose:()=>{_||(_=!0,a.removeEventListener(h,f,d))}}}},6465:function(T,i,a){var h=this&&this.__decorate||function(e,s,n,t){var r,l=arguments.length,u=l<3?s:t===null?t=Object.getOwnPropertyDescriptor(s,n):t;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")u=Reflect.decorate(e,s,n,t);else for(var m=e.length-1;m>=0;m--)(r=e[m])&&(u=(l<3?r(u):l>3?r(s,n,u):r(s,n))||u);return l>3&&u&&Object.defineProperty(s,n,u),u},f=this&&this.__param||function(e,s){return function(n,t){s(n,t,e)}};Object.defineProperty(i,"__esModule",{value:!0}),i.Linkifier2=void 0;const d=a(2585),_=a(8460),g=a(844),v=a(3656);let c=class extends g.Disposable{constructor(e){super(),this._bufferService=e,this._linkProviders=[],this._linkCacheDisposables=[],this._isMouseOut=!0,this._activeLine=-1,this._onShowLinkUnderline=this.register(new _.EventEmitter),this.onShowLinkUnderline=this._onShowLinkUnderline.event,this._onHideLinkUnderline=this.register(new _.EventEmitter),this.onHideLinkUnderline=this._onHideLinkUnderline.event,this.register((0,g.getDisposeArrayDisposable)(this._linkCacheDisposables)),this.register((0,g.toDisposable)(()=>{this._lastMouseEvent=void 0}))}get currentLink(){return this._currentLink}registerLinkProvider(e){return this._linkProviders.push(e),{dispose:()=>{const s=this._linkProviders.indexOf(e);s!==-1&&this._linkProviders.splice(s,1)}}}attachToDom(e,s,n){this._element=e,this._mouseService=s,this._renderService=n,this.register((0,v.addDisposableDomListener)(this._element,"mouseleave",()=>{this._isMouseOut=!0,this._clearCurrentLink()})),this.register((0,v.addDisposableDomListener)(this._element,"mousemove",this._handleMouseMove.bind(this))),this.register((0,v.addDisposableDomListener)(this._element,"mousedown",this._handleMouseDown.bind(this))),this.register((0,v.addDisposableDomListener)(this._element,"mouseup",this._handleMouseUp.bind(this)))}_handleMouseMove(e){if(this._lastMouseEvent=e,!this._element||!this._mouseService)return;const s=this._positionFromMouseEvent(e,this._element,this._mouseService);if(!s)return;this._isMouseOut=!1;const n=e.composedPath();for(let t=0;t<n.length;t++){const r=n[t];if(r.classList.contains("xterm"))break;if(r.classList.contains("xterm-hover"))return}this._lastBufferCell&&s.x===this._lastBufferCell.x&&s.y===this._lastBufferCell.y||(this._handleHover(s),this._lastBufferCell=s)}_handleHover(e){if(this._activeLine!==e.y)return this._clearCurrentLink(),void this._askForLink(e,!1);this._currentLink&&this._linkAtPosition(this._currentLink.link,e)||(this._clearCurrentLink(),this._askForLink(e,!0))}_askForLink(e,s){var n,t;this._activeProviderReplies&&s||((n=this._activeProviderReplies)===null||n===void 0||n.forEach(l=>{l==null||l.forEach(u=>{u.link.dispose&&u.link.dispose()})}),this._activeProviderReplies=new Map,this._activeLine=e.y);let r=!1;for(const[l,u]of this._linkProviders.entries())s?!((t=this._activeProviderReplies)===null||t===void 0)&&t.get(l)&&(r=this._checkLinkProviderResult(l,e,r)):u.provideLinks(e.y,m=>{var o,p;if(this._isMouseOut)return;const C=m==null?void 0:m.map(y=>({link:y}));(o=this._activeProviderReplies)===null||o===void 0||o.set(l,C),r=this._checkLinkProviderResult(l,e,r),((p=this._activeProviderReplies)===null||p===void 0?void 0:p.size)===this._linkProviders.length&&this._removeIntersectingLinks(e.y,this._activeProviderReplies)})}_removeIntersectingLinks(e,s){const n=new Set;for(let t=0;t<s.size;t++){const r=s.get(t);if(r)for(let l=0;l<r.length;l++){const u=r[l],m=u.link.range.start.y<e?0:u.link.range.start.x,o=u.link.range.end.y>e?this._bufferService.cols:u.link.range.end.x;for(let p=m;p<=o;p++){if(n.has(p)){r.splice(l--,1);break}n.add(p)}}}}_checkLinkProviderResult(e,s,n){var t;if(!this._activeProviderReplies)return n;const r=this._activeProviderReplies.get(e);let l=!1;for(let u=0;u<e;u++)this._activeProviderReplies.has(u)&&!this._activeProviderReplies.get(u)||(l=!0);if(!l&&r){const u=r.find(m=>this._linkAtPosition(m.link,s));u&&(n=!0,this._handleNewLink(u))}if(this._activeProviderReplies.size===this._linkProviders.length&&!n)for(let u=0;u<this._activeProviderReplies.size;u++){const m=(t=this._activeProviderReplies.get(u))===null||t===void 0?void 0:t.find(o=>this._linkAtPosition(o.link,s));if(m){n=!0,this._handleNewLink(m);break}}return n}_handleMouseDown(){this._mouseDownLink=this._currentLink}_handleMouseUp(e){if(!this._element||!this._mouseService||!this._currentLink)return;const s=this._positionFromMouseEvent(e,this._element,this._mouseService);s&&this._mouseDownLink===this._currentLink&&this._linkAtPosition(this._currentLink.link,s)&&this._currentLink.link.activate(e,this._currentLink.link.text)}_clearCurrentLink(e,s){this._element&&this._currentLink&&this._lastMouseEvent&&(!e||!s||this._currentLink.link.range.start.y>=e&&this._currentLink.link.range.end.y<=s)&&(this._linkLeave(this._element,this._currentLink.link,this._lastMouseEvent),this._currentLink=void 0,(0,g.disposeArray)(this._linkCacheDisposables))}_handleNewLink(e){if(!this._element||!this._lastMouseEvent||!this._mouseService)return;const s=this._positionFromMouseEvent(this._lastMouseEvent,this._element,this._mouseService);s&&this._linkAtPosition(e.link,s)&&(this._currentLink=e,this._currentLink.state={decorations:{underline:e.link.decorations===void 0||e.link.decorations.underline,pointerCursor:e.link.decorations===void 0||e.link.decorations.pointerCursor},isHovered:!0},this._linkHover(this._element,e.link,this._lastMouseEvent),e.link.decorations={},Object.defineProperties(e.link.decorations,{pointerCursor:{get:()=>{var n,t;return(t=(n=this._currentLink)===null||n===void 0?void 0:n.state)===null||t===void 0?void 0:t.decorations.pointerCursor},set:n=>{var t,r;!((t=this._currentLink)===null||t===void 0)&&t.state&&this._currentLink.state.decorations.pointerCursor!==n&&(this._currentLink.state.decorations.pointerCursor=n,this._currentLink.state.isHovered&&((r=this._element)===null||r===void 0||r.classList.toggle("xterm-cursor-pointer",n)))}},underline:{get:()=>{var n,t;return(t=(n=this._currentLink)===null||n===void 0?void 0:n.state)===null||t===void 0?void 0:t.decorations.underline},set:n=>{var t,r,l;!((t=this._currentLink)===null||t===void 0)&&t.state&&((l=(r=this._currentLink)===null||r===void 0?void 0:r.state)===null||l===void 0?void 0:l.decorations.underline)!==n&&(this._currentLink.state.decorations.underline=n,this._currentLink.state.isHovered&&this._fireUnderlineEvent(e.link,n))}}}),this._renderService&&this._linkCacheDisposables.push(this._renderService.onRenderedViewportChange(n=>{const t=n.start===0?0:n.start+1+this._bufferService.buffer.ydisp,r=this._currentLink?this._lastMouseEvent:void 0;if(this._clearCurrentLink(t,n.end+1+this._bufferService.buffer.ydisp),r&&this._element){const l=this._positionFromMouseEvent(r,this._element,this._mouseService);l&&this._askForLink(l,!1)}})))}_linkHover(e,s,n){var t;!((t=this._currentLink)===null||t===void 0)&&t.state&&(this._currentLink.state.isHovered=!0,this._currentLink.state.decorations.underline&&this._fireUnderlineEvent(s,!0),this._currentLink.state.decorations.pointerCursor&&e.classList.add("xterm-cursor-pointer")),s.hover&&s.hover(n,s.text)}_fireUnderlineEvent(e,s){const n=e.range,t=this._bufferService.buffer.ydisp,r=this._createLinkUnderlineEvent(n.start.x-1,n.start.y-t-1,n.end.x,n.end.y-t-1,void 0);(s?this._onShowLinkUnderline:this._onHideLinkUnderline).fire(r)}_linkLeave(e,s,n){var t;!((t=this._currentLink)===null||t===void 0)&&t.state&&(this._currentLink.state.isHovered=!1,this._currentLink.state.decorations.underline&&this._fireUnderlineEvent(s,!1),this._currentLink.state.decorations.pointerCursor&&e.classList.remove("xterm-cursor-pointer")),s.leave&&s.leave(n,s.text)}_linkAtPosition(e,s){const n=e.range.start.y*this._bufferService.cols+e.range.start.x,t=e.range.end.y*this._bufferService.cols+e.range.end.x,r=s.y*this._bufferService.cols+s.x;return n<=r&&r<=t}_positionFromMouseEvent(e,s,n){const t=n.getCoords(e,s,this._bufferService.cols,this._bufferService.rows);if(t)return{x:t[0],y:t[1]+this._bufferService.buffer.ydisp}}_createLinkUnderlineEvent(e,s,n,t,r){return{x1:e,y1:s,x2:n,y2:t,cols:this._bufferService.cols,fg:r}}};c=h([f(0,d.IBufferService)],c),i.Linkifier2=c},9042:(T,i)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.tooMuchOutput=i.promptLabel=void 0,i.promptLabel="Terminal input",i.tooMuchOutput="Too much output to announce, navigate to rows manually to read"},3730:function(T,i,a){var h=this&&this.__decorate||function(c,e,s,n){var t,r=arguments.length,l=r<3?e:n===null?n=Object.getOwnPropertyDescriptor(e,s):n;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")l=Reflect.decorate(c,e,s,n);else for(var u=c.length-1;u>=0;u--)(t=c[u])&&(l=(r<3?t(l):r>3?t(e,s,l):t(e,s))||l);return r>3&&l&&Object.defineProperty(e,s,l),l},f=this&&this.__param||function(c,e){return function(s,n){e(s,n,c)}};Object.defineProperty(i,"__esModule",{value:!0}),i.OscLinkProvider=void 0;const d=a(511),_=a(2585);let g=class{constructor(c,e,s){this._bufferService=c,this._optionsService=e,this._oscLinkService=s}provideLinks(c,e){var s;const n=this._bufferService.buffer.lines.get(c-1);if(!n)return void e(void 0);const t=[],r=this._optionsService.rawOptions.linkHandler,l=new d.CellData,u=n.getTrimmedLength();let m=-1,o=-1,p=!1;for(let C=0;C<u;C++)if(o!==-1||n.hasContent(C)){if(n.loadCell(C,l),l.hasExtendedAttrs()&&l.extended.urlId){if(o===-1){o=C,m=l.extended.urlId;continue}p=l.extended.urlId!==m}else o!==-1&&(p=!0);if(p||o!==-1&&C===u-1){const y=(s=this._oscLinkService.getLinkData(m))===null||s===void 0?void 0:s.uri;if(y){const w={start:{x:o+1,y:c},end:{x:C+(p||C!==u-1?0:1),y:c}};let D=!1;if(!(r!=null&&r.allowNonHttpProtocols))try{const x=new URL(y);["http:","https:"].includes(x.protocol)||(D=!0)}catch{D=!0}D||t.push({text:y,range:w,activate:(x,I)=>r?r.activate(x,I,w):v(0,I),hover:(x,I)=>{var H;return(H=r==null?void 0:r.hover)===null||H===void 0?void 0:H.call(r,x,I,w)},leave:(x,I)=>{var H;return(H=r==null?void 0:r.leave)===null||H===void 0?void 0:H.call(r,x,I,w)}})}p=!1,l.hasExtendedAttrs()&&l.extended.urlId?(o=C,m=l.extended.urlId):(o=-1,m=-1)}}e(t)}};function v(c,e){if(confirm(`Do you want to navigate to ${e}?

WARNING: This link could potentially be dangerous`)){const s=window.open();if(s){try{s.opener=null}catch{}s.location.href=e}else console.warn("Opening link blocked as opener could not be cleared")}}g=h([f(0,_.IBufferService),f(1,_.IOptionsService),f(2,_.IOscLinkService)],g),i.OscLinkProvider=g},6193:(T,i)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.RenderDebouncer=void 0,i.RenderDebouncer=class{constructor(a,h){this._parentWindow=a,this._renderCallback=h,this._refreshCallbacks=[]}dispose(){this._animationFrame&&(this._parentWindow.cancelAnimationFrame(this._animationFrame),this._animationFrame=void 0)}addRefreshCallback(a){return this._refreshCallbacks.push(a),this._animationFrame||(this._animationFrame=this._parentWindow.requestAnimationFrame(()=>this._innerRefresh())),this._animationFrame}refresh(a,h,f){this._rowCount=f,a=a!==void 0?a:0,h=h!==void 0?h:this._rowCount-1,this._rowStart=this._rowStart!==void 0?Math.min(this._rowStart,a):a,this._rowEnd=this._rowEnd!==void 0?Math.max(this._rowEnd,h):h,this._animationFrame||(this._animationFrame=this._parentWindow.requestAnimationFrame(()=>this._innerRefresh()))}_innerRefresh(){if(this._animationFrame=void 0,this._rowStart===void 0||this._rowEnd===void 0||this._rowCount===void 0)return void this._runRefreshCallbacks();const a=Math.max(this._rowStart,0),h=Math.min(this._rowEnd,this._rowCount-1);this._rowStart=void 0,this._rowEnd=void 0,this._renderCallback(a,h),this._runRefreshCallbacks()}_runRefreshCallbacks(){for(const a of this._refreshCallbacks)a(0);this._refreshCallbacks=[]}}},5596:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.ScreenDprMonitor=void 0;const h=a(844);class f extends h.Disposable{constructor(_){super(),this._parentWindow=_,this._currentDevicePixelRatio=this._parentWindow.devicePixelRatio,this.register((0,h.toDisposable)(()=>{this.clearListener()}))}setListener(_){this._listener&&this.clearListener(),this._listener=_,this._outerListener=()=>{this._listener&&(this._listener(this._parentWindow.devicePixelRatio,this._currentDevicePixelRatio),this._updateDpr())},this._updateDpr()}_updateDpr(){var _;this._outerListener&&((_=this._resolutionMediaMatchList)===null||_===void 0||_.removeListener(this._outerListener),this._currentDevicePixelRatio=this._parentWindow.devicePixelRatio,this._resolutionMediaMatchList=this._parentWindow.matchMedia(`screen and (resolution: ${this._parentWindow.devicePixelRatio}dppx)`),this._resolutionMediaMatchList.addListener(this._outerListener))}clearListener(){this._resolutionMediaMatchList&&this._listener&&this._outerListener&&(this._resolutionMediaMatchList.removeListener(this._outerListener),this._resolutionMediaMatchList=void 0,this._listener=void 0,this._outerListener=void 0)}}i.ScreenDprMonitor=f},3236:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.Terminal=void 0;const h=a(2950),f=a(1680),d=a(3614),_=a(2584),g=a(5435),v=a(9312),c=a(6114),e=a(3656),s=a(9042),n=a(4567),t=a(1296),r=a(7399),l=a(8460),u=a(8437),m=a(3230),o=a(4725),p=a(428),C=a(8934),y=a(6465),w=a(5114),D=a(8969),x=a(8055),I=a(4269),H=a(5941),S=a(3107),b=a(5744),L=a(9074),R=a(2585),M=a(3730),F=a(844),N=a(6731),O=typeof window<"u"?window.document:null;class W extends D.CoreTerminal{constructor(E={}){super(E),this.browser=c,this._keyDownHandled=!1,this._keyDownSeen=!1,this._keyPressHandled=!1,this._unprocessedDeadKey=!1,this._onCursorMove=this.register(new l.EventEmitter),this.onCursorMove=this._onCursorMove.event,this._onKey=this.register(new l.EventEmitter),this.onKey=this._onKey.event,this._onRender=this.register(new l.EventEmitter),this.onRender=this._onRender.event,this._onSelectionChange=this.register(new l.EventEmitter),this.onSelectionChange=this._onSelectionChange.event,this._onTitleChange=this.register(new l.EventEmitter),this.onTitleChange=this._onTitleChange.event,this._onBell=this.register(new l.EventEmitter),this.onBell=this._onBell.event,this._onFocus=this.register(new l.EventEmitter),this._onBlur=this.register(new l.EventEmitter),this._onA11yCharEmitter=this.register(new l.EventEmitter),this._onA11yTabEmitter=this.register(new l.EventEmitter),this._onWillOpen=this.register(new l.EventEmitter),this._setup(),this.linkifier2=this.register(this._instantiationService.createInstance(y.Linkifier2)),this.linkifier2.registerLinkProvider(this._instantiationService.createInstance(M.OscLinkProvider)),this._decorationService=this._instantiationService.createInstance(L.DecorationService),this._instantiationService.setService(R.IDecorationService,this._decorationService),this.register(this._inputHandler.onRequestBell(()=>this._onBell.fire())),this.register(this._inputHandler.onRequestRefreshRows((A,B)=>this.refresh(A,B))),this.register(this._inputHandler.onRequestSendFocus(()=>this._reportFocus())),this.register(this._inputHandler.onRequestReset(()=>this.reset())),this.register(this._inputHandler.onRequestWindowsOptionsReport(A=>this._reportWindowsOptions(A))),this.register(this._inputHandler.onColor(A=>this._handleColorEvent(A))),this.register((0,l.forwardEvent)(this._inputHandler.onCursorMove,this._onCursorMove)),this.register((0,l.forwardEvent)(this._inputHandler.onTitleChange,this._onTitleChange)),this.register((0,l.forwardEvent)(this._inputHandler.onA11yChar,this._onA11yCharEmitter)),this.register((0,l.forwardEvent)(this._inputHandler.onA11yTab,this._onA11yTabEmitter)),this.register(this._bufferService.onResize(A=>this._afterResize(A.cols,A.rows))),this.register((0,F.toDisposable)(()=>{var A,B;this._customKeyEventHandler=void 0,(B=(A=this.element)===null||A===void 0?void 0:A.parentNode)===null||B===void 0||B.removeChild(this.element)}))}get onFocus(){return this._onFocus.event}get onBlur(){return this._onBlur.event}get onA11yChar(){return this._onA11yCharEmitter.event}get onA11yTab(){return this._onA11yTabEmitter.event}get onWillOpen(){return this._onWillOpen.event}_handleColorEvent(E){if(this._themeService)for(const A of E){let B,P="";switch(A.index){case 256:B="foreground",P="10";break;case 257:B="background",P="11";break;case 258:B="cursor",P="12";break;default:B="ansi",P="4;"+A.index}switch(A.type){case 0:const j=x.color.toColorRGB(B==="ansi"?this._themeService.colors.ansi[A.index]:this._themeService.colors[B]);this.coreService.triggerDataEvent(`${_.C0.ESC}]${P};${(0,H.toRgbString)(j)}${_.C1_ESCAPED.ST}`);break;case 1:if(B==="ansi")this._themeService.modifyColors(U=>U.ansi[A.index]=x.rgba.toColor(...A.color));else{const U=B;this._themeService.modifyColors(K=>K[U]=x.rgba.toColor(...A.color))}break;case 2:this._themeService.restoreColor(A.index)}}}_setup(){super._setup(),this._customKeyEventHandler=void 0}get buffer(){return this.buffers.active}focus(){this.textarea&&this.textarea.focus({preventScroll:!0})}_handleScreenReaderModeOptionChange(E){var A;E?!this._accessibilityManager&&this._renderService&&(this._accessibilityManager=new n.AccessibilityManager(this,this._renderService)):((A=this._accessibilityManager)===null||A===void 0||A.dispose(),this._accessibilityManager=void 0)}_handleTextAreaFocus(E){this.coreService.decPrivateModes.sendFocus&&this.coreService.triggerDataEvent(_.C0.ESC+"[I"),this.updateCursorStyle(E),this.element.classList.add("focus"),this._showCursor(),this._onFocus.fire()}blur(){var E;return(E=this.textarea)===null||E===void 0?void 0:E.blur()}_handleTextAreaBlur(){this.textarea.value="",this.refresh(this.buffer.y,this.buffer.y),this.coreService.decPrivateModes.sendFocus&&this.coreService.triggerDataEvent(_.C0.ESC+"[O"),this.element.classList.remove("focus"),this._onBlur.fire()}_syncTextArea(){if(!this.textarea||!this.buffer.isCursorInViewport||this._compositionHelper.isComposing||!this._renderService)return;const E=this.buffer.ybase+this.buffer.y,A=this.buffer.lines.get(E);if(!A)return;const B=Math.min(this.buffer.x,this.cols-1),P=this._renderService.dimensions.css.cell.height,j=A.getWidth(B),U=this._renderService.dimensions.css.cell.width*j,K=this.buffer.y*this._renderService.dimensions.css.cell.height,V=B*this._renderService.dimensions.css.cell.width;this.textarea.style.left=V+"px",this.textarea.style.top=K+"px",this.textarea.style.width=U+"px",this.textarea.style.height=P+"px",this.textarea.style.lineHeight=P+"px",this.textarea.style.zIndex="-5"}_initGlobal(){this._bindKeys(),this.register((0,e.addDisposableDomListener)(this.element,"copy",A=>{this.hasSelection()&&(0,d.copyHandler)(A,this._selectionService)}));const E=A=>(0,d.handlePasteEvent)(A,this.textarea,this.coreService);this.register((0,e.addDisposableDomListener)(this.textarea,"paste",E)),this.register((0,e.addDisposableDomListener)(this.element,"paste",E)),c.isFirefox?this.register((0,e.addDisposableDomListener)(this.element,"mousedown",A=>{A.button===2&&(0,d.rightClickHandler)(A,this.textarea,this.screenElement,this._selectionService,this.options.rightClickSelectsWord)})):this.register((0,e.addDisposableDomListener)(this.element,"contextmenu",A=>{(0,d.rightClickHandler)(A,this.textarea,this.screenElement,this._selectionService,this.options.rightClickSelectsWord)})),c.isLinux&&this.register((0,e.addDisposableDomListener)(this.element,"auxclick",A=>{A.button===1&&(0,d.moveTextAreaUnderMouseCursor)(A,this.textarea,this.screenElement)}))}_bindKeys(){this.register((0,e.addDisposableDomListener)(this.textarea,"keyup",E=>this._keyUp(E),!0)),this.register((0,e.addDisposableDomListener)(this.textarea,"keydown",E=>this._keyDown(E),!0)),this.register((0,e.addDisposableDomListener)(this.textarea,"keypress",E=>this._keyPress(E),!0)),this.register((0,e.addDisposableDomListener)(this.textarea,"compositionstart",()=>this._compositionHelper.compositionstart())),this.register((0,e.addDisposableDomListener)(this.textarea,"compositionupdate",E=>this._compositionHelper.compositionupdate(E))),this.register((0,e.addDisposableDomListener)(this.textarea,"compositionend",()=>this._compositionHelper.compositionend())),this.register((0,e.addDisposableDomListener)(this.textarea,"input",E=>this._inputEvent(E),!0)),this.register(this.onRender(()=>this._compositionHelper.updateCompositionElements()))}open(E){var A;if(!E)throw new Error("Terminal requires a parent element.");E.isConnected||this._logService.debug("Terminal.open was called on an element that was not attached to the DOM"),this._document=E.ownerDocument,this.element=this._document.createElement("div"),this.element.dir="ltr",this.element.classList.add("terminal"),this.element.classList.add("xterm"),this.element.setAttribute("tabindex","0"),E.appendChild(this.element);const B=O.createDocumentFragment();this._viewportElement=O.createElement("div"),this._viewportElement.classList.add("xterm-viewport"),B.appendChild(this._viewportElement),this._viewportScrollArea=O.createElement("div"),this._viewportScrollArea.classList.add("xterm-scroll-area"),this._viewportElement.appendChild(this._viewportScrollArea),this.screenElement=O.createElement("div"),this.screenElement.classList.add("xterm-screen"),this._helperContainer=O.createElement("div"),this._helperContainer.classList.add("xterm-helpers"),this.screenElement.appendChild(this._helperContainer),B.appendChild(this.screenElement),this.textarea=O.createElement("textarea"),this.textarea.classList.add("xterm-helper-textarea"),this.textarea.setAttribute("aria-label",s.promptLabel),c.isChromeOS||this.textarea.setAttribute("aria-multiline","false"),this.textarea.setAttribute("autocorrect","off"),this.textarea.setAttribute("autocapitalize","off"),this.textarea.setAttribute("spellcheck","false"),this.textarea.tabIndex=0,this._coreBrowserService=this._instantiationService.createInstance(w.CoreBrowserService,this.textarea,(A=this._document.defaultView)!==null&&A!==void 0?A:window),this._instantiationService.setService(o.ICoreBrowserService,this._coreBrowserService),this.register((0,e.addDisposableDomListener)(this.textarea,"focus",P=>this._handleTextAreaFocus(P))),this.register((0,e.addDisposableDomListener)(this.textarea,"blur",()=>this._handleTextAreaBlur())),this._helperContainer.appendChild(this.textarea),this._charSizeService=this._instantiationService.createInstance(p.CharSizeService,this._document,this._helperContainer),this._instantiationService.setService(o.ICharSizeService,this._charSizeService),this._themeService=this._instantiationService.createInstance(N.ThemeService),this._instantiationService.setService(o.IThemeService,this._themeService),this._characterJoinerService=this._instantiationService.createInstance(I.CharacterJoinerService),this._instantiationService.setService(o.ICharacterJoinerService,this._characterJoinerService),this._renderService=this.register(this._instantiationService.createInstance(m.RenderService,this.rows,this.screenElement)),this._instantiationService.setService(o.IRenderService,this._renderService),this.register(this._renderService.onRenderedViewportChange(P=>this._onRender.fire(P))),this.onResize(P=>this._renderService.resize(P.cols,P.rows)),this._compositionView=O.createElement("div"),this._compositionView.classList.add("composition-view"),this._compositionHelper=this._instantiationService.createInstance(h.CompositionHelper,this.textarea,this._compositionView),this._helperContainer.appendChild(this._compositionView),this.element.appendChild(B);try{this._onWillOpen.fire(this.element)}catch{}this._renderService.hasRenderer()||this._renderService.setRenderer(this._createRenderer()),this._mouseService=this._instantiationService.createInstance(C.MouseService),this._instantiationService.setService(o.IMouseService,this._mouseService),this.viewport=this._instantiationService.createInstance(f.Viewport,P=>this.scrollLines(P,!0,1),this._viewportElement,this._viewportScrollArea),this.register(this._inputHandler.onRequestSyncScrollBar(()=>this.viewport.syncScrollArea())),this.register(this.viewport),this.register(this.onCursorMove(()=>{this._renderService.handleCursorMove(),this._syncTextArea()})),this.register(this.onResize(()=>this._renderService.handleResize(this.cols,this.rows))),this.register(this.onBlur(()=>this._renderService.handleBlur())),this.register(this.onFocus(()=>this._renderService.handleFocus())),this.register(this._renderService.onDimensionsChange(()=>this.viewport.syncScrollArea())),this._selectionService=this.register(this._instantiationService.createInstance(v.SelectionService,this.element,this.screenElement,this.linkifier2)),this._instantiationService.setService(o.ISelectionService,this._selectionService),this.register(this._selectionService.onRequestScrollLines(P=>this.scrollLines(P.amount,P.suppressScrollEvent))),this.register(this._selectionService.onSelectionChange(()=>this._onSelectionChange.fire())),this.register(this._selectionService.onRequestRedraw(P=>this._renderService.handleSelectionChanged(P.start,P.end,P.columnSelectMode))),this.register(this._selectionService.onLinuxMouseSelection(P=>{this.textarea.value=P,this.textarea.focus(),this.textarea.select()})),this.register(this._onScroll.event(P=>{this.viewport.syncScrollArea(),this._selectionService.refresh()})),this.register((0,e.addDisposableDomListener)(this._viewportElement,"scroll",()=>this._selectionService.refresh())),this.linkifier2.attachToDom(this.screenElement,this._mouseService,this._renderService),this.register(this._instantiationService.createInstance(S.BufferDecorationRenderer,this.screenElement)),this.register((0,e.addDisposableDomListener)(this.element,"mousedown",P=>this._selectionService.handleMouseDown(P))),this.coreMouseService.areMouseEventsActive?(this._selectionService.disable(),this.element.classList.add("enable-mouse-events")):this._selectionService.enable(),this.options.screenReaderMode&&(this._accessibilityManager=new n.AccessibilityManager(this,this._renderService)),this.register(this.optionsService.onSpecificOptionChange("screenReaderMode",P=>this._handleScreenReaderModeOptionChange(P))),this.options.overviewRulerWidth&&(this._overviewRulerRenderer=this.register(this._instantiationService.createInstance(b.OverviewRulerRenderer,this._viewportElement,this.screenElement))),this.optionsService.onSpecificOptionChange("overviewRulerWidth",P=>{!this._overviewRulerRenderer&&P&&this._viewportElement&&this.screenElement&&(this._overviewRulerRenderer=this.register(this._instantiationService.createInstance(b.OverviewRulerRenderer,this._viewportElement,this.screenElement)))}),this._charSizeService.measure(),this.refresh(0,this.rows-1),this._initGlobal(),this.bindMouse()}_createRenderer(){return this._instantiationService.createInstance(t.DomRenderer,this.element,this.screenElement,this._viewportElement,this.linkifier2)}bindMouse(){const E=this,A=this.element;function B(U){const K=E._mouseService.getMouseReportCoords(U,E.screenElement);if(!K)return!1;let V,q;switch(U.overrideType||U.type){case"mousemove":q=32,U.buttons===void 0?(V=3,U.button!==void 0&&(V=U.button<3?U.button:3)):V=1&U.buttons?0:4&U.buttons?1:2&U.buttons?2:3;break;case"mouseup":q=0,V=U.button<3?U.button:3;break;case"mousedown":q=1,V=U.button<3?U.button:3;break;case"wheel":if(E.viewport.getLinesScrolled(U)===0)return!1;q=U.deltaY<0?0:1,V=4;break;default:return!1}return!(q===void 0||V===void 0||V>4)&&E.coreMouseService.triggerMouseEvent({col:K.col,row:K.row,x:K.x,y:K.y,button:V,action:q,ctrl:U.ctrlKey,alt:U.altKey,shift:U.shiftKey})}const P={mouseup:null,wheel:null,mousedrag:null,mousemove:null},j={mouseup:U=>(B(U),U.buttons||(this._document.removeEventListener("mouseup",P.mouseup),P.mousedrag&&this._document.removeEventListener("mousemove",P.mousedrag)),this.cancel(U)),wheel:U=>(B(U),this.cancel(U,!0)),mousedrag:U=>{U.buttons&&B(U)},mousemove:U=>{U.buttons||B(U)}};this.register(this.coreMouseService.onProtocolChange(U=>{U?(this.optionsService.rawOptions.logLevel==="debug"&&this._logService.debug("Binding to mouse events:",this.coreMouseService.explainEvents(U)),this.element.classList.add("enable-mouse-events"),this._selectionService.disable()):(this._logService.debug("Unbinding from mouse events."),this.element.classList.remove("enable-mouse-events"),this._selectionService.enable()),8&U?P.mousemove||(A.addEventListener("mousemove",j.mousemove),P.mousemove=j.mousemove):(A.removeEventListener("mousemove",P.mousemove),P.mousemove=null),16&U?P.wheel||(A.addEventListener("wheel",j.wheel,{passive:!1}),P.wheel=j.wheel):(A.removeEventListener("wheel",P.wheel),P.wheel=null),2&U?P.mouseup||(P.mouseup=j.mouseup):(this._document.removeEventListener("mouseup",P.mouseup),P.mouseup=null),4&U?P.mousedrag||(P.mousedrag=j.mousedrag):(this._document.removeEventListener("mousemove",P.mousedrag),P.mousedrag=null)})),this.coreMouseService.activeProtocol=this.coreMouseService.activeProtocol,this.register((0,e.addDisposableDomListener)(A,"mousedown",U=>{if(U.preventDefault(),this.focus(),this.coreMouseService.areMouseEventsActive&&!this._selectionService.shouldForceSelection(U))return B(U),P.mouseup&&this._document.addEventListener("mouseup",P.mouseup),P.mousedrag&&this._document.addEventListener("mousemove",P.mousedrag),this.cancel(U)})),this.register((0,e.addDisposableDomListener)(A,"wheel",U=>{if(!P.wheel){if(!this.buffer.hasScrollback){const K=this.viewport.getLinesScrolled(U);if(K===0)return;const V=_.C0.ESC+(this.coreService.decPrivateModes.applicationCursorKeys?"O":"[")+(U.deltaY<0?"A":"B");let q="";for(let $=0;$<Math.abs(K);$++)q+=V;return this.coreService.triggerDataEvent(q,!0),this.cancel(U,!0)}return this.viewport.handleWheel(U)?this.cancel(U):void 0}},{passive:!1})),this.register((0,e.addDisposableDomListener)(A,"touchstart",U=>{if(!this.coreMouseService.areMouseEventsActive)return this.viewport.handleTouchStart(U),this.cancel(U)},{passive:!0})),this.register((0,e.addDisposableDomListener)(A,"touchmove",U=>{if(!this.coreMouseService.areMouseEventsActive)return this.viewport.handleTouchMove(U)?void 0:this.cancel(U)},{passive:!1}))}refresh(E,A){var B;(B=this._renderService)===null||B===void 0||B.refreshRows(E,A)}updateCursorStyle(E){var A;!((A=this._selectionService)===null||A===void 0)&&A.shouldColumnSelect(E)?this.element.classList.add("column-select"):this.element.classList.remove("column-select")}_showCursor(){this.coreService.isCursorInitialized||(this.coreService.isCursorInitialized=!0,this.refresh(this.buffer.y,this.buffer.y))}scrollLines(E,A,B=0){super.scrollLines(E,A,B),this.refresh(0,this.rows-1)}paste(E){(0,d.paste)(E,this.textarea,this.coreService)}attachCustomKeyEventHandler(E){this._customKeyEventHandler=E}registerLinkProvider(E){return this.linkifier2.registerLinkProvider(E)}registerCharacterJoiner(E){if(!this._characterJoinerService)throw new Error("Terminal must be opened first");const A=this._characterJoinerService.register(E);return this.refresh(0,this.rows-1),A}deregisterCharacterJoiner(E){if(!this._characterJoinerService)throw new Error("Terminal must be opened first");this._characterJoinerService.deregister(E)&&this.refresh(0,this.rows-1)}get markers(){return this.buffer.markers}addMarker(E){return this.buffer.addMarker(this.buffer.ybase+this.buffer.y+E)}registerDecoration(E){return this._decorationService.registerDecoration(E)}hasSelection(){return!!this._selectionService&&this._selectionService.hasSelection}select(E,A,B){this._selectionService.setSelection(E,A,B)}getSelection(){return this._selectionService?this._selectionService.selectionText:""}getSelectionPosition(){if(this._selectionService&&this._selectionService.hasSelection)return{start:{x:this._selectionService.selectionStart[0],y:this._selectionService.selectionStart[1]},end:{x:this._selectionService.selectionEnd[0],y:this._selectionService.selectionEnd[1]}}}clearSelection(){var E;(E=this._selectionService)===null||E===void 0||E.clearSelection()}selectAll(){var E;(E=this._selectionService)===null||E===void 0||E.selectAll()}selectLines(E,A){var B;(B=this._selectionService)===null||B===void 0||B.selectLines(E,A)}_keyDown(E){if(this._keyDownHandled=!1,this._keyDownSeen=!0,this._customKeyEventHandler&&this._customKeyEventHandler(E)===!1)return!1;const A=this.browser.isMac&&this.options.macOptionIsMeta&&E.altKey;if(!A&&!this._compositionHelper.keydown(E))return this.options.scrollOnUserInput&&this.buffer.ybase!==this.buffer.ydisp&&this._bufferService.scrollToBottom(),!1;A||E.key!=="Dead"&&E.key!=="AltGraph"||(this._unprocessedDeadKey=!0);const B=(0,r.evaluateKeyboardEvent)(E,this.coreService.decPrivateModes.applicationCursorKeys,this.browser.isMac,this.options.macOptionIsMeta);if(this.updateCursorStyle(E),B.type===3||B.type===2){const P=this.rows-1;return this.scrollLines(B.type===2?-P:P),this.cancel(E,!0)}return B.type===1&&this.selectAll(),!!this._isThirdLevelShift(this.browser,E)||(B.cancel&&this.cancel(E,!0),!B.key||!!(E.key&&!E.ctrlKey&&!E.altKey&&!E.metaKey&&E.key.length===1&&E.key.charCodeAt(0)>=65&&E.key.charCodeAt(0)<=90)||(this._unprocessedDeadKey?(this._unprocessedDeadKey=!1,!0):(B.key!==_.C0.ETX&&B.key!==_.C0.CR||(this.textarea.value=""),this._onKey.fire({key:B.key,domEvent:E}),this._showCursor(),this.coreService.triggerDataEvent(B.key,!0),!this.optionsService.rawOptions.screenReaderMode||E.altKey||E.ctrlKey?this.cancel(E,!0):void(this._keyDownHandled=!0))))}_isThirdLevelShift(E,A){const B=E.isMac&&!this.options.macOptionIsMeta&&A.altKey&&!A.ctrlKey&&!A.metaKey||E.isWindows&&A.altKey&&A.ctrlKey&&!A.metaKey||E.isWindows&&A.getModifierState("AltGraph");return A.type==="keypress"?B:B&&(!A.keyCode||A.keyCode>47)}_keyUp(E){this._keyDownSeen=!1,this._customKeyEventHandler&&this._customKeyEventHandler(E)===!1||(function(A){return A.keyCode===16||A.keyCode===17||A.keyCode===18}(E)||this.focus(),this.updateCursorStyle(E),this._keyPressHandled=!1)}_keyPress(E){let A;if(this._keyPressHandled=!1,this._keyDownHandled||this._customKeyEventHandler&&this._customKeyEventHandler(E)===!1)return!1;if(this.cancel(E),E.charCode)A=E.charCode;else if(E.which===null||E.which===void 0)A=E.keyCode;else{if(E.which===0||E.charCode===0)return!1;A=E.which}return!(!A||(E.altKey||E.ctrlKey||E.metaKey)&&!this._isThirdLevelShift(this.browser,E)||(A=String.fromCharCode(A),this._onKey.fire({key:A,domEvent:E}),this._showCursor(),this.coreService.triggerDataEvent(A,!0),this._keyPressHandled=!0,this._unprocessedDeadKey=!1,0))}_inputEvent(E){if(E.data&&E.inputType==="insertText"&&(!E.composed||!this._keyDownSeen)&&!this.optionsService.rawOptions.screenReaderMode){if(this._keyPressHandled)return!1;this._unprocessedDeadKey=!1;const A=E.data;return this.coreService.triggerDataEvent(A,!0),this.cancel(E),!0}return!1}resize(E,A){E!==this.cols||A!==this.rows?super.resize(E,A):this._charSizeService&&!this._charSizeService.hasValidSize&&this._charSizeService.measure()}_afterResize(E,A){var B,P;(B=this._charSizeService)===null||B===void 0||B.measure(),(P=this.viewport)===null||P===void 0||P.syncScrollArea(!0)}clear(){if(this.buffer.ybase!==0||this.buffer.y!==0){this.buffer.clearAllMarkers(),this.buffer.lines.set(0,this.buffer.lines.get(this.buffer.ybase+this.buffer.y)),this.buffer.lines.length=1,this.buffer.ydisp=0,this.buffer.ybase=0,this.buffer.y=0;for(let E=1;E<this.rows;E++)this.buffer.lines.push(this.buffer.getBlankLine(u.DEFAULT_ATTR_DATA));this.refresh(0,this.rows-1),this._onScroll.fire({position:this.buffer.ydisp,source:0})}}reset(){var E,A;this.options.rows=this.rows,this.options.cols=this.cols;const B=this._customKeyEventHandler;this._setup(),super.reset(),(E=this._selectionService)===null||E===void 0||E.reset(),this._decorationService.reset(),this._customKeyEventHandler=B,this.refresh(0,this.rows-1),(A=this.viewport)===null||A===void 0||A.syncScrollArea()}clearTextureAtlas(){var E;(E=this._renderService)===null||E===void 0||E.clearTextureAtlas()}_reportFocus(){var E;!((E=this.element)===null||E===void 0)&&E.classList.contains("focus")?this.coreService.triggerDataEvent(_.C0.ESC+"[I"):this.coreService.triggerDataEvent(_.C0.ESC+"[O")}_reportWindowsOptions(E){if(this._renderService)switch(E){case g.WindowsOptionsReportType.GET_WIN_SIZE_PIXELS:const A=this._renderService.dimensions.css.canvas.width.toFixed(0),B=this._renderService.dimensions.css.canvas.height.toFixed(0);this.coreService.triggerDataEvent(`${_.C0.ESC}[4;${B};${A}t`);break;case g.WindowsOptionsReportType.GET_CELL_SIZE_PIXELS:const P=this._renderService.dimensions.css.cell.width.toFixed(0),j=this._renderService.dimensions.css.cell.height.toFixed(0);this.coreService.triggerDataEvent(`${_.C0.ESC}[6;${j};${P}t`)}}cancel(E,A){if(this.options.cancelEvents||A)return E.preventDefault(),E.stopPropagation(),!1}}i.Terminal=W},9924:(T,i)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.TimeBasedDebouncer=void 0,i.TimeBasedDebouncer=class{constructor(a,h=1e3){this._renderCallback=a,this._debounceThresholdMS=h,this._lastRefreshMs=0,this._additionalRefreshRequested=!1}dispose(){this._refreshTimeoutID&&clearTimeout(this._refreshTimeoutID)}refresh(a,h,f){this._rowCount=f,a=a!==void 0?a:0,h=h!==void 0?h:this._rowCount-1,this._rowStart=this._rowStart!==void 0?Math.min(this._rowStart,a):a,this._rowEnd=this._rowEnd!==void 0?Math.max(this._rowEnd,h):h;const d=Date.now();if(d-this._lastRefreshMs>=this._debounceThresholdMS)this._lastRefreshMs=d,this._innerRefresh();else if(!this._additionalRefreshRequested){const _=d-this._lastRefreshMs,g=this._debounceThresholdMS-_;this._additionalRefreshRequested=!0,this._refreshTimeoutID=window.setTimeout(()=>{this._lastRefreshMs=Date.now(),this._innerRefresh(),this._additionalRefreshRequested=!1,this._refreshTimeoutID=void 0},g)}}_innerRefresh(){if(this._rowStart===void 0||this._rowEnd===void 0||this._rowCount===void 0)return;const a=Math.max(this._rowStart,0),h=Math.min(this._rowEnd,this._rowCount-1);this._rowStart=void 0,this._rowEnd=void 0,this._renderCallback(a,h)}}},1680:function(T,i,a){var h=this&&this.__decorate||function(e,s,n,t){var r,l=arguments.length,u=l<3?s:t===null?t=Object.getOwnPropertyDescriptor(s,n):t;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")u=Reflect.decorate(e,s,n,t);else for(var m=e.length-1;m>=0;m--)(r=e[m])&&(u=(l<3?r(u):l>3?r(s,n,u):r(s,n))||u);return l>3&&u&&Object.defineProperty(s,n,u),u},f=this&&this.__param||function(e,s){return function(n,t){s(n,t,e)}};Object.defineProperty(i,"__esModule",{value:!0}),i.Viewport=void 0;const d=a(844),_=a(3656),g=a(4725),v=a(2585);let c=class extends d.Disposable{constructor(e,s,n,t,r,l,u,m,o){super(),this._scrollLines=e,this._viewportElement=s,this._scrollArea=n,this._bufferService=t,this._optionsService=r,this._charSizeService=l,this._renderService=u,this._coreBrowserService=m,this.scrollBarWidth=0,this._currentRowHeight=0,this._currentDeviceCellHeight=0,this._lastRecordedBufferLength=0,this._lastRecordedViewportHeight=0,this._lastRecordedBufferHeight=0,this._lastTouchY=0,this._lastScrollTop=0,this._wheelPartialScroll=0,this._refreshAnimationFrame=null,this._ignoreNextScrollEvent=!1,this._smoothScrollState={startTime:0,origin:-1,target:-1},this.scrollBarWidth=this._viewportElement.offsetWidth-this._scrollArea.offsetWidth||15,this.register((0,_.addDisposableDomListener)(this._viewportElement,"scroll",this._handleScroll.bind(this))),this._activeBuffer=this._bufferService.buffer,this.register(this._bufferService.buffers.onBufferActivate(p=>this._activeBuffer=p.activeBuffer)),this._renderDimensions=this._renderService.dimensions,this.register(this._renderService.onDimensionsChange(p=>this._renderDimensions=p)),this._handleThemeChange(o.colors),this.register(o.onChangeColors(p=>this._handleThemeChange(p))),this.register(this._optionsService.onSpecificOptionChange("scrollback",()=>this.syncScrollArea())),setTimeout(()=>this.syncScrollArea(),0)}_handleThemeChange(e){this._viewportElement.style.backgroundColor=e.background.css}_refresh(e){if(e)return this._innerRefresh(),void(this._refreshAnimationFrame!==null&&this._coreBrowserService.window.cancelAnimationFrame(this._refreshAnimationFrame));this._refreshAnimationFrame===null&&(this._refreshAnimationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>this._innerRefresh()))}_innerRefresh(){if(this._charSizeService.height>0){this._currentRowHeight=this._renderService.dimensions.device.cell.height/this._coreBrowserService.dpr,this._currentDeviceCellHeight=this._renderService.dimensions.device.cell.height,this._lastRecordedViewportHeight=this._viewportElement.offsetHeight;const s=Math.round(this._currentRowHeight*this._lastRecordedBufferLength)+(this._lastRecordedViewportHeight-this._renderService.dimensions.css.canvas.height);this._lastRecordedBufferHeight!==s&&(this._lastRecordedBufferHeight=s,this._scrollArea.style.height=this._lastRecordedBufferHeight+"px")}const e=this._bufferService.buffer.ydisp*this._currentRowHeight;this._viewportElement.scrollTop!==e&&(this._ignoreNextScrollEvent=!0,this._viewportElement.scrollTop=e),this._refreshAnimationFrame=null}syncScrollArea(e=!1){if(this._lastRecordedBufferLength!==this._bufferService.buffer.lines.length)return this._lastRecordedBufferLength=this._bufferService.buffer.lines.length,void this._refresh(e);this._lastRecordedViewportHeight===this._renderService.dimensions.css.canvas.height&&this._lastScrollTop===this._activeBuffer.ydisp*this._currentRowHeight&&this._renderDimensions.device.cell.height===this._currentDeviceCellHeight||this._refresh(e)}_handleScroll(e){if(this._lastScrollTop=this._viewportElement.scrollTop,!this._viewportElement.offsetParent)return;if(this._ignoreNextScrollEvent)return this._ignoreNextScrollEvent=!1,void this._scrollLines(0);const s=Math.round(this._lastScrollTop/this._currentRowHeight)-this._bufferService.buffer.ydisp;this._scrollLines(s)}_smoothScroll(){if(this._isDisposed||this._smoothScrollState.origin===-1||this._smoothScrollState.target===-1)return;const e=this._smoothScrollPercent();this._viewportElement.scrollTop=this._smoothScrollState.origin+Math.round(e*(this._smoothScrollState.target-this._smoothScrollState.origin)),e<1?this._coreBrowserService.window.requestAnimationFrame(()=>this._smoothScroll()):this._clearSmoothScrollState()}_smoothScrollPercent(){return this._optionsService.rawOptions.smoothScrollDuration&&this._smoothScrollState.startTime?Math.max(Math.min((Date.now()-this._smoothScrollState.startTime)/this._optionsService.rawOptions.smoothScrollDuration,1),0):1}_clearSmoothScrollState(){this._smoothScrollState.startTime=0,this._smoothScrollState.origin=-1,this._smoothScrollState.target=-1}_bubbleScroll(e,s){const n=this._viewportElement.scrollTop+this._lastRecordedViewportHeight;return!(s<0&&this._viewportElement.scrollTop!==0||s>0&&n<this._lastRecordedBufferHeight)||(e.cancelable&&e.preventDefault(),!1)}handleWheel(e){const s=this._getPixelsScrolled(e);return s!==0&&(this._optionsService.rawOptions.smoothScrollDuration?(this._smoothScrollState.startTime=Date.now(),this._smoothScrollPercent()<1?(this._smoothScrollState.origin=this._viewportElement.scrollTop,this._smoothScrollState.target===-1?this._smoothScrollState.target=this._viewportElement.scrollTop+s:this._smoothScrollState.target+=s,this._smoothScrollState.target=Math.max(Math.min(this._smoothScrollState.target,this._viewportElement.scrollHeight),0),this._smoothScroll()):this._clearSmoothScrollState()):this._viewportElement.scrollTop+=s,this._bubbleScroll(e,s))}_getPixelsScrolled(e){if(e.deltaY===0||e.shiftKey)return 0;let s=this._applyScrollModifier(e.deltaY,e);return e.deltaMode===WheelEvent.DOM_DELTA_LINE?s*=this._currentRowHeight:e.deltaMode===WheelEvent.DOM_DELTA_PAGE&&(s*=this._currentRowHeight*this._bufferService.rows),s}getLinesScrolled(e){if(e.deltaY===0||e.shiftKey)return 0;let s=this._applyScrollModifier(e.deltaY,e);return e.deltaMode===WheelEvent.DOM_DELTA_PIXEL?(s/=this._currentRowHeight+0,this._wheelPartialScroll+=s,s=Math.floor(Math.abs(this._wheelPartialScroll))*(this._wheelPartialScroll>0?1:-1),this._wheelPartialScroll%=1):e.deltaMode===WheelEvent.DOM_DELTA_PAGE&&(s*=this._bufferService.rows),s}_applyScrollModifier(e,s){const n=this._optionsService.rawOptions.fastScrollModifier;return n==="alt"&&s.altKey||n==="ctrl"&&s.ctrlKey||n==="shift"&&s.shiftKey?e*this._optionsService.rawOptions.fastScrollSensitivity*this._optionsService.rawOptions.scrollSensitivity:e*this._optionsService.rawOptions.scrollSensitivity}handleTouchStart(e){this._lastTouchY=e.touches[0].pageY}handleTouchMove(e){const s=this._lastTouchY-e.touches[0].pageY;return this._lastTouchY=e.touches[0].pageY,s!==0&&(this._viewportElement.scrollTop+=s,this._bubbleScroll(e,s))}};c=h([f(3,v.IBufferService),f(4,v.IOptionsService),f(5,g.ICharSizeService),f(6,g.IRenderService),f(7,g.ICoreBrowserService),f(8,g.IThemeService)],c),i.Viewport=c},3107:function(T,i,a){var h=this&&this.__decorate||function(e,s,n,t){var r,l=arguments.length,u=l<3?s:t===null?t=Object.getOwnPropertyDescriptor(s,n):t;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")u=Reflect.decorate(e,s,n,t);else for(var m=e.length-1;m>=0;m--)(r=e[m])&&(u=(l<3?r(u):l>3?r(s,n,u):r(s,n))||u);return l>3&&u&&Object.defineProperty(s,n,u),u},f=this&&this.__param||function(e,s){return function(n,t){s(n,t,e)}};Object.defineProperty(i,"__esModule",{value:!0}),i.BufferDecorationRenderer=void 0;const d=a(3656),_=a(4725),g=a(844),v=a(2585);let c=class extends g.Disposable{constructor(e,s,n,t){super(),this._screenElement=e,this._bufferService=s,this._decorationService=n,this._renderService=t,this._decorationElements=new Map,this._altBufferIsActive=!1,this._dimensionsChanged=!1,this._container=document.createElement("div"),this._container.classList.add("xterm-decoration-container"),this._screenElement.appendChild(this._container),this.register(this._renderService.onRenderedViewportChange(()=>this._doRefreshDecorations())),this.register(this._renderService.onDimensionsChange(()=>{this._dimensionsChanged=!0,this._queueRefresh()})),this.register((0,d.addDisposableDomListener)(window,"resize",()=>this._queueRefresh())),this.register(this._bufferService.buffers.onBufferActivate(()=>{this._altBufferIsActive=this._bufferService.buffer===this._bufferService.buffers.alt})),this.register(this._decorationService.onDecorationRegistered(()=>this._queueRefresh())),this.register(this._decorationService.onDecorationRemoved(r=>this._removeDecoration(r))),this.register((0,g.toDisposable)(()=>{this._container.remove(),this._decorationElements.clear()}))}_queueRefresh(){this._animationFrame===void 0&&(this._animationFrame=this._renderService.addRefreshCallback(()=>{this._doRefreshDecorations(),this._animationFrame=void 0}))}_doRefreshDecorations(){for(const e of this._decorationService.decorations)this._renderDecoration(e);this._dimensionsChanged=!1}_renderDecoration(e){this._refreshStyle(e),this._dimensionsChanged&&this._refreshXPosition(e)}_createElement(e){var s;const n=document.createElement("div");n.classList.add("xterm-decoration"),n.style.width=`${Math.round((e.options.width||1)*this._renderService.dimensions.css.cell.width)}px`,n.style.height=(e.options.height||1)*this._renderService.dimensions.css.cell.height+"px",n.style.top=(e.marker.line-this._bufferService.buffers.active.ydisp)*this._renderService.dimensions.css.cell.height+"px",n.style.lineHeight=`${this._renderService.dimensions.css.cell.height}px`;const t=(s=e.options.x)!==null&&s!==void 0?s:0;return t&&t>this._bufferService.cols&&(n.style.display="none"),this._refreshXPosition(e,n),n}_refreshStyle(e){const s=e.marker.line-this._bufferService.buffers.active.ydisp;if(s<0||s>=this._bufferService.rows)e.element&&(e.element.style.display="none",e.onRenderEmitter.fire(e.element));else{let n=this._decorationElements.get(e);n||(n=this._createElement(e),e.element=n,this._decorationElements.set(e,n),this._container.appendChild(n)),n.style.top=s*this._renderService.dimensions.css.cell.height+"px",n.style.display=this._altBufferIsActive?"none":"block",e.onRenderEmitter.fire(n)}}_refreshXPosition(e,s=e.element){var n;if(!s)return;const t=(n=e.options.x)!==null&&n!==void 0?n:0;(e.options.anchor||"left")==="right"?s.style.right=t?t*this._renderService.dimensions.css.cell.width+"px":"":s.style.left=t?t*this._renderService.dimensions.css.cell.width+"px":""}_removeDecoration(e){var s;(s=this._decorationElements.get(e))===null||s===void 0||s.remove(),this._decorationElements.delete(e),e.dispose()}};c=h([f(1,v.IBufferService),f(2,v.IDecorationService),f(3,_.IRenderService)],c),i.BufferDecorationRenderer=c},5871:(T,i)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.ColorZoneStore=void 0,i.ColorZoneStore=class{constructor(){this._zones=[],this._zonePool=[],this._zonePoolIndex=0,this._linePadding={full:0,left:0,center:0,right:0}}get zones(){return this._zonePool.length=Math.min(this._zonePool.length,this._zones.length),this._zones}clear(){this._zones.length=0,this._zonePoolIndex=0}addDecoration(a){if(a.options.overviewRulerOptions){for(const h of this._zones)if(h.color===a.options.overviewRulerOptions.color&&h.position===a.options.overviewRulerOptions.position){if(this._lineIntersectsZone(h,a.marker.line))return;if(this._lineAdjacentToZone(h,a.marker.line,a.options.overviewRulerOptions.position))return void this._addLineToZone(h,a.marker.line)}if(this._zonePoolIndex<this._zonePool.length)return this._zonePool[this._zonePoolIndex].color=a.options.overviewRulerOptions.color,this._zonePool[this._zonePoolIndex].position=a.options.overviewRulerOptions.position,this._zonePool[this._zonePoolIndex].startBufferLine=a.marker.line,this._zonePool[this._zonePoolIndex].endBufferLine=a.marker.line,void this._zones.push(this._zonePool[this._zonePoolIndex++]);this._zones.push({color:a.options.overviewRulerOptions.color,position:a.options.overviewRulerOptions.position,startBufferLine:a.marker.line,endBufferLine:a.marker.line}),this._zonePool.push(this._zones[this._zones.length-1]),this._zonePoolIndex++}}setPadding(a){this._linePadding=a}_lineIntersectsZone(a,h){return h>=a.startBufferLine&&h<=a.endBufferLine}_lineAdjacentToZone(a,h,f){return h>=a.startBufferLine-this._linePadding[f||"full"]&&h<=a.endBufferLine+this._linePadding[f||"full"]}_addLineToZone(a,h){a.startBufferLine=Math.min(a.startBufferLine,h),a.endBufferLine=Math.max(a.endBufferLine,h)}}},5744:function(T,i,a){var h=this&&this.__decorate||function(r,l,u,m){var o,p=arguments.length,C=p<3?l:m===null?m=Object.getOwnPropertyDescriptor(l,u):m;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")C=Reflect.decorate(r,l,u,m);else for(var y=r.length-1;y>=0;y--)(o=r[y])&&(C=(p<3?o(C):p>3?o(l,u,C):o(l,u))||C);return p>3&&C&&Object.defineProperty(l,u,C),C},f=this&&this.__param||function(r,l){return function(u,m){l(u,m,r)}};Object.defineProperty(i,"__esModule",{value:!0}),i.OverviewRulerRenderer=void 0;const d=a(5871),_=a(3656),g=a(4725),v=a(844),c=a(2585),e={full:0,left:0,center:0,right:0},s={full:0,left:0,center:0,right:0},n={full:0,left:0,center:0,right:0};let t=class extends v.Disposable{constructor(r,l,u,m,o,p,C){var y;super(),this._viewportElement=r,this._screenElement=l,this._bufferService=u,this._decorationService=m,this._renderService=o,this._optionsService=p,this._coreBrowseService=C,this._colorZoneStore=new d.ColorZoneStore,this._shouldUpdateDimensions=!0,this._shouldUpdateAnchor=!0,this._lastKnownBufferLength=0,this._canvas=document.createElement("canvas"),this._canvas.classList.add("xterm-decoration-overview-ruler"),this._refreshCanvasDimensions(),(y=this._viewportElement.parentElement)===null||y===void 0||y.insertBefore(this._canvas,this._viewportElement);const w=this._canvas.getContext("2d");if(!w)throw new Error("Ctx cannot be null");this._ctx=w,this._registerDecorationListeners(),this._registerBufferChangeListeners(),this._registerDimensionChangeListeners(),this.register((0,v.toDisposable)(()=>{var D;(D=this._canvas)===null||D===void 0||D.remove()}))}get _width(){return this._optionsService.options.overviewRulerWidth||0}_registerDecorationListeners(){this.register(this._decorationService.onDecorationRegistered(()=>this._queueRefresh(void 0,!0))),this.register(this._decorationService.onDecorationRemoved(()=>this._queueRefresh(void 0,!0)))}_registerBufferChangeListeners(){this.register(this._renderService.onRenderedViewportChange(()=>this._queueRefresh())),this.register(this._bufferService.buffers.onBufferActivate(()=>{this._canvas.style.display=this._bufferService.buffer===this._bufferService.buffers.alt?"none":"block"})),this.register(this._bufferService.onScroll(()=>{this._lastKnownBufferLength!==this._bufferService.buffers.normal.lines.length&&(this._refreshDrawHeightConstants(),this._refreshColorZonePadding())}))}_registerDimensionChangeListeners(){this.register(this._renderService.onRender(()=>{this._containerHeight&&this._containerHeight===this._screenElement.clientHeight||(this._queueRefresh(!0),this._containerHeight=this._screenElement.clientHeight)})),this.register(this._optionsService.onSpecificOptionChange("overviewRulerWidth",()=>this._queueRefresh(!0))),this.register((0,_.addDisposableDomListener)(this._coreBrowseService.window,"resize",()=>this._queueRefresh(!0))),this._queueRefresh(!0)}_refreshDrawConstants(){const r=Math.floor(this._canvas.width/3),l=Math.ceil(this._canvas.width/3);s.full=this._canvas.width,s.left=r,s.center=l,s.right=r,this._refreshDrawHeightConstants(),n.full=0,n.left=0,n.center=s.left,n.right=s.left+s.center}_refreshDrawHeightConstants(){e.full=Math.round(2*this._coreBrowseService.dpr);const r=this._canvas.height/this._bufferService.buffer.lines.length,l=Math.round(Math.max(Math.min(r,12),6)*this._coreBrowseService.dpr);e.left=l,e.center=l,e.right=l}_refreshColorZonePadding(){this._colorZoneStore.setPadding({full:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*e.full),left:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*e.left),center:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*e.center),right:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*e.right)}),this._lastKnownBufferLength=this._bufferService.buffers.normal.lines.length}_refreshCanvasDimensions(){this._canvas.style.width=`${this._width}px`,this._canvas.width=Math.round(this._width*this._coreBrowseService.dpr),this._canvas.style.height=`${this._screenElement.clientHeight}px`,this._canvas.height=Math.round(this._screenElement.clientHeight*this._coreBrowseService.dpr),this._refreshDrawConstants(),this._refreshColorZonePadding()}_refreshDecorations(){this._shouldUpdateDimensions&&this._refreshCanvasDimensions(),this._ctx.clearRect(0,0,this._canvas.width,this._canvas.height),this._colorZoneStore.clear();for(const l of this._decorationService.decorations)this._colorZoneStore.addDecoration(l);this._ctx.lineWidth=1;const r=this._colorZoneStore.zones;for(const l of r)l.position!=="full"&&this._renderColorZone(l);for(const l of r)l.position==="full"&&this._renderColorZone(l);this._shouldUpdateDimensions=!1,this._shouldUpdateAnchor=!1}_renderColorZone(r){this._ctx.fillStyle=r.color,this._ctx.fillRect(n[r.position||"full"],Math.round((this._canvas.height-1)*(r.startBufferLine/this._bufferService.buffers.active.lines.length)-e[r.position||"full"]/2),s[r.position||"full"],Math.round((this._canvas.height-1)*((r.endBufferLine-r.startBufferLine)/this._bufferService.buffers.active.lines.length)+e[r.position||"full"]))}_queueRefresh(r,l){this._shouldUpdateDimensions=r||this._shouldUpdateDimensions,this._shouldUpdateAnchor=l||this._shouldUpdateAnchor,this._animationFrame===void 0&&(this._animationFrame=this._coreBrowseService.window.requestAnimationFrame(()=>{this._refreshDecorations(),this._animationFrame=void 0}))}};t=h([f(2,c.IBufferService),f(3,c.IDecorationService),f(4,g.IRenderService),f(5,c.IOptionsService),f(6,g.ICoreBrowserService)],t),i.OverviewRulerRenderer=t},2950:function(T,i,a){var h=this&&this.__decorate||function(c,e,s,n){var t,r=arguments.length,l=r<3?e:n===null?n=Object.getOwnPropertyDescriptor(e,s):n;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")l=Reflect.decorate(c,e,s,n);else for(var u=c.length-1;u>=0;u--)(t=c[u])&&(l=(r<3?t(l):r>3?t(e,s,l):t(e,s))||l);return r>3&&l&&Object.defineProperty(e,s,l),l},f=this&&this.__param||function(c,e){return function(s,n){e(s,n,c)}};Object.defineProperty(i,"__esModule",{value:!0}),i.CompositionHelper=void 0;const d=a(4725),_=a(2585),g=a(2584);let v=class{constructor(c,e,s,n,t,r){this._textarea=c,this._compositionView=e,this._bufferService=s,this._optionsService=n,this._coreService=t,this._renderService=r,this._isComposing=!1,this._isSendingComposition=!1,this._compositionPosition={start:0,end:0},this._dataAlreadySent=""}get isComposing(){return this._isComposing}compositionstart(){this._isComposing=!0,this._compositionPosition.start=this._textarea.value.length,this._compositionView.textContent="",this._dataAlreadySent="",this._compositionView.classList.add("active")}compositionupdate(c){this._compositionView.textContent=c.data,this.updateCompositionElements(),setTimeout(()=>{this._compositionPosition.end=this._textarea.value.length},0)}compositionend(){this._finalizeComposition(!0)}keydown(c){if(this._isComposing||this._isSendingComposition){if(c.keyCode===229||c.keyCode===16||c.keyCode===17||c.keyCode===18)return!1;this._finalizeComposition(!1)}return c.keyCode!==229||(this._handleAnyTextareaChanges(),!1)}_finalizeComposition(c){if(this._compositionView.classList.remove("active"),this._isComposing=!1,c){const e={start:this._compositionPosition.start,end:this._compositionPosition.end};this._isSendingComposition=!0,setTimeout(()=>{if(this._isSendingComposition){let s;this._isSendingComposition=!1,e.start+=this._dataAlreadySent.length,s=this._isComposing?this._textarea.value.substring(e.start,e.end):this._textarea.value.substring(e.start),s.length>0&&this._coreService.triggerDataEvent(s,!0)}},0)}else{this._isSendingComposition=!1;const e=this._textarea.value.substring(this._compositionPosition.start,this._compositionPosition.end);this._coreService.triggerDataEvent(e,!0)}}_handleAnyTextareaChanges(){const c=this._textarea.value;setTimeout(()=>{if(!this._isComposing){const e=this._textarea.value,s=e.replace(c,"");this._dataAlreadySent=s,e.length>c.length?this._coreService.triggerDataEvent(s,!0):e.length<c.length?this._coreService.triggerDataEvent(`${g.C0.DEL}`,!0):e.length===c.length&&e!==c&&this._coreService.triggerDataEvent(e,!0)}},0)}updateCompositionElements(c){if(this._isComposing){if(this._bufferService.buffer.isCursorInViewport){const e=Math.min(this._bufferService.buffer.x,this._bufferService.cols-1),s=this._renderService.dimensions.css.cell.height,n=this._bufferService.buffer.y*this._renderService.dimensions.css.cell.height,t=e*this._renderService.dimensions.css.cell.width;this._compositionView.style.left=t+"px",this._compositionView.style.top=n+"px",this._compositionView.style.height=s+"px",this._compositionView.style.lineHeight=s+"px",this._compositionView.style.fontFamily=this._optionsService.rawOptions.fontFamily,this._compositionView.style.fontSize=this._optionsService.rawOptions.fontSize+"px";const r=this._compositionView.getBoundingClientRect();this._textarea.style.left=t+"px",this._textarea.style.top=n+"px",this._textarea.style.width=Math.max(r.width,1)+"px",this._textarea.style.height=Math.max(r.height,1)+"px",this._textarea.style.lineHeight=r.height+"px"}c||setTimeout(()=>this.updateCompositionElements(!0),0)}}};v=h([f(2,_.IBufferService),f(3,_.IOptionsService),f(4,_.ICoreService),f(5,d.IRenderService)],v),i.CompositionHelper=v},9806:(T,i)=>{function a(h,f,d){const _=d.getBoundingClientRect(),g=h.getComputedStyle(d),v=parseInt(g.getPropertyValue("padding-left")),c=parseInt(g.getPropertyValue("padding-top"));return[f.clientX-_.left-v,f.clientY-_.top-c]}Object.defineProperty(i,"__esModule",{value:!0}),i.getCoords=i.getCoordsRelativeToElement=void 0,i.getCoordsRelativeToElement=a,i.getCoords=function(h,f,d,_,g,v,c,e,s){if(!v)return;const n=a(h,f,d);return n?(n[0]=Math.ceil((n[0]+(s?c/2:0))/c),n[1]=Math.ceil(n[1]/e),n[0]=Math.min(Math.max(n[0],1),_+(s?1:0)),n[1]=Math.min(Math.max(n[1],1),g),n):void 0}},9504:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.moveToCellSequence=void 0;const h=a(2584);function f(e,s,n,t){const r=e-d(e,n),l=s-d(s,n),u=Math.abs(r-l)-function(m,o,p){let C=0;const y=m-d(m,p),w=o-d(o,p);for(let D=0;D<Math.abs(y-w);D++){const x=_(m,o)==="A"?-1:1,I=p.buffer.lines.get(y+x*D);I!=null&&I.isWrapped&&C++}return C}(e,s,n);return c(u,v(_(e,s),t))}function d(e,s){let n=0,t=s.buffer.lines.get(e),r=t==null?void 0:t.isWrapped;for(;r&&e>=0&&e<s.rows;)n++,t=s.buffer.lines.get(--e),r=t==null?void 0:t.isWrapped;return n}function _(e,s){return e>s?"A":"B"}function g(e,s,n,t,r,l){let u=e,m=s,o="";for(;u!==n||m!==t;)u+=r?1:-1,r&&u>l.cols-1?(o+=l.buffer.translateBufferLineToString(m,!1,e,u),u=0,e=0,m++):!r&&u<0&&(o+=l.buffer.translateBufferLineToString(m,!1,0,e+1),u=l.cols-1,e=u,m--);return o+l.buffer.translateBufferLineToString(m,!1,e,u)}function v(e,s){const n=s?"O":"[";return h.C0.ESC+n+e}function c(e,s){e=Math.floor(e);let n="";for(let t=0;t<e;t++)n+=s;return n}i.moveToCellSequence=function(e,s,n,t){const r=n.buffer.x,l=n.buffer.y;if(!n.buffer.hasScrollback)return function(o,p,C,y,w,D){return f(p,y,w,D).length===0?"":c(g(o,p,o,p-d(p,w),!1,w).length,v("D",D))}(r,l,0,s,n,t)+f(l,s,n,t)+function(o,p,C,y,w,D){let x;x=f(p,y,w,D).length>0?y-d(y,w):p;const I=y,H=function(S,b,L,R,M,F){let N;return N=f(L,R,M,F).length>0?R-d(R,M):b,S<L&&N<=R||S>=L&&N<R?"C":"D"}(o,p,C,y,w,D);return c(g(o,x,C,I,H==="C",w).length,v(H,D))}(r,l,e,s,n,t);let u;if(l===s)return u=r>e?"D":"C",c(Math.abs(r-e),v(u,t));u=l>s?"D":"C";const m=Math.abs(l-s);return c(function(o,p){return p.cols-o}(l>s?e:r,n)+(m-1)*n.cols+1+((l>s?r:e)-1),v(u,t))}},1296:function(T,i,a){var h=this&&this.__decorate||function(o,p,C,y){var w,D=arguments.length,x=D<3?p:y===null?y=Object.getOwnPropertyDescriptor(p,C):y;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")x=Reflect.decorate(o,p,C,y);else for(var I=o.length-1;I>=0;I--)(w=o[I])&&(x=(D<3?w(x):D>3?w(p,C,x):w(p,C))||x);return D>3&&x&&Object.defineProperty(p,C,x),x},f=this&&this.__param||function(o,p){return function(C,y){p(C,y,o)}};Object.defineProperty(i,"__esModule",{value:!0}),i.DomRenderer=void 0;const d=a(9631),_=a(3787),g=a(2223),v=a(6171),c=a(4725),e=a(8055),s=a(8460),n=a(844),t=a(2585),r="xterm-dom-renderer-owner-",l="xterm-focus";let u=1,m=class extends n.Disposable{constructor(o,p,C,y,w,D,x,I,H,S){super(),this._element=o,this._screenElement=p,this._viewportElement=C,this._linkifier2=y,this._charSizeService=D,this._optionsService=x,this._bufferService=I,this._coreBrowserService=H,this._terminalClass=u++,this._rowElements=[],this._cellToRowElements=[],this.onRequestRedraw=this.register(new s.EventEmitter).event,this._rowContainer=document.createElement("div"),this._rowContainer.classList.add("xterm-rows"),this._rowContainer.style.lineHeight="normal",this._rowContainer.setAttribute("aria-hidden","true"),this._refreshRowElements(this._bufferService.cols,this._bufferService.rows),this._selectionContainer=document.createElement("div"),this._selectionContainer.classList.add("xterm-selection"),this._selectionContainer.setAttribute("aria-hidden","true"),this.dimensions=(0,v.createRenderDimensions)(),this._updateDimensions(),this.register(this._optionsService.onOptionChange(()=>this._handleOptionsChanged())),this.register(S.onChangeColors(b=>this._injectCss(b))),this._injectCss(S.colors),this._rowFactory=w.createInstance(_.DomRendererRowFactory,document),this._element.classList.add(r+this._terminalClass),this._screenElement.appendChild(this._rowContainer),this._screenElement.appendChild(this._selectionContainer),this.register(this._linkifier2.onShowLinkUnderline(b=>this._handleLinkHover(b))),this.register(this._linkifier2.onHideLinkUnderline(b=>this._handleLinkLeave(b))),this.register((0,n.toDisposable)(()=>{this._element.classList.remove(r+this._terminalClass),(0,d.removeElementFromParent)(this._rowContainer,this._selectionContainer,this._themeStyleElement,this._dimensionsStyleElement)}))}_updateDimensions(){const o=this._coreBrowserService.dpr;this.dimensions.device.char.width=this._charSizeService.width*o,this.dimensions.device.char.height=Math.ceil(this._charSizeService.height*o),this.dimensions.device.cell.width=this.dimensions.device.char.width+Math.round(this._optionsService.rawOptions.letterSpacing),this.dimensions.device.cell.height=Math.floor(this.dimensions.device.char.height*this._optionsService.rawOptions.lineHeight),this.dimensions.device.char.left=0,this.dimensions.device.char.top=0,this.dimensions.device.canvas.width=this.dimensions.device.cell.width*this._bufferService.cols,this.dimensions.device.canvas.height=this.dimensions.device.cell.height*this._bufferService.rows,this.dimensions.css.canvas.width=Math.round(this.dimensions.device.canvas.width/o),this.dimensions.css.canvas.height=Math.round(this.dimensions.device.canvas.height/o),this.dimensions.css.cell.width=this.dimensions.css.canvas.width/this._bufferService.cols,this.dimensions.css.cell.height=this.dimensions.css.canvas.height/this._bufferService.rows;for(const C of this._rowElements)C.style.width=`${this.dimensions.css.canvas.width}px`,C.style.height=`${this.dimensions.css.cell.height}px`,C.style.lineHeight=`${this.dimensions.css.cell.height}px`,C.style.overflow="hidden";this._dimensionsStyleElement||(this._dimensionsStyleElement=document.createElement("style"),this._screenElement.appendChild(this._dimensionsStyleElement));const p=`${this._terminalSelector} .xterm-rows span { display: inline-block; height: 100%; vertical-align: top; width: ${this.dimensions.css.cell.width}px}`;this._dimensionsStyleElement.textContent=p,this._selectionContainer.style.height=this._viewportElement.style.height,this._screenElement.style.width=`${this.dimensions.css.canvas.width}px`,this._screenElement.style.height=`${this.dimensions.css.canvas.height}px`}_injectCss(o){this._themeStyleElement||(this._themeStyleElement=document.createElement("style"),this._screenElement.appendChild(this._themeStyleElement));let p=`${this._terminalSelector} .xterm-rows { color: ${o.foreground.css}; font-family: ${this._optionsService.rawOptions.fontFamily}; font-size: ${this._optionsService.rawOptions.fontSize}px;}`;p+=`${this._terminalSelector} span:not(.${_.BOLD_CLASS}) { font-weight: ${this._optionsService.rawOptions.fontWeight};}${this._terminalSelector} span.${_.BOLD_CLASS} { font-weight: ${this._optionsService.rawOptions.fontWeightBold};}${this._terminalSelector} span.${_.ITALIC_CLASS} { font-style: italic;}`,p+="@keyframes blink_box_shadow_"+this._terminalClass+" { 50% {  box-shadow: none; }}",p+="@keyframes blink_block_"+this._terminalClass+` { 0% {  background-color: ${o.cursor.css};  color: ${o.cursorAccent.css}; } 50% {  background-color: ${o.cursorAccent.css};  color: ${o.cursor.css}; }}`,p+=`${this._terminalSelector} .xterm-rows:not(.xterm-focus) .${_.CURSOR_CLASS}.${_.CURSOR_STYLE_BLOCK_CLASS} { outline: 1px solid ${o.cursor.css}; outline-offset: -1px;}${this._terminalSelector} .xterm-rows.xterm-focus .${_.CURSOR_CLASS}.${_.CURSOR_BLINK_CLASS}:not(.${_.CURSOR_STYLE_BLOCK_CLASS}) { animation: blink_box_shadow_`+this._terminalClass+` 1s step-end infinite;}${this._terminalSelector} .xterm-rows.xterm-focus .${_.CURSOR_CLASS}.${_.CURSOR_BLINK_CLASS}.${_.CURSOR_STYLE_BLOCK_CLASS} { animation: blink_block_`+this._terminalClass+` 1s step-end infinite;}${this._terminalSelector} .xterm-rows.xterm-focus .${_.CURSOR_CLASS}.${_.CURSOR_STYLE_BLOCK_CLASS} { background-color: ${o.cursor.css}; color: ${o.cursorAccent.css};}${this._terminalSelector} .xterm-rows .${_.CURSOR_CLASS}.${_.CURSOR_STYLE_BAR_CLASS} { box-shadow: ${this._optionsService.rawOptions.cursorWidth}px 0 0 ${o.cursor.css} inset;}${this._terminalSelector} .xterm-rows .${_.CURSOR_CLASS}.${_.CURSOR_STYLE_UNDERLINE_CLASS} { box-shadow: 0 -1px 0 ${o.cursor.css} inset;}`,p+=`${this._terminalSelector} .xterm-selection { position: absolute; top: 0; left: 0; z-index: 1; pointer-events: none;}${this._terminalSelector}.focus .xterm-selection div { position: absolute; background-color: ${o.selectionBackgroundOpaque.css};}${this._terminalSelector} .xterm-selection div { position: absolute; background-color: ${o.selectionInactiveBackgroundOpaque.css};}`;for(const[C,y]of o.ansi.entries())p+=`${this._terminalSelector} .xterm-fg-${C} { color: ${y.css}; }${this._terminalSelector} .xterm-bg-${C} { background-color: ${y.css}; }`;p+=`${this._terminalSelector} .xterm-fg-${g.INVERTED_DEFAULT_COLOR} { color: ${e.color.opaque(o.background).css}; }${this._terminalSelector} .xterm-bg-${g.INVERTED_DEFAULT_COLOR} { background-color: ${o.foreground.css}; }`,this._themeStyleElement.textContent=p}handleDevicePixelRatioChange(){this._updateDimensions()}_refreshRowElements(o,p){for(let C=this._rowElements.length;C<=p;C++){const y=document.createElement("div");this._rowContainer.appendChild(y),this._rowElements.push(y)}for(;this._rowElements.length>p;)this._rowContainer.removeChild(this._rowElements.pop())}handleResize(o,p){this._refreshRowElements(o,p),this._updateDimensions()}handleCharSizeChanged(){this._updateDimensions()}handleBlur(){this._rowContainer.classList.remove(l)}handleFocus(){this._rowContainer.classList.add(l)}handleSelectionChanged(o,p,C){for(;this._selectionContainer.children.length;)this._selectionContainer.removeChild(this._selectionContainer.children[0]);if(this._rowFactory.handleSelectionChanged(o,p,C),this.renderRows(0,this._bufferService.rows-1),!o||!p)return;const y=o[1]-this._bufferService.buffer.ydisp,w=p[1]-this._bufferService.buffer.ydisp,D=Math.max(y,0),x=Math.min(w,this._bufferService.rows-1);if(D>=this._bufferService.rows||x<0)return;const I=document.createDocumentFragment();if(C){const H=o[0]>p[0];I.appendChild(this._createSelectionElement(D,H?p[0]:o[0],H?o[0]:p[0],x-D+1))}else{const H=y===D?o[0]:0,S=D===w?p[0]:this._bufferService.cols;I.appendChild(this._createSelectionElement(D,H,S));const b=x-D-1;if(I.appendChild(this._createSelectionElement(D+1,0,this._bufferService.cols,b)),D!==x){const L=w===x?p[0]:this._bufferService.cols;I.appendChild(this._createSelectionElement(x,0,L))}}this._selectionContainer.appendChild(I)}_createSelectionElement(o,p,C,y=1){const w=document.createElement("div");return w.style.height=y*this.dimensions.css.cell.height+"px",w.style.top=o*this.dimensions.css.cell.height+"px",w.style.left=p*this.dimensions.css.cell.width+"px",w.style.width=this.dimensions.css.cell.width*(C-p)+"px",w}handleCursorMove(){}_handleOptionsChanged(){this._updateDimensions()}clear(){for(const o of this._rowElements)o.replaceChildren()}renderRows(o,p){const C=this._bufferService.buffer.ybase+this._bufferService.buffer.y,y=Math.min(this._bufferService.buffer.x,this._bufferService.cols-1),w=this._optionsService.rawOptions.cursorBlink;for(let D=o;D<=p;D++){const x=this._rowElements[D],I=D+this._bufferService.buffer.ydisp,H=this._bufferService.buffer.lines.get(I),S=this._optionsService.rawOptions.cursorStyle;this._cellToRowElements[D]&&this._cellToRowElements[D].length===this._bufferService.cols||(this._cellToRowElements[D]=new Int16Array(this._bufferService.cols)),x.replaceChildren(this._rowFactory.createRow(H,I,I===C,S,y,w,this.dimensions.css.cell.width,this._bufferService.cols,this._cellToRowElements[D]))}}get _terminalSelector(){return`.${r}${this._terminalClass}`}_handleLinkHover(o){this._setCellUnderline(o.x1,o.x2,o.y1,o.y2,o.cols,!0)}_handleLinkLeave(o){this._setCellUnderline(o.x1,o.x2,o.y1,o.y2,o.cols,!1)}_setCellUnderline(o,p,C,y,w,D){if(o=this._cellToRowElements[C][o],p=this._cellToRowElements[y][p],o!==-1&&p!==-1)for(;o!==p||C!==y;){const x=this._rowElements[C];if(!x)return;const I=x.children[o];I&&(I.style.textDecoration=D?"underline":"none"),++o>=w&&(o=0,C++)}}};m=h([f(4,t.IInstantiationService),f(5,c.ICharSizeService),f(6,t.IOptionsService),f(7,t.IBufferService),f(8,c.ICoreBrowserService),f(9,c.IThemeService)],m),i.DomRenderer=m},3787:function(T,i,a){var h=this&&this.__decorate||function(u,m,o,p){var C,y=arguments.length,w=y<3?m:p===null?p=Object.getOwnPropertyDescriptor(m,o):p;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")w=Reflect.decorate(u,m,o,p);else for(var D=u.length-1;D>=0;D--)(C=u[D])&&(w=(y<3?C(w):y>3?C(m,o,w):C(m,o))||w);return y>3&&w&&Object.defineProperty(m,o,w),w},f=this&&this.__param||function(u,m){return function(o,p){m(o,p,u)}};Object.defineProperty(i,"__esModule",{value:!0}),i.DomRendererRowFactory=i.CURSOR_STYLE_UNDERLINE_CLASS=i.CURSOR_STYLE_BAR_CLASS=i.CURSOR_STYLE_BLOCK_CLASS=i.CURSOR_BLINK_CLASS=i.CURSOR_CLASS=i.STRIKETHROUGH_CLASS=i.UNDERLINE_CLASS=i.ITALIC_CLASS=i.DIM_CLASS=i.BOLD_CLASS=void 0;const d=a(2223),_=a(643),g=a(511),v=a(2585),c=a(8055),e=a(4725),s=a(4269),n=a(6171),t=a(3734);i.BOLD_CLASS="xterm-bold",i.DIM_CLASS="xterm-dim",i.ITALIC_CLASS="xterm-italic",i.UNDERLINE_CLASS="xterm-underline",i.STRIKETHROUGH_CLASS="xterm-strikethrough",i.CURSOR_CLASS="xterm-cursor",i.CURSOR_BLINK_CLASS="xterm-cursor-blink",i.CURSOR_STYLE_BLOCK_CLASS="xterm-cursor-block",i.CURSOR_STYLE_BAR_CLASS="xterm-cursor-bar",i.CURSOR_STYLE_UNDERLINE_CLASS="xterm-cursor-underline";let r=class{constructor(u,m,o,p,C,y,w){this._document=u,this._characterJoinerService=m,this._optionsService=o,this._coreBrowserService=p,this._coreService=C,this._decorationService=y,this._themeService=w,this._workCell=new g.CellData,this._columnSelectMode=!1}handleSelectionChanged(u,m,o){this._selectionStart=u,this._selectionEnd=m,this._columnSelectMode=o}createRow(u,m,o,p,C,y,w,D,x){const I=this._document.createDocumentFragment(),H=this._characterJoinerService.getJoinedCharacters(m);let S=0;for(let M=Math.min(u.length,D)-1;M>=0;M--)if(u.loadCell(M,this._workCell).getCode()!==_.NULL_CELL_CODE||o&&M===C){S=M+1;break}const b=this._themeService.colors;let L=-1,R=0;for(;R<S;R++){u.loadCell(R,this._workCell);let M=this._workCell.getWidth();if(M===0){x[R]=L;continue}let F=!1,N=R,O=this._workCell;if(H.length>0&&R===H[0][0]){F=!0;const $=H.shift();O=new s.JoinedCellData(this._workCell,u.translateToString(!0,$[0],$[1]),$[1]-$[0]),N=$[1]-1,M=O.getWidth()}const W=this._document.createElement("span");if(M>1&&(W.style.width=w*M+"px"),F&&(W.style.display="inline",C>=R&&C<=N&&(C=R)),!this._coreService.isCursorHidden&&o&&R===C)switch(W.classList.add(i.CURSOR_CLASS),y&&W.classList.add(i.CURSOR_BLINK_CLASS),p){case"bar":W.classList.add(i.CURSOR_STYLE_BAR_CLASS);break;case"underline":W.classList.add(i.CURSOR_STYLE_UNDERLINE_CLASS);break;default:W.classList.add(i.CURSOR_STYLE_BLOCK_CLASS)}if(O.isBold()&&W.classList.add(i.BOLD_CLASS),O.isItalic()&&W.classList.add(i.ITALIC_CLASS),O.isDim()&&W.classList.add(i.DIM_CLASS),O.isInvisible()?W.textContent=_.WHITESPACE_CELL_CHAR:W.textContent=O.getChars()||_.WHITESPACE_CELL_CHAR,O.isUnderline()&&(W.classList.add(`${i.UNDERLINE_CLASS}-${O.extended.underlineStyle}`),W.textContent===" "&&(W.textContent=" "),!O.isUnderlineColorDefault()))if(O.isUnderlineColorRGB())W.style.textDecorationColor=`rgb(${t.AttributeData.toColorRGB(O.getUnderlineColor()).join(",")})`;else{let $=O.getUnderlineColor();this._optionsService.rawOptions.drawBoldTextInBrightColors&&O.isBold()&&$<8&&($+=8),W.style.textDecorationColor=b.ansi[$].css}O.isStrikethrough()&&W.classList.add(i.STRIKETHROUGH_CLASS);let k=O.getFgColor(),E=O.getFgColorMode(),A=O.getBgColor(),B=O.getBgColorMode();const P=!!O.isInverse();if(P){const $=k;k=A,A=$;const ie=E;E=B,B=ie}let j,U,K=!1;this._decorationService.forEachDecorationAtCell(R,m,void 0,$=>{$.options.layer!=="top"&&K||($.backgroundColorRGB&&(B=50331648,A=$.backgroundColorRGB.rgba>>8&16777215,j=$.backgroundColorRGB),$.foregroundColorRGB&&(E=50331648,k=$.foregroundColorRGB.rgba>>8&16777215,U=$.foregroundColorRGB),K=$.options.layer==="top")});const V=this._isCellInSelection(R,m);let q;switch(K||b.selectionForeground&&V&&(E=50331648,k=b.selectionForeground.rgba>>8&16777215,U=b.selectionForeground),V&&(j=this._coreBrowserService.isFocused?b.selectionBackgroundOpaque:b.selectionInactiveBackgroundOpaque,K=!0),K&&W.classList.add("xterm-decoration-top"),B){case 16777216:case 33554432:q=b.ansi[A],W.classList.add(`xterm-bg-${A}`);break;case 50331648:q=c.rgba.toColor(A>>16,A>>8&255,255&A),this._addStyle(W,`background-color:#${l((A>>>0).toString(16),"0",6)}`);break;default:P?(q=b.foreground,W.classList.add(`xterm-bg-${d.INVERTED_DEFAULT_COLOR}`)):q=b.background}switch(j||O.isDim()&&(j=c.color.multiplyOpacity(q,.5)),E){case 16777216:case 33554432:O.isBold()&&k<8&&this._optionsService.rawOptions.drawBoldTextInBrightColors&&(k+=8),this._applyMinimumContrast(W,q,b.ansi[k],O,j,void 0)||W.classList.add(`xterm-fg-${k}`);break;case 50331648:const $=c.rgba.toColor(k>>16&255,k>>8&255,255&k);this._applyMinimumContrast(W,q,$,O,j,U)||this._addStyle(W,`color:#${l(k.toString(16),"0",6)}`);break;default:this._applyMinimumContrast(W,q,b.foreground,O,j,void 0)||P&&W.classList.add(`xterm-fg-${d.INVERTED_DEFAULT_COLOR}`)}I.appendChild(W),x[R]=++L,R=N}return R<D-1&&x.subarray(R).fill(++L),I}_applyMinimumContrast(u,m,o,p,C,y){if(this._optionsService.rawOptions.minimumContrastRatio===1||(0,n.excludeFromContrastRatioDemands)(p.getCode()))return!1;let w;return C||y||(w=this._themeService.colors.contrastCache.getColor(m.rgba,o.rgba)),w===void 0&&(w=c.color.ensureContrastRatio(C||m,y||o,this._optionsService.rawOptions.minimumContrastRatio),this._themeService.colors.contrastCache.setColor((C||m).rgba,(y||o).rgba,w??null)),!!w&&(this._addStyle(u,`color:${w.css}`),!0)}_addStyle(u,m){u.setAttribute("style",`${u.getAttribute("style")||""}${m};`)}_isCellInSelection(u,m){const o=this._selectionStart,p=this._selectionEnd;return!(!o||!p)&&(this._columnSelectMode?o[0]<=p[0]?u>=o[0]&&m>=o[1]&&u<p[0]&&m<=p[1]:u<o[0]&&m>=o[1]&&u>=p[0]&&m<=p[1]:m>o[1]&&m<p[1]||o[1]===p[1]&&m===o[1]&&u>=o[0]&&u<p[0]||o[1]<p[1]&&m===p[1]&&u<p[0]||o[1]<p[1]&&m===o[1]&&u>=o[0])}};function l(u,m,o){for(;u.length<o;)u=m+u;return u}r=h([f(1,e.ICharacterJoinerService),f(2,v.IOptionsService),f(3,e.ICoreBrowserService),f(4,v.ICoreService),f(5,v.IDecorationService),f(6,e.IThemeService)],r),i.DomRendererRowFactory=r},2223:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.TEXT_BASELINE=i.DIM_OPACITY=i.INVERTED_DEFAULT_COLOR=void 0;const h=a(6114);i.INVERTED_DEFAULT_COLOR=257,i.DIM_OPACITY=.5,i.TEXT_BASELINE=h.isFirefox||h.isLegacyEdge?"bottom":"ideographic"},6171:(T,i)=>{function a(h){return 57508<=h&&h<=57558}Object.defineProperty(i,"__esModule",{value:!0}),i.createRenderDimensions=i.excludeFromContrastRatioDemands=i.isRestrictedPowerlineGlyph=i.isPowerlineGlyph=i.throwIfFalsy=void 0,i.throwIfFalsy=function(h){if(!h)throw new Error("value must not be falsy");return h},i.isPowerlineGlyph=a,i.isRestrictedPowerlineGlyph=function(h){return 57520<=h&&h<=57527},i.excludeFromContrastRatioDemands=function(h){return a(h)||function(f){return 9472<=f&&f<=9631}(h)},i.createRenderDimensions=function(){return{css:{canvas:{width:0,height:0},cell:{width:0,height:0}},device:{canvas:{width:0,height:0},cell:{width:0,height:0},char:{width:0,height:0,left:0,top:0}}}}},456:(T,i)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.SelectionModel=void 0,i.SelectionModel=class{constructor(a){this._bufferService=a,this.isSelectAllActive=!1,this.selectionStartLength=0}clearSelection(){this.selectionStart=void 0,this.selectionEnd=void 0,this.isSelectAllActive=!1,this.selectionStartLength=0}get finalSelectionStart(){return this.isSelectAllActive?[0,0]:this.selectionEnd&&this.selectionStart&&this.areSelectionValuesReversed()?this.selectionEnd:this.selectionStart}get finalSelectionEnd(){if(this.isSelectAllActive)return[this._bufferService.cols,this._bufferService.buffer.ybase+this._bufferService.rows-1];if(this.selectionStart){if(!this.selectionEnd||this.areSelectionValuesReversed()){const a=this.selectionStart[0]+this.selectionStartLength;return a>this._bufferService.cols?a%this._bufferService.cols==0?[this._bufferService.cols,this.selectionStart[1]+Math.floor(a/this._bufferService.cols)-1]:[a%this._bufferService.cols,this.selectionStart[1]+Math.floor(a/this._bufferService.cols)]:[a,this.selectionStart[1]]}if(this.selectionStartLength&&this.selectionEnd[1]===this.selectionStart[1]){const a=this.selectionStart[0]+this.selectionStartLength;return a>this._bufferService.cols?[a%this._bufferService.cols,this.selectionStart[1]+Math.floor(a/this._bufferService.cols)]:[Math.max(a,this.selectionEnd[0]),this.selectionEnd[1]]}return this.selectionEnd}}areSelectionValuesReversed(){const a=this.selectionStart,h=this.selectionEnd;return!(!a||!h)&&(a[1]>h[1]||a[1]===h[1]&&a[0]>h[0])}handleTrim(a){return this.selectionStart&&(this.selectionStart[1]-=a),this.selectionEnd&&(this.selectionEnd[1]-=a),this.selectionEnd&&this.selectionEnd[1]<0?(this.clearSelection(),!0):(this.selectionStart&&this.selectionStart[1]<0&&(this.selectionStart[1]=0),!1)}}},428:function(T,i,a){var h=this&&this.__decorate||function(e,s,n,t){var r,l=arguments.length,u=l<3?s:t===null?t=Object.getOwnPropertyDescriptor(s,n):t;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")u=Reflect.decorate(e,s,n,t);else for(var m=e.length-1;m>=0;m--)(r=e[m])&&(u=(l<3?r(u):l>3?r(s,n,u):r(s,n))||u);return l>3&&u&&Object.defineProperty(s,n,u),u},f=this&&this.__param||function(e,s){return function(n,t){s(n,t,e)}};Object.defineProperty(i,"__esModule",{value:!0}),i.CharSizeService=void 0;const d=a(2585),_=a(8460),g=a(844);let v=class extends g.Disposable{constructor(e,s,n){super(),this._optionsService=n,this.width=0,this.height=0,this._onCharSizeChange=this.register(new _.EventEmitter),this.onCharSizeChange=this._onCharSizeChange.event,this._measureStrategy=new c(e,s,this._optionsService),this.register(this._optionsService.onMultipleOptionChange(["fontFamily","fontSize"],()=>this.measure()))}get hasValidSize(){return this.width>0&&this.height>0}measure(){const e=this._measureStrategy.measure();e.width===this.width&&e.height===this.height||(this.width=e.width,this.height=e.height,this._onCharSizeChange.fire())}};v=h([f(2,d.IOptionsService)],v),i.CharSizeService=v;class c{constructor(s,n,t){this._document=s,this._parentElement=n,this._optionsService=t,this._result={width:0,height:0},this._measureElement=this._document.createElement("span"),this._measureElement.classList.add("xterm-char-measure-element"),this._measureElement.textContent="W",this._measureElement.setAttribute("aria-hidden","true"),this._parentElement.appendChild(this._measureElement)}measure(){this._measureElement.style.fontFamily=this._optionsService.rawOptions.fontFamily,this._measureElement.style.fontSize=`${this._optionsService.rawOptions.fontSize}px`;const s=this._measureElement.getBoundingClientRect();return s.width!==0&&s.height!==0&&(this._result.width=s.width,this._result.height=Math.ceil(s.height)),this._result}}},4269:function(T,i,a){var h=this&&this.__decorate||function(s,n,t,r){var l,u=arguments.length,m=u<3?n:r===null?r=Object.getOwnPropertyDescriptor(n,t):r;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")m=Reflect.decorate(s,n,t,r);else for(var o=s.length-1;o>=0;o--)(l=s[o])&&(m=(u<3?l(m):u>3?l(n,t,m):l(n,t))||m);return u>3&&m&&Object.defineProperty(n,t,m),m},f=this&&this.__param||function(s,n){return function(t,r){n(t,r,s)}};Object.defineProperty(i,"__esModule",{value:!0}),i.CharacterJoinerService=i.JoinedCellData=void 0;const d=a(3734),_=a(643),g=a(511),v=a(2585);class c extends d.AttributeData{constructor(n,t,r){super(),this.content=0,this.combinedData="",this.fg=n.fg,this.bg=n.bg,this.combinedData=t,this._width=r}isCombined(){return 2097152}getWidth(){return this._width}getChars(){return this.combinedData}getCode(){return 2097151}setFromCharData(n){throw new Error("not implemented")}getAsCharData(){return[this.fg,this.getChars(),this.getWidth(),this.getCode()]}}i.JoinedCellData=c;let e=class Q{constructor(n){this._bufferService=n,this._characterJoiners=[],this._nextCharacterJoinerId=0,this._workCell=new g.CellData}register(n){const t={id:this._nextCharacterJoinerId++,handler:n};return this._characterJoiners.push(t),t.id}deregister(n){for(let t=0;t<this._characterJoiners.length;t++)if(this._characterJoiners[t].id===n)return this._characterJoiners.splice(t,1),!0;return!1}getJoinedCharacters(n){if(this._characterJoiners.length===0)return[];const t=this._bufferService.buffer.lines.get(n);if(!t||t.length===0)return[];const r=[],l=t.translateToString(!0);let u=0,m=0,o=0,p=t.getFg(0),C=t.getBg(0);for(let y=0;y<t.getTrimmedLength();y++)if(t.loadCell(y,this._workCell),this._workCell.getWidth()!==0){if(this._workCell.fg!==p||this._workCell.bg!==C){if(y-u>1){const w=this._getJoinedRanges(l,o,m,t,u);for(let D=0;D<w.length;D++)r.push(w[D])}u=y,o=m,p=this._workCell.fg,C=this._workCell.bg}m+=this._workCell.getChars().length||_.WHITESPACE_CELL_CHAR.length}if(this._bufferService.cols-u>1){const y=this._getJoinedRanges(l,o,m,t,u);for(let w=0;w<y.length;w++)r.push(y[w])}return r}_getJoinedRanges(n,t,r,l,u){const m=n.substring(t,r);let o=[];try{o=this._characterJoiners[0].handler(m)}catch(p){console.error(p)}for(let p=1;p<this._characterJoiners.length;p++)try{const C=this._characterJoiners[p].handler(m);for(let y=0;y<C.length;y++)Q._mergeRanges(o,C[y])}catch(C){console.error(C)}return this._stringRangesToCellRanges(o,l,u),o}_stringRangesToCellRanges(n,t,r){let l=0,u=!1,m=0,o=n[l];if(o){for(let p=r;p<this._bufferService.cols;p++){const C=t.getWidth(p),y=t.getString(p).length||_.WHITESPACE_CELL_CHAR.length;if(C!==0){if(!u&&o[0]<=m&&(o[0]=p,u=!0),o[1]<=m){if(o[1]=p,o=n[++l],!o)break;o[0]<=m?(o[0]=p,u=!0):u=!1}m+=y}}o&&(o[1]=this._bufferService.cols)}}static _mergeRanges(n,t){let r=!1;for(let l=0;l<n.length;l++){const u=n[l];if(r){if(t[1]<=u[0])return n[l-1][1]=t[1],n;if(t[1]<=u[1])return n[l-1][1]=Math.max(t[1],u[1]),n.splice(l,1),n;n.splice(l,1),l--}else{if(t[1]<=u[0])return n.splice(l,0,t),n;if(t[1]<=u[1])return u[0]=Math.min(t[0],u[0]),n;t[0]<u[1]&&(u[0]=Math.min(t[0],u[0]),r=!0)}}return r?n[n.length-1][1]=t[1]:n.push(t),n}};e=h([f(0,v.IBufferService)],e),i.CharacterJoinerService=e},5114:(T,i)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.CoreBrowserService=void 0,i.CoreBrowserService=class{constructor(a,h){this._textarea=a,this.window=h,this._isFocused=!1,this._cachedIsFocused=void 0,this._textarea.addEventListener("focus",()=>this._isFocused=!0),this._textarea.addEventListener("blur",()=>this._isFocused=!1)}get dpr(){return this.window.devicePixelRatio}get isFocused(){return this._cachedIsFocused===void 0&&(this._cachedIsFocused=this._isFocused&&this._textarea.ownerDocument.hasFocus(),queueMicrotask(()=>this._cachedIsFocused=void 0)),this._cachedIsFocused}}},8934:function(T,i,a){var h=this&&this.__decorate||function(v,c,e,s){var n,t=arguments.length,r=t<3?c:s===null?s=Object.getOwnPropertyDescriptor(c,e):s;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(v,c,e,s);else for(var l=v.length-1;l>=0;l--)(n=v[l])&&(r=(t<3?n(r):t>3?n(c,e,r):n(c,e))||r);return t>3&&r&&Object.defineProperty(c,e,r),r},f=this&&this.__param||function(v,c){return function(e,s){c(e,s,v)}};Object.defineProperty(i,"__esModule",{value:!0}),i.MouseService=void 0;const d=a(4725),_=a(9806);let g=class{constructor(v,c){this._renderService=v,this._charSizeService=c}getCoords(v,c,e,s,n){return(0,_.getCoords)(window,v,c,e,s,this._charSizeService.hasValidSize,this._renderService.dimensions.css.cell.width,this._renderService.dimensions.css.cell.height,n)}getMouseReportCoords(v,c){const e=(0,_.getCoordsRelativeToElement)(window,v,c);if(!(!this._charSizeService.hasValidSize||e[0]<0||e[1]<0||e[0]>=this._renderService.dimensions.css.canvas.width||e[1]>=this._renderService.dimensions.css.canvas.height))return{col:Math.floor(e[0]/this._renderService.dimensions.css.cell.width),row:Math.floor(e[1]/this._renderService.dimensions.css.cell.height),x:Math.floor(e[0]),y:Math.floor(e[1])}}};g=h([f(0,d.IRenderService),f(1,d.ICharSizeService)],g),i.MouseService=g},3230:function(T,i,a){var h=this&&this.__decorate||function(r,l,u,m){var o,p=arguments.length,C=p<3?l:m===null?m=Object.getOwnPropertyDescriptor(l,u):m;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")C=Reflect.decorate(r,l,u,m);else for(var y=r.length-1;y>=0;y--)(o=r[y])&&(C=(p<3?o(C):p>3?o(l,u,C):o(l,u))||C);return p>3&&C&&Object.defineProperty(l,u,C),C},f=this&&this.__param||function(r,l){return function(u,m){l(u,m,r)}};Object.defineProperty(i,"__esModule",{value:!0}),i.RenderService=void 0;const d=a(6193),_=a(8460),g=a(844),v=a(5596),c=a(3656),e=a(2585),s=a(4725),n=a(7226);let t=class extends g.Disposable{constructor(r,l,u,m,o,p,C,y){if(super(),this._rowCount=r,this._charSizeService=m,this._pausedResizeTask=new n.DebouncedIdleTask,this._isPaused=!1,this._needsFullRefresh=!1,this._isNextRenderRedrawOnly=!0,this._needsSelectionRefresh=!1,this._canvasWidth=0,this._canvasHeight=0,this._selectionState={start:void 0,end:void 0,columnSelectMode:!1},this._onDimensionsChange=this.register(new _.EventEmitter),this.onDimensionsChange=this._onDimensionsChange.event,this._onRenderedViewportChange=this.register(new _.EventEmitter),this.onRenderedViewportChange=this._onRenderedViewportChange.event,this._onRender=this.register(new _.EventEmitter),this.onRender=this._onRender.event,this._onRefreshRequest=this.register(new _.EventEmitter),this.onRefreshRequest=this._onRefreshRequest.event,this.register({dispose:()=>{var w;return(w=this._renderer)===null||w===void 0?void 0:w.dispose()}}),this._renderDebouncer=new d.RenderDebouncer(C.window,(w,D)=>this._renderRows(w,D)),this.register(this._renderDebouncer),this._screenDprMonitor=new v.ScreenDprMonitor(C.window),this._screenDprMonitor.setListener(()=>this.handleDevicePixelRatioChange()),this.register(this._screenDprMonitor),this.register(p.onResize(()=>this._fullRefresh())),this.register(p.buffers.onBufferActivate(()=>{var w;return(w=this._renderer)===null||w===void 0?void 0:w.clear()})),this.register(u.onOptionChange(()=>this._handleOptionsChanged())),this.register(this._charSizeService.onCharSizeChange(()=>this.handleCharSizeChanged())),this.register(o.onDecorationRegistered(()=>this._fullRefresh())),this.register(o.onDecorationRemoved(()=>this._fullRefresh())),this.register(u.onMultipleOptionChange(["customGlyphs","drawBoldTextInBrightColors","letterSpacing","lineHeight","fontFamily","fontSize","fontWeight","fontWeightBold","minimumContrastRatio"],()=>{this.clear(),this.handleResize(p.cols,p.rows),this._fullRefresh()})),this.register(u.onMultipleOptionChange(["cursorBlink","cursorStyle"],()=>this.refreshRows(p.buffer.y,p.buffer.y,!0))),this.register((0,c.addDisposableDomListener)(C.window,"resize",()=>this.handleDevicePixelRatioChange())),this.register(y.onChangeColors(()=>this._fullRefresh())),"IntersectionObserver"in C.window){const w=new C.window.IntersectionObserver(D=>this._handleIntersectionChange(D[D.length-1]),{threshold:0});w.observe(l),this.register({dispose:()=>w.disconnect()})}}get dimensions(){return this._renderer.dimensions}_handleIntersectionChange(r){this._isPaused=r.isIntersecting===void 0?r.intersectionRatio===0:!r.isIntersecting,this._isPaused||this._charSizeService.hasValidSize||this._charSizeService.measure(),!this._isPaused&&this._needsFullRefresh&&(this._pausedResizeTask.flush(),this.refreshRows(0,this._rowCount-1),this._needsFullRefresh=!1)}refreshRows(r,l,u=!1){this._isPaused?this._needsFullRefresh=!0:(u||(this._isNextRenderRedrawOnly=!1),this._renderDebouncer.refresh(r,l,this._rowCount))}_renderRows(r,l){this._renderer&&(this._renderer.renderRows(r,l),this._needsSelectionRefresh&&(this._renderer.handleSelectionChanged(this._selectionState.start,this._selectionState.end,this._selectionState.columnSelectMode),this._needsSelectionRefresh=!1),this._isNextRenderRedrawOnly||this._onRenderedViewportChange.fire({start:r,end:l}),this._onRender.fire({start:r,end:l}),this._isNextRenderRedrawOnly=!0)}resize(r,l){this._rowCount=l,this._fireOnCanvasResize()}_handleOptionsChanged(){this._renderer&&(this.refreshRows(0,this._rowCount-1),this._fireOnCanvasResize())}_fireOnCanvasResize(){this._renderer&&(this._renderer.dimensions.css.canvas.width===this._canvasWidth&&this._renderer.dimensions.css.canvas.height===this._canvasHeight||this._onDimensionsChange.fire(this._renderer.dimensions))}hasRenderer(){return!!this._renderer}setRenderer(r){var l;(l=this._renderer)===null||l===void 0||l.dispose(),this._renderer=r,this._renderer.onRequestRedraw(u=>this.refreshRows(u.start,u.end,!0)),this._needsSelectionRefresh=!0,this._fullRefresh()}addRefreshCallback(r){return this._renderDebouncer.addRefreshCallback(r)}_fullRefresh(){this._isPaused?this._needsFullRefresh=!0:this.refreshRows(0,this._rowCount-1)}clearTextureAtlas(){var r,l;this._renderer&&((l=(r=this._renderer).clearTextureAtlas)===null||l===void 0||l.call(r),this._fullRefresh())}handleDevicePixelRatioChange(){this._charSizeService.measure(),this._renderer&&(this._renderer.handleDevicePixelRatioChange(),this.refreshRows(0,this._rowCount-1))}handleResize(r,l){this._renderer&&(this._isPaused?this._pausedResizeTask.set(()=>this._renderer.handleResize(r,l)):this._renderer.handleResize(r,l),this._fullRefresh())}handleCharSizeChanged(){var r;(r=this._renderer)===null||r===void 0||r.handleCharSizeChanged()}handleBlur(){var r;(r=this._renderer)===null||r===void 0||r.handleBlur()}handleFocus(){var r;(r=this._renderer)===null||r===void 0||r.handleFocus()}handleSelectionChanged(r,l,u){var m;this._selectionState.start=r,this._selectionState.end=l,this._selectionState.columnSelectMode=u,(m=this._renderer)===null||m===void 0||m.handleSelectionChanged(r,l,u)}handleCursorMove(){var r;(r=this._renderer)===null||r===void 0||r.handleCursorMove()}clear(){var r;(r=this._renderer)===null||r===void 0||r.clear()}};t=h([f(2,e.IOptionsService),f(3,s.ICharSizeService),f(4,e.IDecorationService),f(5,e.IBufferService),f(6,s.ICoreBrowserService),f(7,s.IThemeService)],t),i.RenderService=t},9312:function(T,i,a){var h=this&&this.__decorate||function(o,p,C,y){var w,D=arguments.length,x=D<3?p:y===null?y=Object.getOwnPropertyDescriptor(p,C):y;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")x=Reflect.decorate(o,p,C,y);else for(var I=o.length-1;I>=0;I--)(w=o[I])&&(x=(D<3?w(x):D>3?w(p,C,x):w(p,C))||x);return D>3&&x&&Object.defineProperty(p,C,x),x},f=this&&this.__param||function(o,p){return function(C,y){p(C,y,o)}};Object.defineProperty(i,"__esModule",{value:!0}),i.SelectionService=void 0;const d=a(6114),_=a(456),g=a(511),v=a(8460),c=a(4725),e=a(2585),s=a(9806),n=a(9504),t=a(844),r=a(4841),l=String.fromCharCode(160),u=new RegExp(l,"g");let m=class extends t.Disposable{constructor(o,p,C,y,w,D,x,I,H){super(),this._element=o,this._screenElement=p,this._linkifier=C,this._bufferService=y,this._coreService=w,this._mouseService=D,this._optionsService=x,this._renderService=I,this._coreBrowserService=H,this._dragScrollAmount=0,this._enabled=!0,this._workCell=new g.CellData,this._mouseDownTimeStamp=0,this._oldHasSelection=!1,this._oldSelectionStart=void 0,this._oldSelectionEnd=void 0,this._onLinuxMouseSelection=this.register(new v.EventEmitter),this.onLinuxMouseSelection=this._onLinuxMouseSelection.event,this._onRedrawRequest=this.register(new v.EventEmitter),this.onRequestRedraw=this._onRedrawRequest.event,this._onSelectionChange=this.register(new v.EventEmitter),this.onSelectionChange=this._onSelectionChange.event,this._onRequestScrollLines=this.register(new v.EventEmitter),this.onRequestScrollLines=this._onRequestScrollLines.event,this._mouseMoveListener=S=>this._handleMouseMove(S),this._mouseUpListener=S=>this._handleMouseUp(S),this._coreService.onUserInput(()=>{this.hasSelection&&this.clearSelection()}),this._trimListener=this._bufferService.buffer.lines.onTrim(S=>this._handleTrim(S)),this.register(this._bufferService.buffers.onBufferActivate(S=>this._handleBufferActivate(S))),this.enable(),this._model=new _.SelectionModel(this._bufferService),this._activeSelectionMode=0,this.register((0,t.toDisposable)(()=>{this._removeMouseDownListeners()}))}reset(){this.clearSelection()}disable(){this.clearSelection(),this._enabled=!1}enable(){this._enabled=!0}get selectionStart(){return this._model.finalSelectionStart}get selectionEnd(){return this._model.finalSelectionEnd}get hasSelection(){const o=this._model.finalSelectionStart,p=this._model.finalSelectionEnd;return!(!o||!p||o[0]===p[0]&&o[1]===p[1])}get selectionText(){const o=this._model.finalSelectionStart,p=this._model.finalSelectionEnd;if(!o||!p)return"";const C=this._bufferService.buffer,y=[];if(this._activeSelectionMode===3){if(o[0]===p[0])return"";const w=o[0]<p[0]?o[0]:p[0],D=o[0]<p[0]?p[0]:o[0];for(let x=o[1];x<=p[1];x++){const I=C.translateBufferLineToString(x,!0,w,D);y.push(I)}}else{const w=o[1]===p[1]?p[0]:void 0;y.push(C.translateBufferLineToString(o[1],!0,o[0],w));for(let D=o[1]+1;D<=p[1]-1;D++){const x=C.lines.get(D),I=C.translateBufferLineToString(D,!0);x!=null&&x.isWrapped?y[y.length-1]+=I:y.push(I)}if(o[1]!==p[1]){const D=C.lines.get(p[1]),x=C.translateBufferLineToString(p[1],!0,0,p[0]);D&&D.isWrapped?y[y.length-1]+=x:y.push(x)}}return y.map(w=>w.replace(u," ")).join(d.isWindows?`\r
`:`
`)}clearSelection(){this._model.clearSelection(),this._removeMouseDownListeners(),this.refresh(),this._onSelectionChange.fire()}refresh(o){this._refreshAnimationFrame||(this._refreshAnimationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>this._refresh())),d.isLinux&&o&&this.selectionText.length&&this._onLinuxMouseSelection.fire(this.selectionText)}_refresh(){this._refreshAnimationFrame=void 0,this._onRedrawRequest.fire({start:this._model.finalSelectionStart,end:this._model.finalSelectionEnd,columnSelectMode:this._activeSelectionMode===3})}_isClickInSelection(o){const p=this._getMouseBufferCoords(o),C=this._model.finalSelectionStart,y=this._model.finalSelectionEnd;return!!(C&&y&&p)&&this._areCoordsInSelection(p,C,y)}isCellInSelection(o,p){const C=this._model.finalSelectionStart,y=this._model.finalSelectionEnd;return!(!C||!y)&&this._areCoordsInSelection([o,p],C,y)}_areCoordsInSelection(o,p,C){return o[1]>p[1]&&o[1]<C[1]||p[1]===C[1]&&o[1]===p[1]&&o[0]>=p[0]&&o[0]<C[0]||p[1]<C[1]&&o[1]===C[1]&&o[0]<C[0]||p[1]<C[1]&&o[1]===p[1]&&o[0]>=p[0]}_selectWordAtCursor(o,p){var C,y;const w=(y=(C=this._linkifier.currentLink)===null||C===void 0?void 0:C.link)===null||y===void 0?void 0:y.range;if(w)return this._model.selectionStart=[w.start.x-1,w.start.y-1],this._model.selectionStartLength=(0,r.getRangeLength)(w,this._bufferService.cols),this._model.selectionEnd=void 0,!0;const D=this._getMouseBufferCoords(o);return!!D&&(this._selectWordAt(D,p),this._model.selectionEnd=void 0,!0)}selectAll(){this._model.isSelectAllActive=!0,this.refresh(),this._onSelectionChange.fire()}selectLines(o,p){this._model.clearSelection(),o=Math.max(o,0),p=Math.min(p,this._bufferService.buffer.lines.length-1),this._model.selectionStart=[0,o],this._model.selectionEnd=[this._bufferService.cols,p],this.refresh(),this._onSelectionChange.fire()}_handleTrim(o){this._model.handleTrim(o)&&this.refresh()}_getMouseBufferCoords(o){const p=this._mouseService.getCoords(o,this._screenElement,this._bufferService.cols,this._bufferService.rows,!0);if(p)return p[0]--,p[1]--,p[1]+=this._bufferService.buffer.ydisp,p}_getMouseEventScrollAmount(o){let p=(0,s.getCoordsRelativeToElement)(this._coreBrowserService.window,o,this._screenElement)[1];const C=this._renderService.dimensions.css.canvas.height;return p>=0&&p<=C?0:(p>C&&(p-=C),p=Math.min(Math.max(p,-50),50),p/=50,p/Math.abs(p)+Math.round(14*p))}shouldForceSelection(o){return d.isMac?o.altKey&&this._optionsService.rawOptions.macOptionClickForcesSelection:o.shiftKey}handleMouseDown(o){if(this._mouseDownTimeStamp=o.timeStamp,(o.button!==2||!this.hasSelection)&&o.button===0){if(!this._enabled){if(!this.shouldForceSelection(o))return;o.stopPropagation()}o.preventDefault(),this._dragScrollAmount=0,this._enabled&&o.shiftKey?this._handleIncrementalClick(o):o.detail===1?this._handleSingleClick(o):o.detail===2?this._handleDoubleClick(o):o.detail===3&&this._handleTripleClick(o),this._addMouseDownListeners(),this.refresh(!0)}}_addMouseDownListeners(){this._screenElement.ownerDocument&&(this._screenElement.ownerDocument.addEventListener("mousemove",this._mouseMoveListener),this._screenElement.ownerDocument.addEventListener("mouseup",this._mouseUpListener)),this._dragScrollIntervalTimer=this._coreBrowserService.window.setInterval(()=>this._dragScroll(),50)}_removeMouseDownListeners(){this._screenElement.ownerDocument&&(this._screenElement.ownerDocument.removeEventListener("mousemove",this._mouseMoveListener),this._screenElement.ownerDocument.removeEventListener("mouseup",this._mouseUpListener)),this._coreBrowserService.window.clearInterval(this._dragScrollIntervalTimer),this._dragScrollIntervalTimer=void 0}_handleIncrementalClick(o){this._model.selectionStart&&(this._model.selectionEnd=this._getMouseBufferCoords(o))}_handleSingleClick(o){if(this._model.selectionStartLength=0,this._model.isSelectAllActive=!1,this._activeSelectionMode=this.shouldColumnSelect(o)?3:0,this._model.selectionStart=this._getMouseBufferCoords(o),!this._model.selectionStart)return;this._model.selectionEnd=void 0;const p=this._bufferService.buffer.lines.get(this._model.selectionStart[1]);p&&p.length!==this._model.selectionStart[0]&&p.hasWidth(this._model.selectionStart[0])===0&&this._model.selectionStart[0]++}_handleDoubleClick(o){this._selectWordAtCursor(o,!0)&&(this._activeSelectionMode=1)}_handleTripleClick(o){const p=this._getMouseBufferCoords(o);p&&(this._activeSelectionMode=2,this._selectLineAt(p[1]))}shouldColumnSelect(o){return o.altKey&&!(d.isMac&&this._optionsService.rawOptions.macOptionClickForcesSelection)}_handleMouseMove(o){if(o.stopImmediatePropagation(),!this._model.selectionStart)return;const p=this._model.selectionEnd?[this._model.selectionEnd[0],this._model.selectionEnd[1]]:null;if(this._model.selectionEnd=this._getMouseBufferCoords(o),!this._model.selectionEnd)return void this.refresh(!0);this._activeSelectionMode===2?this._model.selectionEnd[1]<this._model.selectionStart[1]?this._model.selectionEnd[0]=0:this._model.selectionEnd[0]=this._bufferService.cols:this._activeSelectionMode===1&&this._selectToWordAt(this._model.selectionEnd),this._dragScrollAmount=this._getMouseEventScrollAmount(o),this._activeSelectionMode!==3&&(this._dragScrollAmount>0?this._model.selectionEnd[0]=this._bufferService.cols:this._dragScrollAmount<0&&(this._model.selectionEnd[0]=0));const C=this._bufferService.buffer;if(this._model.selectionEnd[1]<C.lines.length){const y=C.lines.get(this._model.selectionEnd[1]);y&&y.hasWidth(this._model.selectionEnd[0])===0&&this._model.selectionEnd[0]++}p&&p[0]===this._model.selectionEnd[0]&&p[1]===this._model.selectionEnd[1]||this.refresh(!0)}_dragScroll(){if(this._model.selectionEnd&&this._model.selectionStart&&this._dragScrollAmount){this._onRequestScrollLines.fire({amount:this._dragScrollAmount,suppressScrollEvent:!1});const o=this._bufferService.buffer;this._dragScrollAmount>0?(this._activeSelectionMode!==3&&(this._model.selectionEnd[0]=this._bufferService.cols),this._model.selectionEnd[1]=Math.min(o.ydisp+this._bufferService.rows,o.lines.length-1)):(this._activeSelectionMode!==3&&(this._model.selectionEnd[0]=0),this._model.selectionEnd[1]=o.ydisp),this.refresh()}}_handleMouseUp(o){const p=o.timeStamp-this._mouseDownTimeStamp;if(this._removeMouseDownListeners(),this.selectionText.length<=1&&p<500&&o.altKey&&this._optionsService.rawOptions.altClickMovesCursor){if(this._bufferService.buffer.ybase===this._bufferService.buffer.ydisp){const C=this._mouseService.getCoords(o,this._element,this._bufferService.cols,this._bufferService.rows,!1);if(C&&C[0]!==void 0&&C[1]!==void 0){const y=(0,n.moveToCellSequence)(C[0]-1,C[1]-1,this._bufferService,this._coreService.decPrivateModes.applicationCursorKeys);this._coreService.triggerDataEvent(y,!0)}}}else this._fireEventIfSelectionChanged()}_fireEventIfSelectionChanged(){const o=this._model.finalSelectionStart,p=this._model.finalSelectionEnd,C=!(!o||!p||o[0]===p[0]&&o[1]===p[1]);C?o&&p&&(this._oldSelectionStart&&this._oldSelectionEnd&&o[0]===this._oldSelectionStart[0]&&o[1]===this._oldSelectionStart[1]&&p[0]===this._oldSelectionEnd[0]&&p[1]===this._oldSelectionEnd[1]||this._fireOnSelectionChange(o,p,C)):this._oldHasSelection&&this._fireOnSelectionChange(o,p,C)}_fireOnSelectionChange(o,p,C){this._oldSelectionStart=o,this._oldSelectionEnd=p,this._oldHasSelection=C,this._onSelectionChange.fire()}_handleBufferActivate(o){this.clearSelection(),this._trimListener.dispose(),this._trimListener=o.activeBuffer.lines.onTrim(p=>this._handleTrim(p))}_convertViewportColToCharacterIndex(o,p){let C=p;for(let y=0;p>=y;y++){const w=o.loadCell(y,this._workCell).getChars().length;this._workCell.getWidth()===0?C--:w>1&&p!==y&&(C+=w-1)}return C}setSelection(o,p,C){this._model.clearSelection(),this._removeMouseDownListeners(),this._model.selectionStart=[o,p],this._model.selectionStartLength=C,this.refresh(),this._fireEventIfSelectionChanged()}rightClickSelect(o){this._isClickInSelection(o)||(this._selectWordAtCursor(o,!1)&&this.refresh(!0),this._fireEventIfSelectionChanged())}_getWordAt(o,p,C=!0,y=!0){if(o[0]>=this._bufferService.cols)return;const w=this._bufferService.buffer,D=w.lines.get(o[1]);if(!D)return;const x=w.translateBufferLineToString(o[1],!1);let I=this._convertViewportColToCharacterIndex(D,o[0]),H=I;const S=o[0]-I;let b=0,L=0,R=0,M=0;if(x.charAt(I)===" "){for(;I>0&&x.charAt(I-1)===" ";)I--;for(;H<x.length&&x.charAt(H+1)===" ";)H++}else{let O=o[0],W=o[0];D.getWidth(O)===0&&(b++,O--),D.getWidth(W)===2&&(L++,W++);const k=D.getString(W).length;for(k>1&&(M+=k-1,H+=k-1);O>0&&I>0&&!this._isCharWordSeparator(D.loadCell(O-1,this._workCell));){D.loadCell(O-1,this._workCell);const E=this._workCell.getChars().length;this._workCell.getWidth()===0?(b++,O--):E>1&&(R+=E-1,I-=E-1),I--,O--}for(;W<D.length&&H+1<x.length&&!this._isCharWordSeparator(D.loadCell(W+1,this._workCell));){D.loadCell(W+1,this._workCell);const E=this._workCell.getChars().length;this._workCell.getWidth()===2?(L++,W++):E>1&&(M+=E-1,H+=E-1),H++,W++}}H++;let F=I+S-b+R,N=Math.min(this._bufferService.cols,H-I+b+L-R-M);if(p||x.slice(I,H).trim()!==""){if(C&&F===0&&D.getCodePoint(0)!==32){const O=w.lines.get(o[1]-1);if(O&&D.isWrapped&&O.getCodePoint(this._bufferService.cols-1)!==32){const W=this._getWordAt([this._bufferService.cols-1,o[1]-1],!1,!0,!1);if(W){const k=this._bufferService.cols-W.start;F-=k,N+=k}}}if(y&&F+N===this._bufferService.cols&&D.getCodePoint(this._bufferService.cols-1)!==32){const O=w.lines.get(o[1]+1);if(O!=null&&O.isWrapped&&O.getCodePoint(0)!==32){const W=this._getWordAt([0,o[1]+1],!1,!1,!0);W&&(N+=W.length)}}return{start:F,length:N}}}_selectWordAt(o,p){const C=this._getWordAt(o,p);if(C){for(;C.start<0;)C.start+=this._bufferService.cols,o[1]--;this._model.selectionStart=[C.start,o[1]],this._model.selectionStartLength=C.length}}_selectToWordAt(o){const p=this._getWordAt(o,!0);if(p){let C=o[1];for(;p.start<0;)p.start+=this._bufferService.cols,C--;if(!this._model.areSelectionValuesReversed())for(;p.start+p.length>this._bufferService.cols;)p.length-=this._bufferService.cols,C++;this._model.selectionEnd=[this._model.areSelectionValuesReversed()?p.start:p.start+p.length,C]}}_isCharWordSeparator(o){return o.getWidth()!==0&&this._optionsService.rawOptions.wordSeparator.indexOf(o.getChars())>=0}_selectLineAt(o){const p=this._bufferService.buffer.getWrappedRangeForLine(o),C={start:{x:0,y:p.first},end:{x:this._bufferService.cols-1,y:p.last}};this._model.selectionStart=[0,p.first],this._model.selectionEnd=void 0,this._model.selectionStartLength=(0,r.getRangeLength)(C,this._bufferService.cols)}};m=h([f(3,e.IBufferService),f(4,e.ICoreService),f(5,c.IMouseService),f(6,e.IOptionsService),f(7,c.IRenderService),f(8,c.ICoreBrowserService)],m),i.SelectionService=m},4725:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.IThemeService=i.ICharacterJoinerService=i.ISelectionService=i.IRenderService=i.IMouseService=i.ICoreBrowserService=i.ICharSizeService=void 0;const h=a(8343);i.ICharSizeService=(0,h.createDecorator)("CharSizeService"),i.ICoreBrowserService=(0,h.createDecorator)("CoreBrowserService"),i.IMouseService=(0,h.createDecorator)("MouseService"),i.IRenderService=(0,h.createDecorator)("RenderService"),i.ISelectionService=(0,h.createDecorator)("SelectionService"),i.ICharacterJoinerService=(0,h.createDecorator)("CharacterJoinerService"),i.IThemeService=(0,h.createDecorator)("ThemeService")},6731:function(T,i,a){var h=this&&this.__decorate||function(m,o,p,C){var y,w=arguments.length,D=w<3?o:C===null?C=Object.getOwnPropertyDescriptor(o,p):C;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")D=Reflect.decorate(m,o,p,C);else for(var x=m.length-1;x>=0;x--)(y=m[x])&&(D=(w<3?y(D):w>3?y(o,p,D):y(o,p))||D);return w>3&&D&&Object.defineProperty(o,p,D),D},f=this&&this.__param||function(m,o){return function(p,C){o(p,C,m)}};Object.defineProperty(i,"__esModule",{value:!0}),i.ThemeService=i.DEFAULT_ANSI_COLORS=void 0;const d=a(7239),_=a(8055),g=a(8460),v=a(844),c=a(2585),e=_.css.toColor("#ffffff"),s=_.css.toColor("#000000"),n=_.css.toColor("#ffffff"),t=_.css.toColor("#000000"),r={css:"rgba(255, 255, 255, 0.3)",rgba:4294967117};i.DEFAULT_ANSI_COLORS=Object.freeze((()=>{const m=[_.css.toColor("#2e3436"),_.css.toColor("#cc0000"),_.css.toColor("#4e9a06"),_.css.toColor("#c4a000"),_.css.toColor("#3465a4"),_.css.toColor("#75507b"),_.css.toColor("#06989a"),_.css.toColor("#d3d7cf"),_.css.toColor("#555753"),_.css.toColor("#ef2929"),_.css.toColor("#8ae234"),_.css.toColor("#fce94f"),_.css.toColor("#729fcf"),_.css.toColor("#ad7fa8"),_.css.toColor("#34e2e2"),_.css.toColor("#eeeeec")],o=[0,95,135,175,215,255];for(let p=0;p<216;p++){const C=o[p/36%6|0],y=o[p/6%6|0],w=o[p%6];m.push({css:_.channels.toCss(C,y,w),rgba:_.channels.toRgba(C,y,w)})}for(let p=0;p<24;p++){const C=8+10*p;m.push({css:_.channels.toCss(C,C,C),rgba:_.channels.toRgba(C,C,C)})}return m})());let l=class extends v.Disposable{constructor(m){super(),this._optionsService=m,this._onChangeColors=this.register(new g.EventEmitter),this.onChangeColors=this._onChangeColors.event,this._contrastCache=new d.ColorContrastCache,this._colors={foreground:e,background:s,cursor:n,cursorAccent:t,selectionForeground:void 0,selectionBackgroundTransparent:r,selectionBackgroundOpaque:_.color.blend(s,r),selectionInactiveBackgroundTransparent:r,selectionInactiveBackgroundOpaque:_.color.blend(s,r),ansi:i.DEFAULT_ANSI_COLORS.slice(),contrastCache:this._contrastCache},this._updateRestoreColors(),this._setTheme(this._optionsService.rawOptions.theme),this.register(this._optionsService.onSpecificOptionChange("minimumContrastRatio",()=>this._contrastCache.clear())),this.register(this._optionsService.onSpecificOptionChange("theme",()=>this._setTheme(this._optionsService.rawOptions.theme)))}get colors(){return this._colors}_setTheme(m={}){const o=this._colors;if(o.foreground=u(m.foreground,e),o.background=u(m.background,s),o.cursor=u(m.cursor,n),o.cursorAccent=u(m.cursorAccent,t),o.selectionBackgroundTransparent=u(m.selectionBackground,r),o.selectionBackgroundOpaque=_.color.blend(o.background,o.selectionBackgroundTransparent),o.selectionInactiveBackgroundTransparent=u(m.selectionInactiveBackground,o.selectionBackgroundTransparent),o.selectionInactiveBackgroundOpaque=_.color.blend(o.background,o.selectionInactiveBackgroundTransparent),o.selectionForeground=m.selectionForeground?u(m.selectionForeground,_.NULL_COLOR):void 0,o.selectionForeground===_.NULL_COLOR&&(o.selectionForeground=void 0),_.color.isOpaque(o.selectionBackgroundTransparent)&&(o.selectionBackgroundTransparent=_.color.opacity(o.selectionBackgroundTransparent,.3)),_.color.isOpaque(o.selectionInactiveBackgroundTransparent)&&(o.selectionInactiveBackgroundTransparent=_.color.opacity(o.selectionInactiveBackgroundTransparent,.3)),o.ansi=i.DEFAULT_ANSI_COLORS.slice(),o.ansi[0]=u(m.black,i.DEFAULT_ANSI_COLORS[0]),o.ansi[1]=u(m.red,i.DEFAULT_ANSI_COLORS[1]),o.ansi[2]=u(m.green,i.DEFAULT_ANSI_COLORS[2]),o.ansi[3]=u(m.yellow,i.DEFAULT_ANSI_COLORS[3]),o.ansi[4]=u(m.blue,i.DEFAULT_ANSI_COLORS[4]),o.ansi[5]=u(m.magenta,i.DEFAULT_ANSI_COLORS[5]),o.ansi[6]=u(m.cyan,i.DEFAULT_ANSI_COLORS[6]),o.ansi[7]=u(m.white,i.DEFAULT_ANSI_COLORS[7]),o.ansi[8]=u(m.brightBlack,i.DEFAULT_ANSI_COLORS[8]),o.ansi[9]=u(m.brightRed,i.DEFAULT_ANSI_COLORS[9]),o.ansi[10]=u(m.brightGreen,i.DEFAULT_ANSI_COLORS[10]),o.ansi[11]=u(m.brightYellow,i.DEFAULT_ANSI_COLORS[11]),o.ansi[12]=u(m.brightBlue,i.DEFAULT_ANSI_COLORS[12]),o.ansi[13]=u(m.brightMagenta,i.DEFAULT_ANSI_COLORS[13]),o.ansi[14]=u(m.brightCyan,i.DEFAULT_ANSI_COLORS[14]),o.ansi[15]=u(m.brightWhite,i.DEFAULT_ANSI_COLORS[15]),m.extendedAnsi){const p=Math.min(o.ansi.length-16,m.extendedAnsi.length);for(let C=0;C<p;C++)o.ansi[C+16]=u(m.extendedAnsi[C],i.DEFAULT_ANSI_COLORS[C+16])}this._contrastCache.clear(),this._updateRestoreColors(),this._onChangeColors.fire(this.colors)}restoreColor(m){this._restoreColor(m),this._onChangeColors.fire(this.colors)}_restoreColor(m){if(m!==void 0)switch(m){case 256:this._colors.foreground=this._restoreColors.foreground;break;case 257:this._colors.background=this._restoreColors.background;break;case 258:this._colors.cursor=this._restoreColors.cursor;break;default:this._colors.ansi[m]=this._restoreColors.ansi[m]}else for(let o=0;o<this._restoreColors.ansi.length;++o)this._colors.ansi[o]=this._restoreColors.ansi[o]}modifyColors(m){m(this._colors),this._onChangeColors.fire(this.colors)}_updateRestoreColors(){this._restoreColors={foreground:this._colors.foreground,background:this._colors.background,cursor:this._colors.cursor,ansi:this._colors.ansi.slice()}}};function u(m,o){if(m!==void 0)try{return _.css.toColor(m)}catch{}return o}l=h([f(0,c.IOptionsService)],l),i.ThemeService=l},6349:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.CircularList=void 0;const h=a(8460),f=a(844);class d extends f.Disposable{constructor(g){super(),this._maxLength=g,this.onDeleteEmitter=this.register(new h.EventEmitter),this.onDelete=this.onDeleteEmitter.event,this.onInsertEmitter=this.register(new h.EventEmitter),this.onInsert=this.onInsertEmitter.event,this.onTrimEmitter=this.register(new h.EventEmitter),this.onTrim=this.onTrimEmitter.event,this._array=new Array(this._maxLength),this._startIndex=0,this._length=0}get maxLength(){return this._maxLength}set maxLength(g){if(this._maxLength===g)return;const v=new Array(g);for(let c=0;c<Math.min(g,this.length);c++)v[c]=this._array[this._getCyclicIndex(c)];this._array=v,this._maxLength=g,this._startIndex=0}get length(){return this._length}set length(g){if(g>this._length)for(let v=this._length;v<g;v++)this._array[v]=void 0;this._length=g}get(g){return this._array[this._getCyclicIndex(g)]}set(g,v){this._array[this._getCyclicIndex(g)]=v}push(g){this._array[this._getCyclicIndex(this._length)]=g,this._length===this._maxLength?(this._startIndex=++this._startIndex%this._maxLength,this.onTrimEmitter.fire(1)):this._length++}recycle(){if(this._length!==this._maxLength)throw new Error("Can only recycle when the buffer is full");return this._startIndex=++this._startIndex%this._maxLength,this.onTrimEmitter.fire(1),this._array[this._getCyclicIndex(this._length-1)]}get isFull(){return this._length===this._maxLength}pop(){return this._array[this._getCyclicIndex(this._length---1)]}splice(g,v,...c){if(v){for(let e=g;e<this._length-v;e++)this._array[this._getCyclicIndex(e)]=this._array[this._getCyclicIndex(e+v)];this._length-=v,this.onDeleteEmitter.fire({index:g,amount:v})}for(let e=this._length-1;e>=g;e--)this._array[this._getCyclicIndex(e+c.length)]=this._array[this._getCyclicIndex(e)];for(let e=0;e<c.length;e++)this._array[this._getCyclicIndex(g+e)]=c[e];if(c.length&&this.onInsertEmitter.fire({index:g,amount:c.length}),this._length+c.length>this._maxLength){const e=this._length+c.length-this._maxLength;this._startIndex+=e,this._length=this._maxLength,this.onTrimEmitter.fire(e)}else this._length+=c.length}trimStart(g){g>this._length&&(g=this._length),this._startIndex+=g,this._length-=g,this.onTrimEmitter.fire(g)}shiftElements(g,v,c){if(!(v<=0)){if(g<0||g>=this._length)throw new Error("start argument out of range");if(g+c<0)throw new Error("Cannot shift elements in list beyond index 0");if(c>0){for(let s=v-1;s>=0;s--)this.set(g+s+c,this.get(g+s));const e=g+v+c-this._length;if(e>0)for(this._length+=e;this._length>this._maxLength;)this._length--,this._startIndex++,this.onTrimEmitter.fire(1)}else for(let e=0;e<v;e++)this.set(g+e+c,this.get(g+e))}}_getCyclicIndex(g){return(this._startIndex+g)%this._maxLength}}i.CircularList=d},1439:(T,i)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.clone=void 0,i.clone=function a(h,f=5){if(typeof h!="object")return h;const d=Array.isArray(h)?[]:{};for(const _ in h)d[_]=f<=1?h[_]:h[_]&&a(h[_],f-1);return d}},8055:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.contrastRatio=i.toPaddedHex=i.rgba=i.rgb=i.css=i.color=i.channels=i.NULL_COLOR=void 0;const h=a(6114);let f=0,d=0,_=0,g=0;var v,c,e;function s(t){const r=t.toString(16);return r.length<2?"0"+r:r}function n(t,r){return t<r?(r+.05)/(t+.05):(t+.05)/(r+.05)}i.NULL_COLOR={css:"#00000000",rgba:0},function(t){t.toCss=function(r,l,u,m){return m!==void 0?`#${s(r)}${s(l)}${s(u)}${s(m)}`:`#${s(r)}${s(l)}${s(u)}`},t.toRgba=function(r,l,u,m=255){return(r<<24|l<<16|u<<8|m)>>>0}}(v=i.channels||(i.channels={})),function(t){function r(l,u){return g=Math.round(255*u),[f,d,_]=e.toChannels(l.rgba),{css:v.toCss(f,d,_,g),rgba:v.toRgba(f,d,_,g)}}t.blend=function(l,u){if(g=(255&u.rgba)/255,g===1)return{css:u.css,rgba:u.rgba};const m=u.rgba>>24&255,o=u.rgba>>16&255,p=u.rgba>>8&255,C=l.rgba>>24&255,y=l.rgba>>16&255,w=l.rgba>>8&255;return f=C+Math.round((m-C)*g),d=y+Math.round((o-y)*g),_=w+Math.round((p-w)*g),{css:v.toCss(f,d,_),rgba:v.toRgba(f,d,_)}},t.isOpaque=function(l){return(255&l.rgba)==255},t.ensureContrastRatio=function(l,u,m){const o=e.ensureContrastRatio(l.rgba,u.rgba,m);if(o)return e.toColor(o>>24&255,o>>16&255,o>>8&255)},t.opaque=function(l){const u=(255|l.rgba)>>>0;return[f,d,_]=e.toChannels(u),{css:v.toCss(f,d,_),rgba:u}},t.opacity=r,t.multiplyOpacity=function(l,u){return g=255&l.rgba,r(l,g*u/255)},t.toColorRGB=function(l){return[l.rgba>>24&255,l.rgba>>16&255,l.rgba>>8&255]}}(i.color||(i.color={})),function(t){let r,l;if(!h.isNode){const u=document.createElement("canvas");u.width=1,u.height=1;const m=u.getContext("2d",{willReadFrequently:!0});m&&(r=m,r.globalCompositeOperation="copy",l=r.createLinearGradient(0,0,1,1))}t.toColor=function(u){if(u.match(/#[\da-f]{3,8}/i))switch(u.length){case 4:return f=parseInt(u.slice(1,2).repeat(2),16),d=parseInt(u.slice(2,3).repeat(2),16),_=parseInt(u.slice(3,4).repeat(2),16),e.toColor(f,d,_);case 5:return f=parseInt(u.slice(1,2).repeat(2),16),d=parseInt(u.slice(2,3).repeat(2),16),_=parseInt(u.slice(3,4).repeat(2),16),g=parseInt(u.slice(4,5).repeat(2),16),e.toColor(f,d,_,g);case 7:return{css:u,rgba:(parseInt(u.slice(1),16)<<8|255)>>>0};case 9:return{css:u,rgba:parseInt(u.slice(1),16)>>>0}}const m=u.match(/rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(,\s*(0|1|\d?\.(\d+))\s*)?\)/);if(m)return f=parseInt(m[1]),d=parseInt(m[2]),_=parseInt(m[3]),g=Math.round(255*(m[5]===void 0?1:parseFloat(m[5]))),e.toColor(f,d,_,g);if(!r||!l)throw new Error("css.toColor: Unsupported css format");if(r.fillStyle=l,r.fillStyle=u,typeof r.fillStyle!="string")throw new Error("css.toColor: Unsupported css format");if(r.fillRect(0,0,1,1),[f,d,_,g]=r.getImageData(0,0,1,1).data,g!==255)throw new Error("css.toColor: Unsupported css format");return{rgba:v.toRgba(f,d,_,g),css:u}}}(i.css||(i.css={})),function(t){function r(l,u,m){const o=l/255,p=u/255,C=m/255;return .2126*(o<=.03928?o/12.92:Math.pow((o+.055)/1.055,2.4))+.7152*(p<=.03928?p/12.92:Math.pow((p+.055)/1.055,2.4))+.0722*(C<=.03928?C/12.92:Math.pow((C+.055)/1.055,2.4))}t.relativeLuminance=function(l){return r(l>>16&255,l>>8&255,255&l)},t.relativeLuminance2=r}(c=i.rgb||(i.rgb={})),function(t){function r(u,m,o){const p=u>>24&255,C=u>>16&255,y=u>>8&255;let w=m>>24&255,D=m>>16&255,x=m>>8&255,I=n(c.relativeLuminance2(w,D,x),c.relativeLuminance2(p,C,y));for(;I<o&&(w>0||D>0||x>0);)w-=Math.max(0,Math.ceil(.1*w)),D-=Math.max(0,Math.ceil(.1*D)),x-=Math.max(0,Math.ceil(.1*x)),I=n(c.relativeLuminance2(w,D,x),c.relativeLuminance2(p,C,y));return(w<<24|D<<16|x<<8|255)>>>0}function l(u,m,o){const p=u>>24&255,C=u>>16&255,y=u>>8&255;let w=m>>24&255,D=m>>16&255,x=m>>8&255,I=n(c.relativeLuminance2(w,D,x),c.relativeLuminance2(p,C,y));for(;I<o&&(w<255||D<255||x<255);)w=Math.min(255,w+Math.ceil(.1*(255-w))),D=Math.min(255,D+Math.ceil(.1*(255-D))),x=Math.min(255,x+Math.ceil(.1*(255-x))),I=n(c.relativeLuminance2(w,D,x),c.relativeLuminance2(p,C,y));return(w<<24|D<<16|x<<8|255)>>>0}t.ensureContrastRatio=function(u,m,o){const p=c.relativeLuminance(u>>8),C=c.relativeLuminance(m>>8);if(n(p,C)<o){if(C<p){const D=r(u,m,o),x=n(p,c.relativeLuminance(D>>8));if(x<o){const I=l(u,m,o);return x>n(p,c.relativeLuminance(I>>8))?D:I}return D}const y=l(u,m,o),w=n(p,c.relativeLuminance(y>>8));if(w<o){const D=r(u,m,o);return w>n(p,c.relativeLuminance(D>>8))?y:D}return y}},t.reduceLuminance=r,t.increaseLuminance=l,t.toChannels=function(u){return[u>>24&255,u>>16&255,u>>8&255,255&u]},t.toColor=function(u,m,o,p){return{css:v.toCss(u,m,o,p),rgba:v.toRgba(u,m,o,p)}}}(e=i.rgba||(i.rgba={})),i.toPaddedHex=s,i.contrastRatio=n},8969:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.CoreTerminal=void 0;const h=a(844),f=a(2585),d=a(4348),_=a(7866),g=a(744),v=a(7302),c=a(6975),e=a(8460),s=a(1753),n=a(1480),t=a(7994),r=a(9282),l=a(5435),u=a(5981),m=a(2660);let o=!1;class p extends h.Disposable{constructor(y){super(),this._onBinary=this.register(new e.EventEmitter),this.onBinary=this._onBinary.event,this._onData=this.register(new e.EventEmitter),this.onData=this._onData.event,this._onLineFeed=this.register(new e.EventEmitter),this.onLineFeed=this._onLineFeed.event,this._onResize=this.register(new e.EventEmitter),this.onResize=this._onResize.event,this._onWriteParsed=this.register(new e.EventEmitter),this.onWriteParsed=this._onWriteParsed.event,this._onScroll=this.register(new e.EventEmitter),this._instantiationService=new d.InstantiationService,this.optionsService=this.register(new v.OptionsService(y)),this._instantiationService.setService(f.IOptionsService,this.optionsService),this._bufferService=this.register(this._instantiationService.createInstance(g.BufferService)),this._instantiationService.setService(f.IBufferService,this._bufferService),this._logService=this.register(this._instantiationService.createInstance(_.LogService)),this._instantiationService.setService(f.ILogService,this._logService),this.coreService=this.register(this._instantiationService.createInstance(c.CoreService)),this._instantiationService.setService(f.ICoreService,this.coreService),this.coreMouseService=this.register(this._instantiationService.createInstance(s.CoreMouseService)),this._instantiationService.setService(f.ICoreMouseService,this.coreMouseService),this.unicodeService=this.register(this._instantiationService.createInstance(n.UnicodeService)),this._instantiationService.setService(f.IUnicodeService,this.unicodeService),this._charsetService=this._instantiationService.createInstance(t.CharsetService),this._instantiationService.setService(f.ICharsetService,this._charsetService),this._oscLinkService=this._instantiationService.createInstance(m.OscLinkService),this._instantiationService.setService(f.IOscLinkService,this._oscLinkService),this._inputHandler=this.register(new l.InputHandler(this._bufferService,this._charsetService,this.coreService,this._logService,this.optionsService,this._oscLinkService,this.coreMouseService,this.unicodeService)),this.register((0,e.forwardEvent)(this._inputHandler.onLineFeed,this._onLineFeed)),this.register(this._inputHandler),this.register((0,e.forwardEvent)(this._bufferService.onResize,this._onResize)),this.register((0,e.forwardEvent)(this.coreService.onData,this._onData)),this.register((0,e.forwardEvent)(this.coreService.onBinary,this._onBinary)),this.register(this.coreService.onRequestScrollToBottom(()=>this.scrollToBottom())),this.register(this.coreService.onUserInput(()=>this._writeBuffer.handleUserInput())),this.register(this.optionsService.onSpecificOptionChange("windowsMode",w=>this._handleWindowsModeOptionChange(w))),this.register(this._bufferService.onScroll(w=>{this._onScroll.fire({position:this._bufferService.buffer.ydisp,source:0}),this._inputHandler.markRangeDirty(this._bufferService.buffer.scrollTop,this._bufferService.buffer.scrollBottom)})),this.register(this._inputHandler.onScroll(w=>{this._onScroll.fire({position:this._bufferService.buffer.ydisp,source:0}),this._inputHandler.markRangeDirty(this._bufferService.buffer.scrollTop,this._bufferService.buffer.scrollBottom)})),this._writeBuffer=this.register(new u.WriteBuffer((w,D)=>this._inputHandler.parse(w,D))),this.register((0,e.forwardEvent)(this._writeBuffer.onWriteParsed,this._onWriteParsed)),this.register((0,h.toDisposable)(()=>{var w;(w=this._windowsMode)===null||w===void 0||w.dispose(),this._windowsMode=void 0}))}get onScroll(){return this._onScrollApi||(this._onScrollApi=this.register(new e.EventEmitter),this._onScroll.event(y=>{var w;(w=this._onScrollApi)===null||w===void 0||w.fire(y.position)})),this._onScrollApi.event}get cols(){return this._bufferService.cols}get rows(){return this._bufferService.rows}get buffers(){return this._bufferService.buffers}get options(){return this.optionsService.options}set options(y){for(const w in y)this.optionsService.options[w]=y[w]}write(y,w){this._writeBuffer.write(y,w)}writeSync(y,w){this._logService.logLevel<=f.LogLevelEnum.WARN&&!o&&(this._logService.warn("writeSync is unreliable and will be removed soon."),o=!0),this._writeBuffer.writeSync(y,w)}resize(y,w){isNaN(y)||isNaN(w)||(y=Math.max(y,g.MINIMUM_COLS),w=Math.max(w,g.MINIMUM_ROWS),this._bufferService.resize(y,w))}scroll(y,w=!1){this._bufferService.scroll(y,w)}scrollLines(y,w,D){this._bufferService.scrollLines(y,w,D)}scrollPages(y){this._bufferService.scrollPages(y)}scrollToTop(){this._bufferService.scrollToTop()}scrollToBottom(){this._bufferService.scrollToBottom()}scrollToLine(y){this._bufferService.scrollToLine(y)}registerEscHandler(y,w){return this._inputHandler.registerEscHandler(y,w)}registerDcsHandler(y,w){return this._inputHandler.registerDcsHandler(y,w)}registerCsiHandler(y,w){return this._inputHandler.registerCsiHandler(y,w)}registerOscHandler(y,w){return this._inputHandler.registerOscHandler(y,w)}_setup(){this.optionsService.rawOptions.windowsMode&&this._enableWindowsMode()}reset(){this._inputHandler.reset(),this._bufferService.reset(),this._charsetService.reset(),this.coreService.reset(),this.coreMouseService.reset()}_handleWindowsModeOptionChange(y){var w;y?this._enableWindowsMode():((w=this._windowsMode)===null||w===void 0||w.dispose(),this._windowsMode=void 0)}_enableWindowsMode(){if(!this._windowsMode){const y=[];y.push(this.onLineFeed(r.updateWindowsModeWrappedState.bind(null,this._bufferService))),y.push(this.registerCsiHandler({final:"H"},()=>((0,r.updateWindowsModeWrappedState)(this._bufferService),!1))),this._windowsMode={dispose:()=>{for(const w of y)w.dispose()}}}}}i.CoreTerminal=p},8460:(T,i)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.forwardEvent=i.EventEmitter=void 0,i.EventEmitter=class{constructor(){this._listeners=[],this._disposed=!1}get event(){return this._event||(this._event=a=>(this._listeners.push(a),{dispose:()=>{if(!this._disposed){for(let h=0;h<this._listeners.length;h++)if(this._listeners[h]===a)return void this._listeners.splice(h,1)}}})),this._event}fire(a,h){const f=[];for(let d=0;d<this._listeners.length;d++)f.push(this._listeners[d]);for(let d=0;d<f.length;d++)f[d].call(void 0,a,h)}dispose(){this._listeners&&(this._listeners.length=0),this._disposed=!0}},i.forwardEvent=function(a,h){return a(f=>h.fire(f))}},5435:function(T,i,a){var h=this&&this.__decorate||function(H,S,b,L){var R,M=arguments.length,F=M<3?S:L===null?L=Object.getOwnPropertyDescriptor(S,b):L;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")F=Reflect.decorate(H,S,b,L);else for(var N=H.length-1;N>=0;N--)(R=H[N])&&(F=(M<3?R(F):M>3?R(S,b,F):R(S,b))||F);return M>3&&F&&Object.defineProperty(S,b,F),F},f=this&&this.__param||function(H,S){return function(b,L){S(b,L,H)}};Object.defineProperty(i,"__esModule",{value:!0}),i.InputHandler=i.WindowsOptionsReportType=void 0;const d=a(2584),_=a(7116),g=a(2015),v=a(844),c=a(482),e=a(8437),s=a(8460),n=a(643),t=a(511),r=a(3734),l=a(2585),u=a(6242),m=a(6351),o=a(5941),p={"(":0,")":1,"*":2,"+":3,"-":1,".":2},C=131072;function y(H,S){if(H>24)return S.setWinLines||!1;switch(H){case 1:return!!S.restoreWin;case 2:return!!S.minimizeWin;case 3:return!!S.setWinPosition;case 4:return!!S.setWinSizePixels;case 5:return!!S.raiseWin;case 6:return!!S.lowerWin;case 7:return!!S.refreshWin;case 8:return!!S.setWinSizeChars;case 9:return!!S.maximizeWin;case 10:return!!S.fullscreenWin;case 11:return!!S.getWinState;case 13:return!!S.getWinPosition;case 14:return!!S.getWinSizePixels;case 15:return!!S.getScreenSizePixels;case 16:return!!S.getCellSizePixels;case 18:return!!S.getWinSizeChars;case 19:return!!S.getScreenSizeChars;case 20:return!!S.getIconTitle;case 21:return!!S.getWinTitle;case 22:return!!S.pushTitle;case 23:return!!S.popTitle;case 24:return!!S.setWinLines}return!1}var w;(function(H){H[H.GET_WIN_SIZE_PIXELS=0]="GET_WIN_SIZE_PIXELS",H[H.GET_CELL_SIZE_PIXELS=1]="GET_CELL_SIZE_PIXELS"})(w=i.WindowsOptionsReportType||(i.WindowsOptionsReportType={}));let D=0;class x extends v.Disposable{constructor(S,b,L,R,M,F,N,O,W=new g.EscapeSequenceParser){super(),this._bufferService=S,this._charsetService=b,this._coreService=L,this._logService=R,this._optionsService=M,this._oscLinkService=F,this._coreMouseService=N,this._unicodeService=O,this._parser=W,this._parseBuffer=new Uint32Array(4096),this._stringDecoder=new c.StringToUtf32,this._utf8Decoder=new c.Utf8ToUtf32,this._workCell=new t.CellData,this._windowTitle="",this._iconName="",this._windowTitleStack=[],this._iconNameStack=[],this._curAttrData=e.DEFAULT_ATTR_DATA.clone(),this._eraseAttrDataInternal=e.DEFAULT_ATTR_DATA.clone(),this._onRequestBell=this.register(new s.EventEmitter),this.onRequestBell=this._onRequestBell.event,this._onRequestRefreshRows=this.register(new s.EventEmitter),this.onRequestRefreshRows=this._onRequestRefreshRows.event,this._onRequestReset=this.register(new s.EventEmitter),this.onRequestReset=this._onRequestReset.event,this._onRequestSendFocus=this.register(new s.EventEmitter),this.onRequestSendFocus=this._onRequestSendFocus.event,this._onRequestSyncScrollBar=this.register(new s.EventEmitter),this.onRequestSyncScrollBar=this._onRequestSyncScrollBar.event,this._onRequestWindowsOptionsReport=this.register(new s.EventEmitter),this.onRequestWindowsOptionsReport=this._onRequestWindowsOptionsReport.event,this._onA11yChar=this.register(new s.EventEmitter),this.onA11yChar=this._onA11yChar.event,this._onA11yTab=this.register(new s.EventEmitter),this.onA11yTab=this._onA11yTab.event,this._onCursorMove=this.register(new s.EventEmitter),this.onCursorMove=this._onCursorMove.event,this._onLineFeed=this.register(new s.EventEmitter),this.onLineFeed=this._onLineFeed.event,this._onScroll=this.register(new s.EventEmitter),this.onScroll=this._onScroll.event,this._onTitleChange=this.register(new s.EventEmitter),this.onTitleChange=this._onTitleChange.event,this._onColor=this.register(new s.EventEmitter),this.onColor=this._onColor.event,this._parseStack={paused:!1,cursorStartX:0,cursorStartY:0,decodedLength:0,position:0},this._specialColors=[256,257,258],this.register(this._parser),this._dirtyRowTracker=new I(this._bufferService),this._activeBuffer=this._bufferService.buffer,this.register(this._bufferService.buffers.onBufferActivate(k=>this._activeBuffer=k.activeBuffer)),this._parser.setCsiHandlerFallback((k,E)=>{this._logService.debug("Unknown CSI code: ",{identifier:this._parser.identToString(k),params:E.toArray()})}),this._parser.setEscHandlerFallback(k=>{this._logService.debug("Unknown ESC code: ",{identifier:this._parser.identToString(k)})}),this._parser.setExecuteHandlerFallback(k=>{this._logService.debug("Unknown EXECUTE code: ",{code:k})}),this._parser.setOscHandlerFallback((k,E,A)=>{this._logService.debug("Unknown OSC code: ",{identifier:k,action:E,data:A})}),this._parser.setDcsHandlerFallback((k,E,A)=>{E==="HOOK"&&(A=A.toArray()),this._logService.debug("Unknown DCS code: ",{identifier:this._parser.identToString(k),action:E,payload:A})}),this._parser.setPrintHandler((k,E,A)=>this.print(k,E,A)),this._parser.registerCsiHandler({final:"@"},k=>this.insertChars(k)),this._parser.registerCsiHandler({intermediates:" ",final:"@"},k=>this.scrollLeft(k)),this._parser.registerCsiHandler({final:"A"},k=>this.cursorUp(k)),this._parser.registerCsiHandler({intermediates:" ",final:"A"},k=>this.scrollRight(k)),this._parser.registerCsiHandler({final:"B"},k=>this.cursorDown(k)),this._parser.registerCsiHandler({final:"C"},k=>this.cursorForward(k)),this._parser.registerCsiHandler({final:"D"},k=>this.cursorBackward(k)),this._parser.registerCsiHandler({final:"E"},k=>this.cursorNextLine(k)),this._parser.registerCsiHandler({final:"F"},k=>this.cursorPrecedingLine(k)),this._parser.registerCsiHandler({final:"G"},k=>this.cursorCharAbsolute(k)),this._parser.registerCsiHandler({final:"H"},k=>this.cursorPosition(k)),this._parser.registerCsiHandler({final:"I"},k=>this.cursorForwardTab(k)),this._parser.registerCsiHandler({final:"J"},k=>this.eraseInDisplay(k,!1)),this._parser.registerCsiHandler({prefix:"?",final:"J"},k=>this.eraseInDisplay(k,!0)),this._parser.registerCsiHandler({final:"K"},k=>this.eraseInLine(k,!1)),this._parser.registerCsiHandler({prefix:"?",final:"K"},k=>this.eraseInLine(k,!0)),this._parser.registerCsiHandler({final:"L"},k=>this.insertLines(k)),this._parser.registerCsiHandler({final:"M"},k=>this.deleteLines(k)),this._parser.registerCsiHandler({final:"P"},k=>this.deleteChars(k)),this._parser.registerCsiHandler({final:"S"},k=>this.scrollUp(k)),this._parser.registerCsiHandler({final:"T"},k=>this.scrollDown(k)),this._parser.registerCsiHandler({final:"X"},k=>this.eraseChars(k)),this._parser.registerCsiHandler({final:"Z"},k=>this.cursorBackwardTab(k)),this._parser.registerCsiHandler({final:"`"},k=>this.charPosAbsolute(k)),this._parser.registerCsiHandler({final:"a"},k=>this.hPositionRelative(k)),this._parser.registerCsiHandler({final:"b"},k=>this.repeatPrecedingCharacter(k)),this._parser.registerCsiHandler({final:"c"},k=>this.sendDeviceAttributesPrimary(k)),this._parser.registerCsiHandler({prefix:">",final:"c"},k=>this.sendDeviceAttributesSecondary(k)),this._parser.registerCsiHandler({final:"d"},k=>this.linePosAbsolute(k)),this._parser.registerCsiHandler({final:"e"},k=>this.vPositionRelative(k)),this._parser.registerCsiHandler({final:"f"},k=>this.hVPosition(k)),this._parser.registerCsiHandler({final:"g"},k=>this.tabClear(k)),this._parser.registerCsiHandler({final:"h"},k=>this.setMode(k)),this._parser.registerCsiHandler({prefix:"?",final:"h"},k=>this.setModePrivate(k)),this._parser.registerCsiHandler({final:"l"},k=>this.resetMode(k)),this._parser.registerCsiHandler({prefix:"?",final:"l"},k=>this.resetModePrivate(k)),this._parser.registerCsiHandler({final:"m"},k=>this.charAttributes(k)),this._parser.registerCsiHandler({final:"n"},k=>this.deviceStatus(k)),this._parser.registerCsiHandler({prefix:"?",final:"n"},k=>this.deviceStatusPrivate(k)),this._parser.registerCsiHandler({intermediates:"!",final:"p"},k=>this.softReset(k)),this._parser.registerCsiHandler({intermediates:" ",final:"q"},k=>this.setCursorStyle(k)),this._parser.registerCsiHandler({final:"r"},k=>this.setScrollRegion(k)),this._parser.registerCsiHandler({final:"s"},k=>this.saveCursor(k)),this._parser.registerCsiHandler({final:"t"},k=>this.windowOptions(k)),this._parser.registerCsiHandler({final:"u"},k=>this.restoreCursor(k)),this._parser.registerCsiHandler({intermediates:"'",final:"}"},k=>this.insertColumns(k)),this._parser.registerCsiHandler({intermediates:"'",final:"~"},k=>this.deleteColumns(k)),this._parser.registerCsiHandler({intermediates:'"',final:"q"},k=>this.selectProtected(k)),this._parser.registerCsiHandler({intermediates:"$",final:"p"},k=>this.requestMode(k,!0)),this._parser.registerCsiHandler({prefix:"?",intermediates:"$",final:"p"},k=>this.requestMode(k,!1)),this._parser.setExecuteHandler(d.C0.BEL,()=>this.bell()),this._parser.setExecuteHandler(d.C0.LF,()=>this.lineFeed()),this._parser.setExecuteHandler(d.C0.VT,()=>this.lineFeed()),this._parser.setExecuteHandler(d.C0.FF,()=>this.lineFeed()),this._parser.setExecuteHandler(d.C0.CR,()=>this.carriageReturn()),this._parser.setExecuteHandler(d.C0.BS,()=>this.backspace()),this._parser.setExecuteHandler(d.C0.HT,()=>this.tab()),this._parser.setExecuteHandler(d.C0.SO,()=>this.shiftOut()),this._parser.setExecuteHandler(d.C0.SI,()=>this.shiftIn()),this._parser.setExecuteHandler(d.C1.IND,()=>this.index()),this._parser.setExecuteHandler(d.C1.NEL,()=>this.nextLine()),this._parser.setExecuteHandler(d.C1.HTS,()=>this.tabSet()),this._parser.registerOscHandler(0,new u.OscHandler(k=>(this.setTitle(k),this.setIconName(k),!0))),this._parser.registerOscHandler(1,new u.OscHandler(k=>this.setIconName(k))),this._parser.registerOscHandler(2,new u.OscHandler(k=>this.setTitle(k))),this._parser.registerOscHandler(4,new u.OscHandler(k=>this.setOrReportIndexedColor(k))),this._parser.registerOscHandler(8,new u.OscHandler(k=>this.setHyperlink(k))),this._parser.registerOscHandler(10,new u.OscHandler(k=>this.setOrReportFgColor(k))),this._parser.registerOscHandler(11,new u.OscHandler(k=>this.setOrReportBgColor(k))),this._parser.registerOscHandler(12,new u.OscHandler(k=>this.setOrReportCursorColor(k))),this._parser.registerOscHandler(104,new u.OscHandler(k=>this.restoreIndexedColor(k))),this._parser.registerOscHandler(110,new u.OscHandler(k=>this.restoreFgColor(k))),this._parser.registerOscHandler(111,new u.OscHandler(k=>this.restoreBgColor(k))),this._parser.registerOscHandler(112,new u.OscHandler(k=>this.restoreCursorColor(k))),this._parser.registerEscHandler({final:"7"},()=>this.saveCursor()),this._parser.registerEscHandler({final:"8"},()=>this.restoreCursor()),this._parser.registerEscHandler({final:"D"},()=>this.index()),this._parser.registerEscHandler({final:"E"},()=>this.nextLine()),this._parser.registerEscHandler({final:"H"},()=>this.tabSet()),this._parser.registerEscHandler({final:"M"},()=>this.reverseIndex()),this._parser.registerEscHandler({final:"="},()=>this.keypadApplicationMode()),this._parser.registerEscHandler({final:">"},()=>this.keypadNumericMode()),this._parser.registerEscHandler({final:"c"},()=>this.fullReset()),this._parser.registerEscHandler({final:"n"},()=>this.setgLevel(2)),this._parser.registerEscHandler({final:"o"},()=>this.setgLevel(3)),this._parser.registerEscHandler({final:"|"},()=>this.setgLevel(3)),this._parser.registerEscHandler({final:"}"},()=>this.setgLevel(2)),this._parser.registerEscHandler({final:"~"},()=>this.setgLevel(1)),this._parser.registerEscHandler({intermediates:"%",final:"@"},()=>this.selectDefaultCharset()),this._parser.registerEscHandler({intermediates:"%",final:"G"},()=>this.selectDefaultCharset());for(const k in _.CHARSETS)this._parser.registerEscHandler({intermediates:"(",final:k},()=>this.selectCharset("("+k)),this._parser.registerEscHandler({intermediates:")",final:k},()=>this.selectCharset(")"+k)),this._parser.registerEscHandler({intermediates:"*",final:k},()=>this.selectCharset("*"+k)),this._parser.registerEscHandler({intermediates:"+",final:k},()=>this.selectCharset("+"+k)),this._parser.registerEscHandler({intermediates:"-",final:k},()=>this.selectCharset("-"+k)),this._parser.registerEscHandler({intermediates:".",final:k},()=>this.selectCharset("."+k)),this._parser.registerEscHandler({intermediates:"/",final:k},()=>this.selectCharset("/"+k));this._parser.registerEscHandler({intermediates:"#",final:"8"},()=>this.screenAlignmentPattern()),this._parser.setErrorHandler(k=>(this._logService.error("Parsing error: ",k),k)),this._parser.registerDcsHandler({intermediates:"$",final:"q"},new m.DcsHandler((k,E)=>this.requestStatusString(k,E)))}getAttrData(){return this._curAttrData}_preserveStack(S,b,L,R){this._parseStack.paused=!0,this._parseStack.cursorStartX=S,this._parseStack.cursorStartY=b,this._parseStack.decodedLength=L,this._parseStack.position=R}_logSlowResolvingAsync(S){this._logService.logLevel<=l.LogLevelEnum.WARN&&Promise.race([S,new Promise((b,L)=>setTimeout(()=>L("#SLOW_TIMEOUT"),5e3))]).catch(b=>{if(b!=="#SLOW_TIMEOUT")throw b;console.warn("async parser handler taking longer than 5000 ms")})}_getCurrentLinkId(){return this._curAttrData.extended.urlId}parse(S,b){let L,R=this._activeBuffer.x,M=this._activeBuffer.y,F=0;const N=this._parseStack.paused;if(N){if(L=this._parser.parse(this._parseBuffer,this._parseStack.decodedLength,b))return this._logSlowResolvingAsync(L),L;R=this._parseStack.cursorStartX,M=this._parseStack.cursorStartY,this._parseStack.paused=!1,S.length>C&&(F=this._parseStack.position+C)}if(this._logService.logLevel<=l.LogLevelEnum.DEBUG&&this._logService.debug("parsing data"+(typeof S=="string"?` "${S}"`:` "${Array.prototype.map.call(S,O=>String.fromCharCode(O)).join("")}"`),typeof S=="string"?S.split("").map(O=>O.charCodeAt(0)):S),this._parseBuffer.length<S.length&&this._parseBuffer.length<C&&(this._parseBuffer=new Uint32Array(Math.min(S.length,C))),N||this._dirtyRowTracker.clearRange(),S.length>C)for(let O=F;O<S.length;O+=C){const W=O+C<S.length?O+C:S.length,k=typeof S=="string"?this._stringDecoder.decode(S.substring(O,W),this._parseBuffer):this._utf8Decoder.decode(S.subarray(O,W),this._parseBuffer);if(L=this._parser.parse(this._parseBuffer,k))return this._preserveStack(R,M,k,O),this._logSlowResolvingAsync(L),L}else if(!N){const O=typeof S=="string"?this._stringDecoder.decode(S,this._parseBuffer):this._utf8Decoder.decode(S,this._parseBuffer);if(L=this._parser.parse(this._parseBuffer,O))return this._preserveStack(R,M,O,0),this._logSlowResolvingAsync(L),L}this._activeBuffer.x===R&&this._activeBuffer.y===M||this._onCursorMove.fire(),this._onRequestRefreshRows.fire(this._dirtyRowTracker.start,this._dirtyRowTracker.end)}print(S,b,L){let R,M;const F=this._charsetService.charset,N=this._optionsService.rawOptions.screenReaderMode,O=this._bufferService.cols,W=this._coreService.decPrivateModes.wraparound,k=this._coreService.modes.insertMode,E=this._curAttrData;let A=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._activeBuffer.x&&L-b>0&&A.getWidth(this._activeBuffer.x-1)===2&&A.setCellFromCodePoint(this._activeBuffer.x-1,0,1,E.fg,E.bg,E.extended);for(let B=b;B<L;++B){if(R=S[B],M=this._unicodeService.wcwidth(R),R<127&&F){const P=F[String.fromCharCode(R)];P&&(R=P.charCodeAt(0))}if(N&&this._onA11yChar.fire((0,c.stringFromCodePoint)(R)),this._getCurrentLinkId()&&this._oscLinkService.addLineToLink(this._getCurrentLinkId(),this._activeBuffer.ybase+this._activeBuffer.y),M||!this._activeBuffer.x){if(this._activeBuffer.x+M-1>=O){if(W){for(;this._activeBuffer.x<O;)A.setCellFromCodePoint(this._activeBuffer.x++,0,1,E.fg,E.bg,E.extended);this._activeBuffer.x=0,this._activeBuffer.y++,this._activeBuffer.y===this._activeBuffer.scrollBottom+1?(this._activeBuffer.y--,this._bufferService.scroll(this._eraseAttrData(),!0)):(this._activeBuffer.y>=this._bufferService.rows&&(this._activeBuffer.y=this._bufferService.rows-1),this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).isWrapped=!0),A=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y)}else if(this._activeBuffer.x=O-1,M===2)continue}if(k&&(A.insertCells(this._activeBuffer.x,M,this._activeBuffer.getNullCell(E),E),A.getWidth(O-1)===2&&A.setCellFromCodePoint(O-1,n.NULL_CELL_CODE,n.NULL_CELL_WIDTH,E.fg,E.bg,E.extended)),A.setCellFromCodePoint(this._activeBuffer.x++,R,M,E.fg,E.bg,E.extended),M>0)for(;--M;)A.setCellFromCodePoint(this._activeBuffer.x++,0,0,E.fg,E.bg,E.extended)}else A.getWidth(this._activeBuffer.x-1)?A.addCodepointToCell(this._activeBuffer.x-1,R):A.addCodepointToCell(this._activeBuffer.x-2,R)}L-b>0&&(A.loadCell(this._activeBuffer.x-1,this._workCell),this._workCell.getWidth()===2||this._workCell.getCode()>65535?this._parser.precedingCodepoint=0:this._workCell.isCombined()?this._parser.precedingCodepoint=this._workCell.getChars().charCodeAt(0):this._parser.precedingCodepoint=this._workCell.content),this._activeBuffer.x<O&&L-b>0&&A.getWidth(this._activeBuffer.x)===0&&!A.hasContent(this._activeBuffer.x)&&A.setCellFromCodePoint(this._activeBuffer.x,0,1,E.fg,E.bg,E.extended),this._dirtyRowTracker.markDirty(this._activeBuffer.y)}registerCsiHandler(S,b){return S.final!=="t"||S.prefix||S.intermediates?this._parser.registerCsiHandler(S,b):this._parser.registerCsiHandler(S,L=>!y(L.params[0],this._optionsService.rawOptions.windowOptions)||b(L))}registerDcsHandler(S,b){return this._parser.registerDcsHandler(S,new m.DcsHandler(b))}registerEscHandler(S,b){return this._parser.registerEscHandler(S,b)}registerOscHandler(S,b){return this._parser.registerOscHandler(S,new u.OscHandler(b))}bell(){return this._onRequestBell.fire(),!0}lineFeed(){return this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._optionsService.rawOptions.convertEol&&(this._activeBuffer.x=0),this._activeBuffer.y++,this._activeBuffer.y===this._activeBuffer.scrollBottom+1?(this._activeBuffer.y--,this._bufferService.scroll(this._eraseAttrData())):this._activeBuffer.y>=this._bufferService.rows&&(this._activeBuffer.y=this._bufferService.rows-1),this._activeBuffer.x>=this._bufferService.cols&&this._activeBuffer.x--,this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._onLineFeed.fire(),!0}carriageReturn(){return this._activeBuffer.x=0,!0}backspace(){var S;if(!this._coreService.decPrivateModes.reverseWraparound)return this._restrictCursor(),this._activeBuffer.x>0&&this._activeBuffer.x--,!0;if(this._restrictCursor(this._bufferService.cols),this._activeBuffer.x>0)this._activeBuffer.x--;else if(this._activeBuffer.x===0&&this._activeBuffer.y>this._activeBuffer.scrollTop&&this._activeBuffer.y<=this._activeBuffer.scrollBottom&&(!((S=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y))===null||S===void 0)&&S.isWrapped)){this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).isWrapped=!1,this._activeBuffer.y--,this._activeBuffer.x=this._bufferService.cols-1;const b=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);b.hasWidth(this._activeBuffer.x)&&!b.hasContent(this._activeBuffer.x)&&this._activeBuffer.x--}return this._restrictCursor(),!0}tab(){if(this._activeBuffer.x>=this._bufferService.cols)return!0;const S=this._activeBuffer.x;return this._activeBuffer.x=this._activeBuffer.nextStop(),this._optionsService.rawOptions.screenReaderMode&&this._onA11yTab.fire(this._activeBuffer.x-S),!0}shiftOut(){return this._charsetService.setgLevel(1),!0}shiftIn(){return this._charsetService.setgLevel(0),!0}_restrictCursor(S=this._bufferService.cols-1){this._activeBuffer.x=Math.min(S,Math.max(0,this._activeBuffer.x)),this._activeBuffer.y=this._coreService.decPrivateModes.origin?Math.min(this._activeBuffer.scrollBottom,Math.max(this._activeBuffer.scrollTop,this._activeBuffer.y)):Math.min(this._bufferService.rows-1,Math.max(0,this._activeBuffer.y)),this._dirtyRowTracker.markDirty(this._activeBuffer.y)}_setCursor(S,b){this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._coreService.decPrivateModes.origin?(this._activeBuffer.x=S,this._activeBuffer.y=this._activeBuffer.scrollTop+b):(this._activeBuffer.x=S,this._activeBuffer.y=b),this._restrictCursor(),this._dirtyRowTracker.markDirty(this._activeBuffer.y)}_moveCursor(S,b){this._restrictCursor(),this._setCursor(this._activeBuffer.x+S,this._activeBuffer.y+b)}cursorUp(S){const b=this._activeBuffer.y-this._activeBuffer.scrollTop;return b>=0?this._moveCursor(0,-Math.min(b,S.params[0]||1)):this._moveCursor(0,-(S.params[0]||1)),!0}cursorDown(S){const b=this._activeBuffer.scrollBottom-this._activeBuffer.y;return b>=0?this._moveCursor(0,Math.min(b,S.params[0]||1)):this._moveCursor(0,S.params[0]||1),!0}cursorForward(S){return this._moveCursor(S.params[0]||1,0),!0}cursorBackward(S){return this._moveCursor(-(S.params[0]||1),0),!0}cursorNextLine(S){return this.cursorDown(S),this._activeBuffer.x=0,!0}cursorPrecedingLine(S){return this.cursorUp(S),this._activeBuffer.x=0,!0}cursorCharAbsolute(S){return this._setCursor((S.params[0]||1)-1,this._activeBuffer.y),!0}cursorPosition(S){return this._setCursor(S.length>=2?(S.params[1]||1)-1:0,(S.params[0]||1)-1),!0}charPosAbsolute(S){return this._setCursor((S.params[0]||1)-1,this._activeBuffer.y),!0}hPositionRelative(S){return this._moveCursor(S.params[0]||1,0),!0}linePosAbsolute(S){return this._setCursor(this._activeBuffer.x,(S.params[0]||1)-1),!0}vPositionRelative(S){return this._moveCursor(0,S.params[0]||1),!0}hVPosition(S){return this.cursorPosition(S),!0}tabClear(S){const b=S.params[0];return b===0?delete this._activeBuffer.tabs[this._activeBuffer.x]:b===3&&(this._activeBuffer.tabs={}),!0}cursorForwardTab(S){if(this._activeBuffer.x>=this._bufferService.cols)return!0;let b=S.params[0]||1;for(;b--;)this._activeBuffer.x=this._activeBuffer.nextStop();return!0}cursorBackwardTab(S){if(this._activeBuffer.x>=this._bufferService.cols)return!0;let b=S.params[0]||1;for(;b--;)this._activeBuffer.x=this._activeBuffer.prevStop();return!0}selectProtected(S){const b=S.params[0];return b===1&&(this._curAttrData.bg|=536870912),b!==2&&b!==0||(this._curAttrData.bg&=-536870913),!0}_eraseInBufferLine(S,b,L,R=!1,M=!1){const F=this._activeBuffer.lines.get(this._activeBuffer.ybase+S);F.replaceCells(b,L,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData(),M),R&&(F.isWrapped=!1)}_resetBufferLine(S,b=!1){const L=this._activeBuffer.lines.get(this._activeBuffer.ybase+S);L.fill(this._activeBuffer.getNullCell(this._eraseAttrData()),b),this._bufferService.buffer.clearMarkers(this._activeBuffer.ybase+S),L.isWrapped=!1}eraseInDisplay(S,b=!1){let L;switch(this._restrictCursor(this._bufferService.cols),S.params[0]){case 0:for(L=this._activeBuffer.y,this._dirtyRowTracker.markDirty(L),this._eraseInBufferLine(L++,this._activeBuffer.x,this._bufferService.cols,this._activeBuffer.x===0,b);L<this._bufferService.rows;L++)this._resetBufferLine(L,b);this._dirtyRowTracker.markDirty(L);break;case 1:for(L=this._activeBuffer.y,this._dirtyRowTracker.markDirty(L),this._eraseInBufferLine(L,0,this._activeBuffer.x+1,!0,b),this._activeBuffer.x+1>=this._bufferService.cols&&(this._activeBuffer.lines.get(L+1).isWrapped=!1);L--;)this._resetBufferLine(L,b);this._dirtyRowTracker.markDirty(0);break;case 2:for(L=this._bufferService.rows,this._dirtyRowTracker.markDirty(L-1);L--;)this._resetBufferLine(L,b);this._dirtyRowTracker.markDirty(0);break;case 3:const R=this._activeBuffer.lines.length-this._bufferService.rows;R>0&&(this._activeBuffer.lines.trimStart(R),this._activeBuffer.ybase=Math.max(this._activeBuffer.ybase-R,0),this._activeBuffer.ydisp=Math.max(this._activeBuffer.ydisp-R,0),this._onScroll.fire(0))}return!0}eraseInLine(S,b=!1){switch(this._restrictCursor(this._bufferService.cols),S.params[0]){case 0:this._eraseInBufferLine(this._activeBuffer.y,this._activeBuffer.x,this._bufferService.cols,this._activeBuffer.x===0,b);break;case 1:this._eraseInBufferLine(this._activeBuffer.y,0,this._activeBuffer.x+1,!1,b);break;case 2:this._eraseInBufferLine(this._activeBuffer.y,0,this._bufferService.cols,!0,b)}return this._dirtyRowTracker.markDirty(this._activeBuffer.y),!0}insertLines(S){this._restrictCursor();let b=S.params[0]||1;if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const L=this._activeBuffer.ybase+this._activeBuffer.y,R=this._bufferService.rows-1-this._activeBuffer.scrollBottom,M=this._bufferService.rows-1+this._activeBuffer.ybase-R+1;for(;b--;)this._activeBuffer.lines.splice(M-1,1),this._activeBuffer.lines.splice(L,0,this._activeBuffer.getBlankLine(this._eraseAttrData()));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.y,this._activeBuffer.scrollBottom),this._activeBuffer.x=0,!0}deleteLines(S){this._restrictCursor();let b=S.params[0]||1;if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const L=this._activeBuffer.ybase+this._activeBuffer.y;let R;for(R=this._bufferService.rows-1-this._activeBuffer.scrollBottom,R=this._bufferService.rows-1+this._activeBuffer.ybase-R;b--;)this._activeBuffer.lines.splice(L,1),this._activeBuffer.lines.splice(R,0,this._activeBuffer.getBlankLine(this._eraseAttrData()));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.y,this._activeBuffer.scrollBottom),this._activeBuffer.x=0,!0}insertChars(S){this._restrictCursor();const b=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);return b&&(b.insertCells(this._activeBuffer.x,S.params[0]||1,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),this._dirtyRowTracker.markDirty(this._activeBuffer.y)),!0}deleteChars(S){this._restrictCursor();const b=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);return b&&(b.deleteCells(this._activeBuffer.x,S.params[0]||1,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),this._dirtyRowTracker.markDirty(this._activeBuffer.y)),!0}scrollUp(S){let b=S.params[0]||1;for(;b--;)this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollTop,1),this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollBottom,0,this._activeBuffer.getBlankLine(this._eraseAttrData()));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}scrollDown(S){let b=S.params[0]||1;for(;b--;)this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollBottom,1),this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollTop,0,this._activeBuffer.getBlankLine(e.DEFAULT_ATTR_DATA));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}scrollLeft(S){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const b=S.params[0]||1;for(let L=this._activeBuffer.scrollTop;L<=this._activeBuffer.scrollBottom;++L){const R=this._activeBuffer.lines.get(this._activeBuffer.ybase+L);R.deleteCells(0,b,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),R.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}scrollRight(S){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const b=S.params[0]||1;for(let L=this._activeBuffer.scrollTop;L<=this._activeBuffer.scrollBottom;++L){const R=this._activeBuffer.lines.get(this._activeBuffer.ybase+L);R.insertCells(0,b,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),R.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}insertColumns(S){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const b=S.params[0]||1;for(let L=this._activeBuffer.scrollTop;L<=this._activeBuffer.scrollBottom;++L){const R=this._activeBuffer.lines.get(this._activeBuffer.ybase+L);R.insertCells(this._activeBuffer.x,b,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),R.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}deleteColumns(S){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const b=S.params[0]||1;for(let L=this._activeBuffer.scrollTop;L<=this._activeBuffer.scrollBottom;++L){const R=this._activeBuffer.lines.get(this._activeBuffer.ybase+L);R.deleteCells(this._activeBuffer.x,b,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),R.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}eraseChars(S){this._restrictCursor();const b=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);return b&&(b.replaceCells(this._activeBuffer.x,this._activeBuffer.x+(S.params[0]||1),this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),this._dirtyRowTracker.markDirty(this._activeBuffer.y)),!0}repeatPrecedingCharacter(S){if(!this._parser.precedingCodepoint)return!0;const b=S.params[0]||1,L=new Uint32Array(b);for(let R=0;R<b;++R)L[R]=this._parser.precedingCodepoint;return this.print(L,0,L.length),!0}sendDeviceAttributesPrimary(S){return S.params[0]>0||(this._is("xterm")||this._is("rxvt-unicode")||this._is("screen")?this._coreService.triggerDataEvent(d.C0.ESC+"[?1;2c"):this._is("linux")&&this._coreService.triggerDataEvent(d.C0.ESC+"[?6c")),!0}sendDeviceAttributesSecondary(S){return S.params[0]>0||(this._is("xterm")?this._coreService.triggerDataEvent(d.C0.ESC+"[>0;276;0c"):this._is("rxvt-unicode")?this._coreService.triggerDataEvent(d.C0.ESC+"[>85;95;0c"):this._is("linux")?this._coreService.triggerDataEvent(S.params[0]+"c"):this._is("screen")&&this._coreService.triggerDataEvent(d.C0.ESC+"[>83;40003;0c")),!0}_is(S){return(this._optionsService.rawOptions.termName+"").indexOf(S)===0}setMode(S){for(let b=0;b<S.length;b++)switch(S.params[b]){case 4:this._coreService.modes.insertMode=!0;break;case 20:this._optionsService.options.convertEol=!0}return!0}setModePrivate(S){for(let b=0;b<S.length;b++)switch(S.params[b]){case 1:this._coreService.decPrivateModes.applicationCursorKeys=!0;break;case 2:this._charsetService.setgCharset(0,_.DEFAULT_CHARSET),this._charsetService.setgCharset(1,_.DEFAULT_CHARSET),this._charsetService.setgCharset(2,_.DEFAULT_CHARSET),this._charsetService.setgCharset(3,_.DEFAULT_CHARSET);break;case 3:this._optionsService.rawOptions.windowOptions.setWinLines&&(this._bufferService.resize(132,this._bufferService.rows),this._onRequestReset.fire());break;case 6:this._coreService.decPrivateModes.origin=!0,this._setCursor(0,0);break;case 7:this._coreService.decPrivateModes.wraparound=!0;break;case 12:this._optionsService.options.cursorBlink=!0;break;case 45:this._coreService.decPrivateModes.reverseWraparound=!0;break;case 66:this._logService.debug("Serial port requested application keypad."),this._coreService.decPrivateModes.applicationKeypad=!0,this._onRequestSyncScrollBar.fire();break;case 9:this._coreMouseService.activeProtocol="X10";break;case 1e3:this._coreMouseService.activeProtocol="VT200";break;case 1002:this._coreMouseService.activeProtocol="DRAG";break;case 1003:this._coreMouseService.activeProtocol="ANY";break;case 1004:this._coreService.decPrivateModes.sendFocus=!0,this._onRequestSendFocus.fire();break;case 1005:this._logService.debug("DECSET 1005 not supported (see #2507)");break;case 1006:this._coreMouseService.activeEncoding="SGR";break;case 1015:this._logService.debug("DECSET 1015 not supported (see #2507)");break;case 1016:this._coreMouseService.activeEncoding="SGR_PIXELS";break;case 25:this._coreService.isCursorHidden=!1;break;case 1048:this.saveCursor();break;case 1049:this.saveCursor();case 47:case 1047:this._bufferService.buffers.activateAltBuffer(this._eraseAttrData()),this._coreService.isCursorInitialized=!0,this._onRequestRefreshRows.fire(0,this._bufferService.rows-1),this._onRequestSyncScrollBar.fire();break;case 2004:this._coreService.decPrivateModes.bracketedPasteMode=!0}return!0}resetMode(S){for(let b=0;b<S.length;b++)switch(S.params[b]){case 4:this._coreService.modes.insertMode=!1;break;case 20:this._optionsService.options.convertEol=!1}return!0}resetModePrivate(S){for(let b=0;b<S.length;b++)switch(S.params[b]){case 1:this._coreService.decPrivateModes.applicationCursorKeys=!1;break;case 3:this._optionsService.rawOptions.windowOptions.setWinLines&&(this._bufferService.resize(80,this._bufferService.rows),this._onRequestReset.fire());break;case 6:this._coreService.decPrivateModes.origin=!1,this._setCursor(0,0);break;case 7:this._coreService.decPrivateModes.wraparound=!1;break;case 12:this._optionsService.options.cursorBlink=!1;break;case 45:this._coreService.decPrivateModes.reverseWraparound=!1;break;case 66:this._logService.debug("Switching back to normal keypad."),this._coreService.decPrivateModes.applicationKeypad=!1,this._onRequestSyncScrollBar.fire();break;case 9:case 1e3:case 1002:case 1003:this._coreMouseService.activeProtocol="NONE";break;case 1004:this._coreService.decPrivateModes.sendFocus=!1;break;case 1005:this._logService.debug("DECRST 1005 not supported (see #2507)");break;case 1006:case 1016:this._coreMouseService.activeEncoding="DEFAULT";break;case 1015:this._logService.debug("DECRST 1015 not supported (see #2507)");break;case 25:this._coreService.isCursorHidden=!0;break;case 1048:this.restoreCursor();break;case 1049:case 47:case 1047:this._bufferService.buffers.activateNormalBuffer(),S.params[b]===1049&&this.restoreCursor(),this._coreService.isCursorInitialized=!0,this._onRequestRefreshRows.fire(0,this._bufferService.rows-1),this._onRequestSyncScrollBar.fire();break;case 2004:this._coreService.decPrivateModes.bracketedPasteMode=!1}return!0}requestMode(S,b){const L=this._coreService.decPrivateModes,{activeProtocol:R,activeEncoding:M}=this._coreMouseService,F=this._coreService,{buffers:N,cols:O}=this._bufferService,{active:W,alt:k}=N,E=this._optionsService.rawOptions,A=U=>U?1:2,B=S.params[0];return P=B,j=b?B===2?3:B===4?A(F.modes.insertMode):B===12?4:B===20?A(E.convertEol):0:B===1?A(L.applicationCursorKeys):B===3?E.windowOptions.setWinLines?O===80?2:O===132?1:0:0:B===6?A(L.origin):B===7?A(L.wraparound):B===8?3:B===9?A(R==="X10"):B===12?A(E.cursorBlink):B===25?A(!F.isCursorHidden):B===45?A(L.reverseWraparound):B===66?A(L.applicationKeypad):B===1e3?A(R==="VT200"):B===1002?A(R==="DRAG"):B===1003?A(R==="ANY"):B===1004?A(L.sendFocus):B===1005?4:B===1006?A(M==="SGR"):B===1015?4:B===1016?A(M==="SGR_PIXELS"):B===1048?1:B===47||B===1047||B===1049?A(W===k):B===2004?A(L.bracketedPasteMode):0,F.triggerDataEvent(`${d.C0.ESC}[${b?"":"?"}${P};${j}$y`),!0;var P,j}_updateAttrColor(S,b,L,R,M){return b===2?(S|=50331648,S&=-16777216,S|=r.AttributeData.fromColorRGB([L,R,M])):b===5&&(S&=-50331904,S|=33554432|255&L),S}_extractColor(S,b,L){const R=[0,0,-1,0,0,0];let M=0,F=0;do{if(R[F+M]=S.params[b+F],S.hasSubParams(b+F)){const N=S.getSubParams(b+F);let O=0;do R[1]===5&&(M=1),R[F+O+1+M]=N[O];while(++O<N.length&&O+F+1+M<R.length);break}if(R[1]===5&&F+M>=2||R[1]===2&&F+M>=5)break;R[1]&&(M=1)}while(++F+b<S.length&&F+M<R.length);for(let N=2;N<R.length;++N)R[N]===-1&&(R[N]=0);switch(R[0]){case 38:L.fg=this._updateAttrColor(L.fg,R[1],R[3],R[4],R[5]);break;case 48:L.bg=this._updateAttrColor(L.bg,R[1],R[3],R[4],R[5]);break;case 58:L.extended=L.extended.clone(),L.extended.underlineColor=this._updateAttrColor(L.extended.underlineColor,R[1],R[3],R[4],R[5])}return F}_processUnderline(S,b){b.extended=b.extended.clone(),(!~S||S>5)&&(S=1),b.extended.underlineStyle=S,b.fg|=268435456,S===0&&(b.fg&=-268435457),b.updateExtended()}_processSGR0(S){S.fg=e.DEFAULT_ATTR_DATA.fg,S.bg=e.DEFAULT_ATTR_DATA.bg,S.extended=S.extended.clone(),S.extended.underlineStyle=0,S.extended.underlineColor&=-67108864,S.updateExtended()}charAttributes(S){if(S.length===1&&S.params[0]===0)return this._processSGR0(this._curAttrData),!0;const b=S.length;let L;const R=this._curAttrData;for(let M=0;M<b;M++)L=S.params[M],L>=30&&L<=37?(R.fg&=-50331904,R.fg|=16777216|L-30):L>=40&&L<=47?(R.bg&=-50331904,R.bg|=16777216|L-40):L>=90&&L<=97?(R.fg&=-50331904,R.fg|=16777224|L-90):L>=100&&L<=107?(R.bg&=-50331904,R.bg|=16777224|L-100):L===0?this._processSGR0(R):L===1?R.fg|=134217728:L===3?R.bg|=67108864:L===4?(R.fg|=268435456,this._processUnderline(S.hasSubParams(M)?S.getSubParams(M)[0]:1,R)):L===5?R.fg|=536870912:L===7?R.fg|=67108864:L===8?R.fg|=1073741824:L===9?R.fg|=2147483648:L===2?R.bg|=134217728:L===21?this._processUnderline(2,R):L===22?(R.fg&=-134217729,R.bg&=-134217729):L===23?R.bg&=-67108865:L===24?(R.fg&=-268435457,this._processUnderline(0,R)):L===25?R.fg&=-536870913:L===27?R.fg&=-67108865:L===28?R.fg&=-1073741825:L===29?R.fg&=2147483647:L===39?(R.fg&=-67108864,R.fg|=16777215&e.DEFAULT_ATTR_DATA.fg):L===49?(R.bg&=-67108864,R.bg|=16777215&e.DEFAULT_ATTR_DATA.bg):L===38||L===48||L===58?M+=this._extractColor(S,M,R):L===59?(R.extended=R.extended.clone(),R.extended.underlineColor=-1,R.updateExtended()):L===100?(R.fg&=-67108864,R.fg|=16777215&e.DEFAULT_ATTR_DATA.fg,R.bg&=-67108864,R.bg|=16777215&e.DEFAULT_ATTR_DATA.bg):this._logService.debug("Unknown SGR attribute: %d.",L);return!0}deviceStatus(S){switch(S.params[0]){case 5:this._coreService.triggerDataEvent(`${d.C0.ESC}[0n`);break;case 6:const b=this._activeBuffer.y+1,L=this._activeBuffer.x+1;this._coreService.triggerDataEvent(`${d.C0.ESC}[${b};${L}R`)}return!0}deviceStatusPrivate(S){if(S.params[0]===6){const b=this._activeBuffer.y+1,L=this._activeBuffer.x+1;this._coreService.triggerDataEvent(`${d.C0.ESC}[?${b};${L}R`)}return!0}softReset(S){return this._coreService.isCursorHidden=!1,this._onRequestSyncScrollBar.fire(),this._activeBuffer.scrollTop=0,this._activeBuffer.scrollBottom=this._bufferService.rows-1,this._curAttrData=e.DEFAULT_ATTR_DATA.clone(),this._coreService.reset(),this._charsetService.reset(),this._activeBuffer.savedX=0,this._activeBuffer.savedY=this._activeBuffer.ybase,this._activeBuffer.savedCurAttrData.fg=this._curAttrData.fg,this._activeBuffer.savedCurAttrData.bg=this._curAttrData.bg,this._activeBuffer.savedCharset=this._charsetService.charset,this._coreService.decPrivateModes.origin=!1,!0}setCursorStyle(S){const b=S.params[0]||1;switch(b){case 1:case 2:this._optionsService.options.cursorStyle="block";break;case 3:case 4:this._optionsService.options.cursorStyle="underline";break;case 5:case 6:this._optionsService.options.cursorStyle="bar"}const L=b%2==1;return this._optionsService.options.cursorBlink=L,!0}setScrollRegion(S){const b=S.params[0]||1;let L;return(S.length<2||(L=S.params[1])>this._bufferService.rows||L===0)&&(L=this._bufferService.rows),L>b&&(this._activeBuffer.scrollTop=b-1,this._activeBuffer.scrollBottom=L-1,this._setCursor(0,0)),!0}windowOptions(S){if(!y(S.params[0],this._optionsService.rawOptions.windowOptions))return!0;const b=S.length>1?S.params[1]:0;switch(S.params[0]){case 14:b!==2&&this._onRequestWindowsOptionsReport.fire(w.GET_WIN_SIZE_PIXELS);break;case 16:this._onRequestWindowsOptionsReport.fire(w.GET_CELL_SIZE_PIXELS);break;case 18:this._bufferService&&this._coreService.triggerDataEvent(`${d.C0.ESC}[8;${this._bufferService.rows};${this._bufferService.cols}t`);break;case 22:b!==0&&b!==2||(this._windowTitleStack.push(this._windowTitle),this._windowTitleStack.length>10&&this._windowTitleStack.shift()),b!==0&&b!==1||(this._iconNameStack.push(this._iconName),this._iconNameStack.length>10&&this._iconNameStack.shift());break;case 23:b!==0&&b!==2||this._windowTitleStack.length&&this.setTitle(this._windowTitleStack.pop()),b!==0&&b!==1||this._iconNameStack.length&&this.setIconName(this._iconNameStack.pop())}return!0}saveCursor(S){return this._activeBuffer.savedX=this._activeBuffer.x,this._activeBuffer.savedY=this._activeBuffer.ybase+this._activeBuffer.y,this._activeBuffer.savedCurAttrData.fg=this._curAttrData.fg,this._activeBuffer.savedCurAttrData.bg=this._curAttrData.bg,this._activeBuffer.savedCharset=this._charsetService.charset,!0}restoreCursor(S){return this._activeBuffer.x=this._activeBuffer.savedX||0,this._activeBuffer.y=Math.max(this._activeBuffer.savedY-this._activeBuffer.ybase,0),this._curAttrData.fg=this._activeBuffer.savedCurAttrData.fg,this._curAttrData.bg=this._activeBuffer.savedCurAttrData.bg,this._charsetService.charset=this._savedCharset,this._activeBuffer.savedCharset&&(this._charsetService.charset=this._activeBuffer.savedCharset),this._restrictCursor(),!0}setTitle(S){return this._windowTitle=S,this._onTitleChange.fire(S),!0}setIconName(S){return this._iconName=S,!0}setOrReportIndexedColor(S){const b=[],L=S.split(";");for(;L.length>1;){const R=L.shift(),M=L.shift();if(/^\d+$/.exec(R)){const F=parseInt(R);if(0<=F&&F<256)if(M==="?")b.push({type:0,index:F});else{const N=(0,o.parseColor)(M);N&&b.push({type:1,index:F,color:N})}}}return b.length&&this._onColor.fire(b),!0}setHyperlink(S){const b=S.split(";");return!(b.length<2)&&(b[1]?this._createHyperlink(b[0],b[1]):!b[0]&&this._finishHyperlink())}_createHyperlink(S,b){this._getCurrentLinkId()&&this._finishHyperlink();const L=S.split(":");let R;const M=L.findIndex(F=>F.startsWith("id="));return M!==-1&&(R=L[M].slice(3)||void 0),this._curAttrData.extended=this._curAttrData.extended.clone(),this._curAttrData.extended.urlId=this._oscLinkService.registerLink({id:R,uri:b}),this._curAttrData.updateExtended(),!0}_finishHyperlink(){return this._curAttrData.extended=this._curAttrData.extended.clone(),this._curAttrData.extended.urlId=0,this._curAttrData.updateExtended(),!0}_setOrReportSpecialColor(S,b){const L=S.split(";");for(let R=0;R<L.length&&!(b>=this._specialColors.length);++R,++b)if(L[R]==="?")this._onColor.fire([{type:0,index:this._specialColors[b]}]);else{const M=(0,o.parseColor)(L[R]);M&&this._onColor.fire([{type:1,index:this._specialColors[b],color:M}])}return!0}setOrReportFgColor(S){return this._setOrReportSpecialColor(S,0)}setOrReportBgColor(S){return this._setOrReportSpecialColor(S,1)}setOrReportCursorColor(S){return this._setOrReportSpecialColor(S,2)}restoreIndexedColor(S){if(!S)return this._onColor.fire([{type:2}]),!0;const b=[],L=S.split(";");for(let R=0;R<L.length;++R)if(/^\d+$/.exec(L[R])){const M=parseInt(L[R]);0<=M&&M<256&&b.push({type:2,index:M})}return b.length&&this._onColor.fire(b),!0}restoreFgColor(S){return this._onColor.fire([{type:2,index:256}]),!0}restoreBgColor(S){return this._onColor.fire([{type:2,index:257}]),!0}restoreCursorColor(S){return this._onColor.fire([{type:2,index:258}]),!0}nextLine(){return this._activeBuffer.x=0,this.index(),!0}keypadApplicationMode(){return this._logService.debug("Serial port requested application keypad."),this._coreService.decPrivateModes.applicationKeypad=!0,this._onRequestSyncScrollBar.fire(),!0}keypadNumericMode(){return this._logService.debug("Switching back to normal keypad."),this._coreService.decPrivateModes.applicationKeypad=!1,this._onRequestSyncScrollBar.fire(),!0}selectDefaultCharset(){return this._charsetService.setgLevel(0),this._charsetService.setgCharset(0,_.DEFAULT_CHARSET),!0}selectCharset(S){return S.length!==2?(this.selectDefaultCharset(),!0):(S[0]==="/"||this._charsetService.setgCharset(p[S[0]],_.CHARSETS[S[1]]||_.DEFAULT_CHARSET),!0)}index(){return this._restrictCursor(),this._activeBuffer.y++,this._activeBuffer.y===this._activeBuffer.scrollBottom+1?(this._activeBuffer.y--,this._bufferService.scroll(this._eraseAttrData())):this._activeBuffer.y>=this._bufferService.rows&&(this._activeBuffer.y=this._bufferService.rows-1),this._restrictCursor(),!0}tabSet(){return this._activeBuffer.tabs[this._activeBuffer.x]=!0,!0}reverseIndex(){if(this._restrictCursor(),this._activeBuffer.y===this._activeBuffer.scrollTop){const S=this._activeBuffer.scrollBottom-this._activeBuffer.scrollTop;this._activeBuffer.lines.shiftElements(this._activeBuffer.ybase+this._activeBuffer.y,S,1),this._activeBuffer.lines.set(this._activeBuffer.ybase+this._activeBuffer.y,this._activeBuffer.getBlankLine(this._eraseAttrData())),this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom)}else this._activeBuffer.y--,this._restrictCursor();return!0}fullReset(){return this._parser.reset(),this._onRequestReset.fire(),!0}reset(){this._curAttrData=e.DEFAULT_ATTR_DATA.clone(),this._eraseAttrDataInternal=e.DEFAULT_ATTR_DATA.clone()}_eraseAttrData(){return this._eraseAttrDataInternal.bg&=-67108864,this._eraseAttrDataInternal.bg|=67108863&this._curAttrData.bg,this._eraseAttrDataInternal}setgLevel(S){return this._charsetService.setgLevel(S),!0}screenAlignmentPattern(){const S=new t.CellData;S.content=4194304|"E".charCodeAt(0),S.fg=this._curAttrData.fg,S.bg=this._curAttrData.bg,this._setCursor(0,0);for(let b=0;b<this._bufferService.rows;++b){const L=this._activeBuffer.ybase+this._activeBuffer.y+b,R=this._activeBuffer.lines.get(L);R&&(R.fill(S),R.isWrapped=!1)}return this._dirtyRowTracker.markAllDirty(),this._setCursor(0,0),!0}requestStatusString(S,b){const L=this._bufferService.buffer,R=this._optionsService.rawOptions;return(M=>(this._coreService.triggerDataEvent(`${d.C0.ESC}${M}${d.C0.ESC}\\`),!0))(S==='"q'?`P1$r${this._curAttrData.isProtected()?1:0}"q`:S==='"p'?'P1$r61;1"p':S==="r"?`P1$r${L.scrollTop+1};${L.scrollBottom+1}r`:S==="m"?"P1$r0m":S===" q"?`P1$r${{block:2,underline:4,bar:6}[R.cursorStyle]-(R.cursorBlink?1:0)} q`:"P0$r")}markRangeDirty(S,b){this._dirtyRowTracker.markRangeDirty(S,b)}}i.InputHandler=x;let I=class{constructor(H){this._bufferService=H,this.clearRange()}clearRange(){this.start=this._bufferService.buffer.y,this.end=this._bufferService.buffer.y}markDirty(H){H<this.start?this.start=H:H>this.end&&(this.end=H)}markRangeDirty(H,S){H>S&&(D=H,H=S,S=D),H<this.start&&(this.start=H),S>this.end&&(this.end=S)}markAllDirty(){this.markRangeDirty(0,this._bufferService.rows-1)}};I=h([f(0,l.IBufferService)],I)},844:(T,i)=>{function a(h){for(const f of h)f.dispose();h.length=0}Object.defineProperty(i,"__esModule",{value:!0}),i.getDisposeArrayDisposable=i.disposeArray=i.toDisposable=i.Disposable=void 0,i.Disposable=class{constructor(){this._disposables=[],this._isDisposed=!1}dispose(){this._isDisposed=!0;for(const h of this._disposables)h.dispose();this._disposables.length=0}register(h){return this._disposables.push(h),h}unregister(h){const f=this._disposables.indexOf(h);f!==-1&&this._disposables.splice(f,1)}},i.toDisposable=function(h){return{dispose:h}},i.disposeArray=a,i.getDisposeArrayDisposable=function(h){return{dispose:()=>a(h)}}},1505:(T,i)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.FourKeyMap=i.TwoKeyMap=void 0;class a{constructor(){this._data={}}set(f,d,_){this._data[f]||(this._data[f]={}),this._data[f][d]=_}get(f,d){return this._data[f]?this._data[f][d]:void 0}clear(){this._data={}}}i.TwoKeyMap=a,i.FourKeyMap=class{constructor(){this._data=new a}set(h,f,d,_,g){this._data.get(h,f)||this._data.set(h,f,new a),this._data.get(h,f).set(d,_,g)}get(h,f,d,_){var g;return(g=this._data.get(h,f))===null||g===void 0?void 0:g.get(d,_)}clear(){this._data.clear()}}},6114:(T,i)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.isChromeOS=i.isLinux=i.isWindows=i.isIphone=i.isIpad=i.isMac=i.getSafariVersion=i.isSafari=i.isLegacyEdge=i.isFirefox=i.isNode=void 0,i.isNode=typeof navigator>"u";const a=i.isNode?"node":navigator.userAgent,h=i.isNode?"node":navigator.platform;i.isFirefox=a.includes("Firefox"),i.isLegacyEdge=a.includes("Edge"),i.isSafari=/^((?!chrome|android).)*safari/i.test(a),i.getSafariVersion=function(){if(!i.isSafari)return 0;const f=a.match(/Version\/(\d+)/);return f===null||f.length<2?0:parseInt(f[1])},i.isMac=["Macintosh","MacIntel","MacPPC","Mac68K"].includes(h),i.isIpad=h==="iPad",i.isIphone=h==="iPhone",i.isWindows=["Windows","Win16","Win32","WinCE"].includes(h),i.isLinux=h.indexOf("Linux")>=0,i.isChromeOS=/\bCrOS\b/.test(a)},6106:(T,i)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.SortedList=void 0;let a=0;i.SortedList=class{constructor(h){this._getKey=h,this._array=[]}clear(){this._array.length=0}insert(h){this._array.length!==0?(a=this._search(this._getKey(h),0,this._array.length-1),this._array.splice(a,0,h)):this._array.push(h)}delete(h){if(this._array.length===0)return!1;const f=this._getKey(h);if(f===void 0||(a=this._search(f,0,this._array.length-1),a===-1)||this._getKey(this._array[a])!==f)return!1;do if(this._array[a]===h)return this._array.splice(a,1),!0;while(++a<this._array.length&&this._getKey(this._array[a])===f);return!1}*getKeyIterator(h){if(this._array.length!==0&&(a=this._search(h,0,this._array.length-1),!(a<0||a>=this._array.length)&&this._getKey(this._array[a])===h))do yield this._array[a];while(++a<this._array.length&&this._getKey(this._array[a])===h)}forEachByKey(h,f){if(this._array.length!==0&&(a=this._search(h,0,this._array.length-1),!(a<0||a>=this._array.length)&&this._getKey(this._array[a])===h))do f(this._array[a]);while(++a<this._array.length&&this._getKey(this._array[a])===h)}values(){return this._array.values()}_search(h,f,d){if(d<f)return f;let _=Math.floor((f+d)/2);const g=this._getKey(this._array[_]);if(g>h)return this._search(h,f,_-1);if(g<h)return this._search(h,_+1,d);for(;_>0&&this._getKey(this._array[_-1])===h;)_--;return _}}},7226:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.DebouncedIdleTask=i.IdleTaskQueue=i.PriorityTaskQueue=void 0;const h=a(6114);class f{constructor(){this._tasks=[],this._i=0}enqueue(g){this._tasks.push(g),this._start()}flush(){for(;this._i<this._tasks.length;)this._tasks[this._i]()||this._i++;this.clear()}clear(){this._idleCallback&&(this._cancelCallback(this._idleCallback),this._idleCallback=void 0),this._i=0,this._tasks.length=0}_start(){this._idleCallback||(this._idleCallback=this._requestCallback(this._process.bind(this)))}_process(g){this._idleCallback=void 0;let v=0,c=0,e=g.timeRemaining(),s=0;for(;this._i<this._tasks.length;){if(v=Date.now(),this._tasks[this._i]()||this._i++,v=Math.max(1,Date.now()-v),c=Math.max(v,c),s=g.timeRemaining(),1.5*c>s)return e-v<-20&&console.warn(`task queue exceeded allotted deadline by ${Math.abs(Math.round(e-v))}ms`),void this._start();e=s}this.clear()}}class d extends f{_requestCallback(g){return setTimeout(()=>g(this._createDeadline(16)))}_cancelCallback(g){clearTimeout(g)}_createDeadline(g){const v=Date.now()+g;return{timeRemaining:()=>Math.max(0,v-Date.now())}}}i.PriorityTaskQueue=d,i.IdleTaskQueue=!h.isNode&&"requestIdleCallback"in window?class extends f{_requestCallback(_){return requestIdleCallback(_)}_cancelCallback(_){cancelIdleCallback(_)}}:d,i.DebouncedIdleTask=class{constructor(){this._queue=new i.IdleTaskQueue}set(_){this._queue.clear(),this._queue.enqueue(_)}flush(){this._queue.flush()}}},9282:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.updateWindowsModeWrappedState=void 0;const h=a(643);i.updateWindowsModeWrappedState=function(f){const d=f.buffer.lines.get(f.buffer.ybase+f.buffer.y-1),_=d==null?void 0:d.get(f.cols-1),g=f.buffer.lines.get(f.buffer.ybase+f.buffer.y);g&&_&&(g.isWrapped=_[h.CHAR_DATA_CODE_INDEX]!==h.NULL_CELL_CODE&&_[h.CHAR_DATA_CODE_INDEX]!==h.WHITESPACE_CELL_CODE)}},3734:(T,i)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.ExtendedAttrs=i.AttributeData=void 0;class a{constructor(){this.fg=0,this.bg=0,this.extended=new h}static toColorRGB(d){return[d>>>16&255,d>>>8&255,255&d]}static fromColorRGB(d){return(255&d[0])<<16|(255&d[1])<<8|255&d[2]}clone(){const d=new a;return d.fg=this.fg,d.bg=this.bg,d.extended=this.extended.clone(),d}isInverse(){return 67108864&this.fg}isBold(){return 134217728&this.fg}isUnderline(){return this.hasExtendedAttrs()&&this.extended.underlineStyle!==0?1:268435456&this.fg}isBlink(){return 536870912&this.fg}isInvisible(){return 1073741824&this.fg}isItalic(){return 67108864&this.bg}isDim(){return 134217728&this.bg}isStrikethrough(){return 2147483648&this.fg}isProtected(){return 536870912&this.bg}getFgColorMode(){return 50331648&this.fg}getBgColorMode(){return 50331648&this.bg}isFgRGB(){return(50331648&this.fg)==50331648}isBgRGB(){return(50331648&this.bg)==50331648}isFgPalette(){return(50331648&this.fg)==16777216||(50331648&this.fg)==33554432}isBgPalette(){return(50331648&this.bg)==16777216||(50331648&this.bg)==33554432}isFgDefault(){return(50331648&this.fg)==0}isBgDefault(){return(50331648&this.bg)==0}isAttributeDefault(){return this.fg===0&&this.bg===0}getFgColor(){switch(50331648&this.fg){case 16777216:case 33554432:return 255&this.fg;case 50331648:return 16777215&this.fg;default:return-1}}getBgColor(){switch(50331648&this.bg){case 16777216:case 33554432:return 255&this.bg;case 50331648:return 16777215&this.bg;default:return-1}}hasExtendedAttrs(){return 268435456&this.bg}updateExtended(){this.extended.isEmpty()?this.bg&=-268435457:this.bg|=268435456}getUnderlineColor(){if(268435456&this.bg&&~this.extended.underlineColor)switch(50331648&this.extended.underlineColor){case 16777216:case 33554432:return 255&this.extended.underlineColor;case 50331648:return 16777215&this.extended.underlineColor;default:return this.getFgColor()}return this.getFgColor()}getUnderlineColorMode(){return 268435456&this.bg&&~this.extended.underlineColor?50331648&this.extended.underlineColor:this.getFgColorMode()}isUnderlineColorRGB(){return 268435456&this.bg&&~this.extended.underlineColor?(50331648&this.extended.underlineColor)==50331648:this.isFgRGB()}isUnderlineColorPalette(){return 268435456&this.bg&&~this.extended.underlineColor?(50331648&this.extended.underlineColor)==16777216||(50331648&this.extended.underlineColor)==33554432:this.isFgPalette()}isUnderlineColorDefault(){return 268435456&this.bg&&~this.extended.underlineColor?(50331648&this.extended.underlineColor)==0:this.isFgDefault()}getUnderlineStyle(){return 268435456&this.fg?268435456&this.bg?this.extended.underlineStyle:1:0}}i.AttributeData=a;class h{constructor(d=0,_=0){this._ext=0,this._urlId=0,this._ext=d,this._urlId=_}get ext(){return this._urlId?-469762049&this._ext|this.underlineStyle<<26:this._ext}set ext(d){this._ext=d}get underlineStyle(){return this._urlId?5:(469762048&this._ext)>>26}set underlineStyle(d){this._ext&=-469762049,this._ext|=d<<26&469762048}get underlineColor(){return 67108863&this._ext}set underlineColor(d){this._ext&=-67108864,this._ext|=67108863&d}get urlId(){return this._urlId}set urlId(d){this._urlId=d}clone(){return new h(this._ext,this._urlId)}isEmpty(){return this.underlineStyle===0&&this._urlId===0}}i.ExtendedAttrs=h},9092:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.BufferStringIterator=i.Buffer=i.MAX_BUFFER_SIZE=void 0;const h=a(6349),f=a(8437),d=a(511),_=a(643),g=a(4634),v=a(4863),c=a(7116),e=a(3734),s=a(7226);i.MAX_BUFFER_SIZE=4294967295,i.Buffer=class{constructor(t,r,l){this._hasScrollback=t,this._optionsService=r,this._bufferService=l,this.ydisp=0,this.ybase=0,this.y=0,this.x=0,this.tabs={},this.savedY=0,this.savedX=0,this.savedCurAttrData=f.DEFAULT_ATTR_DATA.clone(),this.savedCharset=c.DEFAULT_CHARSET,this.markers=[],this._nullCell=d.CellData.fromCharData([0,_.NULL_CELL_CHAR,_.NULL_CELL_WIDTH,_.NULL_CELL_CODE]),this._whitespaceCell=d.CellData.fromCharData([0,_.WHITESPACE_CELL_CHAR,_.WHITESPACE_CELL_WIDTH,_.WHITESPACE_CELL_CODE]),this._isClearing=!1,this._memoryCleanupQueue=new s.IdleTaskQueue,this._memoryCleanupPosition=0,this._cols=this._bufferService.cols,this._rows=this._bufferService.rows,this.lines=new h.CircularList(this._getCorrectBufferLength(this._rows)),this.scrollTop=0,this.scrollBottom=this._rows-1,this.setupTabStops()}getNullCell(t){return t?(this._nullCell.fg=t.fg,this._nullCell.bg=t.bg,this._nullCell.extended=t.extended):(this._nullCell.fg=0,this._nullCell.bg=0,this._nullCell.extended=new e.ExtendedAttrs),this._nullCell}getWhitespaceCell(t){return t?(this._whitespaceCell.fg=t.fg,this._whitespaceCell.bg=t.bg,this._whitespaceCell.extended=t.extended):(this._whitespaceCell.fg=0,this._whitespaceCell.bg=0,this._whitespaceCell.extended=new e.ExtendedAttrs),this._whitespaceCell}getBlankLine(t,r){return new f.BufferLine(this._bufferService.cols,this.getNullCell(t),r)}get hasScrollback(){return this._hasScrollback&&this.lines.maxLength>this._rows}get isCursorInViewport(){const t=this.ybase+this.y-this.ydisp;return t>=0&&t<this._rows}_getCorrectBufferLength(t){if(!this._hasScrollback)return t;const r=t+this._optionsService.rawOptions.scrollback;return r>i.MAX_BUFFER_SIZE?i.MAX_BUFFER_SIZE:r}fillViewportRows(t){if(this.lines.length===0){t===void 0&&(t=f.DEFAULT_ATTR_DATA);let r=this._rows;for(;r--;)this.lines.push(this.getBlankLine(t))}}clear(){this.ydisp=0,this.ybase=0,this.y=0,this.x=0,this.lines=new h.CircularList(this._getCorrectBufferLength(this._rows)),this.scrollTop=0,this.scrollBottom=this._rows-1,this.setupTabStops()}resize(t,r){const l=this.getNullCell(f.DEFAULT_ATTR_DATA);let u=0;const m=this._getCorrectBufferLength(r);if(m>this.lines.maxLength&&(this.lines.maxLength=m),this.lines.length>0){if(this._cols<t)for(let p=0;p<this.lines.length;p++)u+=+this.lines.get(p).resize(t,l);let o=0;if(this._rows<r)for(let p=this._rows;p<r;p++)this.lines.length<r+this.ybase&&(this._optionsService.rawOptions.windowsMode?this.lines.push(new f.BufferLine(t,l)):this.ybase>0&&this.lines.length<=this.ybase+this.y+o+1?(this.ybase--,o++,this.ydisp>0&&this.ydisp--):this.lines.push(new f.BufferLine(t,l)));else for(let p=this._rows;p>r;p--)this.lines.length>r+this.ybase&&(this.lines.length>this.ybase+this.y+1?this.lines.pop():(this.ybase++,this.ydisp++));if(m<this.lines.maxLength){const p=this.lines.length-m;p>0&&(this.lines.trimStart(p),this.ybase=Math.max(this.ybase-p,0),this.ydisp=Math.max(this.ydisp-p,0),this.savedY=Math.max(this.savedY-p,0)),this.lines.maxLength=m}this.x=Math.min(this.x,t-1),this.y=Math.min(this.y,r-1),o&&(this.y+=o),this.savedX=Math.min(this.savedX,t-1),this.scrollTop=0}if(this.scrollBottom=r-1,this._isReflowEnabled&&(this._reflow(t,r),this._cols>t))for(let o=0;o<this.lines.length;o++)u+=+this.lines.get(o).resize(t,l);this._cols=t,this._rows=r,this._memoryCleanupQueue.clear(),u>.1*this.lines.length&&(this._memoryCleanupPosition=0,this._memoryCleanupQueue.enqueue(()=>this._batchedMemoryCleanup()))}_batchedMemoryCleanup(){let t=!0;this._memoryCleanupPosition>=this.lines.length&&(this._memoryCleanupPosition=0,t=!1);let r=0;for(;this._memoryCleanupPosition<this.lines.length;)if(r+=this.lines.get(this._memoryCleanupPosition++).cleanupMemory(),r>100)return!0;return t}get _isReflowEnabled(){return this._hasScrollback&&!this._optionsService.rawOptions.windowsMode}_reflow(t,r){this._cols!==t&&(t>this._cols?this._reflowLarger(t,r):this._reflowSmaller(t,r))}_reflowLarger(t,r){const l=(0,g.reflowLargerGetLinesToRemove)(this.lines,this._cols,t,this.ybase+this.y,this.getNullCell(f.DEFAULT_ATTR_DATA));if(l.length>0){const u=(0,g.reflowLargerCreateNewLayout)(this.lines,l);(0,g.reflowLargerApplyNewLayout)(this.lines,u.layout),this._reflowLargerAdjustViewport(t,r,u.countRemoved)}}_reflowLargerAdjustViewport(t,r,l){const u=this.getNullCell(f.DEFAULT_ATTR_DATA);let m=l;for(;m-- >0;)this.ybase===0?(this.y>0&&this.y--,this.lines.length<r&&this.lines.push(new f.BufferLine(t,u))):(this.ydisp===this.ybase&&this.ydisp--,this.ybase--);this.savedY=Math.max(this.savedY-l,0)}_reflowSmaller(t,r){const l=this.getNullCell(f.DEFAULT_ATTR_DATA),u=[];let m=0;for(let o=this.lines.length-1;o>=0;o--){let p=this.lines.get(o);if(!p||!p.isWrapped&&p.getTrimmedLength()<=t)continue;const C=[p];for(;p.isWrapped&&o>0;)p=this.lines.get(--o),C.unshift(p);const y=this.ybase+this.y;if(y>=o&&y<o+C.length)continue;const w=C[C.length-1].getTrimmedLength(),D=(0,g.reflowSmallerGetNewLineLengths)(C,this._cols,t),x=D.length-C.length;let I;I=this.ybase===0&&this.y!==this.lines.length-1?Math.max(0,this.y-this.lines.maxLength+x):Math.max(0,this.lines.length-this.lines.maxLength+x);const H=[];for(let F=0;F<x;F++){const N=this.getBlankLine(f.DEFAULT_ATTR_DATA,!0);H.push(N)}H.length>0&&(u.push({start:o+C.length+m,newLines:H}),m+=H.length),C.push(...H);let S=D.length-1,b=D[S];b===0&&(S--,b=D[S]);let L=C.length-x-1,R=w;for(;L>=0;){const F=Math.min(R,b);if(C[S]===void 0)break;if(C[S].copyCellsFrom(C[L],R-F,b-F,F,!0),b-=F,b===0&&(S--,b=D[S]),R-=F,R===0){L--;const N=Math.max(L,0);R=(0,g.getWrappedLineTrimmedLength)(C,N,this._cols)}}for(let F=0;F<C.length;F++)D[F]<t&&C[F].setCell(D[F],l);let M=x-I;for(;M-- >0;)this.ybase===0?this.y<r-1?(this.y++,this.lines.pop()):(this.ybase++,this.ydisp++):this.ybase<Math.min(this.lines.maxLength,this.lines.length+m)-r&&(this.ybase===this.ydisp&&this.ydisp++,this.ybase++);this.savedY=Math.min(this.savedY+x,this.ybase+r-1)}if(u.length>0){const o=[],p=[];for(let S=0;S<this.lines.length;S++)p.push(this.lines.get(S));const C=this.lines.length;let y=C-1,w=0,D=u[w];this.lines.length=Math.min(this.lines.maxLength,this.lines.length+m);let x=0;for(let S=Math.min(this.lines.maxLength-1,C+m-1);S>=0;S--)if(D&&D.start>y+x){for(let b=D.newLines.length-1;b>=0;b--)this.lines.set(S--,D.newLines[b]);S++,o.push({index:y+1,amount:D.newLines.length}),x+=D.newLines.length,D=u[++w]}else this.lines.set(S,p[y--]);let I=0;for(let S=o.length-1;S>=0;S--)o[S].index+=I,this.lines.onInsertEmitter.fire(o[S]),I+=o[S].amount;const H=Math.max(0,C+m-this.lines.maxLength);H>0&&this.lines.onTrimEmitter.fire(H)}}stringIndexToBufferIndex(t,r,l=!1){for(;r;){const u=this.lines.get(t);if(!u)return[-1,-1];const m=l?u.getTrimmedLength():u.length;for(let o=0;o<m;++o)if(u.get(o)[_.CHAR_DATA_WIDTH_INDEX]&&(r-=u.get(o)[_.CHAR_DATA_CHAR_INDEX].length||1),r<0)return[t,o];t++}return[t,0]}translateBufferLineToString(t,r,l=0,u){const m=this.lines.get(t);return m?m.translateToString(r,l,u):""}getWrappedRangeForLine(t){let r=t,l=t;for(;r>0&&this.lines.get(r).isWrapped;)r--;for(;l+1<this.lines.length&&this.lines.get(l+1).isWrapped;)l++;return{first:r,last:l}}setupTabStops(t){for(t!=null?this.tabs[t]||(t=this.prevStop(t)):(this.tabs={},t=0);t<this._cols;t+=this._optionsService.rawOptions.tabStopWidth)this.tabs[t]=!0}prevStop(t){for(t==null&&(t=this.x);!this.tabs[--t]&&t>0;);return t>=this._cols?this._cols-1:t<0?0:t}nextStop(t){for(t==null&&(t=this.x);!this.tabs[++t]&&t<this._cols;);return t>=this._cols?this._cols-1:t<0?0:t}clearMarkers(t){this._isClearing=!0;for(let r=0;r<this.markers.length;r++)this.markers[r].line===t&&(this.markers[r].dispose(),this.markers.splice(r--,1));this._isClearing=!1}clearAllMarkers(){this._isClearing=!0;for(let t=0;t<this.markers.length;t++)this.markers[t].dispose(),this.markers.splice(t--,1);this._isClearing=!1}addMarker(t){const r=new v.Marker(t);return this.markers.push(r),r.register(this.lines.onTrim(l=>{r.line-=l,r.line<0&&r.dispose()})),r.register(this.lines.onInsert(l=>{r.line>=l.index&&(r.line+=l.amount)})),r.register(this.lines.onDelete(l=>{r.line>=l.index&&r.line<l.index+l.amount&&r.dispose(),r.line>l.index&&(r.line-=l.amount)})),r.register(r.onDispose(()=>this._removeMarker(r))),r}_removeMarker(t){this._isClearing||this.markers.splice(this.markers.indexOf(t),1)}iterator(t,r,l,u,m){return new n(this,t,r,l,u,m)}};class n{constructor(r,l,u=0,m=r.lines.length,o=0,p=0){this._buffer=r,this._trimRight=l,this._startIndex=u,this._endIndex=m,this._startOverscan=o,this._endOverscan=p,this._startIndex<0&&(this._startIndex=0),this._endIndex>this._buffer.lines.length&&(this._endIndex=this._buffer.lines.length),this._current=this._startIndex}hasNext(){return this._current<this._endIndex}next(){const r=this._buffer.getWrappedRangeForLine(this._current);r.first<this._startIndex-this._startOverscan&&(r.first=this._startIndex-this._startOverscan),r.last>this._endIndex+this._endOverscan&&(r.last=this._endIndex+this._endOverscan),r.first=Math.max(r.first,0),r.last=Math.min(r.last,this._buffer.lines.length);let l="";for(let u=r.first;u<=r.last;++u)l+=this._buffer.translateBufferLineToString(u,this._trimRight);return this._current=r.last+1,{range:r,content:l}}}i.BufferStringIterator=n},8437:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.BufferLine=i.DEFAULT_ATTR_DATA=void 0;const h=a(482),f=a(643),d=a(511),_=a(3734);i.DEFAULT_ATTR_DATA=Object.freeze(new _.AttributeData);let g=0;class v{constructor(e,s,n=!1){this.isWrapped=n,this._combined={},this._extendedAttrs={},this._data=new Uint32Array(3*e);const t=s||d.CellData.fromCharData([0,f.NULL_CELL_CHAR,f.NULL_CELL_WIDTH,f.NULL_CELL_CODE]);for(let r=0;r<e;++r)this.setCell(r,t);this.length=e}get(e){const s=this._data[3*e+0],n=2097151&s;return[this._data[3*e+1],2097152&s?this._combined[e]:n?(0,h.stringFromCodePoint)(n):"",s>>22,2097152&s?this._combined[e].charCodeAt(this._combined[e].length-1):n]}set(e,s){this._data[3*e+1]=s[f.CHAR_DATA_ATTR_INDEX],s[f.CHAR_DATA_CHAR_INDEX].length>1?(this._combined[e]=s[1],this._data[3*e+0]=2097152|e|s[f.CHAR_DATA_WIDTH_INDEX]<<22):this._data[3*e+0]=s[f.CHAR_DATA_CHAR_INDEX].charCodeAt(0)|s[f.CHAR_DATA_WIDTH_INDEX]<<22}getWidth(e){return this._data[3*e+0]>>22}hasWidth(e){return 12582912&this._data[3*e+0]}getFg(e){return this._data[3*e+1]}getBg(e){return this._data[3*e+2]}hasContent(e){return 4194303&this._data[3*e+0]}getCodePoint(e){const s=this._data[3*e+0];return 2097152&s?this._combined[e].charCodeAt(this._combined[e].length-1):2097151&s}isCombined(e){return 2097152&this._data[3*e+0]}getString(e){const s=this._data[3*e+0];return 2097152&s?this._combined[e]:2097151&s?(0,h.stringFromCodePoint)(2097151&s):""}isProtected(e){return 536870912&this._data[3*e+2]}loadCell(e,s){return g=3*e,s.content=this._data[g+0],s.fg=this._data[g+1],s.bg=this._data[g+2],2097152&s.content&&(s.combinedData=this._combined[e]),268435456&s.bg&&(s.extended=this._extendedAttrs[e]),s}setCell(e,s){2097152&s.content&&(this._combined[e]=s.combinedData),268435456&s.bg&&(this._extendedAttrs[e]=s.extended),this._data[3*e+0]=s.content,this._data[3*e+1]=s.fg,this._data[3*e+2]=s.bg}setCellFromCodePoint(e,s,n,t,r,l){268435456&r&&(this._extendedAttrs[e]=l),this._data[3*e+0]=s|n<<22,this._data[3*e+1]=t,this._data[3*e+2]=r}addCodepointToCell(e,s){let n=this._data[3*e+0];2097152&n?this._combined[e]+=(0,h.stringFromCodePoint)(s):(2097151&n?(this._combined[e]=(0,h.stringFromCodePoint)(2097151&n)+(0,h.stringFromCodePoint)(s),n&=-2097152,n|=2097152):n=s|4194304,this._data[3*e+0]=n)}insertCells(e,s,n,t){if((e%=this.length)&&this.getWidth(e-1)===2&&this.setCellFromCodePoint(e-1,0,1,(t==null?void 0:t.fg)||0,(t==null?void 0:t.bg)||0,(t==null?void 0:t.extended)||new _.ExtendedAttrs),s<this.length-e){const r=new d.CellData;for(let l=this.length-e-s-1;l>=0;--l)this.setCell(e+s+l,this.loadCell(e+l,r));for(let l=0;l<s;++l)this.setCell(e+l,n)}else for(let r=e;r<this.length;++r)this.setCell(r,n);this.getWidth(this.length-1)===2&&this.setCellFromCodePoint(this.length-1,0,1,(t==null?void 0:t.fg)||0,(t==null?void 0:t.bg)||0,(t==null?void 0:t.extended)||new _.ExtendedAttrs)}deleteCells(e,s,n,t){if(e%=this.length,s<this.length-e){const r=new d.CellData;for(let l=0;l<this.length-e-s;++l)this.setCell(e+l,this.loadCell(e+s+l,r));for(let l=this.length-s;l<this.length;++l)this.setCell(l,n)}else for(let r=e;r<this.length;++r)this.setCell(r,n);e&&this.getWidth(e-1)===2&&this.setCellFromCodePoint(e-1,0,1,(t==null?void 0:t.fg)||0,(t==null?void 0:t.bg)||0,(t==null?void 0:t.extended)||new _.ExtendedAttrs),this.getWidth(e)!==0||this.hasContent(e)||this.setCellFromCodePoint(e,0,1,(t==null?void 0:t.fg)||0,(t==null?void 0:t.bg)||0,(t==null?void 0:t.extended)||new _.ExtendedAttrs)}replaceCells(e,s,n,t,r=!1){if(r)for(e&&this.getWidth(e-1)===2&&!this.isProtected(e-1)&&this.setCellFromCodePoint(e-1,0,1,(t==null?void 0:t.fg)||0,(t==null?void 0:t.bg)||0,(t==null?void 0:t.extended)||new _.ExtendedAttrs),s<this.length&&this.getWidth(s-1)===2&&!this.isProtected(s)&&this.setCellFromCodePoint(s,0,1,(t==null?void 0:t.fg)||0,(t==null?void 0:t.bg)||0,(t==null?void 0:t.extended)||new _.ExtendedAttrs);e<s&&e<this.length;)this.isProtected(e)||this.setCell(e,n),e++;else for(e&&this.getWidth(e-1)===2&&this.setCellFromCodePoint(e-1,0,1,(t==null?void 0:t.fg)||0,(t==null?void 0:t.bg)||0,(t==null?void 0:t.extended)||new _.ExtendedAttrs),s<this.length&&this.getWidth(s-1)===2&&this.setCellFromCodePoint(s,0,1,(t==null?void 0:t.fg)||0,(t==null?void 0:t.bg)||0,(t==null?void 0:t.extended)||new _.ExtendedAttrs);e<s&&e<this.length;)this.setCell(e++,n)}resize(e,s){if(e===this.length)return 4*this._data.length*2<this._data.buffer.byteLength;const n=3*e;if(e>this.length){if(this._data.buffer.byteLength>=4*n)this._data=new Uint32Array(this._data.buffer,0,n);else{const t=new Uint32Array(n);t.set(this._data),this._data=t}for(let t=this.length;t<e;++t)this.setCell(t,s)}else{this._data=this._data.subarray(0,n);const t=Object.keys(this._combined);for(let l=0;l<t.length;l++){const u=parseInt(t[l],10);u>=e&&delete this._combined[u]}const r=Object.keys(this._extendedAttrs);for(let l=0;l<r.length;l++){const u=parseInt(r[l],10);u>=e&&delete this._extendedAttrs[u]}}return this.length=e,4*n*2<this._data.buffer.byteLength}cleanupMemory(){if(4*this._data.length*2<this._data.buffer.byteLength){const e=new Uint32Array(this._data.length);return e.set(this._data),this._data=e,1}return 0}fill(e,s=!1){if(s)for(let n=0;n<this.length;++n)this.isProtected(n)||this.setCell(n,e);else{this._combined={},this._extendedAttrs={};for(let n=0;n<this.length;++n)this.setCell(n,e)}}copyFrom(e){this.length!==e.length?this._data=new Uint32Array(e._data):this._data.set(e._data),this.length=e.length,this._combined={};for(const s in e._combined)this._combined[s]=e._combined[s];this._extendedAttrs={};for(const s in e._extendedAttrs)this._extendedAttrs[s]=e._extendedAttrs[s];this.isWrapped=e.isWrapped}clone(){const e=new v(0);e._data=new Uint32Array(this._data),e.length=this.length;for(const s in this._combined)e._combined[s]=this._combined[s];for(const s in this._extendedAttrs)e._extendedAttrs[s]=this._extendedAttrs[s];return e.isWrapped=this.isWrapped,e}getTrimmedLength(){for(let e=this.length-1;e>=0;--e)if(4194303&this._data[3*e+0])return e+(this._data[3*e+0]>>22);return 0}copyCellsFrom(e,s,n,t,r){const l=e._data;if(r)for(let m=t-1;m>=0;m--){for(let o=0;o<3;o++)this._data[3*(n+m)+o]=l[3*(s+m)+o];268435456&l[3*(s+m)+2]&&(this._extendedAttrs[n+m]=e._extendedAttrs[s+m])}else for(let m=0;m<t;m++){for(let o=0;o<3;o++)this._data[3*(n+m)+o]=l[3*(s+m)+o];268435456&l[3*(s+m)+2]&&(this._extendedAttrs[n+m]=e._extendedAttrs[s+m])}const u=Object.keys(e._combined);for(let m=0;m<u.length;m++){const o=parseInt(u[m],10);o>=s&&(this._combined[o-s+n]=e._combined[o])}}translateToString(e=!1,s=0,n=this.length){e&&(n=Math.min(n,this.getTrimmedLength()));let t="";for(;s<n;){const r=this._data[3*s+0],l=2097151&r;t+=2097152&r?this._combined[s]:l?(0,h.stringFromCodePoint)(l):f.WHITESPACE_CELL_CHAR,s+=r>>22||1}return t}}i.BufferLine=v},4841:(T,i)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.getRangeLength=void 0,i.getRangeLength=function(a,h){if(a.start.y>a.end.y)throw new Error(`Buffer range end (${a.end.x}, ${a.end.y}) cannot be before start (${a.start.x}, ${a.start.y})`);return h*(a.end.y-a.start.y)+(a.end.x-a.start.x+1)}},4634:(T,i)=>{function a(h,f,d){if(f===h.length-1)return h[f].getTrimmedLength();const _=!h[f].hasContent(d-1)&&h[f].getWidth(d-1)===1,g=h[f+1].getWidth(0)===2;return _&&g?d-1:d}Object.defineProperty(i,"__esModule",{value:!0}),i.getWrappedLineTrimmedLength=i.reflowSmallerGetNewLineLengths=i.reflowLargerApplyNewLayout=i.reflowLargerCreateNewLayout=i.reflowLargerGetLinesToRemove=void 0,i.reflowLargerGetLinesToRemove=function(h,f,d,_,g){const v=[];for(let c=0;c<h.length-1;c++){let e=c,s=h.get(++e);if(!s.isWrapped)continue;const n=[h.get(c)];for(;e<h.length&&s.isWrapped;)n.push(s),s=h.get(++e);if(_>=c&&_<e){c+=n.length-1;continue}let t=0,r=a(n,t,f),l=1,u=0;for(;l<n.length;){const o=a(n,l,f),p=o-u,C=d-r,y=Math.min(p,C);n[t].copyCellsFrom(n[l],u,r,y,!1),r+=y,r===d&&(t++,r=0),u+=y,u===o&&(l++,u=0),r===0&&t!==0&&n[t-1].getWidth(d-1)===2&&(n[t].copyCellsFrom(n[t-1],d-1,r++,1,!1),n[t-1].setCell(d-1,g))}n[t].replaceCells(r,d,g);let m=0;for(let o=n.length-1;o>0&&(o>t||n[o].getTrimmedLength()===0);o--)m++;m>0&&(v.push(c+n.length-m),v.push(m)),c+=n.length-1}return v},i.reflowLargerCreateNewLayout=function(h,f){const d=[];let _=0,g=f[_],v=0;for(let c=0;c<h.length;c++)if(g===c){const e=f[++_];h.onDeleteEmitter.fire({index:c-v,amount:e}),c+=e-1,v+=e,g=f[++_]}else d.push(c);return{layout:d,countRemoved:v}},i.reflowLargerApplyNewLayout=function(h,f){const d=[];for(let _=0;_<f.length;_++)d.push(h.get(f[_]));for(let _=0;_<d.length;_++)h.set(_,d[_]);h.length=f.length},i.reflowSmallerGetNewLineLengths=function(h,f,d){const _=[],g=h.map((s,n)=>a(h,n,f)).reduce((s,n)=>s+n);let v=0,c=0,e=0;for(;e<g;){if(g-e<d){_.push(g-e);break}v+=d;const s=a(h,c,f);v>s&&(v-=s,c++);const n=h[c].getWidth(v-1)===2;n&&v--;const t=n?d-1:d;_.push(t),e+=t}return _},i.getWrappedLineTrimmedLength=a},5295:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.BufferSet=void 0;const h=a(9092),f=a(8460),d=a(844);class _ extends d.Disposable{constructor(v,c){super(),this._optionsService=v,this._bufferService=c,this._onBufferActivate=this.register(new f.EventEmitter),this.onBufferActivate=this._onBufferActivate.event,this.reset(),this.register(this._optionsService.onSpecificOptionChange("scrollback",()=>this.resize(this._bufferService.cols,this._bufferService.rows))),this.register(this._optionsService.onSpecificOptionChange("tabStopWidth",()=>this.setupTabStops()))}reset(){this._normal=new h.Buffer(!0,this._optionsService,this._bufferService),this._normal.fillViewportRows(),this._alt=new h.Buffer(!1,this._optionsService,this._bufferService),this._activeBuffer=this._normal,this._onBufferActivate.fire({activeBuffer:this._normal,inactiveBuffer:this._alt}),this.setupTabStops()}get alt(){return this._alt}get active(){return this._activeBuffer}get normal(){return this._normal}activateNormalBuffer(){this._activeBuffer!==this._normal&&(this._normal.x=this._alt.x,this._normal.y=this._alt.y,this._alt.clearAllMarkers(),this._alt.clear(),this._activeBuffer=this._normal,this._onBufferActivate.fire({activeBuffer:this._normal,inactiveBuffer:this._alt}))}activateAltBuffer(v){this._activeBuffer!==this._alt&&(this._alt.fillViewportRows(v),this._alt.x=this._normal.x,this._alt.y=this._normal.y,this._activeBuffer=this._alt,this._onBufferActivate.fire({activeBuffer:this._alt,inactiveBuffer:this._normal}))}resize(v,c){this._normal.resize(v,c),this._alt.resize(v,c),this.setupTabStops(v)}setupTabStops(v){this._normal.setupTabStops(v),this._alt.setupTabStops(v)}}i.BufferSet=_},511:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.CellData=void 0;const h=a(482),f=a(643),d=a(3734);class _ extends d.AttributeData{constructor(){super(...arguments),this.content=0,this.fg=0,this.bg=0,this.extended=new d.ExtendedAttrs,this.combinedData=""}static fromCharData(v){const c=new _;return c.setFromCharData(v),c}isCombined(){return 2097152&this.content}getWidth(){return this.content>>22}getChars(){return 2097152&this.content?this.combinedData:2097151&this.content?(0,h.stringFromCodePoint)(2097151&this.content):""}getCode(){return this.isCombined()?this.combinedData.charCodeAt(this.combinedData.length-1):2097151&this.content}setFromCharData(v){this.fg=v[f.CHAR_DATA_ATTR_INDEX],this.bg=0;let c=!1;if(v[f.CHAR_DATA_CHAR_INDEX].length>2)c=!0;else if(v[f.CHAR_DATA_CHAR_INDEX].length===2){const e=v[f.CHAR_DATA_CHAR_INDEX].charCodeAt(0);if(55296<=e&&e<=56319){const s=v[f.CHAR_DATA_CHAR_INDEX].charCodeAt(1);56320<=s&&s<=57343?this.content=1024*(e-55296)+s-56320+65536|v[f.CHAR_DATA_WIDTH_INDEX]<<22:c=!0}else c=!0}else this.content=v[f.CHAR_DATA_CHAR_INDEX].charCodeAt(0)|v[f.CHAR_DATA_WIDTH_INDEX]<<22;c&&(this.combinedData=v[f.CHAR_DATA_CHAR_INDEX],this.content=2097152|v[f.CHAR_DATA_WIDTH_INDEX]<<22)}getAsCharData(){return[this.fg,this.getChars(),this.getWidth(),this.getCode()]}}i.CellData=_},643:(T,i)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.WHITESPACE_CELL_CODE=i.WHITESPACE_CELL_WIDTH=i.WHITESPACE_CELL_CHAR=i.NULL_CELL_CODE=i.NULL_CELL_WIDTH=i.NULL_CELL_CHAR=i.CHAR_DATA_CODE_INDEX=i.CHAR_DATA_WIDTH_INDEX=i.CHAR_DATA_CHAR_INDEX=i.CHAR_DATA_ATTR_INDEX=i.DEFAULT_EXT=i.DEFAULT_ATTR=i.DEFAULT_COLOR=void 0,i.DEFAULT_COLOR=0,i.DEFAULT_ATTR=256|i.DEFAULT_COLOR<<9,i.DEFAULT_EXT=0,i.CHAR_DATA_ATTR_INDEX=0,i.CHAR_DATA_CHAR_INDEX=1,i.CHAR_DATA_WIDTH_INDEX=2,i.CHAR_DATA_CODE_INDEX=3,i.NULL_CELL_CHAR="",i.NULL_CELL_WIDTH=1,i.NULL_CELL_CODE=0,i.WHITESPACE_CELL_CHAR=" ",i.WHITESPACE_CELL_WIDTH=1,i.WHITESPACE_CELL_CODE=32},4863:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.Marker=void 0;const h=a(8460),f=a(844);class d{constructor(g){this.line=g,this.isDisposed=!1,this._disposables=[],this._id=d._nextId++,this._onDispose=this.register(new h.EventEmitter),this.onDispose=this._onDispose.event}get id(){return this._id}dispose(){this.isDisposed||(this.isDisposed=!0,this.line=-1,this._onDispose.fire(),(0,f.disposeArray)(this._disposables),this._disposables.length=0)}register(g){return this._disposables.push(g),g}}i.Marker=d,d._nextId=1},7116:(T,i)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.DEFAULT_CHARSET=i.CHARSETS=void 0,i.CHARSETS={},i.DEFAULT_CHARSET=i.CHARSETS.B,i.CHARSETS[0]={"`":"◆",a:"▒",b:"␉",c:"␌",d:"␍",e:"␊",f:"°",g:"±",h:"␤",i:"␋",j:"┘",k:"┐",l:"┌",m:"└",n:"┼",o:"⎺",p:"⎻",q:"─",r:"⎼",s:"⎽",t:"├",u:"┤",v:"┴",w:"┬",x:"│",y:"≤",z:"≥","{":"π","|":"≠","}":"£","~":"·"},i.CHARSETS.A={"#":"£"},i.CHARSETS.B=void 0,i.CHARSETS[4]={"#":"£","@":"¾","[":"ij","\\":"½","]":"|","{":"¨","|":"f","}":"¼","~":"´"},i.CHARSETS.C=i.CHARSETS[5]={"[":"Ä","\\":"Ö","]":"Å","^":"Ü","`":"é","{":"ä","|":"ö","}":"å","~":"ü"},i.CHARSETS.R={"#":"£","@":"à","[":"°","\\":"ç","]":"§","{":"é","|":"ù","}":"è","~":"¨"},i.CHARSETS.Q={"@":"à","[":"â","\\":"ç","]":"ê","^":"î","`":"ô","{":"é","|":"ù","}":"è","~":"û"},i.CHARSETS.K={"@":"§","[":"Ä","\\":"Ö","]":"Ü","{":"ä","|":"ö","}":"ü","~":"ß"},i.CHARSETS.Y={"#":"£","@":"§","[":"°","\\":"ç","]":"é","`":"ù","{":"à","|":"ò","}":"è","~":"ì"},i.CHARSETS.E=i.CHARSETS[6]={"@":"Ä","[":"Æ","\\":"Ø","]":"Å","^":"Ü","`":"ä","{":"æ","|":"ø","}":"å","~":"ü"},i.CHARSETS.Z={"#":"£","@":"§","[":"¡","\\":"Ñ","]":"¿","{":"°","|":"ñ","}":"ç"},i.CHARSETS.H=i.CHARSETS[7]={"@":"É","[":"Ä","\\":"Ö","]":"Å","^":"Ü","`":"é","{":"ä","|":"ö","}":"å","~":"ü"},i.CHARSETS["="]={"#":"ù","@":"à","[":"é","\\":"ç","]":"ê","^":"î",_:"è","`":"ô","{":"ä","|":"ö","}":"ü","~":"û"}},2584:(T,i)=>{var a,h;Object.defineProperty(i,"__esModule",{value:!0}),i.C1_ESCAPED=i.C1=i.C0=void 0,function(f){f.NUL="\0",f.SOH="",f.STX="",f.ETX="",f.EOT="",f.ENQ="",f.ACK="",f.BEL="\x07",f.BS="\b",f.HT="	",f.LF=`
`,f.VT="\v",f.FF="\f",f.CR="\r",f.SO="",f.SI="",f.DLE="",f.DC1="",f.DC2="",f.DC3="",f.DC4="",f.NAK="",f.SYN="",f.ETB="",f.CAN="",f.EM="",f.SUB="",f.ESC="\x1B",f.FS="",f.GS="",f.RS="",f.US="",f.SP=" ",f.DEL=""}(a=i.C0||(i.C0={})),(h=i.C1||(i.C1={})).PAD="",h.HOP="",h.BPH="",h.NBH="",h.IND="",h.NEL="",h.SSA="",h.ESA="",h.HTS="",h.HTJ="",h.VTS="",h.PLD="",h.PLU="",h.RI="",h.SS2="",h.SS3="",h.DCS="",h.PU1="",h.PU2="",h.STS="",h.CCH="",h.MW="",h.SPA="",h.EPA="",h.SOS="",h.SGCI="",h.SCI="",h.CSI="",h.ST="",h.OSC="",h.PM="",h.APC="",(i.C1_ESCAPED||(i.C1_ESCAPED={})).ST=`${a.ESC}\\`},7399:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.evaluateKeyboardEvent=void 0;const h=a(2584),f={48:["0",")"],49:["1","!"],50:["2","@"],51:["3","#"],52:["4","$"],53:["5","%"],54:["6","^"],55:["7","&"],56:["8","*"],57:["9","("],186:[";",":"],187:["=","+"],188:[",","<"],189:["-","_"],190:[".",">"],191:["/","?"],192:["`","~"],219:["[","{"],220:["\\","|"],221:["]","}"],222:["'",'"']};i.evaluateKeyboardEvent=function(d,_,g,v){const c={type:0,cancel:!1,key:void 0},e=(d.shiftKey?1:0)|(d.altKey?2:0)|(d.ctrlKey?4:0)|(d.metaKey?8:0);switch(d.keyCode){case 0:d.key==="UIKeyInputUpArrow"?c.key=_?h.C0.ESC+"OA":h.C0.ESC+"[A":d.key==="UIKeyInputLeftArrow"?c.key=_?h.C0.ESC+"OD":h.C0.ESC+"[D":d.key==="UIKeyInputRightArrow"?c.key=_?h.C0.ESC+"OC":h.C0.ESC+"[C":d.key==="UIKeyInputDownArrow"&&(c.key=_?h.C0.ESC+"OB":h.C0.ESC+"[B");break;case 8:if(d.altKey){c.key=h.C0.ESC+h.C0.DEL;break}c.key=h.C0.DEL;break;case 9:if(d.shiftKey){c.key=h.C0.ESC+"[Z";break}c.key=h.C0.HT,c.cancel=!0;break;case 13:c.key=d.altKey?h.C0.ESC+h.C0.CR:h.C0.CR,c.cancel=!0;break;case 27:c.key=h.C0.ESC,d.altKey&&(c.key=h.C0.ESC+h.C0.ESC),c.cancel=!0;break;case 37:if(d.metaKey)break;e?(c.key=h.C0.ESC+"[1;"+(e+1)+"D",c.key===h.C0.ESC+"[1;3D"&&(c.key=h.C0.ESC+(g?"b":"[1;5D"))):c.key=_?h.C0.ESC+"OD":h.C0.ESC+"[D";break;case 39:if(d.metaKey)break;e?(c.key=h.C0.ESC+"[1;"+(e+1)+"C",c.key===h.C0.ESC+"[1;3C"&&(c.key=h.C0.ESC+(g?"f":"[1;5C"))):c.key=_?h.C0.ESC+"OC":h.C0.ESC+"[C";break;case 38:if(d.metaKey)break;e?(c.key=h.C0.ESC+"[1;"+(e+1)+"A",g||c.key!==h.C0.ESC+"[1;3A"||(c.key=h.C0.ESC+"[1;5A")):c.key=_?h.C0.ESC+"OA":h.C0.ESC+"[A";break;case 40:if(d.metaKey)break;e?(c.key=h.C0.ESC+"[1;"+(e+1)+"B",g||c.key!==h.C0.ESC+"[1;3B"||(c.key=h.C0.ESC+"[1;5B")):c.key=_?h.C0.ESC+"OB":h.C0.ESC+"[B";break;case 45:d.shiftKey||d.ctrlKey||(c.key=h.C0.ESC+"[2~");break;case 46:c.key=e?h.C0.ESC+"[3;"+(e+1)+"~":h.C0.ESC+"[3~";break;case 36:c.key=e?h.C0.ESC+"[1;"+(e+1)+"H":_?h.C0.ESC+"OH":h.C0.ESC+"[H";break;case 35:c.key=e?h.C0.ESC+"[1;"+(e+1)+"F":_?h.C0.ESC+"OF":h.C0.ESC+"[F";break;case 33:d.shiftKey?c.type=2:d.ctrlKey?c.key=h.C0.ESC+"[5;"+(e+1)+"~":c.key=h.C0.ESC+"[5~";break;case 34:d.shiftKey?c.type=3:d.ctrlKey?c.key=h.C0.ESC+"[6;"+(e+1)+"~":c.key=h.C0.ESC+"[6~";break;case 112:c.key=e?h.C0.ESC+"[1;"+(e+1)+"P":h.C0.ESC+"OP";break;case 113:c.key=e?h.C0.ESC+"[1;"+(e+1)+"Q":h.C0.ESC+"OQ";break;case 114:c.key=e?h.C0.ESC+"[1;"+(e+1)+"R":h.C0.ESC+"OR";break;case 115:c.key=e?h.C0.ESC+"[1;"+(e+1)+"S":h.C0.ESC+"OS";break;case 116:c.key=e?h.C0.ESC+"[15;"+(e+1)+"~":h.C0.ESC+"[15~";break;case 117:c.key=e?h.C0.ESC+"[17;"+(e+1)+"~":h.C0.ESC+"[17~";break;case 118:c.key=e?h.C0.ESC+"[18;"+(e+1)+"~":h.C0.ESC+"[18~";break;case 119:c.key=e?h.C0.ESC+"[19;"+(e+1)+"~":h.C0.ESC+"[19~";break;case 120:c.key=e?h.C0.ESC+"[20;"+(e+1)+"~":h.C0.ESC+"[20~";break;case 121:c.key=e?h.C0.ESC+"[21;"+(e+1)+"~":h.C0.ESC+"[21~";break;case 122:c.key=e?h.C0.ESC+"[23;"+(e+1)+"~":h.C0.ESC+"[23~";break;case 123:c.key=e?h.C0.ESC+"[24;"+(e+1)+"~":h.C0.ESC+"[24~";break;default:if(!d.ctrlKey||d.shiftKey||d.altKey||d.metaKey)if(g&&!v||!d.altKey||d.metaKey)!g||d.altKey||d.ctrlKey||d.shiftKey||!d.metaKey?d.key&&!d.ctrlKey&&!d.altKey&&!d.metaKey&&d.keyCode>=48&&d.key.length===1?c.key=d.key:d.key&&d.ctrlKey&&(d.key==="_"&&(c.key=h.C0.US),d.key==="@"&&(c.key=h.C0.NUL)):d.keyCode===65&&(c.type=1);else{const s=f[d.keyCode],n=s==null?void 0:s[d.shiftKey?1:0];if(n)c.key=h.C0.ESC+n;else if(d.keyCode>=65&&d.keyCode<=90){const t=d.ctrlKey?d.keyCode-64:d.keyCode+32;let r=String.fromCharCode(t);d.shiftKey&&(r=r.toUpperCase()),c.key=h.C0.ESC+r}else if(d.keyCode===32)c.key=h.C0.ESC+(d.ctrlKey?h.C0.NUL:" ");else if(d.key==="Dead"&&d.code.startsWith("Key")){let t=d.code.slice(3,4);d.shiftKey||(t=t.toLowerCase()),c.key=h.C0.ESC+t,c.cancel=!0}}else d.keyCode>=65&&d.keyCode<=90?c.key=String.fromCharCode(d.keyCode-64):d.keyCode===32?c.key=h.C0.NUL:d.keyCode>=51&&d.keyCode<=55?c.key=String.fromCharCode(d.keyCode-51+27):d.keyCode===56?c.key=h.C0.DEL:d.keyCode===219?c.key=h.C0.ESC:d.keyCode===220?c.key=h.C0.FS:d.keyCode===221&&(c.key=h.C0.GS)}return c}},482:(T,i)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.Utf8ToUtf32=i.StringToUtf32=i.utf32ToString=i.stringFromCodePoint=void 0,i.stringFromCodePoint=function(a){return a>65535?(a-=65536,String.fromCharCode(55296+(a>>10))+String.fromCharCode(a%1024+56320)):String.fromCharCode(a)},i.utf32ToString=function(a,h=0,f=a.length){let d="";for(let _=h;_<f;++_){let g=a[_];g>65535?(g-=65536,d+=String.fromCharCode(55296+(g>>10))+String.fromCharCode(g%1024+56320)):d+=String.fromCharCode(g)}return d},i.StringToUtf32=class{constructor(){this._interim=0}clear(){this._interim=0}decode(a,h){const f=a.length;if(!f)return 0;let d=0,_=0;if(this._interim){const g=a.charCodeAt(_++);56320<=g&&g<=57343?h[d++]=1024*(this._interim-55296)+g-56320+65536:(h[d++]=this._interim,h[d++]=g),this._interim=0}for(let g=_;g<f;++g){const v=a.charCodeAt(g);if(55296<=v&&v<=56319){if(++g>=f)return this._interim=v,d;const c=a.charCodeAt(g);56320<=c&&c<=57343?h[d++]=1024*(v-55296)+c-56320+65536:(h[d++]=v,h[d++]=c)}else v!==65279&&(h[d++]=v)}return d}},i.Utf8ToUtf32=class{constructor(){this.interim=new Uint8Array(3)}clear(){this.interim.fill(0)}decode(a,h){const f=a.length;if(!f)return 0;let d,_,g,v,c=0,e=0,s=0;if(this.interim[0]){let r=!1,l=this.interim[0];l&=(224&l)==192?31:(240&l)==224?15:7;let u,m=0;for(;(u=63&this.interim[++m])&&m<4;)l<<=6,l|=u;const o=(224&this.interim[0])==192?2:(240&this.interim[0])==224?3:4,p=o-m;for(;s<p;){if(s>=f)return 0;if(u=a[s++],(192&u)!=128){s--,r=!0;break}this.interim[m++]=u,l<<=6,l|=63&u}r||(o===2?l<128?s--:h[c++]=l:o===3?l<2048||l>=55296&&l<=57343||l===65279||(h[c++]=l):l<65536||l>1114111||(h[c++]=l)),this.interim.fill(0)}const n=f-4;let t=s;for(;t<f;){for(;!(!(t<n)||128&(d=a[t])||128&(_=a[t+1])||128&(g=a[t+2])||128&(v=a[t+3]));)h[c++]=d,h[c++]=_,h[c++]=g,h[c++]=v,t+=4;if(d=a[t++],d<128)h[c++]=d;else if((224&d)==192){if(t>=f)return this.interim[0]=d,c;if(_=a[t++],(192&_)!=128){t--;continue}if(e=(31&d)<<6|63&_,e<128){t--;continue}h[c++]=e}else if((240&d)==224){if(t>=f)return this.interim[0]=d,c;if(_=a[t++],(192&_)!=128){t--;continue}if(t>=f)return this.interim[0]=d,this.interim[1]=_,c;if(g=a[t++],(192&g)!=128){t--;continue}if(e=(15&d)<<12|(63&_)<<6|63&g,e<2048||e>=55296&&e<=57343||e===65279)continue;h[c++]=e}else if((248&d)==240){if(t>=f)return this.interim[0]=d,c;if(_=a[t++],(192&_)!=128){t--;continue}if(t>=f)return this.interim[0]=d,this.interim[1]=_,c;if(g=a[t++],(192&g)!=128){t--;continue}if(t>=f)return this.interim[0]=d,this.interim[1]=_,this.interim[2]=g,c;if(v=a[t++],(192&v)!=128){t--;continue}if(e=(7&d)<<18|(63&_)<<12|(63&g)<<6|63&v,e<65536||e>1114111)continue;h[c++]=e}}return c}}},225:(T,i)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.UnicodeV6=void 0;const a=[[768,879],[1155,1158],[1160,1161],[1425,1469],[1471,1471],[1473,1474],[1476,1477],[1479,1479],[1536,1539],[1552,1557],[1611,1630],[1648,1648],[1750,1764],[1767,1768],[1770,1773],[1807,1807],[1809,1809],[1840,1866],[1958,1968],[2027,2035],[2305,2306],[2364,2364],[2369,2376],[2381,2381],[2385,2388],[2402,2403],[2433,2433],[2492,2492],[2497,2500],[2509,2509],[2530,2531],[2561,2562],[2620,2620],[2625,2626],[2631,2632],[2635,2637],[2672,2673],[2689,2690],[2748,2748],[2753,2757],[2759,2760],[2765,2765],[2786,2787],[2817,2817],[2876,2876],[2879,2879],[2881,2883],[2893,2893],[2902,2902],[2946,2946],[3008,3008],[3021,3021],[3134,3136],[3142,3144],[3146,3149],[3157,3158],[3260,3260],[3263,3263],[3270,3270],[3276,3277],[3298,3299],[3393,3395],[3405,3405],[3530,3530],[3538,3540],[3542,3542],[3633,3633],[3636,3642],[3655,3662],[3761,3761],[3764,3769],[3771,3772],[3784,3789],[3864,3865],[3893,3893],[3895,3895],[3897,3897],[3953,3966],[3968,3972],[3974,3975],[3984,3991],[3993,4028],[4038,4038],[4141,4144],[4146,4146],[4150,4151],[4153,4153],[4184,4185],[4448,4607],[4959,4959],[5906,5908],[5938,5940],[5970,5971],[6002,6003],[6068,6069],[6071,6077],[6086,6086],[6089,6099],[6109,6109],[6155,6157],[6313,6313],[6432,6434],[6439,6440],[6450,6450],[6457,6459],[6679,6680],[6912,6915],[6964,6964],[6966,6970],[6972,6972],[6978,6978],[7019,7027],[7616,7626],[7678,7679],[8203,8207],[8234,8238],[8288,8291],[8298,8303],[8400,8431],[12330,12335],[12441,12442],[43014,43014],[43019,43019],[43045,43046],[64286,64286],[65024,65039],[65056,65059],[65279,65279],[65529,65531]],h=[[68097,68099],[68101,68102],[68108,68111],[68152,68154],[68159,68159],[119143,119145],[119155,119170],[119173,119179],[119210,119213],[119362,119364],[917505,917505],[917536,917631],[917760,917999]];let f;i.UnicodeV6=class{constructor(){if(this.version="6",!f){f=new Uint8Array(65536),f.fill(1),f[0]=0,f.fill(0,1,32),f.fill(0,127,160),f.fill(2,4352,4448),f[9001]=2,f[9002]=2,f.fill(2,11904,42192),f[12351]=1,f.fill(2,44032,55204),f.fill(2,63744,64256),f.fill(2,65040,65050),f.fill(2,65072,65136),f.fill(2,65280,65377),f.fill(2,65504,65511);for(let d=0;d<a.length;++d)f.fill(0,a[d][0],a[d][1]+1)}}wcwidth(d){return d<32?0:d<127?1:d<65536?f[d]:function(_,g){let v,c=0,e=g.length-1;if(_<g[0][0]||_>g[e][1])return!1;for(;e>=c;)if(v=c+e>>1,_>g[v][1])c=v+1;else{if(!(_<g[v][0]))return!0;e=v-1}return!1}(d,h)?0:d>=131072&&d<=196605||d>=196608&&d<=262141?2:1}}},5981:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.WriteBuffer=void 0;const h=a(8460),f=a(844);class d extends f.Disposable{constructor(g){super(),this._action=g,this._writeBuffer=[],this._callbacks=[],this._pendingData=0,this._bufferOffset=0,this._isSyncWriting=!1,this._syncCalls=0,this._didUserInput=!1,this._onWriteParsed=this.register(new h.EventEmitter),this.onWriteParsed=this._onWriteParsed.event}handleUserInput(){this._didUserInput=!0}writeSync(g,v){if(v!==void 0&&this._syncCalls>v)return void(this._syncCalls=0);if(this._pendingData+=g.length,this._writeBuffer.push(g),this._callbacks.push(void 0),this._syncCalls++,this._isSyncWriting)return;let c;for(this._isSyncWriting=!0;c=this._writeBuffer.shift();){this._action(c);const e=this._callbacks.shift();e&&e()}this._pendingData=0,this._bufferOffset=2147483647,this._isSyncWriting=!1,this._syncCalls=0}write(g,v){if(this._pendingData>5e7)throw new Error("write data discarded, use flow control to avoid losing data");if(!this._writeBuffer.length){if(this._bufferOffset=0,this._didUserInput)return this._didUserInput=!1,this._pendingData+=g.length,this._writeBuffer.push(g),this._callbacks.push(v),void this._innerWrite();setTimeout(()=>this._innerWrite())}this._pendingData+=g.length,this._writeBuffer.push(g),this._callbacks.push(v)}_innerWrite(g=0,v=!0){const c=g||Date.now();for(;this._writeBuffer.length>this._bufferOffset;){const e=this._writeBuffer[this._bufferOffset],s=this._action(e,v);if(s){const t=r=>Date.now()-c>=12?setTimeout(()=>this._innerWrite(0,r)):this._innerWrite(c,r);return void s.catch(r=>(queueMicrotask(()=>{throw r}),Promise.resolve(!1))).then(t)}const n=this._callbacks[this._bufferOffset];if(n&&n(),this._bufferOffset++,this._pendingData-=e.length,Date.now()-c>=12)break}this._writeBuffer.length>this._bufferOffset?(this._bufferOffset>50&&(this._writeBuffer=this._writeBuffer.slice(this._bufferOffset),this._callbacks=this._callbacks.slice(this._bufferOffset),this._bufferOffset=0),setTimeout(()=>this._innerWrite())):(this._writeBuffer.length=0,this._callbacks.length=0,this._pendingData=0,this._bufferOffset=0),this._onWriteParsed.fire()}}i.WriteBuffer=d},5941:(T,i)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.toRgbString=i.parseColor=void 0;const a=/^([\da-f])\/([\da-f])\/([\da-f])$|^([\da-f]{2})\/([\da-f]{2})\/([\da-f]{2})$|^([\da-f]{3})\/([\da-f]{3})\/([\da-f]{3})$|^([\da-f]{4})\/([\da-f]{4})\/([\da-f]{4})$/,h=/^[\da-f]+$/;function f(d,_){const g=d.toString(16),v=g.length<2?"0"+g:g;switch(_){case 4:return g[0];case 8:return v;case 12:return(v+v).slice(0,3);default:return v+v}}i.parseColor=function(d){if(!d)return;let _=d.toLowerCase();if(_.indexOf("rgb:")===0){_=_.slice(4);const g=a.exec(_);if(g){const v=g[1]?15:g[4]?255:g[7]?4095:65535;return[Math.round(parseInt(g[1]||g[4]||g[7]||g[10],16)/v*255),Math.round(parseInt(g[2]||g[5]||g[8]||g[11],16)/v*255),Math.round(parseInt(g[3]||g[6]||g[9]||g[12],16)/v*255)]}}else if(_.indexOf("#")===0&&(_=_.slice(1),h.exec(_)&&[3,6,9,12].includes(_.length))){const g=_.length/3,v=[0,0,0];for(let c=0;c<3;++c){const e=parseInt(_.slice(g*c,g*c+g),16);v[c]=g===1?e<<4:g===2?e:g===3?e>>4:e>>8}return v}},i.toRgbString=function(d,_=16){const[g,v,c]=d;return`rgb:${f(g,_)}/${f(v,_)}/${f(c,_)}`}},5770:(T,i)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.PAYLOAD_LIMIT=void 0,i.PAYLOAD_LIMIT=1e7},6351:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.DcsHandler=i.DcsParser=void 0;const h=a(482),f=a(8742),d=a(5770),_=[];i.DcsParser=class{constructor(){this._handlers=Object.create(null),this._active=_,this._ident=0,this._handlerFb=()=>{},this._stack={paused:!1,loopPosition:0,fallThrough:!1}}dispose(){this._handlers=Object.create(null),this._handlerFb=()=>{},this._active=_}registerHandler(v,c){this._handlers[v]===void 0&&(this._handlers[v]=[]);const e=this._handlers[v];return e.push(c),{dispose:()=>{const s=e.indexOf(c);s!==-1&&e.splice(s,1)}}}clearHandler(v){this._handlers[v]&&delete this._handlers[v]}setHandlerFallback(v){this._handlerFb=v}reset(){if(this._active.length)for(let v=this._stack.paused?this._stack.loopPosition-1:this._active.length-1;v>=0;--v)this._active[v].unhook(!1);this._stack.paused=!1,this._active=_,this._ident=0}hook(v,c){if(this.reset(),this._ident=v,this._active=this._handlers[v]||_,this._active.length)for(let e=this._active.length-1;e>=0;e--)this._active[e].hook(c);else this._handlerFb(this._ident,"HOOK",c)}put(v,c,e){if(this._active.length)for(let s=this._active.length-1;s>=0;s--)this._active[s].put(v,c,e);else this._handlerFb(this._ident,"PUT",(0,h.utf32ToString)(v,c,e))}unhook(v,c=!0){if(this._active.length){let e=!1,s=this._active.length-1,n=!1;if(this._stack.paused&&(s=this._stack.loopPosition-1,e=c,n=this._stack.fallThrough,this._stack.paused=!1),!n&&e===!1){for(;s>=0&&(e=this._active[s].unhook(v),e!==!0);s--)if(e instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=s,this._stack.fallThrough=!1,e;s--}for(;s>=0;s--)if(e=this._active[s].unhook(!1),e instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=s,this._stack.fallThrough=!0,e}else this._handlerFb(this._ident,"UNHOOK",v);this._active=_,this._ident=0}};const g=new f.Params;g.addParam(0),i.DcsHandler=class{constructor(v){this._handler=v,this._data="",this._params=g,this._hitLimit=!1}hook(v){this._params=v.length>1||v.params[0]?v.clone():g,this._data="",this._hitLimit=!1}put(v,c,e){this._hitLimit||(this._data+=(0,h.utf32ToString)(v,c,e),this._data.length>d.PAYLOAD_LIMIT&&(this._data="",this._hitLimit=!0))}unhook(v){let c=!1;if(this._hitLimit)c=!1;else if(v&&(c=this._handler(this._data,this._params),c instanceof Promise))return c.then(e=>(this._params=g,this._data="",this._hitLimit=!1,e));return this._params=g,this._data="",this._hitLimit=!1,c}}},2015:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.EscapeSequenceParser=i.VT500_TRANSITION_TABLE=i.TransitionTable=void 0;const h=a(844),f=a(8742),d=a(6242),_=a(6351);class g{constructor(s){this.table=new Uint8Array(s)}setDefault(s,n){this.table.fill(s<<4|n)}add(s,n,t,r){this.table[n<<8|s]=t<<4|r}addMany(s,n,t,r){for(let l=0;l<s.length;l++)this.table[n<<8|s[l]]=t<<4|r}}i.TransitionTable=g;const v=160;i.VT500_TRANSITION_TABLE=function(){const e=new g(4095),s=Array.apply(null,Array(256)).map((m,o)=>o),n=(m,o)=>s.slice(m,o),t=n(32,127),r=n(0,24);r.push(25),r.push.apply(r,n(28,32));const l=n(0,14);let u;for(u in e.setDefault(1,0),e.addMany(t,0,2,0),l)e.addMany([24,26,153,154],u,3,0),e.addMany(n(128,144),u,3,0),e.addMany(n(144,152),u,3,0),e.add(156,u,0,0),e.add(27,u,11,1),e.add(157,u,4,8),e.addMany([152,158,159],u,0,7),e.add(155,u,11,3),e.add(144,u,11,9);return e.addMany(r,0,3,0),e.addMany(r,1,3,1),e.add(127,1,0,1),e.addMany(r,8,0,8),e.addMany(r,3,3,3),e.add(127,3,0,3),e.addMany(r,4,3,4),e.add(127,4,0,4),e.addMany(r,6,3,6),e.addMany(r,5,3,5),e.add(127,5,0,5),e.addMany(r,2,3,2),e.add(127,2,0,2),e.add(93,1,4,8),e.addMany(t,8,5,8),e.add(127,8,5,8),e.addMany([156,27,24,26,7],8,6,0),e.addMany(n(28,32),8,0,8),e.addMany([88,94,95],1,0,7),e.addMany(t,7,0,7),e.addMany(r,7,0,7),e.add(156,7,0,0),e.add(127,7,0,7),e.add(91,1,11,3),e.addMany(n(64,127),3,7,0),e.addMany(n(48,60),3,8,4),e.addMany([60,61,62,63],3,9,4),e.addMany(n(48,60),4,8,4),e.addMany(n(64,127),4,7,0),e.addMany([60,61,62,63],4,0,6),e.addMany(n(32,64),6,0,6),e.add(127,6,0,6),e.addMany(n(64,127),6,0,0),e.addMany(n(32,48),3,9,5),e.addMany(n(32,48),5,9,5),e.addMany(n(48,64),5,0,6),e.addMany(n(64,127),5,7,0),e.addMany(n(32,48),4,9,5),e.addMany(n(32,48),1,9,2),e.addMany(n(32,48),2,9,2),e.addMany(n(48,127),2,10,0),e.addMany(n(48,80),1,10,0),e.addMany(n(81,88),1,10,0),e.addMany([89,90,92],1,10,0),e.addMany(n(96,127),1,10,0),e.add(80,1,11,9),e.addMany(r,9,0,9),e.add(127,9,0,9),e.addMany(n(28,32),9,0,9),e.addMany(n(32,48),9,9,12),e.addMany(n(48,60),9,8,10),e.addMany([60,61,62,63],9,9,10),e.addMany(r,11,0,11),e.addMany(n(32,128),11,0,11),e.addMany(n(28,32),11,0,11),e.addMany(r,10,0,10),e.add(127,10,0,10),e.addMany(n(28,32),10,0,10),e.addMany(n(48,60),10,8,10),e.addMany([60,61,62,63],10,0,11),e.addMany(n(32,48),10,9,12),e.addMany(r,12,0,12),e.add(127,12,0,12),e.addMany(n(28,32),12,0,12),e.addMany(n(32,48),12,9,12),e.addMany(n(48,64),12,0,11),e.addMany(n(64,127),12,12,13),e.addMany(n(64,127),10,12,13),e.addMany(n(64,127),9,12,13),e.addMany(r,13,13,13),e.addMany(t,13,13,13),e.add(127,13,0,13),e.addMany([27,156,24,26],13,14,0),e.add(v,0,2,0),e.add(v,8,5,8),e.add(v,6,0,6),e.add(v,11,0,11),e.add(v,13,13,13),e}();class c extends h.Disposable{constructor(s=i.VT500_TRANSITION_TABLE){super(),this._transitions=s,this._parseStack={state:0,handlers:[],handlerPos:0,transition:0,chunkPos:0},this.initialState=0,this.currentState=this.initialState,this._params=new f.Params,this._params.addParam(0),this._collect=0,this.precedingCodepoint=0,this._printHandlerFb=(n,t,r)=>{},this._executeHandlerFb=n=>{},this._csiHandlerFb=(n,t)=>{},this._escHandlerFb=n=>{},this._errorHandlerFb=n=>n,this._printHandler=this._printHandlerFb,this._executeHandlers=Object.create(null),this._csiHandlers=Object.create(null),this._escHandlers=Object.create(null),this.register((0,h.toDisposable)(()=>{this._csiHandlers=Object.create(null),this._executeHandlers=Object.create(null),this._escHandlers=Object.create(null)})),this._oscParser=this.register(new d.OscParser),this._dcsParser=this.register(new _.DcsParser),this._errorHandler=this._errorHandlerFb,this.registerEscHandler({final:"\\"},()=>!0)}_identifier(s,n=[64,126]){let t=0;if(s.prefix){if(s.prefix.length>1)throw new Error("only one byte as prefix supported");if(t=s.prefix.charCodeAt(0),t&&60>t||t>63)throw new Error("prefix must be in range 0x3c .. 0x3f")}if(s.intermediates){if(s.intermediates.length>2)throw new Error("only two bytes as intermediates are supported");for(let l=0;l<s.intermediates.length;++l){const u=s.intermediates.charCodeAt(l);if(32>u||u>47)throw new Error("intermediate must be in range 0x20 .. 0x2f");t<<=8,t|=u}}if(s.final.length!==1)throw new Error("final must be a single byte");const r=s.final.charCodeAt(0);if(n[0]>r||r>n[1])throw new Error(`final must be in range ${n[0]} .. ${n[1]}`);return t<<=8,t|=r,t}identToString(s){const n=[];for(;s;)n.push(String.fromCharCode(255&s)),s>>=8;return n.reverse().join("")}setPrintHandler(s){this._printHandler=s}clearPrintHandler(){this._printHandler=this._printHandlerFb}registerEscHandler(s,n){const t=this._identifier(s,[48,126]);this._escHandlers[t]===void 0&&(this._escHandlers[t]=[]);const r=this._escHandlers[t];return r.push(n),{dispose:()=>{const l=r.indexOf(n);l!==-1&&r.splice(l,1)}}}clearEscHandler(s){this._escHandlers[this._identifier(s,[48,126])]&&delete this._escHandlers[this._identifier(s,[48,126])]}setEscHandlerFallback(s){this._escHandlerFb=s}setExecuteHandler(s,n){this._executeHandlers[s.charCodeAt(0)]=n}clearExecuteHandler(s){this._executeHandlers[s.charCodeAt(0)]&&delete this._executeHandlers[s.charCodeAt(0)]}setExecuteHandlerFallback(s){this._executeHandlerFb=s}registerCsiHandler(s,n){const t=this._identifier(s);this._csiHandlers[t]===void 0&&(this._csiHandlers[t]=[]);const r=this._csiHandlers[t];return r.push(n),{dispose:()=>{const l=r.indexOf(n);l!==-1&&r.splice(l,1)}}}clearCsiHandler(s){this._csiHandlers[this._identifier(s)]&&delete this._csiHandlers[this._identifier(s)]}setCsiHandlerFallback(s){this._csiHandlerFb=s}registerDcsHandler(s,n){return this._dcsParser.registerHandler(this._identifier(s),n)}clearDcsHandler(s){this._dcsParser.clearHandler(this._identifier(s))}setDcsHandlerFallback(s){this._dcsParser.setHandlerFallback(s)}registerOscHandler(s,n){return this._oscParser.registerHandler(s,n)}clearOscHandler(s){this._oscParser.clearHandler(s)}setOscHandlerFallback(s){this._oscParser.setHandlerFallback(s)}setErrorHandler(s){this._errorHandler=s}clearErrorHandler(){this._errorHandler=this._errorHandlerFb}reset(){this.currentState=this.initialState,this._oscParser.reset(),this._dcsParser.reset(),this._params.reset(),this._params.addParam(0),this._collect=0,this.precedingCodepoint=0,this._parseStack.state!==0&&(this._parseStack.state=2,this._parseStack.handlers=[])}_preserveStack(s,n,t,r,l){this._parseStack.state=s,this._parseStack.handlers=n,this._parseStack.handlerPos=t,this._parseStack.transition=r,this._parseStack.chunkPos=l}parse(s,n,t){let r,l=0,u=0,m=0;if(this._parseStack.state)if(this._parseStack.state===2)this._parseStack.state=0,m=this._parseStack.chunkPos+1;else{if(t===void 0||this._parseStack.state===1)throw this._parseStack.state=1,new Error("improper continuation due to previous async handler, giving up parsing");const o=this._parseStack.handlers;let p=this._parseStack.handlerPos-1;switch(this._parseStack.state){case 3:if(t===!1&&p>-1){for(;p>=0&&(r=o[p](this._params),r!==!0);p--)if(r instanceof Promise)return this._parseStack.handlerPos=p,r}this._parseStack.handlers=[];break;case 4:if(t===!1&&p>-1){for(;p>=0&&(r=o[p](),r!==!0);p--)if(r instanceof Promise)return this._parseStack.handlerPos=p,r}this._parseStack.handlers=[];break;case 6:if(l=s[this._parseStack.chunkPos],r=this._dcsParser.unhook(l!==24&&l!==26,t),r)return r;l===27&&(this._parseStack.transition|=1),this._params.reset(),this._params.addParam(0),this._collect=0;break;case 5:if(l=s[this._parseStack.chunkPos],r=this._oscParser.end(l!==24&&l!==26,t),r)return r;l===27&&(this._parseStack.transition|=1),this._params.reset(),this._params.addParam(0),this._collect=0}this._parseStack.state=0,m=this._parseStack.chunkPos+1,this.precedingCodepoint=0,this.currentState=15&this._parseStack.transition}for(let o=m;o<n;++o){switch(l=s[o],u=this._transitions.table[this.currentState<<8|(l<160?l:v)],u>>4){case 2:for(let D=o+1;;++D){if(D>=n||(l=s[D])<32||l>126&&l<v){this._printHandler(s,o,D),o=D-1;break}if(++D>=n||(l=s[D])<32||l>126&&l<v){this._printHandler(s,o,D),o=D-1;break}if(++D>=n||(l=s[D])<32||l>126&&l<v){this._printHandler(s,o,D),o=D-1;break}if(++D>=n||(l=s[D])<32||l>126&&l<v){this._printHandler(s,o,D),o=D-1;break}}break;case 3:this._executeHandlers[l]?this._executeHandlers[l]():this._executeHandlerFb(l),this.precedingCodepoint=0;break;case 0:break;case 1:if(this._errorHandler({position:o,code:l,currentState:this.currentState,collect:this._collect,params:this._params,abort:!1}).abort)return;break;case 7:const p=this._csiHandlers[this._collect<<8|l];let C=p?p.length-1:-1;for(;C>=0&&(r=p[C](this._params),r!==!0);C--)if(r instanceof Promise)return this._preserveStack(3,p,C,u,o),r;C<0&&this._csiHandlerFb(this._collect<<8|l,this._params),this.precedingCodepoint=0;break;case 8:do switch(l){case 59:this._params.addParam(0);break;case 58:this._params.addSubParam(-1);break;default:this._params.addDigit(l-48)}while(++o<n&&(l=s[o])>47&&l<60);o--;break;case 9:this._collect<<=8,this._collect|=l;break;case 10:const y=this._escHandlers[this._collect<<8|l];let w=y?y.length-1:-1;for(;w>=0&&(r=y[w](),r!==!0);w--)if(r instanceof Promise)return this._preserveStack(4,y,w,u,o),r;w<0&&this._escHandlerFb(this._collect<<8|l),this.precedingCodepoint=0;break;case 11:this._params.reset(),this._params.addParam(0),this._collect=0;break;case 12:this._dcsParser.hook(this._collect<<8|l,this._params);break;case 13:for(let D=o+1;;++D)if(D>=n||(l=s[D])===24||l===26||l===27||l>127&&l<v){this._dcsParser.put(s,o,D),o=D-1;break}break;case 14:if(r=this._dcsParser.unhook(l!==24&&l!==26),r)return this._preserveStack(6,[],0,u,o),r;l===27&&(u|=1),this._params.reset(),this._params.addParam(0),this._collect=0,this.precedingCodepoint=0;break;case 4:this._oscParser.start();break;case 5:for(let D=o+1;;D++)if(D>=n||(l=s[D])<32||l>127&&l<v){this._oscParser.put(s,o,D),o=D-1;break}break;case 6:if(r=this._oscParser.end(l!==24&&l!==26),r)return this._preserveStack(5,[],0,u,o),r;l===27&&(u|=1),this._params.reset(),this._params.addParam(0),this._collect=0,this.precedingCodepoint=0}this.currentState=15&u}}}i.EscapeSequenceParser=c},6242:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.OscHandler=i.OscParser=void 0;const h=a(5770),f=a(482),d=[];i.OscParser=class{constructor(){this._state=0,this._active=d,this._id=-1,this._handlers=Object.create(null),this._handlerFb=()=>{},this._stack={paused:!1,loopPosition:0,fallThrough:!1}}registerHandler(_,g){this._handlers[_]===void 0&&(this._handlers[_]=[]);const v=this._handlers[_];return v.push(g),{dispose:()=>{const c=v.indexOf(g);c!==-1&&v.splice(c,1)}}}clearHandler(_){this._handlers[_]&&delete this._handlers[_]}setHandlerFallback(_){this._handlerFb=_}dispose(){this._handlers=Object.create(null),this._handlerFb=()=>{},this._active=d}reset(){if(this._state===2)for(let _=this._stack.paused?this._stack.loopPosition-1:this._active.length-1;_>=0;--_)this._active[_].end(!1);this._stack.paused=!1,this._active=d,this._id=-1,this._state=0}_start(){if(this._active=this._handlers[this._id]||d,this._active.length)for(let _=this._active.length-1;_>=0;_--)this._active[_].start();else this._handlerFb(this._id,"START")}_put(_,g,v){if(this._active.length)for(let c=this._active.length-1;c>=0;c--)this._active[c].put(_,g,v);else this._handlerFb(this._id,"PUT",(0,f.utf32ToString)(_,g,v))}start(){this.reset(),this._state=1}put(_,g,v){if(this._state!==3){if(this._state===1)for(;g<v;){const c=_[g++];if(c===59){this._state=2,this._start();break}if(c<48||57<c)return void(this._state=3);this._id===-1&&(this._id=0),this._id=10*this._id+c-48}this._state===2&&v-g>0&&this._put(_,g,v)}}end(_,g=!0){if(this._state!==0){if(this._state!==3)if(this._state===1&&this._start(),this._active.length){let v=!1,c=this._active.length-1,e=!1;if(this._stack.paused&&(c=this._stack.loopPosition-1,v=g,e=this._stack.fallThrough,this._stack.paused=!1),!e&&v===!1){for(;c>=0&&(v=this._active[c].end(_),v!==!0);c--)if(v instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=c,this._stack.fallThrough=!1,v;c--}for(;c>=0;c--)if(v=this._active[c].end(!1),v instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=c,this._stack.fallThrough=!0,v}else this._handlerFb(this._id,"END",_);this._active=d,this._id=-1,this._state=0}}},i.OscHandler=class{constructor(_){this._handler=_,this._data="",this._hitLimit=!1}start(){this._data="",this._hitLimit=!1}put(_,g,v){this._hitLimit||(this._data+=(0,f.utf32ToString)(_,g,v),this._data.length>h.PAYLOAD_LIMIT&&(this._data="",this._hitLimit=!0))}end(_){let g=!1;if(this._hitLimit)g=!1;else if(_&&(g=this._handler(this._data),g instanceof Promise))return g.then(v=>(this._data="",this._hitLimit=!1,v));return this._data="",this._hitLimit=!1,g}}},8742:(T,i)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.Params=void 0;const a=2147483647;class h{constructor(d=32,_=32){if(this.maxLength=d,this.maxSubParamsLength=_,_>256)throw new Error("maxSubParamsLength must not be greater than 256");this.params=new Int32Array(d),this.length=0,this._subParams=new Int32Array(_),this._subParamsLength=0,this._subParamsIdx=new Uint16Array(d),this._rejectDigits=!1,this._rejectSubDigits=!1,this._digitIsSub=!1}static fromArray(d){const _=new h;if(!d.length)return _;for(let g=Array.isArray(d[0])?1:0;g<d.length;++g){const v=d[g];if(Array.isArray(v))for(let c=0;c<v.length;++c)_.addSubParam(v[c]);else _.addParam(v)}return _}clone(){const d=new h(this.maxLength,this.maxSubParamsLength);return d.params.set(this.params),d.length=this.length,d._subParams.set(this._subParams),d._subParamsLength=this._subParamsLength,d._subParamsIdx.set(this._subParamsIdx),d._rejectDigits=this._rejectDigits,d._rejectSubDigits=this._rejectSubDigits,d._digitIsSub=this._digitIsSub,d}toArray(){const d=[];for(let _=0;_<this.length;++_){d.push(this.params[_]);const g=this._subParamsIdx[_]>>8,v=255&this._subParamsIdx[_];v-g>0&&d.push(Array.prototype.slice.call(this._subParams,g,v))}return d}reset(){this.length=0,this._subParamsLength=0,this._rejectDigits=!1,this._rejectSubDigits=!1,this._digitIsSub=!1}addParam(d){if(this._digitIsSub=!1,this.length>=this.maxLength)this._rejectDigits=!0;else{if(d<-1)throw new Error("values lesser than -1 are not allowed");this._subParamsIdx[this.length]=this._subParamsLength<<8|this._subParamsLength,this.params[this.length++]=d>a?a:d}}addSubParam(d){if(this._digitIsSub=!0,this.length)if(this._rejectDigits||this._subParamsLength>=this.maxSubParamsLength)this._rejectSubDigits=!0;else{if(d<-1)throw new Error("values lesser than -1 are not allowed");this._subParams[this._subParamsLength++]=d>a?a:d,this._subParamsIdx[this.length-1]++}}hasSubParams(d){return(255&this._subParamsIdx[d])-(this._subParamsIdx[d]>>8)>0}getSubParams(d){const _=this._subParamsIdx[d]>>8,g=255&this._subParamsIdx[d];return g-_>0?this._subParams.subarray(_,g):null}getSubParamsAll(){const d={};for(let _=0;_<this.length;++_){const g=this._subParamsIdx[_]>>8,v=255&this._subParamsIdx[_];v-g>0&&(d[_]=this._subParams.slice(g,v))}return d}addDigit(d){let _;if(this._rejectDigits||!(_=this._digitIsSub?this._subParamsLength:this.length)||this._digitIsSub&&this._rejectSubDigits)return;const g=this._digitIsSub?this._subParams:this.params,v=g[_-1];g[_-1]=~v?Math.min(10*v+d,a):d}}i.Params=h},5741:(T,i)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.AddonManager=void 0,i.AddonManager=class{constructor(){this._addons=[]}dispose(){for(let a=this._addons.length-1;a>=0;a--)this._addons[a].instance.dispose()}loadAddon(a,h){const f={instance:h,dispose:h.dispose,isDisposed:!1};this._addons.push(f),h.dispose=()=>this._wrappedAddonDispose(f),h.activate(a)}_wrappedAddonDispose(a){if(a.isDisposed)return;let h=-1;for(let f=0;f<this._addons.length;f++)if(this._addons[f]===a){h=f;break}if(h===-1)throw new Error("Could not dispose an addon that has not been loaded");a.isDisposed=!0,a.dispose.apply(a.instance),this._addons.splice(h,1)}}},8771:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.BufferApiView=void 0;const h=a(3785),f=a(511);i.BufferApiView=class{constructor(d,_){this._buffer=d,this.type=_}init(d){return this._buffer=d,this}get cursorY(){return this._buffer.y}get cursorX(){return this._buffer.x}get viewportY(){return this._buffer.ydisp}get baseY(){return this._buffer.ybase}get length(){return this._buffer.lines.length}getLine(d){const _=this._buffer.lines.get(d);if(_)return new h.BufferLineApiView(_)}getNullCell(){return new f.CellData}}},3785:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.BufferLineApiView=void 0;const h=a(511);i.BufferLineApiView=class{constructor(f){this._line=f}get isWrapped(){return this._line.isWrapped}get length(){return this._line.length}getCell(f,d){if(!(f<0||f>=this._line.length))return d?(this._line.loadCell(f,d),d):this._line.loadCell(f,new h.CellData)}translateToString(f,d,_){return this._line.translateToString(f,d,_)}}},8285:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.BufferNamespaceApi=void 0;const h=a(8771),f=a(8460);i.BufferNamespaceApi=class{constructor(d){this._core=d,this._onBufferChange=new f.EventEmitter,this.onBufferChange=this._onBufferChange.event,this._normal=new h.BufferApiView(this._core.buffers.normal,"normal"),this._alternate=new h.BufferApiView(this._core.buffers.alt,"alternate"),this._core.buffers.onBufferActivate(()=>this._onBufferChange.fire(this.active))}get active(){if(this._core.buffers.active===this._core.buffers.normal)return this.normal;if(this._core.buffers.active===this._core.buffers.alt)return this.alternate;throw new Error("Active buffer is neither normal nor alternate")}get normal(){return this._normal.init(this._core.buffers.normal)}get alternate(){return this._alternate.init(this._core.buffers.alt)}}},7975:(T,i)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.ParserApi=void 0,i.ParserApi=class{constructor(a){this._core=a}registerCsiHandler(a,h){return this._core.registerCsiHandler(a,f=>h(f.toArray()))}addCsiHandler(a,h){return this.registerCsiHandler(a,h)}registerDcsHandler(a,h){return this._core.registerDcsHandler(a,(f,d)=>h(f,d.toArray()))}addDcsHandler(a,h){return this.registerDcsHandler(a,h)}registerEscHandler(a,h){return this._core.registerEscHandler(a,h)}addEscHandler(a,h){return this.registerEscHandler(a,h)}registerOscHandler(a,h){return this._core.registerOscHandler(a,h)}addOscHandler(a,h){return this.registerOscHandler(a,h)}}},7090:(T,i)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.UnicodeApi=void 0,i.UnicodeApi=class{constructor(a){this._core=a}register(a){this._core.unicodeService.register(a)}get versions(){return this._core.unicodeService.versions}get activeVersion(){return this._core.unicodeService.activeVersion}set activeVersion(a){this._core.unicodeService.activeVersion=a}}},744:function(T,i,a){var h=this&&this.__decorate||function(e,s,n,t){var r,l=arguments.length,u=l<3?s:t===null?t=Object.getOwnPropertyDescriptor(s,n):t;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")u=Reflect.decorate(e,s,n,t);else for(var m=e.length-1;m>=0;m--)(r=e[m])&&(u=(l<3?r(u):l>3?r(s,n,u):r(s,n))||u);return l>3&&u&&Object.defineProperty(s,n,u),u},f=this&&this.__param||function(e,s){return function(n,t){s(n,t,e)}};Object.defineProperty(i,"__esModule",{value:!0}),i.BufferService=i.MINIMUM_ROWS=i.MINIMUM_COLS=void 0;const d=a(2585),_=a(5295),g=a(8460),v=a(844);i.MINIMUM_COLS=2,i.MINIMUM_ROWS=1;let c=class extends v.Disposable{constructor(e){super(),this.isUserScrolling=!1,this._onResize=this.register(new g.EventEmitter),this.onResize=this._onResize.event,this._onScroll=this.register(new g.EventEmitter),this.onScroll=this._onScroll.event,this.cols=Math.max(e.rawOptions.cols||0,i.MINIMUM_COLS),this.rows=Math.max(e.rawOptions.rows||0,i.MINIMUM_ROWS),this.buffers=this.register(new _.BufferSet(e,this))}get buffer(){return this.buffers.active}resize(e,s){this.cols=e,this.rows=s,this.buffers.resize(e,s),this._onResize.fire({cols:e,rows:s})}reset(){this.buffers.reset(),this.isUserScrolling=!1}scroll(e,s=!1){const n=this.buffer;let t;t=this._cachedBlankLine,t&&t.length===this.cols&&t.getFg(0)===e.fg&&t.getBg(0)===e.bg||(t=n.getBlankLine(e,s),this._cachedBlankLine=t),t.isWrapped=s;const r=n.ybase+n.scrollTop,l=n.ybase+n.scrollBottom;if(n.scrollTop===0){const u=n.lines.isFull;l===n.lines.length-1?u?n.lines.recycle().copyFrom(t):n.lines.push(t.clone()):n.lines.splice(l+1,0,t.clone()),u?this.isUserScrolling&&(n.ydisp=Math.max(n.ydisp-1,0)):(n.ybase++,this.isUserScrolling||n.ydisp++)}else{const u=l-r+1;n.lines.shiftElements(r+1,u-1,-1),n.lines.set(l,t.clone())}this.isUserScrolling||(n.ydisp=n.ybase),this._onScroll.fire(n.ydisp)}scrollLines(e,s,n){const t=this.buffer;if(e<0){if(t.ydisp===0)return;this.isUserScrolling=!0}else e+t.ydisp>=t.ybase&&(this.isUserScrolling=!1);const r=t.ydisp;t.ydisp=Math.max(Math.min(t.ydisp+e,t.ybase),0),r!==t.ydisp&&(s||this._onScroll.fire(t.ydisp))}scrollPages(e){this.scrollLines(e*(this.rows-1))}scrollToTop(){this.scrollLines(-this.buffer.ydisp)}scrollToBottom(){this.scrollLines(this.buffer.ybase-this.buffer.ydisp)}scrollToLine(e){const s=e-this.buffer.ydisp;s!==0&&this.scrollLines(s)}};c=h([f(0,d.IOptionsService)],c),i.BufferService=c},7994:(T,i)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.CharsetService=void 0,i.CharsetService=class{constructor(){this.glevel=0,this._charsets=[]}reset(){this.charset=void 0,this._charsets=[],this.glevel=0}setgLevel(a){this.glevel=a,this.charset=this._charsets[a]}setgCharset(a,h){this._charsets[a]=h,this.glevel===a&&(this.charset=h)}}},1753:function(T,i,a){var h=this&&this.__decorate||function(t,r,l,u){var m,o=arguments.length,p=o<3?r:u===null?u=Object.getOwnPropertyDescriptor(r,l):u;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")p=Reflect.decorate(t,r,l,u);else for(var C=t.length-1;C>=0;C--)(m=t[C])&&(p=(o<3?m(p):o>3?m(r,l,p):m(r,l))||p);return o>3&&p&&Object.defineProperty(r,l,p),p},f=this&&this.__param||function(t,r){return function(l,u){r(l,u,t)}};Object.defineProperty(i,"__esModule",{value:!0}),i.CoreMouseService=void 0;const d=a(2585),_=a(8460),g=a(844),v={NONE:{events:0,restrict:()=>!1},X10:{events:1,restrict:t=>t.button!==4&&t.action===1&&(t.ctrl=!1,t.alt=!1,t.shift=!1,!0)},VT200:{events:19,restrict:t=>t.action!==32},DRAG:{events:23,restrict:t=>t.action!==32||t.button!==3},ANY:{events:31,restrict:t=>!0}};function c(t,r){let l=(t.ctrl?16:0)|(t.shift?4:0)|(t.alt?8:0);return t.button===4?(l|=64,l|=t.action):(l|=3&t.button,4&t.button&&(l|=64),8&t.button&&(l|=128),t.action===32?l|=32:t.action!==0||r||(l|=3)),l}const e=String.fromCharCode,s={DEFAULT:t=>{const r=[c(t,!1)+32,t.col+32,t.row+32];return r[0]>255||r[1]>255||r[2]>255?"":`\x1B[M${e(r[0])}${e(r[1])}${e(r[2])}`},SGR:t=>{const r=t.action===0&&t.button!==4?"m":"M";return`\x1B[<${c(t,!0)};${t.col};${t.row}${r}`},SGR_PIXELS:t=>{const r=t.action===0&&t.button!==4?"m":"M";return`\x1B[<${c(t,!0)};${t.x};${t.y}${r}`}};let n=class extends g.Disposable{constructor(t,r){super(),this._bufferService=t,this._coreService=r,this._protocols={},this._encodings={},this._activeProtocol="",this._activeEncoding="",this._lastEvent=null,this._onProtocolChange=this.register(new _.EventEmitter),this.onProtocolChange=this._onProtocolChange.event;for(const l of Object.keys(v))this.addProtocol(l,v[l]);for(const l of Object.keys(s))this.addEncoding(l,s[l]);this.reset()}addProtocol(t,r){this._protocols[t]=r}addEncoding(t,r){this._encodings[t]=r}get activeProtocol(){return this._activeProtocol}get areMouseEventsActive(){return this._protocols[this._activeProtocol].events!==0}set activeProtocol(t){if(!this._protocols[t])throw new Error(`unknown protocol "${t}"`);this._activeProtocol=t,this._onProtocolChange.fire(this._protocols[t].events)}get activeEncoding(){return this._activeEncoding}set activeEncoding(t){if(!this._encodings[t])throw new Error(`unknown encoding "${t}"`);this._activeEncoding=t}reset(){this.activeProtocol="NONE",this.activeEncoding="DEFAULT",this._lastEvent=null}triggerMouseEvent(t){if(t.col<0||t.col>=this._bufferService.cols||t.row<0||t.row>=this._bufferService.rows||t.button===4&&t.action===32||t.button===3&&t.action!==32||t.button!==4&&(t.action===2||t.action===3)||(t.col++,t.row++,t.action===32&&this._lastEvent&&this._equalEvents(this._lastEvent,t,this._activeEncoding==="SGR_PIXELS"))||!this._protocols[this._activeProtocol].restrict(t))return!1;const r=this._encodings[this._activeEncoding](t);return r&&(this._activeEncoding==="DEFAULT"?this._coreService.triggerBinaryEvent(r):this._coreService.triggerDataEvent(r,!0)),this._lastEvent=t,!0}explainEvents(t){return{down:!!(1&t),up:!!(2&t),drag:!!(4&t),move:!!(8&t),wheel:!!(16&t)}}_equalEvents(t,r,l){if(l){if(t.x!==r.x||t.y!==r.y)return!1}else if(t.col!==r.col||t.row!==r.row)return!1;return t.button===r.button&&t.action===r.action&&t.ctrl===r.ctrl&&t.alt===r.alt&&t.shift===r.shift}};n=h([f(0,d.IBufferService),f(1,d.ICoreService)],n),i.CoreMouseService=n},6975:function(T,i,a){var h=this&&this.__decorate||function(n,t,r,l){var u,m=arguments.length,o=m<3?t:l===null?l=Object.getOwnPropertyDescriptor(t,r):l;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")o=Reflect.decorate(n,t,r,l);else for(var p=n.length-1;p>=0;p--)(u=n[p])&&(o=(m<3?u(o):m>3?u(t,r,o):u(t,r))||o);return m>3&&o&&Object.defineProperty(t,r,o),o},f=this&&this.__param||function(n,t){return function(r,l){t(r,l,n)}};Object.defineProperty(i,"__esModule",{value:!0}),i.CoreService=void 0;const d=a(2585),_=a(8460),g=a(1439),v=a(844),c=Object.freeze({insertMode:!1}),e=Object.freeze({applicationCursorKeys:!1,applicationKeypad:!1,bracketedPasteMode:!1,origin:!1,reverseWraparound:!1,sendFocus:!1,wraparound:!0});let s=class extends v.Disposable{constructor(n,t,r){super(),this._bufferService=n,this._logService=t,this._optionsService=r,this.isCursorInitialized=!1,this.isCursorHidden=!1,this._onData=this.register(new _.EventEmitter),this.onData=this._onData.event,this._onUserInput=this.register(new _.EventEmitter),this.onUserInput=this._onUserInput.event,this._onBinary=this.register(new _.EventEmitter),this.onBinary=this._onBinary.event,this._onRequestScrollToBottom=this.register(new _.EventEmitter),this.onRequestScrollToBottom=this._onRequestScrollToBottom.event,this.modes=(0,g.clone)(c),this.decPrivateModes=(0,g.clone)(e)}reset(){this.modes=(0,g.clone)(c),this.decPrivateModes=(0,g.clone)(e)}triggerDataEvent(n,t=!1){if(this._optionsService.rawOptions.disableStdin)return;const r=this._bufferService.buffer;t&&this._optionsService.rawOptions.scrollOnUserInput&&r.ybase!==r.ydisp&&this._onRequestScrollToBottom.fire(),t&&this._onUserInput.fire(),this._logService.debug(`sending data "${n}"`,()=>n.split("").map(l=>l.charCodeAt(0))),this._onData.fire(n)}triggerBinaryEvent(n){this._optionsService.rawOptions.disableStdin||(this._logService.debug(`sending binary "${n}"`,()=>n.split("").map(t=>t.charCodeAt(0))),this._onBinary.fire(n))}};s=h([f(0,d.IBufferService),f(1,d.ILogService),f(2,d.IOptionsService)],s),i.CoreService=s},9074:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.DecorationService=void 0;const h=a(8055),f=a(8460),d=a(844),_=a(6106);let g=0,v=0;class c extends d.Disposable{constructor(){super(),this._decorations=new _.SortedList(n=>n==null?void 0:n.marker.line),this._onDecorationRegistered=this.register(new f.EventEmitter),this.onDecorationRegistered=this._onDecorationRegistered.event,this._onDecorationRemoved=this.register(new f.EventEmitter),this.onDecorationRemoved=this._onDecorationRemoved.event,this.register((0,d.toDisposable)(()=>{for(const n of this._decorations.values())this._onDecorationRemoved.fire(n);this.reset()}))}get decorations(){return this._decorations.values()}registerDecoration(n){if(n.marker.isDisposed)return;const t=new e(n);if(t){const r=t.marker.onDispose(()=>t.dispose());t.onDispose(()=>{t&&(this._decorations.delete(t)&&this._onDecorationRemoved.fire(t),r.dispose())}),this._decorations.insert(t),this._onDecorationRegistered.fire(t)}return t}reset(){for(const n of this._decorations.values())n.dispose();this._decorations.clear()}*getDecorationsAtCell(n,t,r){var l,u,m;let o=0,p=0;for(const C of this._decorations.getKeyIterator(t))o=(l=C.options.x)!==null&&l!==void 0?l:0,p=o+((u=C.options.width)!==null&&u!==void 0?u:1),n>=o&&n<p&&(!r||((m=C.options.layer)!==null&&m!==void 0?m:"bottom")===r)&&(yield C)}forEachDecorationAtCell(n,t,r,l){this._decorations.forEachByKey(t,u=>{var m,o,p;g=(m=u.options.x)!==null&&m!==void 0?m:0,v=g+((o=u.options.width)!==null&&o!==void 0?o:1),n>=g&&n<v&&(!r||((p=u.options.layer)!==null&&p!==void 0?p:"bottom")===r)&&l(u)})}dispose(){for(const n of this._decorations.values())this._onDecorationRemoved.fire(n);this.reset()}}i.DecorationService=c;class e extends d.Disposable{constructor(n){super(),this.options=n,this.isDisposed=!1,this.onRenderEmitter=this.register(new f.EventEmitter),this.onRender=this.onRenderEmitter.event,this._onDispose=this.register(new f.EventEmitter),this.onDispose=this._onDispose.event,this._cachedBg=null,this._cachedFg=null,this.marker=n.marker,this.options.overviewRulerOptions&&!this.options.overviewRulerOptions.position&&(this.options.overviewRulerOptions.position="full")}get backgroundColorRGB(){return this._cachedBg===null&&(this.options.backgroundColor?this._cachedBg=h.css.toColor(this.options.backgroundColor):this._cachedBg=void 0),this._cachedBg}get foregroundColorRGB(){return this._cachedFg===null&&(this.options.foregroundColor?this._cachedFg=h.css.toColor(this.options.foregroundColor):this._cachedFg=void 0),this._cachedFg}dispose(){this._onDispose.fire(),super.dispose()}}},4348:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.InstantiationService=i.ServiceCollection=void 0;const h=a(2585),f=a(8343);class d{constructor(...g){this._entries=new Map;for(const[v,c]of g)this.set(v,c)}set(g,v){const c=this._entries.get(g);return this._entries.set(g,v),c}forEach(g){for(const[v,c]of this._entries.entries())g(v,c)}has(g){return this._entries.has(g)}get(g){return this._entries.get(g)}}i.ServiceCollection=d,i.InstantiationService=class{constructor(){this._services=new d,this._services.set(h.IInstantiationService,this)}setService(_,g){this._services.set(_,g)}getService(_){return this._services.get(_)}createInstance(_,...g){const v=(0,f.getServiceDependencies)(_).sort((s,n)=>s.index-n.index),c=[];for(const s of v){const n=this._services.get(s.id);if(!n)throw new Error(`[createInstance] ${_.name} depends on UNKNOWN service ${s.id}.`);c.push(n)}const e=v.length>0?v[0].index:g.length;if(g.length!==e)throw new Error(`[createInstance] First service dependency of ${_.name} at position ${e+1} conflicts with ${g.length} static arguments`);return new _(...g,...c)}}},7866:function(T,i,a){var h=this&&this.__decorate||function(c,e,s,n){var t,r=arguments.length,l=r<3?e:n===null?n=Object.getOwnPropertyDescriptor(e,s):n;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")l=Reflect.decorate(c,e,s,n);else for(var u=c.length-1;u>=0;u--)(t=c[u])&&(l=(r<3?t(l):r>3?t(e,s,l):t(e,s))||l);return r>3&&l&&Object.defineProperty(e,s,l),l},f=this&&this.__param||function(c,e){return function(s,n){e(s,n,c)}};Object.defineProperty(i,"__esModule",{value:!0}),i.LogService=void 0;const d=a(844),_=a(2585),g={debug:_.LogLevelEnum.DEBUG,info:_.LogLevelEnum.INFO,warn:_.LogLevelEnum.WARN,error:_.LogLevelEnum.ERROR,off:_.LogLevelEnum.OFF};let v=class extends d.Disposable{constructor(c){super(),this._optionsService=c,this.logLevel=_.LogLevelEnum.OFF,this._updateLogLevel(),this.register(this._optionsService.onSpecificOptionChange("logLevel",()=>this._updateLogLevel()))}_updateLogLevel(){this.logLevel=g[this._optionsService.rawOptions.logLevel]}_evalLazyOptionalParams(c){for(let e=0;e<c.length;e++)typeof c[e]=="function"&&(c[e]=c[e]())}_log(c,e,s){this._evalLazyOptionalParams(s),c.call(console,"xterm.js: "+e,...s)}debug(c,...e){this.logLevel<=_.LogLevelEnum.DEBUG&&this._log(console.log,c,e)}info(c,...e){this.logLevel<=_.LogLevelEnum.INFO&&this._log(console.info,c,e)}warn(c,...e){this.logLevel<=_.LogLevelEnum.WARN&&this._log(console.warn,c,e)}error(c,...e){this.logLevel<=_.LogLevelEnum.ERROR&&this._log(console.error,c,e)}};v=h([f(0,_.IOptionsService)],v),i.LogService=v},7302:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.OptionsService=i.DEFAULT_OPTIONS=void 0;const h=a(8460),f=a(6114),d=a(844);i.DEFAULT_OPTIONS={cols:80,rows:24,cursorBlink:!1,cursorStyle:"block",cursorWidth:1,customGlyphs:!0,drawBoldTextInBrightColors:!0,fastScrollModifier:"alt",fastScrollSensitivity:5,fontFamily:"courier-new, courier, monospace",fontSize:15,fontWeight:"normal",fontWeightBold:"bold",lineHeight:1,letterSpacing:0,linkHandler:null,logLevel:"info",scrollback:1e3,scrollOnUserInput:!0,scrollSensitivity:1,screenReaderMode:!1,smoothScrollDuration:0,macOptionIsMeta:!1,macOptionClickForcesSelection:!1,minimumContrastRatio:1,disableStdin:!1,allowProposedApi:!1,allowTransparency:!1,tabStopWidth:8,theme:{},rightClickSelectsWord:f.isMac,windowOptions:{},windowsMode:!1,wordSeparator:" ()[]{}',\"`",altClickMovesCursor:!0,convertEol:!1,termName:"xterm",cancelEvents:!1,overviewRulerWidth:0};const _=["normal","bold","100","200","300","400","500","600","700","800","900"];class g extends d.Disposable{constructor(c){super(),this._onOptionChange=this.register(new h.EventEmitter),this.onOptionChange=this._onOptionChange.event;const e=Object.assign({},i.DEFAULT_OPTIONS);for(const s in c)if(s in e)try{const n=c[s];e[s]=this._sanitizeAndValidateOption(s,n)}catch(n){console.error(n)}this.rawOptions=e,this.options=Object.assign({},e),this._setupOptions()}onSpecificOptionChange(c,e){return this.onOptionChange(s=>{s===c&&e(this.rawOptions[c])})}onMultipleOptionChange(c,e){return this.onOptionChange(s=>{c.indexOf(s)!==-1&&e()})}_setupOptions(){const c=s=>{if(!(s in i.DEFAULT_OPTIONS))throw new Error(`No option with key "${s}"`);return this.rawOptions[s]},e=(s,n)=>{if(!(s in i.DEFAULT_OPTIONS))throw new Error(`No option with key "${s}"`);n=this._sanitizeAndValidateOption(s,n),this.rawOptions[s]!==n&&(this.rawOptions[s]=n,this._onOptionChange.fire(s))};for(const s in this.rawOptions){const n={get:c.bind(this,s),set:e.bind(this,s)};Object.defineProperty(this.options,s,n)}}_sanitizeAndValidateOption(c,e){switch(c){case"cursorStyle":if(e||(e=i.DEFAULT_OPTIONS[c]),!function(s){return s==="block"||s==="underline"||s==="bar"}(e))throw new Error(`"${e}" is not a valid value for ${c}`);break;case"wordSeparator":e||(e=i.DEFAULT_OPTIONS[c]);break;case"fontWeight":case"fontWeightBold":if(typeof e=="number"&&1<=e&&e<=1e3)break;e=_.includes(e)?e:i.DEFAULT_OPTIONS[c];break;case"cursorWidth":e=Math.floor(e);case"lineHeight":case"tabStopWidth":if(e<1)throw new Error(`${c} cannot be less than 1, value: ${e}`);break;case"minimumContrastRatio":e=Math.max(1,Math.min(21,Math.round(10*e)/10));break;case"scrollback":if((e=Math.min(e,4294967295))<0)throw new Error(`${c} cannot be less than 0, value: ${e}`);break;case"fastScrollSensitivity":case"scrollSensitivity":if(e<=0)throw new Error(`${c} cannot be less than or equal to 0, value: ${e}`);case"rows":case"cols":if(!e&&e!==0)throw new Error(`${c} must be numeric, value: ${e}`)}return e}}i.OptionsService=g},2660:function(T,i,a){var h=this&&this.__decorate||function(g,v,c,e){var s,n=arguments.length,t=n<3?v:e===null?e=Object.getOwnPropertyDescriptor(v,c):e;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")t=Reflect.decorate(g,v,c,e);else for(var r=g.length-1;r>=0;r--)(s=g[r])&&(t=(n<3?s(t):n>3?s(v,c,t):s(v,c))||t);return n>3&&t&&Object.defineProperty(v,c,t),t},f=this&&this.__param||function(g,v){return function(c,e){v(c,e,g)}};Object.defineProperty(i,"__esModule",{value:!0}),i.OscLinkService=void 0;const d=a(2585);let _=class{constructor(g){this._bufferService=g,this._nextId=1,this._entriesWithId=new Map,this._dataByLinkId=new Map}registerLink(g){const v=this._bufferService.buffer;if(g.id===void 0){const r=v.addMarker(v.ybase+v.y),l={data:g,id:this._nextId++,lines:[r]};return r.onDispose(()=>this._removeMarkerFromLink(l,r)),this._dataByLinkId.set(l.id,l),l.id}const c=g,e=this._getEntryIdKey(c),s=this._entriesWithId.get(e);if(s)return this.addLineToLink(s.id,v.ybase+v.y),s.id;const n=v.addMarker(v.ybase+v.y),t={id:this._nextId++,key:this._getEntryIdKey(c),data:c,lines:[n]};return n.onDispose(()=>this._removeMarkerFromLink(t,n)),this._entriesWithId.set(t.key,t),this._dataByLinkId.set(t.id,t),t.id}addLineToLink(g,v){const c=this._dataByLinkId.get(g);if(c&&c.lines.every(e=>e.line!==v)){const e=this._bufferService.buffer.addMarker(v);c.lines.push(e),e.onDispose(()=>this._removeMarkerFromLink(c,e))}}getLinkData(g){var v;return(v=this._dataByLinkId.get(g))===null||v===void 0?void 0:v.data}_getEntryIdKey(g){return`${g.id};;${g.uri}`}_removeMarkerFromLink(g,v){const c=g.lines.indexOf(v);c!==-1&&(g.lines.splice(c,1),g.lines.length===0&&(g.data.id!==void 0&&this._entriesWithId.delete(g.key),this._dataByLinkId.delete(g.id)))}};_=h([f(0,d.IBufferService)],_),i.OscLinkService=_},8343:(T,i)=>{function a(h,f,d){f.di$target===f?f.di$dependencies.push({id:h,index:d}):(f.di$dependencies=[{id:h,index:d}],f.di$target=f)}Object.defineProperty(i,"__esModule",{value:!0}),i.createDecorator=i.getServiceDependencies=i.serviceRegistry=void 0,i.serviceRegistry=new Map,i.getServiceDependencies=function(h){return h.di$dependencies||[]},i.createDecorator=function(h){if(i.serviceRegistry.has(h))return i.serviceRegistry.get(h);const f=function(d,_,g){if(arguments.length!==3)throw new Error("@IServiceName-decorator can only be used to decorate a parameter");a(f,d,g)};return f.toString=()=>h,i.serviceRegistry.set(h,f),f}},2585:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.IDecorationService=i.IUnicodeService=i.IOscLinkService=i.IOptionsService=i.ILogService=i.LogLevelEnum=i.IInstantiationService=i.ICharsetService=i.ICoreService=i.ICoreMouseService=i.IBufferService=void 0;const h=a(8343);var f;i.IBufferService=(0,h.createDecorator)("BufferService"),i.ICoreMouseService=(0,h.createDecorator)("CoreMouseService"),i.ICoreService=(0,h.createDecorator)("CoreService"),i.ICharsetService=(0,h.createDecorator)("CharsetService"),i.IInstantiationService=(0,h.createDecorator)("InstantiationService"),(f=i.LogLevelEnum||(i.LogLevelEnum={}))[f.DEBUG=0]="DEBUG",f[f.INFO=1]="INFO",f[f.WARN=2]="WARN",f[f.ERROR=3]="ERROR",f[f.OFF=4]="OFF",i.ILogService=(0,h.createDecorator)("LogService"),i.IOptionsService=(0,h.createDecorator)("OptionsService"),i.IOscLinkService=(0,h.createDecorator)("OscLinkService"),i.IUnicodeService=(0,h.createDecorator)("UnicodeService"),i.IDecorationService=(0,h.createDecorator)("DecorationService")},1480:(T,i,a)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.UnicodeService=void 0;const h=a(8460),f=a(225);i.UnicodeService=class{constructor(){this._providers=Object.create(null),this._active="",this._onChange=new h.EventEmitter,this.onChange=this._onChange.event;const d=new f.UnicodeV6;this.register(d),this._active=d.version,this._activeProvider=d}dispose(){this._onChange.dispose()}get versions(){return Object.keys(this._providers)}get activeVersion(){return this._active}set activeVersion(d){if(!this._providers[d])throw new Error(`unknown Unicode version "${d}"`);this._active=d,this._activeProvider=this._providers[d],this._onChange.fire(d)}register(d){this._providers[d.version]=d}wcwidth(d){return this._activeProvider.wcwidth(d)}getStringCellWidth(d){let _=0;const g=d.length;for(let v=0;v<g;++v){let c=d.charCodeAt(v);if(55296<=c&&c<=56319){if(++v>=g)return _+this.wcwidth(c);const e=d.charCodeAt(v);56320<=e&&e<=57343?c=1024*(c-55296)+e-56320+65536:_+=this.wcwidth(e)}_+=this.wcwidth(c)}return _}}}},G={};function z(T){var i=G[T];if(i!==void 0)return i.exports;var a=G[T]={exports:{}};return Y[T].call(a.exports,a,a.exports,z),a.exports}var X={};return(()=>{var T=X;Object.defineProperty(T,"__esModule",{value:!0}),T.Terminal=void 0;const i=z(3236),a=z(9042),h=z(7975),f=z(7090),d=z(5741),_=z(8285),g=["cols","rows"];T.Terminal=class{constructor(v){this._core=new i.Terminal(v),this._addonManager=new d.AddonManager,this._publicOptions=Object.assign({},this._core.options);const c=s=>this._core.options[s],e=(s,n)=>{this._checkReadonlyOptions(s),this._core.options[s]=n};for(const s in this._core.options){const n={get:c.bind(this,s),set:e.bind(this,s)};Object.defineProperty(this._publicOptions,s,n)}}_checkReadonlyOptions(v){if(g.includes(v))throw new Error(`Option "${v}" can only be set in the constructor`)}_checkProposedApi(){if(!this._core.optionsService.rawOptions.allowProposedApi)throw new Error("You must set the allowProposedApi option to true to use proposed API")}get onBell(){return this._core.onBell}get onBinary(){return this._core.onBinary}get onCursorMove(){return this._core.onCursorMove}get onData(){return this._core.onData}get onKey(){return this._core.onKey}get onLineFeed(){return this._core.onLineFeed}get onRender(){return this._core.onRender}get onResize(){return this._core.onResize}get onScroll(){return this._core.onScroll}get onSelectionChange(){return this._core.onSelectionChange}get onTitleChange(){return this._core.onTitleChange}get onWriteParsed(){return this._core.onWriteParsed}get element(){return this._core.element}get parser(){return this._parser||(this._parser=new h.ParserApi(this._core)),this._parser}get unicode(){return this._checkProposedApi(),new f.UnicodeApi(this._core)}get textarea(){return this._core.textarea}get rows(){return this._core.rows}get cols(){return this._core.cols}get buffer(){return this._buffer||(this._buffer=new _.BufferNamespaceApi(this._core)),this._buffer}get markers(){return this._checkProposedApi(),this._core.markers}get modes(){const v=this._core.coreService.decPrivateModes;let c="none";switch(this._core.coreMouseService.activeProtocol){case"X10":c="x10";break;case"VT200":c="vt200";break;case"DRAG":c="drag";break;case"ANY":c="any"}return{applicationCursorKeysMode:v.applicationCursorKeys,applicationKeypadMode:v.applicationKeypad,bracketedPasteMode:v.bracketedPasteMode,insertMode:this._core.coreService.modes.insertMode,mouseTrackingMode:c,originMode:v.origin,reverseWraparoundMode:v.reverseWraparound,sendFocusMode:v.sendFocus,wraparoundMode:v.wraparound}}get options(){return this._publicOptions}set options(v){for(const c in v)this._publicOptions[c]=v[c]}blur(){this._core.blur()}focus(){this._core.focus()}resize(v,c){this._verifyIntegers(v,c),this._core.resize(v,c)}open(v){this._core.open(v)}attachCustomKeyEventHandler(v){this._core.attachCustomKeyEventHandler(v)}registerLinkProvider(v){return this._core.registerLinkProvider(v)}registerCharacterJoiner(v){return this._checkProposedApi(),this._core.registerCharacterJoiner(v)}deregisterCharacterJoiner(v){this._checkProposedApi(),this._core.deregisterCharacterJoiner(v)}registerMarker(v=0){return this._verifyIntegers(v),this._core.addMarker(v)}registerDecoration(v){var c,e,s;return this._checkProposedApi(),this._verifyPositiveIntegers((c=v.x)!==null&&c!==void 0?c:0,(e=v.width)!==null&&e!==void 0?e:0,(s=v.height)!==null&&s!==void 0?s:0),this._core.registerDecoration(v)}hasSelection(){return this._core.hasSelection()}select(v,c,e){this._verifyIntegers(v,c,e),this._core.select(v,c,e)}getSelection(){return this._core.getSelection()}getSelectionPosition(){return this._core.getSelectionPosition()}clearSelection(){this._core.clearSelection()}selectAll(){this._core.selectAll()}selectLines(v,c){this._verifyIntegers(v,c),this._core.selectLines(v,c)}dispose(){this._addonManager.dispose(),this._core.dispose()}scrollLines(v){this._verifyIntegers(v),this._core.scrollLines(v)}scrollPages(v){this._verifyIntegers(v),this._core.scrollPages(v)}scrollToTop(){this._core.scrollToTop()}scrollToBottom(){this._core.scrollToBottom()}scrollToLine(v){this._verifyIntegers(v),this._core.scrollToLine(v)}clear(){this._core.clear()}write(v,c){this._core.write(v,c)}writeln(v,c){this._core.write(v),this._core.write(`\r
`,c)}paste(v){this._core.paste(v)}refresh(v,c){this._verifyIntegers(v,c),this._core.refresh(v,c)}reset(){this._core.reset()}clearTextureAtlas(){this._core.clearTextureAtlas()}loadAddon(v){return this._addonManager.loadAddon(this,v)}static get strings(){return a}_verifyIntegers(...v){for(const c of v)if(c===1/0||isNaN(c)||c%1!=0)throw new Error("This API only accepts integers")}_verifyPositiveIntegers(...v){for(const c of v)if(c&&(c===1/0||isNaN(c)||c%1!=0||c<0))throw new Error("This API only accepts positive integers")}}})(),X})()})})(Z);var se=Z.exports,ee={exports:{}};(function(J,te){(function(Y,G){J.exports=G()})(self,function(){return(()=>{var Y={};return(()=>{var G=Y;Object.defineProperty(G,"__esModule",{value:!0}),G.FitAddon=void 0,G.FitAddon=class{constructor(){}activate(z){this._terminal=z}dispose(){}fit(){const z=this.proposeDimensions();if(!z||!this._terminal||isNaN(z.cols)||isNaN(z.rows))return;const X=this._terminal._core;this._terminal.rows===z.rows&&this._terminal.cols===z.cols||(X._renderService.clear(),this._terminal.resize(z.cols,z.rows))}proposeDimensions(){if(!this._terminal||!this._terminal.element||!this._terminal.element.parentElement)return;const z=this._terminal._core,X=z._renderService.dimensions;if(X.css.cell.width===0||X.css.cell.height===0)return;const T=this._terminal.options.scrollback===0?0:z.viewport.scrollBarWidth,i=window.getComputedStyle(this._terminal.element.parentElement),a=parseInt(i.getPropertyValue("height")),h=Math.max(0,parseInt(i.getPropertyValue("width"))),f=window.getComputedStyle(this._terminal.element),d=a-(parseInt(f.getPropertyValue("padding-top"))+parseInt(f.getPropertyValue("padding-bottom"))),_=h-(parseInt(f.getPropertyValue("padding-right"))+parseInt(f.getPropertyValue("padding-left")))-T;return{cols:Math.max(2,Math.floor(_/X.css.cell.width)),rows:Math.max(1,Math.floor(d/X.css.cell.height))}}}})(),Y})()})})(ee);var re=ee.exports;const ne={Terminal:se.Terminal,FitAddon:re.FitAddon};export{ne as default};
