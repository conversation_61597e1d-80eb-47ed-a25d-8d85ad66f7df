#nullable disable
using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using ShopBot.Services.Extensions;

namespace ShopBot.Services
{
    public class DropboxService : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly DropboxTokenManager? _tokenManager;
        private readonly string? _staticAccessToken;
        private readonly ILogger<DropboxService> _logger;
        private const string DropboxApiUrl = "https://content.dropboxapi.com/2/files/upload";
        private const string DropboxShareUrl = "https://api.dropboxapi.com/2/sharing/create_shared_link_with_settings";
        private const int MaxRetries = 3;
        private const int DelayBetweenUploads = 500; // 0.5 sekundy między uploadami (zoptymalizowane)

        // New OAuth constructor
        public DropboxService(DropboxTokenManager tokenManager, ILogger<DropboxService>? logger = null)
        {
            _tokenManager = tokenManager;
            _logger = logger ?? Microsoft.Extensions.Logging.Abstractions.NullLogger<DropboxService>.Instance;
            _httpClient = new HttpClient
            {
                Timeout = TimeSpan.FromMinutes(2) // Zoptymalizowany timeout
            };
        }

        // Legacy static token constructor (fallback)
        public DropboxService(string accessToken, ILogger<DropboxService>? logger = null)
        {
            _staticAccessToken = accessToken;
            _logger = logger ?? Microsoft.Extensions.Logging.Abstractions.NullLogger<DropboxService>.Instance;
            _httpClient = new HttpClient
            {
                Timeout = TimeSpan.FromMinutes(2)
            };
        }

        private async Task<string> GetAccessTokenAsync()
        {
            if (_tokenManager != null)
            {
                // Use new OAuth system
                return await _tokenManager.GetValidAccessTokenAsync();
            }
            else if (!string.IsNullOrEmpty(_staticAccessToken))
            {
                // Use legacy static token
                return _staticAccessToken;
            }
            else
            {
                throw new InvalidOperationException("No access token available - neither OAuth nor static token configured");
            }
        }

        public async Task<DropboxUploadResult> UploadImageFromUrlAsync(string imageUrl, string fileName = null)
        {
            var startTime = DateTime.Now;
            var result = new DropboxUploadResult
            {
                OriginalUrl = imageUrl,
                Success = false
            };

            try
            {
                // Pobierz obraz z URL
                _logger.LogDropboxDownload(imageUrl);
                var imageBytes = await DownloadImageAsync(imageUrl);
                
                if (imageBytes == null || imageBytes.Length == 0)
                {
                    result.ErrorMessage = "Nie udało się pobrać obrazu z URL";
                    return result;
                }

                // Wygeneruj nazwę pliku jeśli nie podano
                if (string.IsNullOrEmpty(fileName))
                {
                    var extension = GetImageExtension(imageUrl);
                    fileName = $"screenshot_{DateTime.Now:yyyyMMdd_HHmmss}_{Guid.NewGuid().ToString("N")[..8]}.{extension}";
                }

                // Upload do Dropbox
                var dropboxPath = $"/G2G_Screenshots/{fileName}";
                var uploadResult = await UploadToDropboxAsync(imageBytes, dropboxPath);
                
                if (!uploadResult.Success)
                {
                    result.ErrorMessage = uploadResult.ErrorMessage;
                    return result;
                }

                // Stwórz publiczny link
                var shareResult = await CreateSharedLinkAsync(dropboxPath);
                
                if (shareResult.Success)
                {
                    result.Success = true;
                    result.DropboxUrl = shareResult.SharedUrl;
                    result.DirectUrl = ConvertToDirectUrl(shareResult.SharedUrl);

                    var duration = DateTime.Now - startTime;
                    _logger.LogDropboxUploadSuccess(duration.TotalSeconds, result.DirectUrl);
                }
                else
                {
                    result.ErrorMessage = shareResult.ErrorMessage;
                    _logger.LogDropboxUploadError($"Błąd tworzenia linku: {shareResult.ErrorMessage}");
                }

                return result;
            }
            catch (Exception ex)
            {
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "❌ Błąd uploadu obrazu z URL: {ImageUrl}", imageUrl);
                return result;
            }
        }

        private async Task<byte[]> DownloadImageAsync(string imageUrl)
        {
            try
            {
                var downloadStart = DateTime.Now;
                using var response = await _httpClient.GetAsync(imageUrl);
                if (response.IsSuccessStatusCode)
                {
                    var imageBytes = await response.Content.ReadAsByteArrayAsync();
                    var downloadTime = DateTime.Now - downloadStart;
                    _logger.LogDropboxDownloadComplete(imageBytes.Length / 1024, downloadTime.TotalSeconds);
                    return imageBytes;
                }
                _logger.LogDropboxUploadError($"Błąd pobierania: {response.StatusCode}");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogDropboxUploadError($"Wyjątek podczas pobierania obrazu: {ex.Message}");
                return null;
            }
        }

        private async Task<DropboxApiResult> UploadToDropboxAsync(byte[] imageBytes, string dropboxPath)
        {
            var result = new DropboxApiResult { Success = false };

            // Retry logic z obsługą rate limits
            for (int attempt = 1; attempt <= MaxRetries; attempt++)
            {
                try
                {
                    var accessToken = await GetAccessTokenAsync();
                    var request = new HttpRequestMessage(HttpMethod.Post, DropboxApiUrl);
                    request.Headers.Add("Authorization", $"Bearer {accessToken}");

                    var uploadArgs = new
                    {
                        path = dropboxPath,
                        mode = "add",
                        autorename = true
                    };

                    request.Headers.Add("Dropbox-API-Arg", JsonSerializer.Serialize(uploadArgs));
                    request.Content = new ByteArrayContent(imageBytes);
                    request.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/octet-stream");

                    var response = await _httpClient.SendAsync(request);
                    var responseContent = await response.Content.ReadAsStringAsync();

                    if (response.IsSuccessStatusCode)
                    {
                        result.Success = true;
                        _logger.LogDebug("[Dropbox] Upload do {DropboxPath} pomyślny", dropboxPath);
                        return result;
                    }
                    else if (response.StatusCode == System.Net.HttpStatusCode.TooManyRequests)
                    {
                        // Obsługa HTTP 429 - Too Many Requests
                        var retryAfter = GetRetryAfterSeconds(response, responseContent);

                        if (attempt < MaxRetries)
                        {
                            _logger.LogWarning("[Dropbox] ⚠️ Rate limit hit (attempt {Attempt}/{MaxRetries}). Waiting {RetryAfter} seconds...",
                                attempt, MaxRetries, retryAfter);
                            await Task.Delay(TimeSpan.FromSeconds(retryAfter));
                            continue; // Retry
                        }
                        else
                        {
                            result.ErrorMessage = $"Rate limit exceeded after {MaxRetries} attempts: {responseContent}";
                            _logger.LogError("[Dropbox] ❌ Rate limit exceeded after {MaxRetries} attempts", MaxRetries);
                            return result;
                        }
                    }
                    else
                    {
                        result.ErrorMessage = $"HTTP {response.StatusCode}: {responseContent}";
                        _logger.LogDropboxUploadError($"Błąd uploadu: {result.ErrorMessage}");
                        return result;
                    }
                }
                catch (Exception ex)
                {
                    if (attempt < MaxRetries)
                    {
                        _logger.LogWarning("[Dropbox] ⚠️ Upload attempt {Attempt}/{MaxRetries} failed: {Error}",
                            attempt, MaxRetries, ex.Message);
                        await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, attempt - 1))); // Exponential backoff
                        continue;
                    }

                    result.ErrorMessage = ex.Message;
                    return result;
                }
            }

            return result;
        }

        private async Task<DropboxShareResult> CreateSharedLinkAsync(string dropboxPath)
        {
            var result = new DropboxShareResult { Success = false };

            try
            {
                var accessToken = await GetAccessTokenAsync();
                var request = new HttpRequestMessage(HttpMethod.Post, DropboxShareUrl);
                request.Headers.Add("Authorization", $"Bearer {accessToken}");

                var shareArgs = new
                {
                    path = dropboxPath,
                    settings = new
                    {
                        requested_visibility = "public"
                    }
                };

                request.Content = new StringContent(JsonSerializer.Serialize(shareArgs), Encoding.UTF8, "application/json");

                var response = await _httpClient.SendAsync(request);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    using var doc = JsonDocument.Parse(responseContent);
                    result.Success = true;
                    result.SharedUrl = doc.RootElement.GetProperty("url").GetString();
                    _logger.LogDebug("[Dropbox] Otrzymano shared URL: {SharedUrl}", result.SharedUrl);
                }
                else
                {
                    result.ErrorMessage = $"HTTP {response.StatusCode}: {responseContent}";
                }

                return result;
            }
            catch (Exception ex)
            {
                result.ErrorMessage = ex.Message;
                return result;
            }
        }

        private string ConvertToDirectUrl(string dropboxUrl)
        {
            if (string.IsNullOrEmpty(dropboxUrl))
                return dropboxUrl;

            try
            {
                // Konwertuj Dropbox share URL na direct URL
                var directUrl = dropboxUrl;

                // Zamień domenę na dl.dropboxusercontent.com
                if (directUrl.Contains("dropbox.com"))
                {
                    directUrl = directUrl.Replace("dropbox.com", "dl.dropboxusercontent.com");
                }

                // Usuń wszystkie możliwe warianty parametru dl
                directUrl = directUrl.Replace("?dl=0", "");
                directUrl = directUrl.Replace("&dl=0", "");
                directUrl = directUrl.Replace("?dl=1", "");
                directUrl = directUrl.Replace("&dl=1", "");

                // Usuń trailing & jeśli zostało po usunięciu parametrów
                if (directUrl.EndsWith("&"))
                {
                    directUrl = directUrl.TrimEnd('&');
                }

                // Jeśli URL kończy się na ? (zostało po usunięciu parametrów), usuń to też
                if (directUrl.EndsWith("?"))
                {
                    directUrl = directUrl.TrimEnd('?');
                }

                _logger.LogDebug("[Dropbox] Konwersja URL: {DropboxUrl} -> {DirectUrl}", dropboxUrl, directUrl);
                return directUrl;
            }
            catch (Exception ex)
            {
                _logger.LogWarning("[Dropbox] ⚠️ Błąd konwersji URL {DropboxUrl}: {Error}", dropboxUrl, ex.Message);
                return dropboxUrl; // Zwróć oryginalny URL w przypadku błędu
            }
        }

        private int GetRetryAfterSeconds(HttpResponseMessage response, string responseContent)
        {
            // Sprawdź nagłówek Retry-After
            if (response.Headers.RetryAfter?.Delta.HasValue == true)
            {
                return (int)response.Headers.RetryAfter.Delta.Value.TotalSeconds;
            }

            // Sprawdź w JSON response (dla too_many_write_operations)
            try
            {
                using var doc = JsonDocument.Parse(responseContent);
                if (doc.RootElement.TryGetProperty("error", out var errorElement) &&
                    errorElement.TryGetProperty("retry_after", out var retryAfterElement))
                {
                    return retryAfterElement.GetInt32();
                }
            }
            catch
            {
                // Ignore JSON parsing errors
            }

            // Domyślne opóźnienie jeśli nie ma Retry-After
            return 2;
        }

        private string GetImageExtension(string imageUrl)
        {
            var uri = new Uri(imageUrl);
            var extension = Path.GetExtension(uri.LocalPath).TrimStart('.');
            return string.IsNullOrEmpty(extension) ? "jpg" : extension;
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    public class DropboxUploadResult
    {
        public bool Success { get; set; }
        public string OriginalUrl { get; set; }
        public string DropboxUrl { get; set; }
        public string DirectUrl { get; set; }
        public string ErrorMessage { get; set; }
    }

    public class DropboxApiResult
    {
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
    }

    public class DropboxShareResult
    {
        public bool Success { get; set; }
        public string SharedUrl { get; set; }
        public string ErrorMessage { get; set; }
    }
}
