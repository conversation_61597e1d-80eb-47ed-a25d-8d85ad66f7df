using Microsoft.Extensions.Logging;
using ShopBot.Config;
using ShopBot.Services.Interfaces;
using ShopBot.Services.Extensions;
using System.Collections.Concurrent;
using System.Net.Http;
using System.IO;
using static ShopBot.Config.GameConfig;

namespace ShopBot.Services
{
    /// <summary>
    /// Unified repository dla wszystkich konfiguracji z caching, error handling i validation
    /// Zastępuje bezpośredni dostęp do AppSettings.Instance, FilterConfig.LoadFromFile, GameConfig.GetActiveGames
    /// </summary>
    public class ConfigurationRepository : IConfigurationRepository
    {
        private readonly ILogger<ConfigurationRepository> _logger;
        private readonly ConcurrentDictionary<string, (object Value, DateTime Expiry)> _cache;
        
        // Cache keys
        private const string APP_SETTINGS_KEY = "app_settings";
        private const string FILTER_CONFIG_KEY = "filter_config";
        private const string IMGUR_CONFIG_KEY = "imgur_config";
        private const string FLICKR_CONFIG_KEY = "flickr_config";
        private const string ACTIVE_GAMES_KEY = "active_games";
        private const string GAME_CONFIG_PREFIX = "game_config_";
        
        // Cache expiry times
        private static readonly TimeSpan DefaultCacheExpiry = TimeSpan.FromMinutes(30);
        private static readonly TimeSpan GameConfigCacheExpiry = TimeSpan.FromHours(1);

        public ConfigurationRepository(ILogger<ConfigurationRepository> logger)
        {
            _logger = logger;
            _cache = new ConcurrentDictionary<string, (object Value, DateTime Expiry)>();
        }

        public async Task<AppSettings> GetAppSettingsAsync()
        {
            return await GetOrSetCacheAsync(APP_SETTINGS_KEY, async () =>
            {
                _logger.LogInformation("[ConfigRepo] Loading AppSettings from file");
                var settings = AppSettings.LoadFromFile();
                _logger.LogInformation("[ConfigRepo] ✅ AppSettings loaded successfully");
                return settings;
            }, DefaultCacheExpiry);
        }

        public async Task<OfferFilter> GetFilterConfigAsync(string gameName = "default")
        {
            var cacheKey = $"{FILTER_CONFIG_KEY}_{gameName}";
            return await GetOrSetCacheAsync(cacheKey, async () =>
            {
                _logger.LogInformation("[ConfigRepo] Loading FilterConfig for game: {GameName}", gameName);
                var (filter, _, _) = FilterConfig.LoadFromFile();
                _logger.LogInformation("[ConfigRepo] ✅ FilterConfig loaded for game: {GameName}", gameName);
                return filter;
            }, DefaultCacheExpiry);
        }

        public async Task<FilterConfig.ImgurConfig?> GetImgurConfigAsync()
        {
            return await GetOrSetCacheAsync(IMGUR_CONFIG_KEY, async () =>
            {
                _logger.LogInformation("[ConfigRepo] Loading Imgur configuration");
                var (_, imgur, _) = FilterConfig.LoadFromFile();
                _logger.LogInformation("[ConfigRepo] ✅ Imgur config loaded");
                return imgur;
            }, DefaultCacheExpiry);
        }

        public async Task<FilterConfig.FlickrConfig?> GetFlickrConfigAsync()
        {
            return await GetOrSetCacheAsync(FLICKR_CONFIG_KEY, async () =>
            {
                _logger.LogInformation("[ConfigRepo] Loading Flickr configuration");
                var (_, _, flickr) = FilterConfig.LoadFromFile();
                _logger.LogInformation("[ConfigRepo] ✅ Flickr config loaded");
                return flickr;
            }, DefaultCacheExpiry);
        }

        public async Task<string[]> GetActiveGamesAsync()
        {
            return await GetOrSetCacheAsync(ACTIVE_GAMES_KEY, async () =>
            {
                _logger.LogInformation("[ConfigRepo] Loading active games list");
                var activeGames = GameConfig.GetActiveGames();
                _logger.LogInformation("[ConfigRepo] ✅ Active games loaded: {Count} games", activeGames.Length);
                return activeGames;
            }, GameConfigCacheExpiry);
        }

        public async Task<GameData> GetGameConfigAsync(string gameName)
        {
            var cacheKey = $"{GAME_CONFIG_PREFIX}{gameName}";
            return await GetOrSetCacheAsync(cacheKey, async () =>
            {
                _logger.LogInformation("[ConfigRepo] Loading game config for: {GameName}", gameName);
                
                // Używamy istniejących metod GameConfig, ale z proper error handling
                try
                {
                    var gameId = GameConfig.GetGameId(gameName);
                    var attributes = GameConfig.GetGameAttributes(gameName);
                    var priceRange = GameConfig.GetGamePriceRange(gameName);
                    
                    var gameData = new GameData
                    {
                        Id = gameId,
                        ListAttributes = attributes,
                        PriceRange = priceRange
                    };
                    
                    _logger.LogInformation("[ConfigRepo] ✅ Game config loaded for: {GameName}", gameName);
                    return gameData;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "[ConfigRepo] ❌ Failed to load game config for: {GameName}", gameName);
                    throw new InvalidOperationException($"Game configuration not found for: {gameName}", ex);
                }
            }, GameConfigCacheExpiry);
        }

        public async Task<string> GetGameIdAsync(string gameName)
        {
            var gameConfig = await GetGameConfigAsync(gameName);
            return gameConfig.Id;
        }

        public async Task<string> GetGameUrlAsync(string gameName)
        {
            var gameId = await GetGameIdAsync(gameName);
            if (string.IsNullOrEmpty(gameId))
            {
                throw new InvalidOperationException($"Invalid game ID for game: {gameName}");
            }
            return $"https://funpay.com/en/lots/{gameId}/";
        }

        public async Task<string[]> GetGameAttributesAsync(string gameName)
        {
            var gameConfig = await GetGameConfigAsync(gameName);
            return gameConfig.ListAttributes ?? Array.Empty<string>();
        }

        public async Task<PriceRange> GetGamePriceRangeAsync(string gameName)
        {
            var gameConfig = await GetGameConfigAsync(gameName);
            return gameConfig.PriceRange ?? new PriceRange();
        }

        public async Task<ConfigurationValidationResult> ValidateAllConfigurationsAsync()
        {
            var result = new ConfigurationValidationResult { IsValid = true };
            
            _logger.LogInformation("[ConfigRepo] 🔍 Starting comprehensive configuration validation");

            try
            {
                // Validate AppSettings
                var appSettings = await GetAppSettingsAsync();
                ValidateAppSettings(appSettings, result);

                // Validate Imgur config
                var imgurConfig = await GetImgurConfigAsync();
                ValidateImgurConfig(imgurConfig, result);

                // Validate external services (if enabled)
                if (appSettings.Features?.EnableServiceValidation == true)
                {
                    await ValidateExternalServicesAsync(appSettings, result);
                }

                // Validate active games
                var activeGames = await GetActiveGamesAsync();
                ValidateActiveGames(activeGames, result);

                // Validate each game config
                foreach (var gameName in activeGames)
                {
                    try
                    {
                        var gameConfig = await GetGameConfigAsync(gameName);
                        ValidateGameConfig(gameName, gameConfig, result);
                        _logger.LogDebug("[ConfigRepo] ✅ Game config valid: {GameName}", gameName);
                    }
                    catch (Exception ex)
                    {
                        result.AddError($"Invalid game config for '{gameName}': {ex.Message}");
                        result.IsValid = false;
                    }
                }

                if (result.IsValid)
                {
                    _logger.LogInformation("[ConfigRepo] ✅ All configurations are valid");
                }
                else
                {
                    _logger.LogWarning("[ConfigRepo] ⚠️ Configuration validation found {ErrorCount} errors and {WarningCount} warnings", 
                        result.Errors.Count, result.Warnings.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ConfigRepo] ❌ Configuration validation failed");
                result.AddError($"Configuration validation failed: {ex.Message}");
                result.IsValid = false;
            }

            return result;
        }

        public async Task RefreshCacheAsync()
        {
            _logger.LogInformation("[ConfigRepo] 🔄 Refreshing configuration cache");

            // Clear all cache entries
            var cacheKeys = new[]
            {
                APP_SETTINGS_KEY, FILTER_CONFIG_KEY, IMGUR_CONFIG_KEY,
                FLICKR_CONFIG_KEY, ACTIVE_GAMES_KEY
            };

            foreach (var key in cacheKeys)
            {
                _cache.TryRemove(key, out _);
            }

            // Clear game-specific cache entries
            var activeGames = GameConfig.GetActiveGames(); // Direct call to avoid cache
            foreach (var gameName in activeGames)
            {
                _cache.TryRemove($"{GAME_CONFIG_PREFIX}{gameName}", out _);
            }

            _logger.LogInformation("[ConfigRepo] ✅ Configuration cache refreshed");
        }

        // Private helper methods
        private async Task<T> GetOrSetCacheAsync<T>(string key, Func<Task<T>> factory, TimeSpan expiry)
        {
            if (_cache.TryGetValue(key, out var cachedItem) && cachedItem.Expiry > DateTime.UtcNow)
            {
                _logger.LogDebug("[ConfigRepo] Cache hit for key: {Key}", key);
                return (T)cachedItem.Value;
            }

            _logger.LogDebug("[ConfigRepo] Cache miss for key: {Key}, loading from source", key);
            var value = await factory();

            _cache[key] = (value, DateTime.UtcNow.Add(expiry));
            return value;
        }

        private void ValidateAppSettings(AppSettings settings, ConfigurationValidationResult result)
        {
            _logger.LogDebug("[ConfigRepo] 🔍 Validating AppSettings...");

            // Dropbox validation
            if (string.IsNullOrEmpty(settings.Dropbox?.AccessToken))
                result.AddError("Dropbox Access Token is required");
            else if (settings.Dropbox.AccessToken.Length < 10)
                result.AddError("Dropbox Access Token appears to be invalid (too short)");

            // Application settings validation
            if (settings.Application?.MaxWorkerThreads <= 0)
                result.AddWarning("MaxWorkerThreads should be > 0 (current: 0)");
            else if (settings.Application.MaxWorkerThreads == 1)
                result.AddWarning($"MaxWorkerThreads is set to 1 (recommended: {Environment.ProcessorCount}+)");

            // Timeout validation
            if (settings.Timeouts?.PageLoadTimeoutSeconds <= 0)
                result.AddError("PageLoadTimeoutSeconds must be > 0");
            else if (settings.Timeouts.PageLoadTimeoutSeconds < 10)
                result.AddWarning($"PageLoadTimeoutSeconds is very low ({settings.Timeouts.PageLoadTimeoutSeconds}s, recommended: 30s+)");

            // Sync interval validation
            if (settings.Application?.SyncIntervalMinutes <= 0)
                result.AddError("SyncIntervalMinutes must be > 0");
            else if (settings.Application.SyncIntervalMinutes < 5)
                result.AddWarning($"SyncIntervalMinutes is very low ({settings.Application.SyncIntervalMinutes} min, recommended: 10+ min)");

            // Browser validation
            if (string.IsNullOrEmpty(settings.Application?.BrowserId))
                result.AddWarning("BrowserId is not set (will use default)");

            _logger.LogDebug("[ConfigRepo] ✅ AppSettings validation completed");
        }

        private void ValidateImgurConfig(FilterConfig.ImgurConfig? config, ConfigurationValidationResult result)
        {
            if (config == null)
            {
                result.AddWarning("Imgur configuration is missing");
                return;
            }

            if (string.IsNullOrEmpty(config.ClientId))
                result.AddError("Imgur ClientId is required");
        }

        private void ValidateActiveGames(string[] activeGames, ConfigurationValidationResult result)
        {
            _logger.LogDebug("[ConfigRepo] 🔍 Validating active games...");

            if (activeGames.Length == 0)
                result.AddError("At least one active game must be configured");

            foreach (var game in activeGames)
            {
                if (string.IsNullOrWhiteSpace(game))
                    result.AddError("Active game name cannot be empty");
            }

            _logger.LogDebug("[ConfigRepo] ✅ Active games validation completed ({Count} games)", activeGames.Length);
        }

        private void ValidateGameConfig(string gameName, GameData gameConfig, ConfigurationValidationResult result)
        {
            _logger.LogDebug("[ConfigRepo] 🔍 Validating game config for: {GameName}", gameName);

            // Validate Game ID
            if (string.IsNullOrEmpty(gameConfig.Id))
                result.AddError($"Game '{gameName}' has empty ID");
            else if (!int.TryParse(gameConfig.Id, out var gameId) || gameId <= 0)
                result.AddError($"Game '{gameName}' has invalid ID: '{gameConfig.Id}' (should be positive integer)");

            // Validate Price Range
            if (gameConfig.PriceRange != null)
            {
                if (gameConfig.PriceRange.Min < 0)
                    result.AddError($"Game '{gameName}' has negative minimum price: {gameConfig.PriceRange.Min}");

                if (gameConfig.PriceRange.Max < 0)
                    result.AddError($"Game '{gameName}' has negative maximum price: {gameConfig.PriceRange.Max}");

                if (gameConfig.PriceRange.Min > gameConfig.PriceRange.Max)
                    result.AddError($"Game '{gameName}' has min price ({gameConfig.PriceRange.Min}) > max price ({gameConfig.PriceRange.Max})");

                if (gameConfig.PriceRange.Max > 10000)
                    result.AddWarning($"Game '{gameName}' has very high max price: ${gameConfig.PriceRange.Max} (consider reviewing)");
            }
            else
            {
                result.AddWarning($"Game '{gameName}' has no price range configured");
            }

            // Validate Attributes
            if (gameConfig.ListAttributes == null || gameConfig.ListAttributes.Length == 0)
                result.AddWarning($"Game '{gameName}' has no list attributes configured");
            else
            {
                foreach (var attr in gameConfig.ListAttributes)
                {
                    if (string.IsNullOrWhiteSpace(attr))
                        result.AddError($"Game '{gameName}' has empty attribute in list");
                }
            }

            _logger.LogDebug("[ConfigRepo] ✅ Game config validation completed for: {GameName}", gameName);
        }

        private async Task ValidateExternalServicesAsync(AppSettings appSettings, ConfigurationValidationResult result)
        {
            _logger.LogInformation("[ConfigRepo] 🔍 Validating external services...");

            // Validate Dropbox connection
            if (!string.IsNullOrEmpty(appSettings.Dropbox?.AccessToken))
            {
                try
                {
                    _logger.LogDebug("[ConfigRepo] Testing Dropbox connection...");

                    // Simple HTTP test to Dropbox API
                    using var httpClient = new HttpClient();
                    httpClient.DefaultRequestHeaders.Authorization =
                        new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", appSettings.Dropbox.AccessToken);

                    var response = await httpClient.PostAsync("https://api.dropboxapi.com/2/users/get_current_account",
                        new StringContent("{}", System.Text.Encoding.UTF8, "application/json"));

                    if (response.IsSuccessStatusCode)
                    {
                        _logger.LogInformation("[ConfigRepo] ✅ Dropbox connection test successful");
                    }
                    else
                    {
                        result.AddError($"Dropbox connection test failed: {response.StatusCode} - {response.ReasonPhrase}");
                    }
                }
                catch (Exception ex)
                {
                    result.AddError($"Dropbox connection test failed: {ex.Message}");
                }
            }

            // Validate required files exist
            var requiredFiles = new[]
            {
                "appsettings.json",
                "gamesconfig.json",
                "config.json"
            };

            foreach (var file in requiredFiles)
            {
                if (!File.Exists(file))
                {
                    result.AddError($"Required configuration file not found: {file}");
                }
                else
                {
                    _logger.LogDebug("[ConfigRepo] ✅ Configuration file exists: {File}", file);
                }
            }

            _logger.LogInformation("[ConfigRepo] ✅ External services validation completed");
        }
    }
}
