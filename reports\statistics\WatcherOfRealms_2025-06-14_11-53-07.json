{"TotalOffers": 588, "AcceptedOffers": 205, "RejectedOffers": 383, "RejectionReasons": {"Cena ponizej minimum (0,64 < 15)": 1, "Cena ponizej minimum (12,89 < 15)": 33, "Ocena ponizej minimum (0 < 5)": 129, "Cena ponizej minimum (6,72 < 15)": 3, "Cena ponizej minimum (7,84 < 15)": 2, "Zawiera zabronioną frazę: starter account": 1, "Cena ponizej minimum (13,44 < 15)": 1, "Zawiera zabronioną frazę: your choice": 3, "Cena ponizej minimum (12,66 < 15)": 2, "Cena ponizej minimum (13,81 < 15)": 2, "Cena ponizej minimum (11,51 < 15)": 2, "Cena ponizej minimum (10,32 < 15)": 1, "Cena ponizej minimum (8,06 < 15)": 1, "Cena ponizej minimum (1,12 < 15)": 1, "Cena ponizej minimum (1,42 < 15)": 1, "Cena ponizej minimum (0,03 < 15)": 1, "Zawiera zabronioną frazę: accounts with": 1, "Cena ponizej minimum (11,60 < 15)": 17, "Cena powyzej maximum (3210,02 > 1000)": 1, "Cena ponizej minimum (9,02 < 15)": 18, "Cena ponizej minimum (4,51 < 15)": 8, "Cena ponizej minimum (3,87 < 15)": 7, "Cena ponizej minimum (5,16 < 15)": 9, "Cena ponizej minimum (5,50 < 15)": 1, "Cena ponizej minimum (5,60 < 15)": 3, "Cena ponizej minimum (10,31 < 15)": 11, "Cena ponizej minimum (7,73 < 15)": 10, "Cena ponizej minimum (5,54 < 15)": 1, "Cena ponizej minimum (10,96 < 15)": 6, "Cena ponizej minimum (6,45 < 15)": 19, "Cena ponizej minimum (5,80 < 15)": 5, "Cena ponizej minimum (3,36 < 15)": 2, "Cena ponizej minimum (4,48 < 15)": 1, "Cena ponizej minimum (5,04 < 15)": 1, "Cena ponizej minimum (2,58 < 15)": 5, "Cena ponizej minimum (1,34 < 15)": 6, "Cena ponizej minimum (1,68 < 15)": 1, "Duplikat oferty": 1, "Cena ponizej minimum (10,83 < 15)": 1, "Cena ponizej minimum (8,38 < 15)": 2, "Cena ponizej minimum (3,92 < 15)": 2, "Cena ponizej minimum (3,22 < 15)": 5, "Cena ponizej minimum (7,09 < 15)": 4, "Cena ponizej minimum (4,82 < 15)": 1, "Cena ponizej minimum (9,67 < 15)": 4, "Cena ponizej minimum (11,09 < 15)": 1, "Cena ponizej minimum (14,18 < 15)": 1, "Cena ponizej minimum (11,22 < 15)": 1, "Cena ponizej minimum (14,83 < 15)": 1, "Ocena ponizej minimum (4 < 5)": 6, "Cena ponizej minimum (8,77 < 15)": 1, "Cena ponizej minimum (7,48 < 15)": 1, "Cena ponizej minimum (2,97 < 15)": 1, "Cena ponizej minimum (2,77 < 15)": 1, "Cena ponizej minimum (6,57 < 15)": 1, "Cena ponizej minimum (10,44 < 15)": 1, "Cena ponizej minimum (11,19 < 15)": 1, "Cena ponizej minimum (10,65 < 15)": 1, "Ocena ponizej minimum (3 < 5)": 1, "Cena ponizej minimum (3,61 < 15)": 1, "Cena ponizej minimum (4,25 < 15)": 1, "Cena ponizej minimum (5,41 < 15)": 1, "Cena ponizej minimum (14,17 < 15)": 1, "Cena ponizej minimum (1,29 < 15)": 1, "Cena ponizej minimum (5,11 < 15)": 1, "Cena ponizej minimum (6,39 < 15)": 1, "Cena ponizej minimum (9,13 < 15)": 1, "Cena ponizej minimum (4,31 < 15)": 1, "Cena ponizej minimum (5,76 < 15)": 1, "Cena ponizej minimum (7,37 < 15)": 1, "Cena ponizej minimum (6,83 < 15)": 1, "Cena ponizej minimum (12,88 < 15)": 1, "Cena ponizej minimum (1,10 < 15)": 1, "Ocena ponizej minimum (1 < 5)": 1, "Cena ponizej minimum (3,19 < 15)": 4, "Cena ponizej minimum (5,32 < 15)": 1, "Cena powyzej maximum (1933,74 > 1000)": 1, "Cena ponizej minimum (0,01 < 15)": 1, "Ocena ponizej minimum (2 < 5)": 1, "Cena ponizej minimum (1,15 < 15)": 1, "Cena ponizej minimum (1,28 < 15)": 1, "Cena ponizej minimum (0,77 < 15)": 1, "Cena ponizej minimum (3,45 < 15)": 1}, "GenerationTime": "2025-06-14T11:53:07.9157025+02:00"}