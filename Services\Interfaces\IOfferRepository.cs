using ShopBot.Models;

namespace ShopBot.Services.Interfaces
{
    public enum OfferStatus
    {
        Pending,
        Processing,
        Processed,
        Published,
        Failed,
        Skipped
    }

    public enum ErrorType
    {
        Parsing,
        Upload,
        Publishing,
        Validation,
        Network,
        Configuration
    }

    public enum UploadStatus
    {
        Pending,
        InProgress,
        Success,
        Failed
    }
}

namespace ShopBot.Services.Interfaces
{
    public interface IOfferRepository
    {
        // Basic CRUD operations
        Task<Offer?> GetByIdAsync(Guid id);
        Task<Offer?> GetByFunPayIdAsync(string funPayOfferId);
        Task<List<Offer>> GetAllAsync();
        Task<Offer> CreateAsync(Offer offer);
        Task<Offer> UpdateAsync(Offer offer);
        Task DeleteAsync(Guid id);

        // Query operations
        Task<List<Offer>> GetByGameAsync(string gameName, int limit = 100);
        Task<List<Offer>> GetByStatusAsync(string status, int limit = 100);
        Task<List<Offer>> GetRecentAsync(int limit = 50);
        Task<List<Offer>> GetByDateRangeAsync(DateTime from, DateTime to);
        Task<List<Offer>> GetFailedOffersAsync(int limit = 100);

        // Business logic queries
        Task<bool> IsOfferProcessedAsync(string funPayOfferId);
        Task<List<Offer>> GetOffersForRetryAsync(int limit = 10);
        Task<List<Offer>> GetUnpublishedOffersAsync(string gameName, int limit = 50);

        // Statistics
        Task<int> GetTotalOffersCountAsync();
        Task<int> GetOffersCountByGameAsync(string gameName);
        Task<int> GetOffersCountByStatusAsync(string status);
        Task<Dictionary<string, int>> GetOfferCountsByGameAsync();
        Task<Dictionary<string, int>> GetOfferCountsByStatusAsync();

        // Performance queries
        Task<List<Offer>> GetTopPricedOffersAsync(string gameName, int limit = 10);
        Task<decimal> GetAveragePriceByGameAsync(string gameName);
        Task<(decimal min, decimal max, decimal avg)> GetPriceStatsAsync(string gameName);

        // Cleanup operations
        Task<int> DeleteOldOffersAsync(DateTime olderThan);
        Task<int> DeleteFailedOffersAsync(DateTime olderThan);
    }

    public interface IProcessingStatsRepository
    {
        Task<ProcessingStat?> GetByDateAndGameAsync(DateTime date, string gameName);
        Task<ProcessingStat> CreateOrUpdateAsync(ProcessingStat stat);
        Task<List<ProcessingStat>> GetStatsAsync(DateTime from, DateTime to, string? gameName = null);
        Task<Dictionary<string, int>> GetTotalsByGameAsync(DateTime from, DateTime to);
        Task<List<ProcessingStat>> GetDailyStatsAsync(int days = 30);
    }

    public interface IImageUploadRepository
    {
        Task<ImageUpload> CreateAsync(ImageUpload imageUpload);
        Task<ImageUpload> UpdateAsync(ImageUpload imageUpload);
        Task<List<ImageUpload>> GetByOfferIdAsync(Guid offerId);
        Task<List<ImageUpload>> GetFailedUploadsAsync(int limit = 100);
        Task<List<ImageUpload>> GetPendingUploadsAsync(int limit = 50);
        Task<int> GetUploadCountByStatusAsync(string status);
    }

    public interface IErrorLogRepository
    {
        Task<ErrorLog> CreateAsync(ErrorLog errorLog);
        Task<List<ErrorLog>> GetRecentErrorsAsync(int limit = 100);
        Task<List<ErrorLog>> GetErrorsByTypeAsync(string errorType, int limit = 100);
        Task<List<ErrorLog>> GetErrorsByGameAsync(string gameName, int limit = 100);
        Task<List<ErrorLog>> GetErrorsByOfferAsync(Guid offerId);
        Task<Dictionary<string, int>> GetErrorCountsByTypeAsync(DateTime from, DateTime to);
        Task<int> DeleteOldErrorsAsync(DateTime olderThan);
    }

    // Unified repository interface
    public interface IShopBotRepository
    {
        IOfferRepository Offers { get; }
        IProcessingStatsRepository ProcessingStats { get; }
        IImageUploadRepository ImageUploads { get; }
        IErrorLogRepository ErrorLogs { get; }

        Task<int> SaveChangesAsync();
        Task BeginTransactionAsync();
        Task CommitTransactionAsync();
        Task RollbackTransactionAsync();
    }
}
