{"comment": "Do not edit this file, use utils/roll_browser.js", "browsers": [{"name": "chromium", "revision": "1097", "installByDefault": true, "browserVersion": "121.0.6167.57"}, {"name": "chromium-with-symbols", "revision": "1097", "installByDefault": false, "browserVersion": "121.0.6167.57"}, {"name": "chromium-tip-of-tree", "revision": "1184", "installByDefault": false, "browserVersion": "122.0.6240.0"}, {"name": "firefox", "revision": "1438", "installByDefault": true, "browserVersion": "121.0"}, {"name": "firefox-asan", "revision": "1438", "installByDefault": false, "browserVersion": "121.0"}, {"name": "firefox-beta", "revision": "1437", "installByDefault": false, "browserVersion": "121.0b8"}, {"name": "webkit", "revision": "1967", "installByDefault": true, "revisionOverrides": {"mac10.14": "1446", "mac10.15": "1616", "mac11": "1816", "mac11-arm64": "1816", "ubuntu18.04-x64": "1728"}, "browserVersion": "17.4"}, {"name": "ffmpeg", "revision": "1009", "installByDefault": true}, {"name": "android", "revision": "1000", "installByDefault": false}]}