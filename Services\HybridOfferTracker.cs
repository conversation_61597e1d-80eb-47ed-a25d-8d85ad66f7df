using Microsoft.Extensions.Logging;
using ShopBot.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;

namespace ShopBot.Services
{
    /// <summary>
    /// Hybrid Offer Tracker - przejściowy system łączący Database i JSON
    /// Używa Database jako głównego źródła z fallback na ProcessedOffersTracker
    /// Umożliwia bezpieczną migrację z JSON na Database
    /// Implementuje IOfferTracker dla unified interface
    /// </summary>
    public class HybridOfferTracker : IOfferTracker
    {
        private readonly IDatabaseOfferTracker _databaseTracker;
        private readonly ProcessedOffersTracker _jsonTracker;
        private readonly ILogger<HybridOfferTracker> _logger;
        private readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);
        private bool _migrationCompleted = false;

        public HybridOfferTracker(
            IDatabaseOfferTracker databaseTracker,
            ProcessedOffersTracker jsonTracker,
            ILogger<HybridOfferTracker> logger)
        {
            _databaseTracker = databaseTracker;
            _jsonTracker = jsonTracker;
            _logger = logger;
        }

        public async Task<bool> WasOfferProcessedAsync(string funPayOfferId)
        {
            try
            {
                // Check database first (primary source)
                var dbResult = await _databaseTracker.WasOfferProcessedAsync(funPayOfferId);
                if (dbResult)
                {
                    _logger.LogDebug("[HybridTracker] 🎯 Offer {OfferId} found in database", funPayOfferId);
                    return true;
                }

                // Fallback to JSON tracker
                var jsonResult = _jsonTracker.WasOfferProcessed(funPayOfferId);
                if (jsonResult)
                {
                    _logger.LogDebug("[HybridTracker] 📄 Offer {OfferId} found in JSON, migrating to database", funPayOfferId);
                    
                    // Migrate this offer to database
                    await _databaseTracker.AddProcessedOfferAsync(funPayOfferId, "Unknown");
                }

                return jsonResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[HybridTracker] ❌ Error checking if offer {OfferId} was processed", funPayOfferId);
                
                // Fallback to JSON on database error
                return _jsonTracker.WasOfferProcessed(funPayOfferId);
            }
        }

        public async Task AddProcessedOfferAsync(string funPayOfferId, string gameName)
        {
            try
            {
                // Add to database (primary)
                await _databaseTracker.AddProcessedOfferAsync(funPayOfferId, gameName);
                
                // Also add to JSON for backup during transition
                _jsonTracker.AddProcessedOffer(funPayOfferId);
                
                _logger.LogInformation("[HybridTracker] ✅ Added offer {OfferId} to both database and JSON", funPayOfferId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[HybridTracker] ❌ Error adding offer {OfferId}, falling back to JSON only", funPayOfferId);
                
                // Fallback to JSON only
                _jsonTracker.AddProcessedOffer(funPayOfferId);
            }
        }

        public async Task<List<string>> GetAllOfferIdsAsync()
        {
            try
            {
                // Get from database first
                var dbOffers = await _databaseTracker.GetAllOfferIdsAsync();
                
                // Get from JSON as backup
                var jsonOffers = _jsonTracker.GetAllOfferIds();
                
                // Combine and deduplicate
                var allOffers = dbOffers.Union(jsonOffers).ToList();
                
                _logger.LogDebug("[HybridTracker] 📊 Retrieved {DbCount} from DB, {JsonCount} from JSON, {TotalCount} total unique", 
                    dbOffers.Count, jsonOffers.Count, allOffers.Count);
                
                return allOffers;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[HybridTracker] ❌ Error getting offer IDs, falling back to JSON");
                return _jsonTracker.GetAllOfferIds();
            }
        }

        public async Task<DateTime> GetLastProcessingTimeAsync()
        {
            try
            {
                var dbTime = await _databaseTracker.GetLastProcessingTimeAsync();
                var jsonTime = _jsonTracker.GetLastProcessingTime();
                
                // Return the latest time
                var latestTime = dbTime > jsonTime ? dbTime : jsonTime;
                
                _logger.LogDebug("[HybridTracker] 📅 Last processing time: DB={DbTime}, JSON={JsonTime}, Latest={LatestTime}", 
                    dbTime, jsonTime, latestTime);
                
                return latestTime;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[HybridTracker] ❌ Error getting last processing time, falling back to JSON");
                return _jsonTracker.GetLastProcessingTime();
            }
        }

        public async Task<bool> TryProcessOfferAsync(string funPayOfferId, string gameName)
        {
            await _semaphore.WaitAsync();
            try
            {
                // Check if already processed (hybrid check)
                var wasProcessed = await WasOfferProcessedAsync(funPayOfferId);
                if (wasProcessed)
                {
                    _logger.LogDebug("[HybridTracker] ⚠️ Offer {OfferId} already processed", funPayOfferId);
                    return false;
                }

                // Add to both systems
                await AddProcessedOfferAsync(funPayOfferId, gameName);
                
                _logger.LogInformation("[HybridTracker] ✅ Successfully processed offer {OfferId}", funPayOfferId);
                return true;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        public async Task SynchronizeWithActiveOffersAsync(ISet<string> verifiedOffers)
        {
            try
            {
                _logger.LogInformation("[HybridTracker] 🔄 Synchronizing with {Count} verified offers", verifiedOffers.Count);

                // Synchronize database
                await _databaseTracker.SynchronizeWithActiveOffersAsync(verifiedOffers);
                
                // Synchronize JSON
                _jsonTracker.SynchronizeWithActiveOffers(verifiedOffers);
                
                _logger.LogInformation("[HybridTracker] ✅ Synchronization completed for both systems");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[HybridTracker] ❌ Error during synchronization");
                throw;
            }
        }

        public async Task<int> GetProcessedOffersCountAsync()
        {
            try
            {
                var dbCount = await _databaseTracker.GetProcessedOffersCountAsync();
                var jsonCount = _jsonTracker.GetAllOfferIds().Count;
                
                _logger.LogDebug("[HybridTracker] 📊 Processed offers count: DB={DbCount}, JSON={JsonCount}", dbCount, jsonCount);
                
                // Return database count as primary
                return dbCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[HybridTracker] ❌ Error getting processed offers count, falling back to JSON");
                return _jsonTracker.GetAllOfferIds().Count;
            }
        }

        public async Task<Dictionary<string, int>> GetOfferCountsByGameAsync()
        {
            try
            {
                return await _databaseTracker.GetOfferCountsByGameAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[HybridTracker] ❌ Error getting offer counts by game");
                return new Dictionary<string, int>();
            }
        }

        public async Task MigrateFromJsonToDatabaseAsync()
        {
            if (_migrationCompleted)
            {
                _logger.LogInformation("[HybridTracker] ✅ Migration already completed");
                return;
            }

            try
            {
                _logger.LogInformation("[HybridTracker] 🔄 Starting migration from JSON to Database");

                var jsonOffers = _jsonTracker.GetAllOfferIds();
                var migratedCount = 0;

                foreach (var offerId in jsonOffers)
                {
                    var existsInDb = await _databaseTracker.WasOfferProcessedAsync(offerId);
                    if (!existsInDb)
                    {
                        await _databaseTracker.AddProcessedOfferAsync(offerId, "Migrated");
                        migratedCount++;
                    }
                }

                _migrationCompleted = true;
                _logger.LogInformation("[HybridTracker] ✅ Migration completed: {MigratedCount}/{TotalCount} offers migrated", 
                    migratedCount, jsonOffers.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[HybridTracker] ❌ Error during migration");
                throw;
            }
        }

        // Synchronous methods for backward compatibility
        public bool WasOfferProcessed(string funPayOfferId)
        {
            try
            {
                return WasOfferProcessedAsync(funPayOfferId).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[HybridTracker] ❌ Error in sync WasOfferProcessed for {OfferId}", funPayOfferId);
                return _jsonTracker.WasOfferProcessed(funPayOfferId);
            }
        }

        public void AddProcessedOffer(string funPayOfferId)
        {
            try
            {
                AddProcessedOfferAsync(funPayOfferId, "Unknown").GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[HybridTracker] ❌ Error in sync AddProcessedOffer for {OfferId}", funPayOfferId);
                _jsonTracker.AddProcessedOffer(funPayOfferId);
            }
        }

        public List<string> GetAllOfferIds()
        {
            try
            {
                return GetAllOfferIdsAsync().GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[HybridTracker] ❌ Error in sync GetAllOfferIds");
                return _jsonTracker.GetAllOfferIds();
            }
        }

        public DateTime GetLastProcessingTime()
        {
            try
            {
                return GetLastProcessingTimeAsync().GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[HybridTracker] ❌ Error in sync GetLastProcessingTime");
                return _jsonTracker.GetLastProcessingTime();
            }
        }

        public void SynchronizeWithActiveOffers(ISet<string> verifiedOffers)
        {
            try
            {
                SynchronizeWithActiveOffersAsync(verifiedOffers).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[HybridTracker] ❌ Error in sync SynchronizeWithActiveOffers");
                _jsonTracker.SynchronizeWithActiveOffers(verifiedOffers);
            }
        }

        // Enhanced status management (stub implementations for compatibility)
        public async Task MarkAsPublishedAsync(string funPayOfferId, string g2gOfferId)
        {
            _logger.LogInformation("[HybridTracker] ℹ️ MarkAsPublishedAsync not implemented in HybridOfferTracker");
        }

        public async Task MarkAsFailedAsync(string funPayOfferId, string reason)
        {
            _logger.LogInformation("[HybridTracker] ℹ️ MarkAsFailedAsync not implemented in HybridOfferTracker");
        }

        public async Task MarkAsRejectedAsync(string funPayOfferId, string reason)
        {
            _logger.LogInformation("[HybridTracker] ℹ️ MarkAsRejectedAsync not implemented in HybridOfferTracker");
        }

        public async Task MarkAsLimitReachedAsync(string funPayOfferId)
        {
            _logger.LogInformation("[HybridTracker] ℹ️ MarkAsLimitReachedAsync not implemented in HybridOfferTracker");
        }

        public async Task<Dictionary<string, int>> GetStatusCountsAsync()
        {
            var offers = await _databaseTracker.GetOfferCountsByGameAsync();
            return offers;
        }

        public void ClearSessionCache()
        {
            _logger.LogInformation("[HybridTracker] ℹ️ No session cache in HybridOfferTracker");
        }

        public void Dispose()
        {
            _semaphore?.Dispose();
        }
    }
}
