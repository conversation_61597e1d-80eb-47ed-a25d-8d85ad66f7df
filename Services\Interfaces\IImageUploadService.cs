using System.Collections.Generic;
using System.Threading.Tasks;

namespace ShopBot.Services
{
    /// <summary>
    /// Interfejs dla serwisu uploadowania obrazów
    /// </summary>
    public interface IImageUploadService
    {
        /// <summary>
        /// Inicjalizuje serwis uploadowania obrazów
        /// </summary>
        Task InitializeAsync();

        /// <summary>
        /// Uploaduje pojedynczy obraz
        /// </summary>
        Task<ImageUploadResult> UploadImageAsync(string imageUrl);

        /// <summary>
        /// Uploaduje wiele obrazów jednocześnie
        /// </summary>
        Task<List<ImageUploadResult>> UploadMultipleImagesAsync(List<string> imageUrls);

        /// <summary>
        /// Uploaduje obrazy specjalnie dla G2G (używa Dropbox)
        /// </summary>
        Task<List<ImageUploadResult>> UploadMultipleImagesForG2G(List<string> imageUrls);

        /// <summary>
        /// Sprawdza dostępność serwisów uploadowania
        /// </summary>
        Task<bool> IsServiceAvailable();

        /// <summary>
        /// Pobiera statystyki uploadów
        /// </summary>
        Task<UploadStatistics> GetUploadStatistics();
    }

    /// <summary>
    /// Statystyki uploadów obrazów
    /// </summary>
    public class UploadStatistics
    {
        public int TotalUploads { get; set; }
        public int SuccessfulUploads { get; set; }
        public int FailedUploads { get; set; }
        public double SuccessRate => TotalUploads > 0 ? (double)SuccessfulUploads / TotalUploads * 100 : 0;
    }
}
