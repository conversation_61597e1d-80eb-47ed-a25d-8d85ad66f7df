using ShopBot.Config;
using static ShopBot.Config.GameConfig;

namespace ShopBot.Services.Interfaces
{
    /// <summary>
    /// Repository pattern dla unified access do wszystkich konfiguracji
    /// Zastępuje bezpośredni dostęp do AppSettings.Instance, FilterConfig.LoadFromFile, GameConfig.GetActiveGames
    /// </summary>
    public interface IConfigurationRepository
    {
        /// <summary>
        /// Pobiera AppSettings z walidacją i error handling
        /// </summary>
        Task<AppSettings> GetAppSettingsAsync();

        /// <summary>
        /// Pobiera konfigurację filtrów dla konkretnej gry
        /// </summary>
        Task<OfferFilter> GetFilterConfigAsync(string gameName = "default");

        /// <summary>
        /// Pobiera konfigurację Imgur
        /// </summary>
        Task<FilterConfig.ImgurConfig?> GetImgurConfigAsync();

        /// <summary>
        /// Pobiera konfigurację Flickr
        /// </summary>
        Task<FilterConfig.FlickrConfig?> GetFlickrConfigAsync();

        /// <summary>
        /// Pobiera listę aktywnych gier
        /// </summary>
        Task<string[]> GetActiveGamesAsync();

        /// <summary>
        /// Pobiera konfigurację konkretnej gry
        /// </summary>
        Task<GameData> GetGameConfigAsync(string gameName);

        /// <summary>
        /// Pobiera ID gry dla G2G
        /// </summary>
        Task<string> GetGameIdAsync(string gameName);

        /// <summary>
        /// Pobiera URL gry dla FunPay
        /// </summary>
        Task<string> GetGameUrlAsync(string gameName);

        /// <summary>
        /// Pobiera atrybuty gry dla parsowania
        /// </summary>
        Task<string[]> GetGameAttributesAsync(string gameName);

        /// <summary>
        /// Pobiera zakres cen dla gry
        /// </summary>
        Task<PriceRange> GetGamePriceRangeAsync(string gameName);

        /// <summary>
        /// Waliduje wszystkie konfiguracje przy starcie
        /// </summary>
        Task<ConfigurationValidationResult> ValidateAllConfigurationsAsync();

        /// <summary>
        /// Odświeża cache konfiguracji (jeśli używany)
        /// </summary>
        Task RefreshCacheAsync();
    }

    /// <summary>
    /// Rezultat walidacji konfiguracji
    /// </summary>
    public class ConfigurationValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        
        public void AddError(string error) => Errors.Add(error);
        public void AddWarning(string warning) => Warnings.Add(warning);
    }
}
