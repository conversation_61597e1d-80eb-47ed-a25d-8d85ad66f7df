using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.DependencyInjection;
using ShopBot.Config;
using ShopBot.Services.Interfaces;
using ShopBot.Services.Factories;
using ShopBot.Tasks;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace ShopBot.Services
{
    /// <summary>
    /// Główny serwis hostowany - zarządza cyklem życia aplikacji ShopBot
    /// </summary>
    public class ShopBotHostedService : BackgroundService
    {
        private readonly ILogger<ShopBotHostedService> _logger;
        private readonly AppSettings _appSettings;
        private readonly ITaskScheduler _taskScheduler;
        private readonly IBrowserService _browserService;
        private readonly IFunPayService _funPayService;
        private readonly IFunPayDetailsService _funPayDetailsService;
        private readonly IImageUploadService _imageUploadService;
        private readonly IParserService _parserService;
        private readonly IOfferTracker _offerTracker;
        private readonly IServiceProvider _serviceProvider;
        private readonly IConfigurationValidator _configValidator;
        private readonly ITaskFactory _taskFactory;

        public ShopBotHostedService(
            ILogger<ShopBotHostedService> logger,
            IOptions<AppSettings> appSettings,
            ITaskScheduler taskScheduler,
            IBrowserService browserService,
            IFunPayService funPayService,
            IFunPayDetailsService funPayDetailsService,
            IImageUploadService imageUploadService,
            IParserService parserService,
            IOfferTracker offerTracker,
            IServiceProvider serviceProvider,
            IConfigurationValidator configValidator,
            ITaskFactory taskFactory)
        {
            _logger = logger;
            _appSettings = appSettings.Value;
            _taskScheduler = taskScheduler;
            _browserService = browserService;
            _funPayService = funPayService;
            _funPayDetailsService = funPayDetailsService;
            _imageUploadService = imageUploadService;
            _parserService = parserService;
            _offerTracker = offerTracker;
            _serviceProvider = serviceProvider;
            _configValidator = configValidator;
            _taskFactory = taskFactory;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            try
            {
                _logger.LogInformation("🚀 Uruchamianie ShopBot");

                // Walidacja konfiguracji
                await ValidateConfiguration();

                // Inicjalizacja serwisów
                await InitializeServices();

                // Konfiguracja zadań cyklicznych
                await ConfigureRecurringTasks();

                _logger.LogInformation("✅ ShopBot uruchomiony pomyślnie. Oczekiwanie na zadania...");

                // Główna pętla - czekamy na zatrzymanie
                await Task.Delay(Timeout.Infinite, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("🛑 ShopBot został zatrzymany");
            }
            catch (Exception ex)
            {
                _logger.LogCritical(ex, "💥 Krytyczny błąd w ShopBot");
                throw;
            }
        }

        private async Task ValidateConfiguration()
        {
            if (_appSettings.Features.EnableConfigValidation)
            {
                _logger.LogInformation("🔍 Comprehensive configuration validation starting...");

                // Use new Repository-based validation
                var configRepo = _serviceProvider.GetRequiredService<IConfigurationRepository>();
                var validationResult = await configRepo.ValidateAllConfigurationsAsync();

                if (!validationResult.IsValid)
                {
                    _logger.LogCritical("❌ Configuration validation failed with {ErrorCount} errors:", validationResult.Errors.Count);
                    foreach (var error in validationResult.Errors)
                    {
                        _logger.LogCritical("  • {Error}", error);
                    }

                    _logger.LogCritical("🛑 ShopBot cannot start with invalid configuration. Please fix the errors above.");
                    throw new InvalidOperationException($"Configuration validation failed with {validationResult.Errors.Count} errors. Check logs for details.");
                }

                if (validationResult.Warnings.Count > 0)
                {
                    _logger.LogWarning("⚠️ Configuration warnings ({WarningCount}):", validationResult.Warnings.Count);
                    foreach (var warning in validationResult.Warnings)
                    {
                        _logger.LogWarning("  • {Warning}", warning);
                    }
                    _logger.LogWarning("⚠️ ShopBot will continue, but consider reviewing the warnings above.");
                }

                _logger.LogInformation("✅ Configuration validation completed successfully");
            }
            else
            {
                _logger.LogWarning("⚠️ Configuration validation is disabled. Enable it in Features.EnableConfigValidation for better reliability.");
            }
        }

        private async Task InitializeServices()
        {
            _logger.LogInformation("🔧 Inicjalizacja serwisów");

            // Inicjalizacja bazy danych
            _logger.LogDebug("Inicjalizacja bazy danych");
            var databaseService = _serviceProvider.GetRequiredService<IDatabaseMigrationService>();
            await databaseService.InitializeDatabaseAsync();

            // Inicjalizacja przeglądarki
            _logger.LogDebug("Inicjalizacja przeglądarki (ID: {BrowserId})", _appSettings.Application.BrowserId);
            await _browserService.InitializeBrowserAsync();

            // Inicjalizacja FunPay Details Service (dedykowana zakładka)
            _logger.LogDebug("Inicjalizacja FunPay Details Service");
            await _funPayDetailsService.StartAsync();

            // Inicjalizacja serwisu obrazów
            _logger.LogDebug("Inicjalizacja serwisu obrazów");
            await _imageUploadService.InitializeAsync();

            // Inicjalizacja Dropbox Token Manager
            if (_appSettings.Dropbox.Enabled)
            {
                _logger.LogDebug("Inicjalizacja Dropbox Token Manager");
                var dropboxTokenManager = _serviceProvider.GetRequiredService<DropboxTokenManager>();
                await dropboxTokenManager.InitializeAsync();
            }

            _logger.LogInformation("✅ Wszystkie serwisy zainicjalizowane");
        }

        private async Task ConfigureRecurringTasks()
        {
            _logger.LogInformation("⏰ Konfiguracja zadań cyklicznych");

            // Główne zadanie synchronizacji
            var syncInterval = TimeSpan.FromMinutes(_appSettings.Application.SyncIntervalMinutes);
            _logger.LogInformation("Interwał synchronizacji: {Interval} minut", _appSettings.Application.SyncIntervalMinutes);

            var syncTask = _taskFactory.CreateScanAndSyncOffersTask();

            _taskScheduler.ScheduleRecurringTask(syncTask, syncInterval, () =>
            {
                ScanAndSyncOffersTask.ResetVerificationFlag();
                _logger.LogInformation("🔄 Rozpoczynam nowy cykl synchronizacji");
            });

            // Zadanie czyszczenia
            var cleanupInterval = TimeSpan.FromHours(_appSettings.Application.CleanupIntervalHours);
            _logger.LogInformation("Interwał czyszczenia: {Interval} godzin", _appSettings.Application.CleanupIntervalHours);

            var cleanupTask = new CleanupTask();
            _taskScheduler.ScheduleRecurringTask(cleanupTask, cleanupInterval);

            _logger.LogInformation("✅ Zadania cykliczne skonfigurowane");
        }

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("🛑 Zatrzymywanie ShopBot");

            // Zatrzymaj scheduler zadań
            _taskScheduler.StopAllTasks();

            // Zatrzymaj FunPay Details Service
            await _funPayDetailsService.StopAsync();

            // Zamknij przeglądarkę
            await _browserService.CloseBrowserAsync();

            await base.StopAsync(cancellationToken);

            _logger.LogInformation("✅ ShopBot zatrzymany");
        }
    }
}
