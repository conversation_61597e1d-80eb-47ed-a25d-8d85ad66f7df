using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.Logging;
using ShopBot.Data;
using ShopBot.Services.Interfaces;

namespace ShopBot.Services
{
    public class ShopBotRepository : IShopBotRepository, IDisposable
    {
        private readonly ShopBotDbContext _context;
        private readonly ILogger<ShopBotRepository> _logger;
        private IDbContextTransaction? _transaction;

        public ShopBotRepository(
            ShopBotDbContext context,
            ILogger<ShopBotRepository> logger,
            ILogger<OfferRepository> offerLogger,
            ILogger<ProcessingStatsRepository> statsLogger,
            ILogger<ImageUploadRepository> imageLogger,
            ILogger<ErrorLogRepository> errorLogger)
        {
            _context = context;
            _logger = logger;

            // Initialize repositories
            Offers = new OfferRepository(_context, offerLogger);
            ProcessingStats = new ProcessingStatsRepository(_context, statsLogger);
            ImageUploads = new ImageUploadRepository(_context, imageLogger);
            ErrorLogs = new ErrorLogRepository(_context, errorLogger);
        }

        public IOfferRepository Offers { get; }
        public IProcessingStatsRepository ProcessingStats { get; }
        public IImageUploadRepository ImageUploads { get; }
        public IErrorLogRepository ErrorLogs { get; }

        public async Task<int> SaveChangesAsync()
        {
            try
            {
                var changes = await _context.SaveChangesAsync();
                _logger.LogDebug("[ShopBotRepo] ✅ Saved {Changes} changes to database", changes);
                return changes;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ShopBotRepo] ❌ Failed to save changes to database");
                throw;
            }
        }

        public async Task BeginTransactionAsync()
        {
            if (_transaction != null)
            {
                throw new InvalidOperationException("Transaction already in progress");
            }

            _logger.LogDebug("[ShopBotRepo] 🔄 Beginning database transaction");
            _transaction = await _context.Database.BeginTransactionAsync();
        }

        public async Task CommitTransactionAsync()
        {
            if (_transaction == null)
            {
                throw new InvalidOperationException("No transaction in progress");
            }

            try
            {
                await _transaction.CommitAsync();
                _logger.LogDebug("[ShopBotRepo] ✅ Transaction committed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ShopBotRepo] ❌ Failed to commit transaction");
                await RollbackTransactionAsync();
                throw;
            }
            finally
            {
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public async Task RollbackTransactionAsync()
        {
            if (_transaction == null)
            {
                throw new InvalidOperationException("No transaction in progress");
            }

            try
            {
                await _transaction.RollbackAsync();
                _logger.LogWarning("[ShopBotRepo] ⚠️ Transaction rolled back");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ShopBotRepo] ❌ Failed to rollback transaction");
                throw;
            }
            finally
            {
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public void Dispose()
        {
            _transaction?.Dispose();
            _context?.Dispose();
        }
    }
}
