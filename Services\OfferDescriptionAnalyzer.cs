using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.IO;
using System.Threading.Tasks;
using System.Linq;
using System.Text.RegularExpressions;

namespace ShopBot.Services
{
    public class OfferDescriptionAnalyzer
    {
        private const string DATA_DIR = "data/descriptions";
        private const string ANALYSIS_DIR = "data/analysis";
        private readonly Dictionary<string, Dictionary<string, OfferDescription>> _cache;

        public class OfferDescription
        {
            public string Id { get; set; }
            public string GameName { get; set; }
            public string ShortDescription { get; set; }
            public string DetailedDescription { get; set; }
        }

        public OfferDescriptionAnalyzer()
        {
            _cache = new Dictionary<string, Dictionary<string, OfferDescription>>();
            EnsureDirectoriesExist();
        }

        private void EnsureDirectoriesExist()
        {
            Directory.CreateDirectory(DATA_DIR);
            Directory.CreateDirectory(ANALYSIS_DIR);
        }

        private string GetGameFilePath(string gameName)
        {
            return Path.Combine(DATA_DIR, $"{gameName}_descriptions.json");
        }

        private void LoadGameData(string gameName)
        {
            var filePath = GetGameFilePath(gameName);
            try
            {
                if (File.Exists(filePath))
                {
                    var json = File.ReadAllText(filePath);
                    var offers = JsonSerializer.Deserialize<Dictionary<string, OfferDescription>>(json);
                    _cache[gameName] = offers;
                    Console.WriteLine($"[Cache] ✓ Wczytano {offers.Count} opisów dla gry {gameName}");
                }
                else
                {
                    _cache[gameName] = new Dictionary<string, OfferDescription>();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[Cache] ⚠️ Błąd podczas wczytywania danych dla {gameName}: {ex.Message}");
                _cache[gameName] = new Dictionary<string, OfferDescription>();
            }
        }

        public async Task SaveGameData(string gameName)
        {
            if (!_cache.ContainsKey(gameName)) return;

            var filePath = GetGameFilePath(gameName);
            try
            {
                var json = JsonSerializer.Serialize(_cache[gameName], new JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(filePath, json);
                Console.WriteLine($"[Cache] ✓ Zapisano {_cache[gameName].Count} opisów dla gry {gameName}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[Cache] ⚠️ Błąd podczas zapisywania danych dla {gameName}: {ex.Message}");
            }
        }

        public void AddOrUpdateOffer(string gameName, OfferDescription offer)
        {
            if (!_cache.ContainsKey(gameName))
            {
                LoadGameData(gameName);
            }
            _cache[gameName][offer.Id] = offer;
        }

        public int GetTotalOffers()
        {
            return _cache.Sum(g => g.Value.Count);
        }

        public async Task GenerateAnalysisReport()
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var reportPath = Path.Combine(ANALYSIS_DIR, $"analysis_report_{timestamp}.txt");
                var commonPhrases = AnalyzeCommonPhrases();
                var suspiciousPatterns = AnalyzeSuspiciousPatterns();

                using (var writer = new StreamWriter(reportPath))
                {
                    await writer.WriteLineAsync("=== Raport Analizy Opisów Ofert ===\n");
                    await writer.WriteLineAsync($"Data wygenerowania: {DateTime.Now}\n");
                    await writer.WriteLineAsync($"Liczba przeanalizowanych gier: {_cache.Count}");
                    await writer.WriteLineAsync($"Łączna liczba ofert: {GetTotalOffers()}\n");

                    // Statystyki dla każdej gry
                    foreach (var (game, offers) in _cache)
                    {
                        await writer.WriteLineAsync($"\n=== {game} ===");
                        await writer.WriteLineAsync($"Liczba ofert: {offers.Count}");
                    }

                    // Najczęściej występujące frazy
                    await writer.WriteLineAsync("\n=== Najczęściej występujące frazy ===");
                    foreach (var (phrase, count) in commonPhrases.Take(20))
                    {
                        await writer.WriteLineAsync($"{phrase}: {count} wystąpień");
                    }

                    // Podejrzane wzorce
                    await writer.WriteLineAsync("\n=== Podejrzane wzorce ===");
                    foreach (var (pattern, matches) in suspiciousPatterns)
                    {
                        await writer.WriteLineAsync($"\nWzorzec: {pattern}");
                        foreach (var match in matches.Take(5))
                        {
                            await writer.WriteLineAsync($"- {match}");
                        }
                    }
                }

                Console.WriteLine($"[Analysis] ✓ Wygenerowano raport: {reportPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[Analysis] ❌ Błąd podczas generowania raportu: {ex.Message}");
            }
        }

        private Dictionary<string, int> AnalyzeCommonPhrases()
        {
            var phrases = new Dictionary<string, int>();
            var wordPattern = new Regex(@"\b\w+\b", RegexOptions.Compiled);

            foreach (var offers in _cache.Values)
            {
                foreach (var offer in offers.Values)
                {
                    var text = $"{offer.ShortDescription} {offer.DetailedDescription}";
                    var words = wordPattern.Matches(text.ToLower())
                        .Select(m => m.Value)
                        .Where(w => w.Length > 3);

                    foreach (var word in words)
                    {
                        phrases[word] = phrases.GetValueOrDefault(word) + 1;
                    }
                }
            }

            return phrases.OrderByDescending(p => p.Value)
                .ToDictionary(p => p.Key, p => p.Value);
        }

        private Dictionary<string, List<string>> AnalyzeSuspiciousPatterns()
        {
            var patterns = new Dictionary<string, List<string>>
            {
                { "Linki zewnętrzne", new List<string>() },
                { "Adresy email", new List<string>() },
                { "Numery telefonów", new List<string>() },
                { "Komunikatory", new List<string>() }
            };

            var linkPattern = new Regex(@"https?://\S+|www\.\S+", RegexOptions.Compiled | RegexOptions.IgnoreCase);
            var emailPattern = new Regex(@"\b[\w\.-]+@[\w\.-]+\.\w+\b", RegexOptions.Compiled | RegexOptions.IgnoreCase);
            var phonePattern = new Regex(@"\b\d{3}[-.]?\d{3}[-.]?\d{3}\b", RegexOptions.Compiled);
            var communicatorPattern = new Regex(@"\b(discord|telegram|whatsapp|skype|messenger)[@:]?\s*\S+\b", RegexOptions.Compiled | RegexOptions.IgnoreCase);

            foreach (var offers in _cache.Values)
            {
                foreach (var offer in offers.Values)
                {
                    var text = $"{offer.ShortDescription} {offer.DetailedDescription}";

                    foreach (Match match in linkPattern.Matches(text))
                        patterns["Linki zewnętrzne"].Add($"{offer.GameName} - {offer.Id}: {match.Value}");

                    foreach (Match match in emailPattern.Matches(text))
                        patterns["Adresy email"].Add($"{offer.GameName} - {offer.Id}: {match.Value}");

                    foreach (Match match in phonePattern.Matches(text))
                        patterns["Numery telefonów"].Add($"{offer.GameName} - {offer.Id}: {match.Value}");

                    foreach (Match match in communicatorPattern.Matches(text))
                        patterns["Komunikatory"].Add($"{offer.GameName} - {offer.Id}: {match.Value}");
                }
            }

            return patterns;
        }

        public async Task AnalyzeTemplates()
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var templatesPath = Path.Combine(ANALYSIS_DIR, $"templates_analysis_{timestamp}.txt");
                var templates = FindTemplates();

                using (var writer = new StreamWriter(templatesPath))
                {
                    await writer.WriteLineAsync("=== Analiza Szablonów Opisów ===\n");
                    await writer.WriteLineAsync($"Data wygenerowania: {DateTime.Now}\n");

                    // Szablony w krótkich opisach
                    await writer.WriteLineAsync("\n=== Powtarzające się szablony w krótkich opisach ===");
                    foreach (var (template, occurrences) in templates.ShortDescriptionTemplates.OrderByDescending(x => x.Value.Count))
                    {
                        await writer.WriteLineAsync($"\nSzablon (użyty {occurrences.Count} razy):");
                        await writer.WriteLineAsync(template);
                        await writer.WriteLineAsync("\nPrzykłady użycia:");
                        foreach (var example in occurrences.Take(3))
                        {
                            await writer.WriteLineAsync($"- ID: {example.Id}, Gra: {example.GameName}");
                            await writer.WriteLineAsync($"  {example.ShortDescription}");
                        }
                        await writer.WriteLineAsync("---");
                    }

                    // Szablony w szczegółowych opisach
                    await writer.WriteLineAsync("\n=== Powtarzające się szablony w szczegółowych opisach ===");
                    foreach (var (template, occurrences) in templates.DetailedDescriptionTemplates.OrderByDescending(x => x.Value.Count))
                    {
                        if (string.IsNullOrWhiteSpace(template)) continue;
                        
                        await writer.WriteLineAsync($"\nSzablon (użyty {occurrences.Count} razy):");
                        await writer.WriteLineAsync(template);
                        await writer.WriteLineAsync("\nPrzykłady użycia:");
                        foreach (var example in occurrences.Take(3))
                        {
                            await writer.WriteLineAsync($"- ID: {example.Id}, Gra: {example.GameName}");
                            await writer.WriteLineAsync($"  {example.DetailedDescription}");
                        }
                        await writer.WriteLineAsync("---");
                    }
                }

                Console.WriteLine($"[Analysis] ✓ Wygenerowano analizę szablonów: {templatesPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[Analysis] ❌ Błąd podczas analizy szablonów: {ex.Message}");
            }
        }

        private (Dictionary<string, List<OfferDescription>> ShortDescriptionTemplates, 
                Dictionary<string, List<OfferDescription>> DetailedDescriptionTemplates) FindTemplates()
        {
            var shortTemplates = new Dictionary<string, List<OfferDescription>>();
            var detailedTemplates = new Dictionary<string, List<OfferDescription>>();

            // Normalizuj tekst do porównania
            string NormalizeText(string text)
            {
                if (string.IsNullOrWhiteSpace(text)) return "";
                
                // Usuń emoji i specjalne znaki
                text = Regex.Replace(text, @"[\uD83C-\uDBFF\uDC00-\uDFFF\uFE0F]+", "");
                // Usuń liczby (ale zachowaj wzorce jak "2k1" czy "3k1")
                text = Regex.Replace(text, @"(?<![\dk])\d+(?!k\d)", "X");
                // Zamień wielokrotne spacje na pojedynczą
                text = Regex.Replace(text, @"\s+", " ");
                // Usuń linki
                text = Regex.Replace(text, @"https?://\S+", "URL");
                return text.Trim().ToLower();
            }

            foreach (var gameOffers in _cache.Values)
            {
                foreach (var offer in gameOffers.Values)
                {
                    var normalizedShort = NormalizeText(offer.ShortDescription);
                    var normalizedDetailed = NormalizeText(offer.DetailedDescription);

                    if (!shortTemplates.ContainsKey(normalizedShort))
                        shortTemplates[normalizedShort] = new List<OfferDescription>();
                    shortTemplates[normalizedShort].Add(offer);

                    if (!detailedTemplates.ContainsKey(normalizedDetailed))
                        detailedTemplates[normalizedDetailed] = new List<OfferDescription>();
                    detailedTemplates[normalizedDetailed].Add(offer);
                }
            }

            // Zostaw tylko szablony użyte więcej niż raz
            return (
                ShortDescriptionTemplates: shortTemplates.Where(x => x.Value.Count > 1).ToDictionary(x => x.Key, x => x.Value),
                DetailedDescriptionTemplates: detailedTemplates.Where(x => x.Value.Count > 1).ToDictionary(x => x.Key, x => x.Value)
            );
        }
    }
} 