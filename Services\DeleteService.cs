using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using HtmlAgilityPack;
using Microsoft.Playwright;
using ShopBot.Config;
using ShopBot.Exceptions;

namespace ShopBot.Services
{
    public class DeleteService
    {
        private readonly IBrowser _browser;
        private readonly IBrowserContext _context;
        private readonly ProcessedOffersTracker _tracker;

        public class GameUrlConfig
        {
            public string Name { get; set; } = string.Empty;
            public string Url { get; set; } = string.Empty;
            public bool IsEnabled { get; set; } = false;
        }

        public class Config
        {
            public List<GameUrlConfig> Urls { get; set; } = new List<GameUrlConfig>();
        }

        public DeleteService(IBrowser browser, IBrowserContext context, ProcessedOffersTracker tracker)
        {
            _browser = browser;
            _context = context;
            _tracker = tracker;
        }

        public async Task DeleteAllOffers()
        {
            try
            {
                var configContent = await File.ReadAllTextAsync("deleteconfig.json");
                var config = JsonSerializer.Deserialize<Config>(configContent);

                if (config == null || config.Urls == null)
                {
                    Console.WriteLine("[Delete] Nie udało się wczytać konfiguracji URL.");
                    return;
                }

                Console.WriteLine("[Delete] Starting deletion process...");
                foreach (var gameUrl in config.Urls)
                {
                    if (!gameUrl.IsEnabled) 
                    {
                        Console.WriteLine($"[Delete] Skipping disabled game: {gameUrl.Name}");
                        continue;
                    }

                    Console.WriteLine($"[Delete] Processing: {gameUrl.Name}");
                    var page = await _context.NewPageAsync();
                    try
                    {
                        await ProcessGame(page, gameUrl);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"[Delete] Error processing {gameUrl.Name}: {ex.Message}");
                    }
                    finally
                    {
                        await page.CloseAsync();
                    }
                }

                Console.WriteLine("[Delete] All offers deleted");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[Delete] Error during deletion process: {ex.Message}");
                throw;
            }
        }

        private async Task ProcessGame(IPage page, GameUrlConfig gameUrl)
        {
            await page.GotoAsync(gameUrl.Url);
            await Task.Delay(4500);

            var content = await page.ContentAsync();
            var doc = new HtmlDocument();
            doc.LoadHtml(content);

            var buttons = doc.DocumentNode.SelectNodes("//button[contains(@class, 'q-btn') and contains(@class, 'q-btn-item') and contains(@class, 'q-btn--flat') and contains(@class, 'q-btn--rectangle') and contains(@class, 'text-primary') and contains(@class, 'q-btn--actionable') and contains(@class, 'q-focusable') and contains(@class, 'q-hoverable') and contains(@class, 'q-btn--wrap') and @style='min-width: 2em;']");

            int totalPages = 1;
            if (buttons != null && buttons.Count > 0)
            {
                var lastButton = buttons[buttons.Count - 1];
                var numberSpan = lastButton.SelectSingleNode(".//span[@class='block']");
                if (numberSpan != null && int.TryParse(numberSpan.InnerText, out totalPages))
                {
                    Console.WriteLine($"[Delete] {gameUrl.Name}: Found {totalPages} pages");
                }
            }

            for (int pageNumber = 1; pageNumber <= totalPages; pageNumber++)
            {
                string pageUrl = gameUrl.Url.Contains("&page=")
                    ? gameUrl.Url.Substring(0, gameUrl.Url.IndexOf("&page=")) + $"&page={pageNumber}"
                    : $"{gameUrl.Url}&page={pageNumber}";
                
                var newPage = await _context.NewPageAsync();
                try
                {
                    await ProcessPageAsync(newPage, pageUrl, pageNumber, gameUrl.Name);
                }
                finally
                {
                    await newPage.CloseAsync();
                }
                
                await Task.Delay(2000);
            }
        }

        private async Task ProcessPageAsync(IPage page, string pageUrl, int pageNumber, string gameName)
        {
            try
            {
                Console.WriteLine($"[Delete] Processing page {pageNumber} for {gameName}");
                await page.GotoAsync(pageUrl);
                await page.WaitForSelectorAsync("body", new() { State = WaitForSelectorState.Visible });
                await Task.Delay(9000);

                var noOfferSelector = "div.q-table__bottom.row.items-center.q-table__bottom--nodata div.q-my-md div.column.flex-center.items-center.text-center div.text-h6.q-mt-md:has-text('No offer found')";
                var noOfferFound = await page.QuerySelectorAsync(noOfferSelector);
                if (noOfferFound != null)
                {
                    Console.WriteLine($"[Delete] Page {pageNumber} of {gameName} is already empty");
                    return;
                }

                await page.WaitForSelectorAsync(
                    "th.q-table--col-auto-width div.q-checkbox.cursor-pointer[role='checkbox'][aria-checked='false']",
                    new() { State = WaitForSelectorState.Visible, Timeout = 15000 }
                );
                await Task.Delay(1500);

                await page.ClickAsync("th.q-table--col-auto-width div.q-checkbox.cursor-pointer[role='checkbox'][aria-checked='false']");
                await Task.Delay(1500);

                await page.ClickAsync("button.q-btn.q-btn-item:has-text('delete')");
                await Task.Delay(1500);

                await page.ClickAsync("button.q-btn.q-btn-item:has-text('Confirm')");
                await Task.Delay(3500);

                Console.WriteLine($"[Delete] Page {pageNumber} processed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[Delete] Error processing page {pageNumber} of {gameName}: {ex.Message}");
            }
            finally
            {
                await page.CloseAsync();
            }
        }

        public async Task DeleteOffer(string offerId)
        {
            try
            {
                Console.WriteLine($"[Delete] ❌ Rozpoczynam usuwanie oferty {offerId}...");
                var page = await _context.NewPageAsync();
                try
                {
                    // 1. Przejdź do listy ofert
                    await page.GotoAsync("https://www.g2g.com/offers/list?cat_id=5830014a-b974-45c6-9672-b51e83112fb7&status=live");
                    await Task.Delay(3000);

                    // 2. Znajdź i wypełnij pole wyszukiwania
                    var searchSelector = "input.q-field__native[placeholder='Search title or offer number']";
                    await page.WaitForSelectorAsync(searchSelector, new() { State = WaitForSelectorState.Visible, Timeout = 10000 });
                    await page.EvalOnSelectorAsync(searchSelector, "el => el.value = ''");
                    await page.FillAsync(searchSelector, offerId);
                    await Task.Delay(1000);

                    // 3. Czekaj na załadowanie wyników
                    await page.WaitForSelectorAsync("div.q-table__container", new() { State = WaitForSelectorState.Visible, Timeout = 10000 });
                    await Task.Delay(2000);

                    // Sprawdź czy oferta istnieje
                    var offerRow = await page.QuerySelectorAsync($"tr:has(div.text-body2:has-text('#{offerId}'))");
                    if (offerRow == null)
                    {
                        Console.WriteLine($"[Delete] ℹ️ Oferta {offerId} nie została znaleziona - prawdopodobnie już usunięta");
                        return;
                    }

                    // 4. Zaznacz checkbox oferty
                    var checkboxSelector = $"tr:has(div.text-body2:has-text('#{offerId}')) div[role='checkbox']";
                    var checkbox = await page.WaitForSelectorAsync(checkboxSelector, new() { State = WaitForSelectorState.Visible, Timeout = 10000 });
                    if (checkbox == null)
                    {
                        throw new Exception($"Nie znaleziono checkboxa dla oferty {offerId}");
                    }
                    await checkbox.ClickAsync();
                    await Task.Delay(1000);

                    // 5. Kliknij przycisk Delete
                    var deleteButtonSelector = "button.q-btn.text-negative i.material-icons:has-text('delete')";
                    var deleteButton = await page.WaitForSelectorAsync(deleteButtonSelector, new() { State = WaitForSelectorState.Visible, Timeout = 10000 });
                    if (deleteButton == null)
                    {
                        throw new Exception($"Nie znaleziono przycisku Delete dla oferty {offerId}");
                    }
                    await deleteButton.ClickAsync();
                    await Task.Delay(1000);

                    // 6. Potwierdź usunięcie
                    var confirmSelector = "span.q-btn__content:has-text('Confirm')";
                    var confirmButton = await page.WaitForSelectorAsync(confirmSelector, new() { State = WaitForSelectorState.Visible, Timeout = 10000 });
                    if (confirmButton == null)
                    {
                        throw new Exception($"Nie znaleziono przycisku Confirm dla oferty {offerId}");
                    }
                    await confirmButton.ClickAsync();
                    
                    // 7. Czekaj na odpowiedź API - używaj ustawień z konfiguracji
                    var appSettings = AppSettings.Instance;
                    var totalTimeoutSeconds = appSettings.Timeouts.DeleteOfferTimeoutSeconds;
                    var delayBetweenAttempts = appSettings.Timeouts.DeleteOfferCheckIntervalSeconds * 1000;
                    var maxAttempts = totalTimeoutSeconds * 1000 / delayBetweenAttempts;
                    var currentAttempt = 0;
                    var deleted = false;

                    Console.WriteLine($"[Delete] 🔄 Rozpoczynam weryfikację usunięcia oferty {offerId} (max {totalTimeoutSeconds}s, sprawdzanie co {appSettings.Timeouts.DeleteOfferCheckIntervalSeconds}s)");

                    while (currentAttempt < maxAttempts && !deleted)
                    {
                        await Task.Delay(delayBetweenAttempts);
                        currentAttempt++;

                        try
                        {
                            // Odśwież stronę co 10 prób, aby upewnić się, że mamy aktualne dane
                            if (currentAttempt % 10 == 0)
                            {
                                Console.WriteLine($"[Delete] 🔄 Odświeżam stronę (próba {currentAttempt}/{maxAttempts})");
                                await page.ReloadAsync(new() { WaitUntil = WaitUntilState.NetworkIdle });
                                await Task.Delay(2000);
                            }

                            // Sprawdź czy oferta zniknęła z listy
                            var offerStillExists = await page.QuerySelectorAsync($"div.text-body2:has-text('#{offerId}')");
                            if (offerStillExists == null)
                            {
                                deleted = true;
                                // Usuń z trackera dopiero po potwierdzeniu od API
                                G2GOfferTracker.RemoveOffer(offerId);
                                Console.WriteLine($"[Delete] ✅ API potwierdziło usunięcie oferty {offerId} po {currentAttempt} próbach");
                                break;
                            }
                            else
                            {
                                if (currentAttempt % 5 == 0) // Loguj co 5 prób, aby nie spamować
                                {
                                    Console.WriteLine($"[Delete] ⏳ Czekam na potwierdzenie usunięcia oferty {offerId} (próba {currentAttempt}/{maxAttempts})");
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"[Delete] ⚠️ Błąd podczas sprawdzania statusu oferty {offerId} (próba {currentAttempt}): {ex.Message}");
                            // Kontynuuj pętlę mimo błędu
                            continue;
                        }
                    }

                    if (!deleted)
                    {
                        Console.WriteLine($"[Delete] ❌ Timeout: Nie potwierdzono usunięcia oferty {offerId} po {totalTimeoutSeconds} sekundach");
                        throw new OfferDeletionException(
                            $"Nie potwierdzono usunięcia oferty po {totalTimeoutSeconds} sekundach. Oferta może być nadal w trakcie usuwania przez API G2G.",
                            offerId,
                            TimeSpan.FromSeconds(totalTimeoutSeconds));
                    }
                }
                finally
                {
                    await page.CloseAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[Delete] ❌ Błąd podczas usuwania oferty {offerId}: {ex.Message}");
                throw;
            }
        }
    }
} 