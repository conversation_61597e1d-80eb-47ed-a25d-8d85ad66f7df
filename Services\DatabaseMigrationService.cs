using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ShopBot.Config;
using ShopBot.Data;

namespace ShopBot.Services
{
    public interface IDatabaseMigrationService
    {
        Task InitializeDatabaseAsync();
        Task<bool> IsDatabaseAvailableAsync();
        Task<List<string>> GetPendingMigrationsAsync();
        Task ApplyMigrationsAsync();
    }

    public class DatabaseMigrationService : IDatabaseMigrationService
    {
        private readonly ShopBotDbContext _context;
        private readonly AppSettings _appSettings;
        private readonly ILogger<DatabaseMigrationService> _logger;

        public DatabaseMigrationService(
            ShopBotDbContext context,
            AppSettings appSettings,
            ILogger<DatabaseMigrationService> logger)
        {
            _context = context;
            _appSettings = appSettings;
            _logger = logger;
        }

        public async Task InitializeDatabaseAsync()
        {
            try
            {
                _logger.LogInformation("[DatabaseMigration] 🔄 Initializing database...");

                // Check if database exists and is accessible
                var canConnect = await IsDatabaseAvailableAsync();
                if (!canConnect)
                {
                    _logger.LogError("[DatabaseMigration] ❌ Cannot connect to database");
                    throw new InvalidOperationException("Database connection failed");
                }

                // Apply migrations if enabled
                if (_appSettings.Database.EnableMigrations)
                {
                    await ApplyMigrationsAsync();
                }
                else
                {
                    _logger.LogWarning("[DatabaseMigration] ⚠️ Database migrations are disabled");
                }

                _logger.LogInformation("[DatabaseMigration] ✅ Database initialization completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[DatabaseMigration] ❌ Database initialization failed");
                throw;
            }
        }

        public async Task<bool> IsDatabaseAvailableAsync()
        {
            try
            {
                _logger.LogDebug("[DatabaseMigration] 🔍 Testing database connection...");
                
                var canConnect = await _context.Database.CanConnectAsync();
                
                if (canConnect)
                {
                    _logger.LogDebug("[DatabaseMigration] ✅ Database connection successful");
                }
                else
                {
                    _logger.LogWarning("[DatabaseMigration] ⚠️ Cannot connect to database");
                }

                return canConnect;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[DatabaseMigration] ❌ Database connection test failed");
                return false;
            }
        }

        public async Task<List<string>> GetPendingMigrationsAsync()
        {
            try
            {
                _logger.LogDebug("[DatabaseMigration] 🔍 Checking for pending migrations...");
                
                var pendingMigrations = await _context.Database.GetPendingMigrationsAsync();
                var migrationsList = pendingMigrations.ToList();
                
                _logger.LogDebug("[DatabaseMigration] Found {Count} pending migrations", migrationsList.Count);
                
                return migrationsList;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[DatabaseMigration] ❌ Failed to get pending migrations");
                throw;
            }
        }

        public async Task ApplyMigrationsAsync()
        {
            try
            {
                var pendingMigrations = await GetPendingMigrationsAsync();
                
                if (pendingMigrations.Count == 0)
                {
                    _logger.LogInformation("[DatabaseMigration] ✅ Database is up to date");
                    return;
                }

                _logger.LogInformation("[DatabaseMigration] 🔄 Applying {Count} pending migrations...", pendingMigrations.Count);
                
                foreach (var migration in pendingMigrations)
                {
                    _logger.LogDebug("[DatabaseMigration] Applying migration: {Migration}", migration);
                }

                await _context.Database.MigrateAsync();
                
                _logger.LogInformation("[DatabaseMigration] ✅ All migrations applied successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[DatabaseMigration] ❌ Failed to apply migrations");
                throw;
            }
        }
    }
}
