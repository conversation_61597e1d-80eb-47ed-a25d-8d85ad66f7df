using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ShopBot.Models
{
    public class Offer
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        [Required]
        [MaxLength(100)]
        public string FunPayOfferId { get; set; } = string.Empty;

        [Required]
        [MaxLength(50)]
        public string GameName { get; set; } = string.Empty;

        [Required]
        [MaxLength(500)]
        public string Title { get; set; } = string.Empty;

        public string? Description { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal OriginalPrice { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal ConvertedPrice { get; set; }

        [MaxLength(10)]
        public string Currency { get; set; } = "USD";

        // Status tracking
        [Required]
        [MaxLength(20)]
        public string Status { get; set; } = "Processing"; // Processing, Processed, Published, Failed, Rejected, LimitReached, Inactive // Pending, Processed, Published, Failed, Skipped

        public DateTime ProcessedAt { get; set; }

    // Enhanced tracking fields
    public DateTime? PublishedAt { get; set; }
    public DateTime? LastAttemptAt { get; set; }
    public string? FailureReason { get; set; }

        // Source data
        [MaxLength(1000)]
        public string? SourceUrl { get; set; }

        public string? ImageUrls { get; set; } // JSON array

        public int ImageCount { get; set; } = 0; // Number of images

        // G2G data
        [MaxLength(100)]
        public string? G2GOfferId { get; set; }

        [MaxLength(1000)]
        public string? G2GUrl { get; set; }

        // Metadata
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual ICollection<ImageUpload> ImageUploads { get; set; } = new List<ImageUpload>();
        public virtual ICollection<ErrorLog> ErrorLogs { get; set; } = new List<ErrorLog>();

        /// <summary>
        /// Updates ImageCount based on ImageUrls JSON content
        /// </summary>
        public void UpdateImageCount()
        {
            if (string.IsNullOrEmpty(ImageUrls))
            {
                ImageCount = 0;
                return;
            }

            try
            {
                using var document = System.Text.Json.JsonDocument.Parse(ImageUrls);
                ImageCount = document.RootElement.ValueKind == System.Text.Json.JsonValueKind.Array
                    ? document.RootElement.GetArrayLength()
                    : 0;
            }
            catch
            {
                ImageCount = 0;
            }
        }

        /// <summary>
        /// Sets ImageUrls and automatically updates ImageCount
        /// </summary>
        public void SetImageUrls(string? imageUrls)
        {
            ImageUrls = imageUrls;
            UpdateImageCount();
        }
    }

    public class ProcessingStat
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        [Required]
        public DateTime Date { get; set; }

        [Required]
        [MaxLength(50)]
        public string GameName { get; set; } = string.Empty;

        // Counts
        public int OffersScanned { get; set; }
        public int OffersProcessed { get; set; }
        public int OffersPublished { get; set; }
        public int OffersFailed { get; set; }
        public int OffersSkipped { get; set; }

        // Performance
        public int? AverageProcessingTimeMs { get; set; }
        public long? TotalProcessingTimeMs { get; set; }

        // Errors
        public int ErrorCount { get; set; }
        public string? LastError { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }

    public class ImageUpload
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        [Required]
        public Guid OfferId { get; set; }

        [Required]
        [MaxLength(1000)]
        public string OriginalUrl { get; set; } = string.Empty;

        [MaxLength(1000)]
        public string? DropboxUrl { get; set; }

        [MaxLength(1000)]
        public string? ImgurUrl { get; set; }

        [Required]
        [MaxLength(20)]
        public string UploadStatus { get; set; } = "Pending"; // Pending, Success, Failed

        public DateTime? UploadedAt { get; set; }
        public long? FileSize { get; set; }
        public string? ErrorMessage { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation property
        public virtual Offer Offer { get; set; } = null!;
    }

    public class ErrorLog
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        public Guid? OfferId { get; set; }

        [Required]
        [MaxLength(50)]
        public string ErrorType { get; set; } = string.Empty; // Parsing, Upload, Publishing, Validation

        [Required]
        public string ErrorMessage { get; set; } = string.Empty;

        public string? StackTrace { get; set; }

        [MaxLength(50)]
        public string? GameName { get; set; }

        [MaxLength(1000)]
        public string? SourceUrl { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation property
        public virtual Offer? Offer { get; set; }
    }
}
