{"_comment": "<PERSON><PERSON> w<PERSON>/w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> grę, po prostu dodaj/usuń jej nazwę z ActiveGames. Wszystkie gry są dostępne w GamesData.", "ActiveGames": ["RaidShadowLegends", "WatcherOfRealms", "Albion"], "GamesData": {"RaidShadowLegends": {"id": "566", "priceRange": {"min": 15, "max": 1000}, "listAttributes": ["mhero", "lhero", "ehero", "rhero", "level"]}, "WatcherOfRealms": {"id": "1698", "priceRange": {"min": 15, "max": 1000}, "listAttributes": ["level", "heroes", "power"]}, "ApexLegends": {"id": "468", "priceRange": {"min": 15, "max": 19}, "listAttributes": ["level", "relic", "rank"]}, "Albion": {"id": "47", "priceRange": {"min": 15, "max": 99}, "listAttributes": ["level", "pve fame (m)", "weapon type"]}, "GenshinImpact": {"id": "696", "priceRange": {"min": 50, "max": 209}, "listAttributes": ["server", "event", "ar", "account type", "Email linking"]}, "EscapeFromTarkov": {"id": "275", "priceRange": {"min": 109, "max": 109}, "listAttributes": ["level", "items", "skills"]}, "Rust": {"id": "250", "priceRange": {"min": 50, "max": 399}, "listAttributes": ["level", "skins"]}, "PUBG": {"id": "215", "priceRange": {"min": 70, "max": 399}, "listAttributes": ["platform", "battlegrounds_plus"]}, "LeagueOfLegends": {"id": "85", "priceRange": {"min": 50, "max": 399}, "listAttributes": ["level", "champions", "skins", "rank"]}, "Valorant": {"id": "612", "priceRange": {"min": 35, "max": 199}, "listAttributes": ["Agents quantity", "skins", "rank"]}, "MobileLegends": {"id": "366", "priceRange": {"min": 15, "max": 100}, "listAttributes": ["level", "heroes", "skins"]}, "PUBGMobile": {"id": "346", "priceRange": {"min": 25, "max": 199}, "listAttributes": ["level", "skins", "rank"]}, "Fortnite": {"id": "248", "priceRange": {"min": 25, "max": 199}, "listAttributes": ["level", "skins"]}, "WorldOfWarcraft": {"id": "82", "priceRange": {"min": 25, "max": 199}, "listAttributes": ["faction", "level", "race", "class"]}, "Dota2": {"id": "81", "priceRange": {"min": 25, "max": 109}, "listAttributes": ["mmr", "behavior", "level", "rank"]}, "FC 25": {"id": "2786", "priceRange": {"min": 109, "max": 459}, "listAttributes": ["platform", "edition", "rating", "squad price", "coins on balance", "web app"]}}}