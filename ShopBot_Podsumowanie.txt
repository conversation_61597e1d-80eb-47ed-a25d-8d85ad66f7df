# 🤖 PODSUMOWANIE SYSTEMU SHOPBOT 🤖

## 🔍 Ogólny Przegląd 🌐

**ShopBot** to zaawansowany system automatyzujący proces synchronizacji i zarządzania ofertami między platformami **FunPay** 🟠 i **G2G** 🟢. Program skanuje, porównuje i zarządza ofertami, zapewniając aktualizację danych w czasie rzeczywistym. ⏱️

## 📊 Kluczowe Limity Systemowe ⛓️

| Parametr | Wartość | Uwagi |
|----------|---------|-------|
| **Limit ofert G2G** | 1000 🔢 | Sztywny limit platformy ⚠️ |
| **Limit czasu oczekiwania na usunięcie** | 40 sekund ⏲️ | 20 prób co 2 sekundy 🔄 |
| **Limit czasu oczekiwania na zadania usuwania** | 2 minuty ⏰ | Po tym czasie program kontynuuje pracę ➡️ |
| **Limit prób publikacji oferty** | 5 🔄 | Po przekroczeniu oferta oznaczana jako nieudana ❌ |
| **Limit równoczesnych workerów** | 20 (główny pool) 👥 | Do równoległego przetwarzania zadań 🧵 |
| **Limit workerów synchronizacji** | 3 👤 | Dla zadań synchronizacji międzyplatformowej 🔄 |
| **Limit przesyłania obrazów Imgur** | ~50 dziennie 🖼️ | API blokuje po przekroczeniu 🚫 |

## ⚙️ Główne Funkcjonalności 🛠️

1. **Synchronizacja Ofert** 🔄
   - Skanowanie ofert z FunPay: ~3500 ofert przetwarzanych w jednym cyklu 📊
   - Filtrowanie według gier (np. GenshinImpact: 337 z 3592 ofert) 🎮
   - Porównywanie z ofertami G2G 🔍
   - Czas wykonania pełnej synchronizacji: ~160 sekund ⏱️

2. **Zarządzanie Ofertami** 📝
   - Automatyczne publikowanie nowych ofert (~50-55 sekund na ofertę) ⏫
   - Usuwanie nieaktywnych ofert 🗑️
   - Śledzenie publikowanych ofert (obecnie ponad 1210 śledzonych ofert) 📈
   - Ostatni czas przetwarzania: co ~51 godzin 🕒

3. **Weryfikacja Ofert** ✅
   - Równoległe przetwarzanie gier (RaidShadowLegends: 510 ofert 🛡️, WatcherOfRealms: 238 ofert 👁️, itd.)
   - Kontrola duplikatów 🔍🔍
   - Wykrywanie i usuwanie nieaktualnych ofert 🕵️‍♂️🗑️

4. **Zarządzanie Zadaniami** 📋
   - System kolejkowania zadań 📑
   - Monitoring aktywnych workerów (średnio 1-6 aktywnych jednocześnie) 👷‍♂️
   - Zaawansowane raportowanie błędów 📊❌

## 🚀 Zaimplementowane Optymalizacje 💪

1. **Równoległe Przetwarzanie** ⚡
   - Wielowątkowe wykonywanie zadań 🧵🧵
   - Oddzielne pule dla synchronizacji i głównych zadań 🏊‍♂️

2. **Mechanizmy Zabezpieczające** 🔒
   - Flaga zapobiegająca wielokrotnej weryfikacji w jednym cyklu 🚩
   - Timeout dla zadań usuwania (zapobiega zawieszaniu) ⏱️
   - System ponawiania prób przy niepowodzeniach 🔄

3. **Buforowanie Danych** 💾
   - Przechowywanie opisów ofert w plikach JSON 📄
   - Śledzenie ostatnio przetworzonych ofert 📝

## 🛠️ Planowane Ulepszenia 📈

1. **System Obsługi Obrazów** 🖼️
   - Rotacja dostawców hostingu obrazów 🔄
   - Kolejkowanie i opóźnione przesyłanie obrazów ⏳
   - Integracja z wieloma API (Imgur 🖼️, Postimage 📷, Imgbb 🏞️)

2. **Mechanizmy Współbieżności** ⚡
   - Granularne blokady dla operacji na współdzielonych danych 🔐
   - Wzorzec producent-konsument dla przetwarzania ofert 🏭
   - Asynchroniczna kolejka zadań 📩

3. **Edycja Ofert po Publikacji** ✏️
   - System śledzenia stanu ofert 📊
   - Planowanie edycji (dodawanie obrazów 🖼️, aktualizacja opisów 📝)
   - Generowanie ulepszonych opisów ✨

4. **Wykrywanie Zmian Zawartości Ofert** 🔍
   - Detekcja znaczących zmian w ofertach o tym samym ID 🆔
   - Porównywanie cen, tytułów i opisów 💰📝
   - Automatyczne usuwanie starych wersji i publikowanie nowych 🔄
   - Algorytm Levenshteina dla porównywania podobieństwa tekstów 📊

## 📈 Wydajność Systemu ⚡

| Operacja | Czas Wykonania | Uwagi |
|----------|----------------|-------|
| Pełna synchronizacja | ~160 sekund ⏱️ | Dla wszystkich wspieranych gier 🎮 |
| Publikacja pojedynczej oferty | ~50-55 sekund ⏲️ | Włącznie z potwierdzeniem ✓ |
| Usuwanie oferty | Do 40 sekund ⌛ | Z ponawianiem prób 🔄 |
| Weryfikacja wszystkich gier | ~2-3 minuty ⏰ | Z równoległym przetwarzaniem ⚡ |
| Publikacja 50 ofert (bez blokad) | ~5 sekund ⚡ | Niskie bezpieczeństwo danych ⚠️ |
| Publikacja 50 ofert (naiwne blokady) | ~50 sekund 🐢 | Wysokie bezpieczeństwo danych 🔒 |
| Publikacja 50 ofert (zoptymalizowane) | ~7-15 sekund 🚀 | Zależnie od implementacji 🛠️ |

## � Zidentyfikowane Problemy i Rozwiązania 🛠️

### 🔄 Problem: Zmiana Zawartości Ofert o Tym Samym ID

**Opis problemu:** 📋
Na FunPay może się zdarzyć, że oferta o tym samym ID zmienia swoją zawartość (tytuł, cenę, opis). Jest to scenariusz, który wymaga dodatkowej obsługi w systemie.

**Przykład:** 💡
- Oferta ID=123: "Konto WoW 60 lvl" ($50) → "Konto Dota 60 poziom" ($15)
- System nadal identyfikuje ją jako tę samą ofertę
- Nie wykrywa znaczącej zmiany zawartości
- Może nieprawidłowo aktualizować dane na G2G

**Rozwiązanie:** ✅
```csharp
// Wykrywanie znaczących zmian w ofertach
public class OfferContentChangeDetector
{
    public bool HasSignificantChanges(Offer existingOffer, OfferDetails newDetails)
    {
        // Sprawdzenie zmiany ceny (>15% różnicy)
        var priceChange = Math.Abs(existingOffer.Price - newDetails.ActualOfferPrice) / existingOffer.Price;
        if (priceChange > 0.15m) return true;

        // Sprawdzenie podobieństwa tytułu (algorytm Levenshteina)
        var titleSimilarity = CalculateLevenshteinSimilarity(existingOffer.Title, newDetails.ShortDescription);
        if (titleSimilarity < 0.7) return true;

        return false;
    }
}
```

**Integracja z procesem synchronizacji:** 🔧
```csharp
// W ScanAndSyncOffersTask
foreach (var existingOffer in existingOffers)
{
    var newDetails = currentOffers.FirstOrDefault(o => o.Id == existingOffer.FunPayOfferId);
    if (newDetails != null && _changeDetector.HasSignificantChanges(existingOffer, newDetails))
    {
        // Traktuj jako nową ofertę - usuń starą i dodaj nową
        await _deleteOfferTask.ExecuteAsync(existingOffer.G2GOfferId);
        await _addOfferTask.ExecuteAsync(newDetails);
    }
}
```

**Analiza wpływu na wydajność:** 📊
- **Złożoność obliczeniowa:** O(N * L²) gdzie N = liczba ofert, L = długość tytułu
- **Szacunkowy czas:** ~50-100ms dla 500 ofert (po optymalizacjach)
- **Wpływ na całkowity proces:** <1% czasu synchronizacji (~161 vs 160 sekund)
- **Korzyści:** Wykrywanie nieprawidłowych ofert, lepsza jakość danych

## �🔗 Integracje 🌉

- **FunPay** 🟠 - Źródło danych o ofertach 📊
- **G2G** 🟢 - Platforma docelowa dla publikacji 🚀
- **Imgur** 🖼️ (i planowane inne) - Hosting obrazów 🏞️

---

✨ ShopBot zapewnia kompleksową automatyzację zarządzania ofertami, obsługując pełen cykl życia oferty - od skanowania 🔍, przez publikację 📤, weryfikację ✅, aż po usunięcie 🗑️. System skutecznie radzi sobie z limitami zewnętrznych platform, optymalizując procesy w celu maksymalizacji efektywności przy zachowaniu wysokiej jakości danych. 💯 