using System;
using System.Collections.Generic;
using System.Text.Json;
using System.IO;
using System.Linq;

namespace ShopBot.Services
{
    public class RejectedOffer
    {
        public string Id { get; set; }
        public string GameName { get; set; }
        public string Description { get; set; }
        public decimal Price { get; set; }
        public int Rating { get; set; }
        public int Reviews { get; set; }
        public string RejectionReason { get; set; }
        public Dictionary<string, string> Attributes { get; set; }
        public DateTime RejectionTime { get; set; }
    }

    public class FilteringStatistics
    {
        public int TotalOffers { get; set; }
        public int AcceptedOffers { get; set; }
        public int RejectedOffers { get; set; }
        public Dictionary<string, int> RejectionReasons { get; set; } = new();
        public DateTime GenerationTime { get; set; } = DateTime.Now;
    }

    public class FilteringReport
    {
        private const string REPORTS_DIR = "reports";
        private const string ACCEPTED_DIR = "accepted";
        private const string REJECTED_DIR = "rejected";
        private const string STATS_DIR = "statistics";

        public FilteringStatistics Statistics { get; private set; }
        public List<OfferListItem> AcceptedOffers { get; private set; }
        public List<RejectedOffer> RejectedOffers { get; private set; }
        private readonly string _gameName;

        public FilteringReport(string gameName)
        {
            _gameName = gameName;
            Statistics = new FilteringStatistics();
            AcceptedOffers = new List<OfferListItem>();
            RejectedOffers = new List<RejectedOffer>();
            EnsureDirectoriesExist();
        }

        private void EnsureDirectoriesExist()
        {
            var dirs = new[]
            {
                REPORTS_DIR,
                Path.Combine(REPORTS_DIR, ACCEPTED_DIR),
                Path.Combine(REPORTS_DIR, REJECTED_DIR),
                Path.Combine(REPORTS_DIR, STATS_DIR)
            };

            foreach (var dir in dirs)
            {
                Directory.CreateDirectory(dir);
            }
        }

        public void AddAcceptedOffer(OfferListItem offer)
        {
            AcceptedOffers.Add(offer);
            Statistics.AcceptedOffers++;
            Statistics.TotalOffers++;
        }

        public void AddRejectedOffer(OfferListItem offer, string reason)
        {
            var rejectedOffer = new RejectedOffer
            {
                Id = offer.Id,
                GameName = _gameName,
                Description = offer.Description,
                Price = offer.Price,
                Rating = offer.SellerRating,
                Reviews = offer.ReviewCount,
                RejectionReason = reason,
                Attributes = offer.Attributes,
                RejectionTime = DateTime.Now
            };

            RejectedOffers.Add(rejectedOffer);
            Statistics.RejectedOffers++;
            Statistics.TotalOffers++;

            if (!Statistics.RejectionReasons.ContainsKey(reason))
                Statistics.RejectionReasons[reason] = 0;
            Statistics.RejectionReasons[reason]++;
        }

        public void SaveReport()
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
            var options = new JsonSerializerOptions { WriteIndented = true };

            // Zapisz zaakceptowane oferty
            if (AcceptedOffers.Any())
            {
                var acceptedPath = Path.Combine(REPORTS_DIR, ACCEPTED_DIR, $"{_gameName}_{timestamp}.json");
                File.WriteAllText(acceptedPath, JsonSerializer.Serialize(AcceptedOffers, options));
            }

            // Zapisz odrzucone oferty
            if (RejectedOffers.Any())
            {
                var rejectedPath = Path.Combine(REPORTS_DIR, REJECTED_DIR, $"{_gameName}_{timestamp}.json");
                File.WriteAllText(rejectedPath, JsonSerializer.Serialize(RejectedOffers, options));
            }

            // Zapisz statystyki
            var statsPath = Path.Combine(REPORTS_DIR, STATS_DIR, $"{_gameName}_{timestamp}.json");
            File.WriteAllText(statsPath, JsonSerializer.Serialize(Statistics, options));

            // Generuj raport tekstowy
            var reportPath = Path.Combine(REPORTS_DIR, $"{_gameName}_{timestamp}_report.txt");
            using (var writer = new StreamWriter(reportPath))
            {
                writer.WriteLine($"Raport filtrowania dla gry {_gameName}");
                writer.WriteLine($"Data wygenerowania: {DateTime.Now}");
                writer.WriteLine("\nStatystyki:");
                writer.WriteLine($"- Calkowita liczba ofert: {Statistics.TotalOffers}");
                writer.WriteLine($"- Zaakceptowane oferty: {Statistics.AcceptedOffers}");
                writer.WriteLine($"- Odrzucone oferty: {Statistics.RejectedOffers}");
                
                writer.WriteLine("\nPowody odrzucenia:");
                foreach (var reason in Statistics.RejectionReasons.OrderByDescending(x => x.Value))
                {
                    writer.WriteLine($"- {reason.Key}: {reason.Value} ofert");
                }

                if (AcceptedOffers.Any())
                {
                    writer.WriteLine("\nZaakceptowane oferty:");
                    foreach (var offer in AcceptedOffers)
                    {
                        writer.WriteLine($"- ID: {offer.Id}, Cena: {offer.Price}$, Ocena: {offer.SellerRating}, Recenzje: {offer.ReviewCount}");
                    }
                }
            }
        }
    }
} 