using Microsoft.Playwright;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using ShopBot.Config;
using System.Globalization;
using System.Text.Json.Serialization;
using System.Text.Json;
using ShopBot.Services;
using ShopBot.Exceptions;
using ShopBot.Services.Formatters;
using ShopBot.Services.Extensions;

namespace ShopBot.Services
{
    public class G2GPublisherService : IG2GPublisherService
    {
        // Mapowanie nazw gier z konfiguracji na nazwy G2G
        private readonly Dictionary<string, string> _g2gGameNames = new()
        {
            // ✅ AKTYWNE GRY (włączone w ActiveGames)
            {"RaidShadowLegends", "Raid: Shadow Legend"},
            {"WatcherOfRealms", "Watcher of Realms"},

            // 💤 NIEAKTYWNE GRY (wyłączone w ActiveGames - można łatwo włączyć)
            {"Fortnite", "FN"},
            {"EscapeFromTarkov", "Escape from Tarkov"},
            {"LeagueOfLegends", "League of Legends"},
            {"PUBG", "PlayerUnknown's Battlegrounds"},
            {"PUBGMobile", "PUBG Mobile"},
            {"Valorant", "Valorant"},
            {"Albion", "Albion Online"},
            {"Rust", "Rust"},
            {"MobileLegends", "Mobile Legends: Bang Bang"},
            {"Dota2", "Dota 2"},
            {"GenshinImpact", "Genshin Impact"},
            {"CallOfDutyWarzone", "Call of Duty"},
            {"ApexLegends", "Apex Legends"},
            {"FC 25", "FC 25"},
            {"WorldOfWarcraft", "World of Warcraft"}
        };

        private readonly IBrowser _browser;
        private readonly IBrowserContext _context;
        private readonly ImageUploadService _imageUploadService;
        private readonly DescriptionFormatterFactory _formatterFactory;
        private readonly ILogger<G2GPublisherService> _logger;

        public G2GPublisherService(IBrowser browser, IBrowserContext context, ImageUploadService imageUploadService, DescriptionFormatterFactory formatterFactory, ILogger<G2GPublisherService>? logger = null)
        {
            _browser = browser;
            _context = context;
            _imageUploadService = imageUploadService;
            _formatterFactory = formatterFactory;
            _logger = logger ?? Microsoft.Extensions.Logging.Abstractions.NullLogger<G2GPublisherService>.Instance;
        }

        public async Task<string> PublishOffer(OfferDetails offer, string gameName, IPage page)
        {
            try
            {
                await page.GotoAsync("https://www.g2g.com/offers/sell");

                // Mapowanie nazwy gry na format G2G
                var g2gGameName = _g2gGameNames.GetValueOrDefault(gameName, gameName);

                // Logika dla konkretnych gier
                switch (gameName)
                {
                    case "RaidShadowLegends":
                        await ProcessRaidShadowLegends(offer, page);
                        break;
                    case "WatcherOfRealms":
                        // WatcherOfRealms używa standardowego procesu
                        await ProcessGame(offer, g2gGameName, page);
                        break;
                    case "LeagueOfLegends":
                        await ProcessLeagueOfLegends(offer, page);
                        break;
                    case "EscapeFromTarkov":
                        await ProcessEscapeFromTarkov(offer, page);
                        break;
                    case "Valorant":
                        await ProcessValorant(offer, page);
                        break;
                    case "PUBGMobile":
                        await ProcessPUBGMobile(offer, page);
                        break;
                    case "Albion":
                        await ProcessAlbion(offer, page);
                        break;
                    case "ApexLegends":
                        await ProcessApexLegends(offer, page);
                        break;
                    case "PUBG":
                        await ProcessPUBG(offer, page);
                        break;
                    case "Rust":
                        await ProcessRust(offer, page);
                        break;
                    case "GenshinImpact":
                        await ProcessGenshinImpact(offer, page);
                        break;
                    case "CallOfDutyWarzone":
                        await ProcessCallOfDutyWarzone(offer, page);
                        break;
                    case "MobileLegends":
                        await ProcessMobileLegends(offer, page);
                        break;
                    case "Dota2":
                        await ProcessDota2(offer, page);
                        break;
                    case "Fortnite":
                        await ProcessFortnite(offer, page);
                        break;
                    case "FC 25":
                        await ProcessFC25(offer, page);
                        break;
                    case "WorldOfWarcraft":
                        await ProcessWorldOfWarcraft(offer, page);
                        break;

                    default:
                        await ProcessGame(offer, g2gGameName, page);
                        break;
                }

                // Wypełnianie tytułu i opisu
                _logger.LogInformation("🚀 Rozpoczynam wypełnianie tytułu i opisu dla gry: {GameName}", gameName);
                await FillTitleAndDescription(offer, gameName, page);
                _logger.LogInformation("✅ Zakończono wypełnianie tytułu i opisu");

                // Upload i dodanie obrazów jeśli są dostępne
                if (offer.Images != null && offer.Images.Count > 0)
                {
                    _logger.LogInformation("📸 Rozpoczynam upload {ImageCount} obrazów", offer.Images.Count);
                    await UploadAndAddImages(offer.Images, page);
                    _logger.LogInformation("✅ Zakończono upload obrazów");
                }

                // Ustawienie ceny i ilości
                await SetPrice(page, offer.ActualOfferPrice, gameName);
                await SetQuantity(page);

                // Publikacja oferty
                _logger.LogInformation("🚀 Publikuję ofertę na G2G");
                var publishedOfferId = await WaitForOfferIdResponse(page);

                if (!string.IsNullOrEmpty(publishedOfferId))
                {
                    _logger.LogInformation("✅ Oferta opublikowana pomyślnie (ID: {OfferId})", publishedOfferId);
                }
                
                return publishedOfferId;
            }
            catch (Exception ex)
            {
                if (ex is OfferLimitExceededException)
                {
                    throw;
                }
                
                _logger.LogError(ex, "❌ Błąd podczas publikacji oferty");

                var screenshotPath = $"error_{offer.Id}.png";
                await page.ScreenshotAsync(new PageScreenshotOptions { Path = screenshotPath });

                var htmlContent = await page.ContentAsync();
                if (htmlContent.Contains("You have exceeded the maximum number"))
                {
                    throw new OfferLimitExceededException("Exceeded the limit of 1000 offers on G2G");
                }
                
                throw;
            }
        }

        private class ApiResponse
        {
            [JsonPropertyName("code")]
            public int Code { get; set; }

            [JsonPropertyName("messages")]
            public List<ApiMessage> Messages { get; set; }

            [JsonPropertyName("payload")]
            public OfferPayload Payload { get; set; }
        }

        private class ApiMessage
        {
            [JsonPropertyName("code")]
            public int Code { get; set; }

            [JsonPropertyName("type")]
            public string Type { get; set; }

            [JsonPropertyName("text")]
            public string Text { get; set; }
        }

        private class OfferPayload
        {
            [JsonPropertyName("offer_id")]
            public string OfferId { get; set; }

            [JsonPropertyName("title")]
            public string Title { get; set; }

            [JsonPropertyName("status")]
            public string Status { get; set; }

            [JsonPropertyName("currency")]
            public string Currency { get; set; }

            [JsonPropertyName("unit_price")]
            public decimal UnitPrice { get; set; }
        }

        public class OfferLimitExceededException : Exception
        {
            public OfferLimitExceededException(string message) : base(message) { }
        }

        private async Task<string> WaitForOfferIdResponse(IPage page)
        {
            var appSettings = AppSettings.Instance;
            var maxAttempts = appSettings.Retry.MaxRetryAttempts;
            var baseDelayMs = appSettings.Retry.RetryDelaySeconds * 1000;

            for (int attempt = 1; attempt <= maxAttempts; attempt++)
            {
                try
                {
                    _logger.LogDebug("🔄 Próba publikacji {Attempt}/{MaxAttempts}", attempt, maxAttempts);

                    var publishButton = await page.QuerySelectorAsync("button.q-btn span:has-text('Publish')");
                    if (publishButton == null)
                    {
                        _logger.LogWarning("⚠️ Nie znaleziono przycisku Publish");
                        throw new Exception("Nie znaleziono przycisku Publish");
                    }

                    // Nasłuchujemy odpowiedzi API
                    var publishTimeout = appSettings.Timeouts.PublishOfferTimeoutSeconds * 1000;
                    var responseTask = page.WaitForResponseAsync(response =>
                        response.Url.Contains("sls.g2g.com/offer") &&
                        response.Request.Method == "POST",
                        new() { Timeout = publishTimeout });

                    _logger.LogDebug("🖱️ Klikam przycisk Publish");
                    await publishButton.ClickAsync(new() { Timeout = publishTimeout });

                    var response = await responseTask;
                    var responseText = await response.TextAsync();
                    _logger.LogDebug("📡 Odpowiedź API (Status: {Status}): {Response}", response.Status, responseText);

                    try
                    {
                        var apiResponse = JsonSerializer.Deserialize<ApiResponse>(responseText);
                        
                        // Sprawdzamy czy przekroczono limit ofert
                        if (apiResponse?.Code == 4002)
                        {
                            var limitExceededMessage = apiResponse.Messages?.FirstOrDefault(m => m.Code == 11034);
                            if (limitExceededMessage != null)
                            {
                                _logger.LogWarning("⚠️ Wykryto przekroczenie limitu 1000 ofert!");
                                throw new OfferLimitExceededException("Exceeded the limit of 1000 offers on G2G");
                            }
                        }

                        if (responseText.Contains("too many attempts") || responseText.Contains("Too Many Requests"))
                        {
                            var delayMs = baseDelayMs * attempt;
                            _logger.LogWarning("⏳ Otrzymano błąd 'too many attempts'. Czekam {DelaySeconds} sekund przed ponowną próbą ({Attempt}/{MaxAttempts})",
                                             delayMs/1000, attempt, maxAttempts);
                            await Task.Delay(delayMs);
                            continue;
                        }

                        if (apiResponse?.Code == 2000 && apiResponse?.Payload?.OfferId != null)
                        {
                            _logger.LogInformation("🎉 Sukces! Otrzymano ID oferty: {OfferId}", apiResponse.Payload.OfferId);
                            return apiResponse.Payload.OfferId;
                        }
                        else
                        {
                            var messages = string.Join(", ", apiResponse?.Messages?.Select(m => m.Text) ?? new List<string>());
                            _logger.LogError("❌ Błąd API - Code: {Code}, Messages: {Messages}", apiResponse?.Code, messages);
                        }
                    }
                    catch (JsonException ex)
                    {
                        _logger.LogError(ex, "🔧 Błąd deserializacji odpowiedzi JSON");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "❌ Błąd podczas próby {Attempt}", attempt);
                    if (attempt < maxAttempts)
                    {
                        var delayMs = baseDelayMs * attempt;
                        _logger.LogInformation("⏳ Czekam {DelaySeconds} sekund przed kolejną próbą", delayMs/1000);
                        await Task.Delay(delayMs);
                        continue;
                    }
                    throw;
                }
            }
            
            throw new Exception($"Nie udało się opublikować oferty po {maxAttempts} próbach");
        }

        private async Task ProcessGame(OfferDetails offerDetails, string gameName, IPage page)
        {
            var appSettings = AppSettings.Instance;
            var timeout = appSettings.Timeouts.GameSelectionTimeoutSeconds * 1000;

            await page.ClickAsync("div.text-center:has-text('Accounts')",
                new PageClickOptions { Timeout = timeout });
            await page.WaitForSelectorAsync("button.g-btn-select:has-text('Select brand')",
                new PageWaitForSelectorOptions { Timeout = timeout });
            await page.ClickAsync("button.g-btn-select:has-text('Select brand')",
                new PageClickOptions { Timeout = timeout });
            await page.FillAsync("input[type='text']", gameName,
                new PageFillOptions { Timeout = timeout });

            if (gameName == "Call of Duty")
            {
                await page.ClickAsync($"div.q-item:has-text('{gameName}'):not(:has-text('Mobile'))",
                    new PageClickOptions { Timeout = timeout });
            }
            else if (gameName == "Apex Legends")
            {
                await page.ClickAsync($"div.q-item:has-text('{gameName}'):not(:has-text('Mobile'))",
                    new PageClickOptions { Timeout = timeout });
            }
            else
            {
                await page.ClickAsync($"div.q-item:has-text('{gameName}')",
                    new PageClickOptions { Timeout = timeout });
            }

            await page.ClickAsync("a.g-btn-min:has-text('Continue')",
                new PageClickOptions { Timeout = timeout });
            await Task.Delay(appSettings.Timeouts.GameProcessDelayMs);

            // Wypełnianie tytułu i opisu
            await FillTitleAndDescription(offerDetails, gameName, page);
            await Task.Delay(appSettings.Timeouts.FormFillDelayMs);

            await page.ClickAsync("div[role='radio']:has-text('Manual delivery')",
                new PageClickOptions { Timeout = timeout });
            await Task.Delay(appSettings.Timeouts.DropdownSelectionDelayMs);

            await page.ClickAsync("button.g-btn-select:has-text('0 hour')",
                new PageClickOptions { Timeout = timeout });
            await Task.Delay(appSettings.Timeouts.DropdownSelectionDelayMs);

            await page.ClickAsync("div.q-item:has-text('9 hours')",
                new PageClickOptions { Timeout = timeout });
            await Task.Delay(appSettings.Timeouts.DropdownSelectionDelayMs);

            await SetPrice(page, offerDetails.ActualOfferPrice, gameName);
            await SetQuantity(page);
        }

        private async Task SetPrice(IPage page, object priceObj, string gameName)
        {
            decimal price;

            if (priceObj is decimal decimalPrice)
            {
                price = decimalPrice;
            }
            else if (priceObj is string priceString)
            {
                // Obsługa różnych walut (USD, USDT, EUR)
                if (priceString.Contains("USDT"))
                {
                    // Parsowanie USDT
                    var usdtMatch = System.Text.RegularExpressions.Regex.Match(priceString, @"([\d.,]+)\s*USDT");
                    if (usdtMatch.Success && decimal.TryParse(usdtMatch.Groups[1].Value, NumberStyles.Any, CultureInfo.InvariantCulture, out price))
                    {
                        _logger.LogInformation("[G2G Publisher] 💰 Parsed USDT price: {Price} USDT → {USDPrice} USD", usdtMatch.Groups[1].Value, price);
                    }
                    else
                    {
                        price = 0.0M;
                        _logger.LogPriceParsingError(priceString);
                    }
                }
                else
                {
                    // Parsowanie USD/EUR/innych walut
                    var cleanString = System.Text.RegularExpressions.Regex.Replace(priceString, @"[^\d.,]", "");
                    if (!decimal.TryParse(cleanString, NumberStyles.Any, CultureInfo.InvariantCulture, out price))
                    {
                        price = 0.0M;
                        _logger.LogPriceParsingError(priceString);
                    }
                    else
                    {
                        _logger.LogInformation("[G2G Publisher] 💰 Parsed price: {OriginalPrice} → {CleanPrice} USD", priceString, price);
                    }
                }
            }
            else
            {
                price = 0.0M;
                _logger.LogInvalidPriceType(priceObj?.GetType().Name ?? "null");
            }

            // Użyj nowej metody z limitami per gra i 25% marżą
            decimal higherPrice = PriceCalculator.CalculatePriceWithGameLimits(price, gameName);
            var selector = "div[data-v-7b6b2732] input.q-field__native";

            _logger.LogInformation("💰 Cena oryginalna: ${OriginalPrice:F2} → Cena G2G: ${G2GPrice:F2} (gra: {GameName})",
                                 price, higherPrice, gameName);

            var appSettings = AppSettings.Instance;
            await page.FillAsync(selector, higherPrice.ToString("F2", CultureInfo.InvariantCulture));
            await Task.Delay(appSettings.Timeouts.PriceSetDelayMs);
            await page.FillAsync(selector, higherPrice.ToString("F2", CultureInfo.InvariantCulture));
        }

        private static async Task SetQuantity(IPage page)
        {
            var appSettings = AppSettings.Instance;
            var keyboardDelay = appSettings.Timeouts.KeyboardDelayMs;

            for (int i = 0; i < 5; i++)
            {
                await page.Keyboard.PressAsync("Tab");
                await Task.Delay(keyboardDelay);
            }
            await page.Keyboard.TypeAsync("1");

            for (int i = 0; i < 2; i++)
            {
                await page.Keyboard.PressAsync("Tab");
                await Task.Delay(keyboardDelay);
            }
            await page.Keyboard.TypeAsync("1");
        }

        /// <summary>
        /// Wypełnia tytuł i opis oferty - ujednolicona logika dla wszystkich gier
        /// </summary>
        private async Task FillTitleAndDescription(OfferDetails offer, string gameName, IPage page)
        {
            // Wypełnianie tytułu
            string title = _formatterFactory.GetFormatter(gameName).CleanDescription(offer.ShortDescription);
            await page.FillAsync("input[placeholder='Offer title']", title);

            // Wypełnianie opisu
            string description;
            if (gameName == "Mobile Legends: Bang Bang")
            {
                var formatter = _formatterFactory.GetFormatter("MobileLegends");
                description = formatter.FormatDescription(offer);
            }
            else if (gameName == "FC 25")
            {
                var parameters = new Dictionary<string, string> { { "Id", offer.Id } };
                var formatter = _formatterFactory.GetFormatter("FC 25");
                description = formatter.FormatDescription(offer, parameters);
            }
            else
            {
                var parameters = new Dictionary<string, string> { { "Id", offer.Id } };
                var formatter = _formatterFactory.GetFormatter(gameName);
                description = formatter.FormatDescription(offer, parameters);
            }

            var preview = description.Substring(0, Math.Min(100, description.Length));
            _logger.LogDebug("📝 Wypełniam opis ({Length} znaków): {Preview}...", description.Length, preview);
            await page.FillAsync("textarea[placeholder='Type offer description here']", description);
        }
        
        private async Task ClickSelectButton(string label, IPage page)
        {
            try
            {
                // Najpierw próbuj standardowy selektor
                var standardSelector = $"div.col-12:has(div.text-secondary:has-text('{label}')) button.g-btn-select:has-text('Please select')";
                await page.ClickAsync(standardSelector, new PageClickOptions { Timeout = 5000 });
            }
            catch
            {
                // Fallback dla Mobile Legends i innych gier z innym selektorem
                _logger.LogDropdownFallback(label);
                var fallbackSelector = $"div.col-12:has(div.text-font-2nd:has-text('{label}')) button.g-btn-select:has-text('Please select')";
                await page.ClickAsync(fallbackSelector);
            }
        }

        // Uniwersalna metoda do wybierania opcji z listy rozwijanej
        private async Task SelectOptionFromDropdown(string label, string optionValue, IPage page, string logMessage = null, string customSelector = null)
        {
            await ClickSelectButton(label, page);
            await Task.Delay(200);

            string selector;
            if (customSelector != null)
            {
                selector = customSelector;
            }
            else
            {
                selector = GameSpecificHelper.GetPreciseSelector(optionValue);
            }

            _logger.LogDropdownSelection(label, optionValue);
            _logger.LogSelectorUsage(selector);

            await page.ClickAsync(selector);
            await Task.Delay(200);
        }
        
        private async Task ProcessRaidShadowLegends(OfferDetails offerDetails, IPage page)
        {
            _logger.LogGameProcessingStart("RaidShadowLegends");
            var appSettings = AppSettings.Instance;
            var timeout = appSettings.Timeouts.GameSelectionTimeoutSeconds * 1000;

            await page.ClickAsync("div.text-center:has-text('Accounts')",
                new PageClickOptions { Timeout = timeout });
            await page.WaitForSelectorAsync("button.g-btn-select:has-text('Select brand')",
                new PageWaitForSelectorOptions { Timeout = timeout });
            await page.ClickAsync("button.g-btn-select:has-text('Select brand')",
                new PageClickOptions { Timeout = timeout });
            await page.FillAsync("input[type='text']", "Raid: Shadow Legend",
                new PageFillOptions { Timeout = timeout });

            await page.ClickAsync($"div.q-item:has-text('Raid: Shadow Legend')",
                new PageClickOptions { Timeout = timeout });

            await page.ClickAsync("a.g-btn-min:has-text('Continue')",
                new PageClickOptions { Timeout = timeout });
            await Task.Delay(appSettings.Timeouts.GameProcessDelayMs);

            // Nie wywołujemy FillTitleAndDescription tutaj - zostanie wywołane w głównej metodzie

            await page.ClickAsync("div[role='radio']:has-text('Manual delivery')",
                new PageClickOptions { Timeout = timeout });
            await Task.Delay(appSettings.Timeouts.DropdownSelectionDelayMs);

            await page.ClickAsync("button.g-btn-select:has-text('0 hour')",
                new PageClickOptions { Timeout = timeout });
            await Task.Delay(appSettings.Timeouts.DropdownSelectionDelayMs);

            await page.ClickAsync("div.q-item:has-text('9 hours')",
                new PageClickOptions { Timeout = timeout });
            await Task.Delay(appSettings.Timeouts.DropdownSelectionDelayMs);

            // Specjalne ustawienia dla Raid Shadow Legends
            await page.ClickAsync("//*[@id=\"q-app\"]/div/div[1]/main/div[3]/form/div/div[1]/div[1]/div/div/div[2]/div[2]/div/div/div[2]/div[1]/div/span/label/div/div/div/div/div/button");
            await page.ClickAsync("div.q-item__section:has-text('Android')");

            await SetPrice(page, offerDetails.ActualOfferPrice, "RaidShadowLegends");
            await SetQuantity(page);
            _logger.LogGameProcessingEnd("RaidShadowLegends");
        }

        private async Task ProcessLeagueOfLegends(OfferDetails offerDetails, IPage page)
        {
            await ProcessGame(offerDetails, "League of Legends", page);
            _logger.LogGameAccountDetails("League of Legends", new {
                Server = offerDetails.Server,
                Level = offerDetails.Level,
                Rank = offerDetails.Rank,
                Champions = offerDetails.Champions,
                Skins = offerDetails.Skins
            });

            // Wybór serwera
            await SelectOptionFromDropdown("Server", GameSpecificHelper.MapLoLServer(offerDetails.Server), page);

            // Account Type
            await SelectOptionFromDropdown("Account Type", "Ranked Accounts", page);

            // Rank
            await SelectOptionFromDropdown("Rank", offerDetails.Rank, page);

            // Champions
            await SelectOptionFromDropdown("Champions", GameSpecificHelper.DetermineLoLChampionsRange(offerDetails.Champions), page);

            // Skins
            await SelectOptionFromDropdown("Skins", GameSpecificHelper.DetermineLoLSkinsRange(offerDetails.Skins), page);

            _logger.LogGameProcessingFinished("League of Legends");
        }

        private async Task ProcessValorant(OfferDetails offerDetails, IPage page)
        {
            await ProcessGame(offerDetails, "Valorant", page);
            _logger.LogGameAccountDetails("Valorant", new {
                AgentsQuantity = offerDetails.AgentsQuantity,
                SkinsQuantity = offerDetails.SkinsQuantity,
                Rank = offerDetails.Rank
            });

            // Server
            await SelectOptionFromDropdown("Server", offerDetails.Server == "Any" ? "Eu" : offerDetails.Server, page);

            // Account Type
            await SelectOptionFromDropdown("Account Type", "Ranked Accounts", page);

            // Rank
            await SelectOptionFromDropdown("Rank", offerDetails.Rank?.Split(' ').FirstOrDefault() ?? "Unranked", page);

            // Agents
            await SelectOptionFromDropdown("Agents", GameSpecificHelper.DetermineAgentsRange(offerDetails.AgentsQuantity), page);

            // Skins
            await SelectOptionFromDropdown("Skins", GameSpecificHelper.DetermineSkinsRange(offerDetails.SkinsQuantity), page);

            _logger.LogGameProcessingFinished("Valorant");
        }

        private async Task ProcessPUBGMobile(OfferDetails offerDetails, IPage page)
        {
            await ProcessGame(offerDetails, "PUBG Mobile", page);

            await SelectOptionFromDropdown("Rank", offerDetails.Rank?.Split(' ').FirstOrDefault() ?? "UnRanked", page);

            await SelectOptionFromDropdown("Mythic Skins", "10+", page);

            await SelectOptionFromDropdown("Lab Weapons", "10+", page);
        }

        private async Task ProcessAlbion(OfferDetails offerDetails, IPage page)
        {
            await ProcessGame(offerDetails, "Albion", page);

            await SelectOptionFromDropdown("Server", GameSpecificHelper.MapAlbionServer(offerDetails.Server), page);
        }

        private async Task ProcessDota2(OfferDetails offerDetails, IPage page)
        {
            await ProcessGame(offerDetails, "Dota 2", page);

            await SelectOptionFromDropdown("Rank", offerDetails.AccountType == "Without access to ranked matches" ? "UnRanked" : offerDetails.AccountType == "TBD" ? "Rank Ready" : offerDetails.Rank, page);

            await SelectOptionFromDropdown("MMR", GameSpecificHelper.GetMMRRange(offerDetails.MMR), page);

            await SelectOptionFromDropdown("Behavior Score", GameSpecificHelper.GetBehaviorScoreRange(offerDetails.BehaviorScore), page);
        }

        private async Task ProcessFortnite(OfferDetails offerDetails, IPage page)
        {
            await ProcessGame(offerDetails, "FN", page);

            await SelectOptionFromDropdown("Rank", "Rank Ready", page);

            await SelectOptionFromDropdown("Outfits", "10+", page);

            await SelectOptionFromDropdown("Pickaxes", "10+", page);
        }

        private async Task ProcessApexLegends(OfferDetails offerDetails, IPage page)
        {
            await ProcessGame(offerDetails, "Apex Legends", page);
            _logger.LogGameAccountDetails("Apex", new { Rank = offerDetails.Rank });

            await SelectOptionFromDropdown("Rank", GameSpecificHelper.MapApexRank(offerDetails.Rank), page);

            // Level
            await SelectOptionFromDropdown("Level", GameSpecificHelper.MapApexLevel(offerDetails.Level), page);

            // Legendary Skins
            await SelectOptionFromDropdown("Legendary Skins", "9 or below", page);

            // Mythic Skins
            await SelectOptionFromDropdown("Mythic Skins", "9 or below", page);

            _logger.LogGameProcessingFinished("Apex");
        }

        private async Task ProcessPUBG(OfferDetails offerDetails, IPage page)
        {
            _logger.LogGameAccountDetails("PUBG", new { });
            await ProcessGame(offerDetails, "PlayerUnknown", page);
            // BATTLEGROUNDS Plus - zawsze będzie "Yes" lub "No"
            await SelectOptionFromDropdown("Plus Status", offerDetails.BattlegroundsPlus.ToString(), page);
            await Task.Delay(500);

            await SelectOptionFromDropdown("Rank", "Rank Ready", page);
            await SelectOptionFromDropdown("Epic+ Skins", "10+", page);
        }

        private async Task ProcessRust(OfferDetails offerDetails, IPage page)
        {
            await ProcessGame(offerDetails, "Rust", page);

            await SelectOptionFromDropdown("Platform", "PC", page);

            // Hours
            await SelectOptionFromDropdown("Hours", GameSpecificHelper.DetermineHoursOption(offerDetails.HoursPlayed), page);

            // Skins
            await SelectOptionFromDropdown("Skins", GameSpecificHelper.DetermineSkinsOption(offerDetails.Skins), page);
        }

        private async Task ProcessGenshinImpact(OfferDetails offerDetails, IPage page)
        {
            await ProcessGame(offerDetails, "Genshin Impact", page);
            _logger.LogGameProcessingStart("Genshin Impact");

            // Server
            await SelectOptionFromDropdown("Server", offerDetails.Server, page);

            // AR - Adventure Rank
            await SelectOptionFromDropdown("AR", GameSpecificHelper.DetermineARRange(offerDetails.AR), page);

            // Characters
            await SelectOptionFromDropdown("Characters", GameSpecificHelper.DetermineGenshinCharactersRange(offerDetails.Heroes), page);

            // Weapons
            await SelectOptionFromDropdown("Weapons", GameSpecificHelper.DetermineGenshinWeaponsRange(offerDetails.WeaponType), page);

            _logger.LogGameProcessingFinished("Genshin Impact");
        }

        private async Task ProcessCallOfDutyWarzone(OfferDetails offerDetails, IPage page)
        {
            await ProcessGame(offerDetails, "Call of Duty Warzone", page);

            // Call of Duty Black Ops czy Modern Warfare - na razie wybieramy domyślnie Warzone 2.0
            await SelectOptionFromDropdown("Choose Game", "Warzone 2", page);

            // Wybieramy typ gry
            await SelectOptionFromDropdown("Choose Mode", "Warzone", page);

            // Platform
            string platform = GameSpecificHelper.MapCoDPlatform(offerDetails.Platform);
            await SelectOptionFromDropdown("Platform", platform, page);

            // Rank
            await SelectOptionFromDropdown("Rank", "Rank Ready", page);

            // Wybieramy domyślnie 5+ skinów
            await SelectOptionFromDropdown("Legendary Weapons", "5+", page);
        }

        private async Task ProcessMobileLegends(OfferDetails offerDetails, IPage page)
        {
            await ProcessGame(offerDetails, "Mobile Legends: Bang Bang", page);

            _logger.LogPlatformSelectionStart("Mobile Legends");

            try
            {
                // Specjalny selektor dla Mobile Legends Platform
                await SelectOptionFromDropdown("Platform", "Android", page);
                _logger.LogPlatformSelectionSuccess("Android");
            }
            catch (Exception ex)
            {
                _logger.LogPlatformSelectionError(ex.Message);

                // Fallback - spróbuj bezpośredniego selektora
                try
                {
                    _logger.LogPlatformFallbackStart();
                    var directSelector = "button.g-btn-select:has-text('Please select')";
                    await page.ClickAsync(directSelector);
                    await Task.Delay(1000);
                    await page.ClickAsync("div.q-item__label:has-text('Android')");
                    await Task.Delay(1000);
                    _logger.LogPlatformFallbackSuccess();
                }
                catch (Exception fallbackEx)
                {
                    _logger.LogPlatformFallbackError(fallbackEx.Message);
                    throw;
                }
            }
        }

        private async Task ProcessEscapeFromTarkov(OfferDetails offerDetails, IPage page)
        {
            await ProcessGame(offerDetails, "Escape from Tarkov", page);

            // Level
            string level = GameSpecificHelper.DetermineTarkovLevel(offerDetails.Level);
            await SelectOptionFromDropdown("Level", level, page);

            // Edition
            string edition = GameSpecificHelper.DetermineGameEdition(offerDetails.DetailedDescription);
            await SelectOptionFromDropdown("Edition", edition, page);
        }

        private async Task ProcessWorldOfWarcraft(OfferDetails offerDetails, IPage page)
        {
            await ProcessGame(offerDetails, "World of Warcraft", page);

            // Faction
            string faction = offerDetails.Faction ?? "Alliance";
            await SelectOptionFromDropdown("Faction", faction, page);

            // Level
            string levelRange = GameSpecificHelper.DetermineWoWLevel(offerDetails.Level);
            await SelectOptionFromDropdown("Level", levelRange, page);

            // Race
            string race = GameSpecificHelper.MapWoWRace(offerDetails.Race);
            await SelectOptionFromDropdown("Race", race, page);

            // Class
            string characterClass = GameSpecificHelper.MapWoWClass(offerDetails.Class);
            await SelectOptionFromDropdown("Class", characterClass, page);
        }

        private async Task ProcessFC25(OfferDetails offerDetails, IPage page)
        {
            await ProcessGame(offerDetails, "FC 25", page);

            // Platform
            string g2gPlatform = GameSpecificHelper.MapFC25Platform(offerDetails.Platform);
            await SelectOptionFromDropdown("Platform", g2gPlatform, page);
        }
        
        private async Task ClickRangeOption(string optionText, IPage page, bool isExactMatch = false)
        {
            if (isExactMatch)
            {
                // Dla liczb jak "30+" używamy selektora, który upewnia się że przed liczbą nie ma innej cyfry
                var baseNumber = optionText.TrimEnd('+');
                await page.ClickAsync($"div.q-item:has-text('{optionText}'):not(:has-text('1{baseNumber}+')):not(:has-text('2{baseNumber}+')):not(:has-text('3{baseNumber}+'))");
            }
            else
            {
                // Dla zwykłych opcji używamy standardowego selektora
                await page.ClickAsync($"div.q-item:has-text('{optionText}')");
            }
            await Task.Delay(500);
        }

        private async Task ClickButton(string buttonText, IPage page, string logMessage = null)
        {
            _logger.LogButtonClick(buttonText);

            await page.ClickAsync($"button:has-text('{buttonText}')");
            await Task.Delay(200);
        }

        private async Task FillTextField(string label, string value, IPage page, string logMessage = null)
        {
            _logger.LogFieldFill(label, value);

            var selector = $"label.q-field:has(div.text-secondary:has-text('{label}')) input.q-field__native";
            await page.FillAsync(selector, value);
            await Task.Delay(200);
        }

        public async Task<bool> UpdateOfferPrice(string offerId, decimal newPrice)
        {
            try
            {
                _logger.LogPriceUpdate(offerId, newPrice);
                var page = await _context.NewPageAsync();
                try
                {
                    await page.GotoAsync($"https://www.g2g.com/offers/{offerId}/edit");
                    await page.WaitForSelectorAsync("input[name='price']", new() { State = WaitForSelectorState.Visible });
                    await page.FillAsync("input[name='price']", newPrice.ToString("0.00"));
                    await page.ClickAsync("button[type='submit']");
                    await Task.Delay(3000); // Wait for update to complete

                    _logger.LogPriceUpdateSuccess(offerId);
                    return true;
                }
                finally
                {
                    await page.CloseAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogPriceUpdateError(offerId, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Upload obrazów do Dropbox i dodanie ich do oferty G2G
        /// </summary>
        private async Task UploadAndAddImages(List<string> imageUrls, IPage page)
        {
            try
            {
                if (imageUrls == null || imageUrls.Count == 0)
                {
                    _logger.LogNoImagesToUpload();
                    return;
                }

                _logger.LogImageUploadStart(imageUrls.Count);

                // Upload obrazów do Dropbox
                var uploadResults = await _imageUploadService.UploadMultipleImagesForG2G(imageUrls);
                var successfulUploads = uploadResults.Where(r => r.Success).ToList();

                _logger.LogImageUploadResult(successfulUploads.Count, imageUrls.Count);

                if (successfulUploads.Count == 0)
                {
                    _logger.LogNoSuccessfulUploads();
                    return;
                }

                // Dodaj obrazy do oferty G2G
                await AddImagesToG2GOffer(successfulUploads, page);
            }
            catch (Exception ex)
            {
                _logger.LogImageUploadError(ex.Message);
            }
        }

        /// <summary>
        /// Dodaje zauploadowane obrazy do oferty G2G
        /// </summary>
        private async Task AddImagesToG2GOffer(List<ImageUploadResult> uploadedImages, IPage page)
        {
            try
            {
                _logger.LogMediaUploadStart(uploadedImages.Count);

                // Spróbuj najpierw dodać obrazy przez sekcję media
                var success = await AddImagesToMediaSection(uploadedImages, page);

                if (!success)
                {
                    _logger.LogMediaUploadFallback();
                    await AddImageUrlsToDescription(uploadedImages, page);
                }
            }
            catch (Exception ex)
            {
                _logger.LogMediaUploadError(ex.Message);
                // Fallback - dodaj do opisu
                await AddImageUrlsToDescription(uploadedImages, page);
            }
        }

        /// <summary>
        /// Dodaje obrazy przez sekcję Media na G2G
        /// </summary>
        private async Task<bool> AddImagesToMediaSection(List<ImageUploadResult> uploadedImages, IPage page)
        {
            try
            {
                _logger.LogMediaSectionStart();

                for (int i = 0; i < uploadedImages.Count; i++)
                {
                    var image = uploadedImages[i];
                    _logger.LogMediaUploadProgress(i + 1, uploadedImages.Count, image.ImageUrl);

                    // Znajdź PUSTE pola dla aktualnego obrazu
                    var (titleInput, urlInput) = await FindEmptyMediaFields(page);

                    if (titleInput == null || urlInput == null)
                    {
                        _logger.LogMediaFieldsNotFound(i);
                        _logger.LogMediaFieldsDebug(titleInput != null, urlInput != null);

                        // Spróbuj jeszcze raz z opóźnieniem
                        await Task.Delay(2000);
                        var (retryTitle, retryUrl) = await FindEmptyMediaFields(page);

                        if (retryTitle != null && retryUrl != null)
                        {
                            titleInput = retryTitle;
                            urlInput = retryUrl;
                            _logger.LogMediaFieldsRetrySuccess();
                        }
                        else
                        {
                            _logger.LogMediaFieldsRetryFailed();
                            return false;
                        }
                    }

                    // Wypełnij tytuł obrazu
                    var imageTitle = $"Screenshot {i + 1}";
                    await titleInput.ClickAsync(); // Kliknij żeby aktywować pole
                    await titleInput.FillAsync("");  // Wyczyść pole
                    await titleInput.TypeAsync(imageTitle); // Wpisz tytuł
                    await Task.Delay(500);

                    // Wypełnij URL obrazu - waliduj i wyczyść URL przed wysłaniem
                    var cleanImageUrl = ValidateAndCleanImageUrl(image.ImageUrl);
                    await urlInput.ClickAsync(); // Kliknij żeby aktywować pole
                    await urlInput.FillAsync("");  // Wyczyść pole
                    await urlInput.TypeAsync(cleanImageUrl); // Wpisz URL
                    await Task.Delay(500);

                    _logger.LogDebug("✅ Dodano obraz {ImageNumber}: {ImageTitle} -> {ImageUrl}", i + 1, imageTitle, cleanImageUrl);

                    // Jeśli to nie ostatni obraz, kliknij "Add media"
                    if (i < uploadedImages.Count - 1)
                    {
                        // Spróbuj różnych selektorów dla przycisku "Add media"
                        var addMediaButton = await page.QuerySelectorAsync("button:has-text('Add media')");

                        if (addMediaButton == null)
                        {
                            // Alternatywny selektor
                            addMediaButton = await page.QuerySelectorAsync("button .q-btn__content:has-text('Add media')");
                            if (addMediaButton != null)
                            {
                                addMediaButton = await addMediaButton.QuerySelectorAsync("xpath=ancestor::button");
                            }
                        }

                        if (addMediaButton == null)
                        {
                            // Jeszcze jeden alternatywny selektor
                            addMediaButton = await page.QuerySelectorAsync("button[class*='q-btn']:has(.material-icons:text('add'))");
                        }

                        if (addMediaButton != null)
                        {
                            // Sprawdź czy przycisk nie jest disabled
                            var isDisabled = await addMediaButton.GetAttributeAsync("disabled");
                            var hasDisabledClass = await addMediaButton.GetAttributeAsync("class");

                            if (isDisabled == null && !hasDisabledClass.Contains("disabled"))
                            {
                                await addMediaButton.ClickAsync();
                                await Task.Delay(1500); // Czekaj na pojawienie się nowych pól
                                _logger.LogAddMediaButtonClick();
                            }
                            else
                            {
                                _logger.LogAddMediaButtonDisabled();
                                break;
                            }
                        }
                        else
                        {
                            _logger.LogAddMediaButtonNotFound();
                            break;
                        }
                    }
                }

                _logger.LogMediaUploadSuccess(uploadedImages.Count);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Błąd podczas dodawania obrazów przez sekcję Media");
                return false;
            }
        }

        /// <summary>
        /// Znajduje puste pola Media title i URL
        /// </summary>
        private async Task<(IElementHandle titleInput, IElementHandle urlInput)> FindEmptyMediaFields(IPage page)
        {
            try
            {
                // Znajdź wszystkie pola tytułu i URL
                var allTitleInputs = await page.QuerySelectorAllAsync("input[placeholder='Media title']");
                var allUrlInputs = await page.QuerySelectorAllAsync("input[placeholder='https://']");

                _logger.LogDebug("🔍 Znaleziono {TitleFieldsCount} pól tytułu i {UrlFieldsCount} pól URL",
                               allTitleInputs.Count, allUrlInputs.Count);

                // Znajdź pierwsze puste pole tytułu
                IElementHandle emptyTitleInput = null;
                foreach (var titleInput in allTitleInputs)
                {
                    var value = await titleInput.GetAttributeAsync("value") ?? "";
                    var inputValue = await titleInput.InputValueAsync();

                    if (string.IsNullOrEmpty(value) && string.IsNullOrEmpty(inputValue))
                    {
                        emptyTitleInput = titleInput;
                        break;
                    }
                }

                // Znajdź pierwsze puste pole URL
                IElementHandle emptyUrlInput = null;
                foreach (var urlInput in allUrlInputs)
                {
                    var value = await urlInput.GetAttributeAsync("value") ?? "";
                    var inputValue = await urlInput.InputValueAsync();

                    if (string.IsNullOrEmpty(value) && string.IsNullOrEmpty(inputValue))
                    {
                        emptyUrlInput = urlInput;
                        break;
                    }
                }

                if (emptyTitleInput != null && emptyUrlInput != null)
                {
                    _logger.LogDebug("✅ Znaleziono puste pola Media");
                    return (emptyTitleInput, emptyUrlInput);
                }
                else
                {
                    _logger.LogWarning("⚠️ Nie znaleziono pustych pól: title={HasTitle}, url={HasUrl}",
                                     emptyTitleInput != null, emptyUrlInput != null);
                    return (null, null);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Błąd podczas szukania pustych pól Media");
                return (null, null);
            }
        }

        /// <summary>
        /// Dodaje linki do obrazów na końcu opisu oferty
        /// </summary>
        private async Task AddImageUrlsToDescription(List<ImageUploadResult> uploadedImages, IPage page)
        {
            try
            {
                var descriptionTextarea = await page.QuerySelectorAsync("textarea[placeholder='Type offer description here']");

                if (descriptionTextarea != null)
                {
                    // Pobierz aktualny opis
                    var currentDescription = await descriptionTextarea.GetAttributeAsync("value") ??
                                           await descriptionTextarea.TextContentAsync() ?? "";

                    // Dodaj linki do obrazów - waliduj i wyczyść URL-e
                    var cleanImageLinks = uploadedImages.Select(img => ValidateAndCleanImageUrl(img.ImageUrl));
                    var imageLinks = string.Join("\n", cleanImageLinks);
                    var newDescription = $"{currentDescription}\n\n📸 Screenshots:\n{imageLinks}";

                    // Zaktualizuj opis
                    await page.FillAsync("textarea[placeholder='Type offer description here']", newDescription);

                    _logger.LogInformation("✅ Dodano {ImageCount} linków do obrazów w opisie", uploadedImages.Count);
                }
                else
                {
                    _logger.LogWarning("⚠️ Nie znaleziono pola opisu do dodania linków obrazów");
                }
            }
            catch (Exception ex)
            {
                _logger.LogDescriptionLinkAddError(ex.Message);
            }
        }

        /// <summary>
        /// Waliduje i czyści URL obrazu przed wysłaniem do G2G
        /// </summary>
        private string ValidateAndCleanImageUrl(string imageUrl)
        {
            if (string.IsNullOrEmpty(imageUrl))
            {
                _logger.LogEmptyImageUrl();
                return imageUrl;
            }

            try
            {
                var cleanUrl = imageUrl;

                // Usuń wszystkie możliwe warianty parametru dl z Dropbox
                cleanUrl = cleanUrl.Replace("?dl=0", "");
                cleanUrl = cleanUrl.Replace("&dl=0", "");
                cleanUrl = cleanUrl.Replace("?dl=1", "");
                cleanUrl = cleanUrl.Replace("&dl=1", "");

                // Usuń trailing znaki
                cleanUrl = cleanUrl.TrimEnd('&', '?');

                // Sprawdź czy URL jest prawidłowy
                if (Uri.TryCreate(cleanUrl, UriKind.Absolute, out Uri? result) &&
                    (result.Scheme == Uri.UriSchemeHttp || result.Scheme == Uri.UriSchemeHttps))
                {
                    if (cleanUrl != imageUrl)
                    {
                        _logger.LogUrlValidation(imageUrl, cleanUrl);
                    }
                    return cleanUrl;
                }
                else
                {
                    _logger.LogInvalidImageUrl(cleanUrl);
                    return imageUrl; // Zwróć oryginalny jeśli czyszczenie nie pomogło
                }
            }
            catch (Exception ex)
            {
                _logger.LogUrlValidationError(imageUrl, ex.Message);
                return imageUrl; // Zwróć oryginalny w przypadku błędu
            }
        }

        // Implementacja brakujących metod z interfejsu IG2GPublisherService
        public async Task<bool> IsServiceAvailable()
        {
            try
            {
                var page = await _context.NewPageAsync();
                try
                {
                    await page.GotoAsync("https://www.g2g.com");
                    return true;
                }
                finally
                {
                    await page.CloseAsync();
                }
            }
            catch
            {
                return false;
            }
        }

        public async Task<List<string>> GetActiveOffers()
        {
            try
            {
                var page = await _context.NewPageAsync();
                try
                {
                    await page.GotoAsync("https://www.g2g.com/my-offers");
                    // Implementacja pobierania aktywnych ofert
                    return new List<string>();
                }
                finally
                {
                    await page.CloseAsync();
                }
            }
            catch
            {
                return new List<string>();
            }
        }

        public object GetPublishingStatistics()
        {
            return new
            {
                LastPublishTime = DateTime.Now,
                ServiceAvailable = true,
                TotalPublished = 0
            };
        }

        public async Task<string> GetOfferStatus(string offerId)
        {
            try
            {
                var page = await _context.NewPageAsync();
                try
                {
                    await page.GotoAsync($"https://www.g2g.com/offers/{offerId}");
                    // Implementacja sprawdzania statusu oferty
                    return "active";
                }
                finally
                {
                    await page.CloseAsync();
                }
            }
            catch
            {
                return "unknown";
            }
        }

        public async Task<bool> DeleteOffer(string offerId)
        {
            try
            {
                var page = await _context.NewPageAsync();
                try
                {
                    await page.GotoAsync($"https://www.g2g.com/offers/{offerId}/edit");
                    // Implementacja usuwania oferty
                    return true;
                }
                finally
                {
                    await page.CloseAsync();
                }
            }
            catch
            {
                return false;
            }
        }

        // Nieużywane metody pomocnicze zostały usunięte
    }
}