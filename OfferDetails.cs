namespace ShopBot
{
    public class OfferDetails
    {
        public string Id { get; set; } = string.Empty;
        public string DataFType { get; set; } = string.Empty;
        public string ShortDescription { get; set; } = string.Empty;
        public string DetailedDescription { get; set; } = string.Empty;
        public string RatingStars { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public string FilteringPrice { get; set; } = string.Empty;
        public string ActualOfferPrice { get; set; } = string.Empty;
        public int MMR { get; set; }
        public string AccountType { get; set; } = string.Empty;
        public int BehaviorScore { get; set; }
        public string Platform { get; set; } = string.Empty;
        public string Server { get; set; } = string.Empty;
        public decimal PveFame { get; set; }
        public string WeaponType { get; set; } = string.Empty;
        public int Trophies { get; set; }
        public int Brawlers { get; set; }
        public int Level { get; set; }
        public string Rank { get; set; } = string.Empty;
        public int Skins { get; set; }
        public int SkinsQuantity { get; set; }
        public int AgentsQuantity { get; set; }
        public int Champions { get; set; }
        public string BattlegroundsPlus { get; set; } = string.Empty;
        public int MythicalChampions { get; set; }
        public int LegendaryChampions { get; set; }
        public int EpicChampions { get; set; }
        public int RareChampions { get; set; }
        public List<string> Images { get; set; } = new List<string>();
        public string Edition { get; set; } = "Standard";
        public int Heroes { get; set; }
        public int MythicSkins { get; set; }
        public int KillChats { get; set; }
        public int SportCars { get; set; }
        public int Stage { get; set; }
        public int Winrate { get; set; }
        public int HoursPlayed { get; set; }
        public int Primogems { get; set; }
        public int AR { get; set; }
        public string Faction { get; set; } = string.Empty;
        public string Race { get; set; } = string.Empty;
        public string Class { get; set; } = string.Empty;
        public List<string> ImgurUrls { get; set; } = new List<string>();
        public string SquadPrice { get; set; } = string.Empty;
        public string CoinsOnBalance { get; set; } = string.Empty;
        public string WebApp { get; set; } = string.Empty;
        public string RatingFC25 { get; set; } = string.Empty;

        // Dodatkowe właściwości dla kompatybilności z parserami
        public decimal Price { get; set; }
        public Dictionary<string, string> Parameters { get; set; } = new Dictionary<string, string>();
        public int Power { get; set; }
        public int MythicHeroes { get; set; }
        public int LegendaryHeroes { get; set; }
        public int EpicHeroes { get; set; }
        public int RareHeroes { get; set; }
    }
}