{"TotalOffers": 629, "AcceptedOffers": 218, "RejectedOffers": 411, "RejectionReasons": {"Cena ponizej minimum (13,07 < 15)": 32, "Cena ponizej minimum (7,19 < 15)": 5, "Cena ponizej minimum (5,88 < 15)": 3, "Cena ponizej minimum (3,27 < 15)": 8, "Cena ponizej minimum (2,61 < 15)": 4, "Cena ponizej minimum (10,46 < 15)": 15, "Cena ponizej minimum (5,23 < 15)": 10, "Cena ponizej minimum (2,77 < 15)": 1, "Cena ponizej minimum (1,31 < 15)": 1, "Cena ponizej minimum (3,92 < 15)": 11, "Ocena ponizej minimum (0 < 5)": 153, "Ocena ponizej minimum (4 < 5)": 7, "Cena ponizej minimum (7,84 < 15)": 12, "Cena ponizej minimum (6,82 < 15)": 3, "Cena ponizej minimum (7,95 < 15)": 2, "Cena ponizej minimum (13,63 < 15)": 1, "Zawiera zabronioną frazę (hardcoded): your choice": 3, "Cena ponizej minimum (12,84 < 15)": 2, "Cena ponizej minimum (14,00 < 15)": 2, "Cena ponizej minimum (11,67 < 15)": 2, "Cena ponizej minimum (8,17 < 15)": 1, "Cena ponizej minimum (3,41 < 15)": 3, "Cena ponizej minimum (4,54 < 15)": 2, "Cena ponizej minimum (5,11 < 15)": 1, "Cena ponizej minimum (5,68 < 15)": 3, "Cena ponizej minimum (1,36 < 15)": 6, "Cena ponizej minimum (1,70 < 15)": 1, "Duplikat oferty": 1, "Ocena ponizej minimum (3 < 5)": 1, "Cena ponizej minimum (3,66 < 15)": 1, "Cena ponizej minimum (6,54 < 15)": 18, "Cena ponizej minimum (9,15 < 15)": 20, "Cena ponizej minimum (4,31 < 15)": 1, "Cena ponizej minimum (1,14 < 15)": 1, "Cena ponizej minimum (1,44 < 15)": 1, "Cena ponizej minimum (0,03 < 15)": 1, "Cena ponizej minimum (2,84 < 15)": 1, "Cena ponizej minimum (9,80 < 15)": 6, "Cena ponizej minimum (2,27 < 15)": 1, "Cena ponizej minimum (11,77 < 15)": 14, "Cena ponizej minimum (7,39 < 15)": 1, "Cena ponizej minimum (4,58 < 15)": 6, "Cena ponizej minimum (8,50 < 15)": 2, "Cena ponizej minimum (5,57 < 15)": 1, "Cena ponizej minimum (11,11 < 15)": 4, "Cena ponizej minimum (1,05 < 15)": 1, "Cena ponizej minimum (13,06 < 15)": 1, "Cena ponizej minimum (6,80 < 15)": 1, "Cena ponizej minimum (11,35 < 15)": 1, "Cena ponizej minimum (10,65 < 15)": 1, "Cena ponizej minimum (0,78 < 15)": 2, "Cena ponizej minimum (7,58 < 15)": 1, "Cena ponizej minimum (6,27 < 15)": 1, "Cena ponizej minimum (14,38 < 15)": 2, "Cena ponizej minimum (5,18 < 15)": 1, "Cena ponizej minimum (6,48 < 15)": 1, "Cena ponizej minimum (9,26 < 15)": 1, "Cena ponizej minimum (4,37 < 15)": 1, "Cena ponizej minimum (5,84 < 15)": 1, "Cena ponizej minimum (7,48 < 15)": 1, "Cena ponizej minimum (6,93 < 15)": 1, "Cena powyzej maximum (3255,08 > 1000)": 1, "Cena ponizej minimum (7,69 < 15)": 1, "Cena ponizej minimum (5,62 < 15)": 1, "Cena ponizej minimum (1,11 < 15)": 1, "Cena ponizej minimum (11,37 < 15)": 1, "Ocena ponizej minimum (1 < 5)": 1, "Cena ponizej minimum (6,16 < 15)": 1, "Cena ponizej minimum (3,19 < 15)": 4, "Cena ponizej minimum (5,32 < 15)": 1, "Cena powyzej maximum (1568,71 > 1000)": 1, "Cena ponizej minimum (0,39 < 15)": 1, "Cena ponizej minimum (0,01 < 15)": 1, "Ocena ponizej minimum (2 < 5)": 1, "Cena ponizej minimum (1,16 < 15)": 1, "Cena ponizej minimum (3,50 < 15)": 1}, "GenerationTime": "2025-06-17T17:09:01.231132+02:00"}