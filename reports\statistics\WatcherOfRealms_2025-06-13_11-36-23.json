{"TotalOffers": 569, "AcceptedOffers": 202, "RejectedOffers": 367, "RejectionReasons": {"Cena ponizej minimum (9,03 < 15)": 18, "Cena ponizej minimum (4,52 < 15)": 8, "Cena ponizej minimum (3,87 < 15)": 8, "Cena ponizej minimum (5,16 < 15)": 9, "Cena ponizej minimum (12,90 < 15)": 30, "Cena ponizej minimum (1,12 < 15)": 1, "Cena ponizej minimum (1,42 < 15)": 1, "Cena ponizej minimum (0,03 < 15)": 1, "Ocena ponizej minimum (0 < 5)": 127, "Cena ponizej minimum (3,23 < 15)": 6, "Cena ponizej minimum (9,68 < 15)": 4, "Cena ponizej minimum (5,11 < 15)": 1, "Cena ponizej minimum (6,40 < 15)": 1, "Cena ponizej minimum (9,13 < 15)": 1, "Cena ponizej minimum (6,45 < 15)": 18, "Cena ponizej minimum (4,31 < 15)": 1, "Cena ponizej minimum (5,77 < 15)": 1, "Cena ponizej minimum (7,38 < 15)": 1, "Cena ponizej minimum (6,84 < 15)": 1, "Cena ponizej minimum (11,61 < 15)": 16, "Cena ponizej minimum (2,58 < 15)": 7, "Cena ponizej minimum (5,55 < 15)": 1, "Zawiera zabronioną frazę: your choice": 3, "Cena ponizej minimum (12,89 < 15)": 1, "Ocena ponizej minimum (4 < 5)": 6, "Cena ponizej minimum (12,67 < 15)": 2, "Cena ponizej minimum (13,82 < 15)": 2, "Cena ponizej minimum (11,52 < 15)": 2, "Cena ponizej minimum (8,06 < 15)": 1, "Cena ponizej minimum (5,50 < 15)": 1, "Cena ponizej minimum (5,61 < 15)": 3, "Cena ponizej minimum (8,77 < 15)": 1, "Cena ponizej minimum (7,48 < 15)": 1, "Cena ponizej minimum (2,97 < 15)": 1, "Cena ponizej minimum (7,74 < 15)": 10, "Cena ponizej minimum (3,92 < 15)": 2, "Cena ponizej minimum (7,10 < 15)": 4, "Cena ponizej minimum (4,82 < 15)": 1, "Cena ponizej minimum (10,32 < 15)": 12, "Cena ponizej minimum (8,39 < 15)": 2, "Cena ponizej minimum (5,81 < 15)": 3, "Cena ponizej minimum (3,61 < 15)": 1, "Cena ponizej minimum (4,26 < 15)": 1, "Cena ponizej minimum (3,36 < 15)": 2, "Cena ponizej minimum (4,48 < 15)": 1, "Cena ponizej minimum (5,04 < 15)": 1, "Cena ponizej minimum (1,35 < 15)": 6, "Cena ponizej minimum (1,68 < 15)": 1, "Duplikat oferty": 1, "Ocena ponizej minimum (3 < 5)": 1, "Cena ponizej minimum (10,97 < 15)": 6, "Cena ponizej minimum (11,09 < 15)": 3, "Cena ponizej minimum (0,65 < 15)": 1, "Cena ponizej minimum (14,19 < 15)": 1, "Cena ponizej minimum (14,18 < 15)": 1, "Cena ponizej minimum (1,29 < 15)": 1, "Cena ponizej minimum (6,58 < 15)": 1, "Cena ponizej minimum (10,45 < 15)": 1, "Cena ponizej minimum (11,20 < 15)": 1, "Cena powyzej maximum (3212,21 > 1000)": 1, "Cena ponizej minimum (2,77 < 15)": 1, "Cena ponizej minimum (1,10 < 15)": 1, "Ocena ponizej minimum (1 < 5)": 1, "Cena ponizej minimum (11,22 < 15)": 1, "Cena ponizej minimum (14,84 < 15)": 1, "Cena ponizej minimum (6,08 < 15)": 1, "Zawiera zabronioną frazę: accounts with": 1, "Cena powyzej maximum (1935,07 > 1000)": 1, "Cena ponizej minimum (1,16 < 15)": 1, "Cena ponizej minimum (2,05 < 15)": 1, "Cena ponizej minimum (1,03 < 15)": 1, "Cena ponizej minimum (0,01 < 15)": 1, "Cena powyzej maximum (1290,05 > 1000)": 1, "Cena ponizej minimum (0,77 < 15)": 1, "Cena ponizej minimum (3,19 < 15)": 1}, "GenerationTime": "2025-06-13T11:36:23.2196664+02:00"}