using System.Collections.Generic;
using System.Text;
using Microsoft.Extensions.Logging;
using ShopBot;

namespace ShopBot.Services.Formatters
{
    public class MobileLegendsDescriptionFormatter : BaseDescriptionFormatter
    {
        public MobileLegendsDescriptionFormatter(ILogger<MobileLegendsDescriptionFormatter> logger) : base(logger)
        {
        }
        
        public override string FormatDescription(OfferDetails offerDetails, Dictionary<string, string> parameters = null)
        {
            if (offerDetails == null)
            {
                _logger.LogWarning("OfferDetails is null in MobileLegends formatter");
                return string.Empty;
            }
            
            _logger.LogInformation("Formatting Mobile Legends description for offer {Id}", offerDetails.Id);
            
            var description = new StringBuilder();
            
            // Formatowanie statystyk konta
            description.AppendLine("=== MOBILE LEGENDS ACCOUNT INFO ===");
            
            var stats = new[]
            {
                $"Rank: {offerDetails.Rank}",
                $"Level: {offerDetails.Level}",
                $"Winrate: {offerDetails.Winrate}%",
                $"Heroes: {offerDetails.Heroes}",
                $"Skins: {offerDetails.Skins}"
            };
            
            description.AppendLine("• " + NormalizeDelimiters(string.Join(" | ", stats)));
            description.AppendLine("\n===========================");
            
            // Używamy tylko czyszczenia tekstu, bez dodawania standardowego tekstu
            var cleanDesc = RemoveEmoji(offerDetails.DetailedDescription);
            cleanDesc = RemoveBannedPhrases(cleanDesc);
            cleanDesc = NormalizeDelimiters(cleanDesc);
            description.AppendLine(cleanDesc);

            // Dodajemy standardowy tekst
            description.AppendLine("\n\n===============================================================");
            description.AppendLine("Got questions? Contact me on G2G chat before buying! Offers like this go fast – reserve your ideal account now!");
            
            // Dodajemy ID
            description.AppendLine($"\n({offerDetails.Id})");
            
            return description.ToString();
        }
        
        public override string FormatTitle(OfferDetails offerDetails)
        {
            if (offerDetails == null)
            {
                _logger.LogWarning("OfferDetails is null in MobileLegends title formatter");
                return string.Empty;
            }
            
            return $"MLBB | {offerDetails.Rank} | {offerDetails.Heroes} Heroes | {offerDetails.Skins} Skins";
        }
    }
} 