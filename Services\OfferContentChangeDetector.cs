using Microsoft.Extensions.Logging;
using ShopBot.Models;
using ShopBot.Services.Extensions;
using ShopBot.Services.Formatters;

namespace ShopBot.Services
{
    /// <summary>
    /// Serwis do wykrywania znaczących zmian w zawartości ofert FunPay.
    /// Porównuje oferty o tym samym ID i określa czy zmiany są na tyle znaczące,
    /// że oferta powinna być traktowana jako nowa.
    /// </summary>
    public class OfferContentChangeDetector
    {
        private readonly ILogger<OfferContentChangeDetector> _logger;
        private readonly DescriptionFormatterFactory _formatterFactory;
        
        // Progi dla wykrywania znaczących zmian
        private const decimal SIGNIFICANT_PRICE_CHANGE_THRESHOLD = 0.10m; // 10% zmiany ceny (obniżone z 15%)
        private const double TITLE_SIMILARITY_THRESHOLD = 0.7; // 70% podobieństwa tytułu
        
        public OfferContentChangeDetector(
            ILogger<OfferContentChangeDetector> logger,
            DescriptionFormatterFactory formatterFactory)
        {
            _logger = logger;
            _formatterFactory = formatterFactory;
        }

        /// <summary>
        /// Sprawdza czy oferta ma znaczące zmiany w porównaniu do poprzedniej wersji.
        /// </summary>
        /// <param name="existingOffer">Istniejąca oferta w bazie danych</param>
        /// <param name="newOfferDetails">Nowe szczegóły oferty z FunPay</param>
        /// <returns>True jeśli wykryto znaczące zmiany</returns>
        public bool HasSignificantChanges(Offer existingOffer, OfferDetails newOfferDetails)
        {
            try
            {
                _logger.LogDebug("[ChangeDetector] 🔍 Sprawdzam zmiany dla oferty {OfferId}", existingOffer.FunPayOfferId);
                
                // 1. Sprawdzenie zmiany ceny
                var priceChangeInfo = GetPriceChangeInfo(existingOffer, newOfferDetails);
                if (priceChangeInfo.HasSignificantChange)
                {
                    _logger.LogPriceChangeDetected(existingOffer.FunPayOfferId, existingOffer.OriginalPrice,
                        priceChangeInfo.NewPrice, priceChangeInfo.ChangePercent);
                    return true;
                }

                // 2. Sprawdzenie podobieństwa tytułu
                var titleChangeInfo = GetTitleChangeInfo(existingOffer, newOfferDetails);
                if (titleChangeInfo.HasSignificantChange)
                {
                    _logger.LogTitleChangeDetected(existingOffer.FunPayOfferId, existingOffer.Title,
                        newOfferDetails.ShortDescription, titleChangeInfo.Similarity);
                    return true;
                }
                
                _logger.LogDebug("[ChangeDetector] ✅ Brak znaczących zmian dla oferty {OfferId}", existingOffer.FunPayOfferId);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ChangeDetector] ❌ Błąd podczas sprawdzania zmian dla oferty {OfferId}", existingOffer.FunPayOfferId);
                // W przypadku błędu zakładamy brak zmian, aby nie usuwać ofert niepotrzebnie
                return false;
            }
        }

        /// <summary>
        /// Pobiera informacje o zmianie ceny.
        /// </summary>
        private PriceChangeInfo GetPriceChangeInfo(Offer existingOffer, OfferDetails newOfferDetails)
        {
            try
            {
                // Parsowanie nowej ceny - obsługa różnych formatów (podobnie jak w ExtractPriceFromText)
                var priceString = newOfferDetails.ActualOfferPrice;
                if (string.IsNullOrEmpty(priceString))
                {
                    _logger.LogWarning("[ChangeDetector] ⚠️ Brak ceny w nowych szczegółach oferty");
                    return new PriceChangeInfo { HasSignificantChange = false, NewPrice = 0, ChangePercent = 0 };
                }

                // Usuń wszystkie znaki oprócz cyfr, kropek i przecinków (podobnie jak w ExtractPriceFromText)
                var cleanPrice = System.Text.RegularExpressions.Regex.Replace(priceString, @"[^\d.,]", "");

                if (!decimal.TryParse(cleanPrice, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out decimal newPrice))
                {
                    // Spróbuj z polskim formatem
                    if (!decimal.TryParse(cleanPrice, System.Globalization.NumberStyles.Any, new System.Globalization.CultureInfo("pl-PL"), out newPrice))
                    {
                        _logger.LogWarning("[ChangeDetector] ⚠️ Nie można sparsować nowej ceny: '{OriginalPrice}' → '{CleanPrice}'", priceString, cleanPrice);
                        return new PriceChangeInfo { HasSignificantChange = false, NewPrice = 0, ChangePercent = 0 };
                    }
                }

                var oldPrice = existingOffer.OriginalPrice;

                // Unikaj dzielenia przez zero
                if (oldPrice == 0)
                {
                    return new PriceChangeInfo
                    {
                        HasSignificantChange = newPrice != 0,
                        NewPrice = newPrice,
                        ChangePercent = newPrice != 0 ? 1.0 : 0.0
                    };
                }

                var priceChangeRatio = Math.Abs(newPrice - oldPrice) / oldPrice;

                _logger.LogDebug("[ChangeDetector] 📊 Zmiana ceny: {OldPrice} → {NewPrice} (zmiana: {ChangePercent:P2})",
                    oldPrice, newPrice, priceChangeRatio);

                return new PriceChangeInfo
                {
                    HasSignificantChange = priceChangeRatio > SIGNIFICANT_PRICE_CHANGE_THRESHOLD,
                    NewPrice = newPrice,
                    ChangePercent = (double)priceChangeRatio
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ChangeDetector] ❌ Błąd podczas porównywania cen");
                return new PriceChangeInfo { HasSignificantChange = false, NewPrice = 0, ChangePercent = 0 };
            }
        }

        /// <summary>
        /// Pobiera informacje o zmianie tytułu.
        /// WAŻNE: Formatuje oba tytuły tym samym formatterem przed porównaniem,
        /// aby usunąć emoji i inne znaki nieakceptowane przez G2G.
        /// </summary>
        private TitleChangeInfo GetTitleChangeInfo(Offer existingOffer, OfferDetails newOfferDetails)
        {
            try
            {
                var oldTitle = existingOffer.Title ?? string.Empty;
                var newTitle = newOfferDetails.ShortDescription ?? string.Empty;

                // Jeśli oba tytuły są puste, nie ma zmiany
                if (string.IsNullOrWhiteSpace(oldTitle) && string.IsNullOrWhiteSpace(newTitle))
                {
                    return new TitleChangeInfo { HasSignificantChange = false, Similarity = 1.0 };
                }

                // Jeśli jeden jest pusty a drugi nie, to znacząca zmiana
                if (string.IsNullOrWhiteSpace(oldTitle) || string.IsNullOrWhiteSpace(newTitle))
                {
                    return new TitleChangeInfo { HasSignificantChange = true, Similarity = 0.0 };
                }

                // KROK 1: Wyciągnij kluczowe słowa z obu tytułów (nazwy bohaterów, przedmiotów)
                var oldKeywords = ExtractKeywords(oldTitle);
                var newKeywords = ExtractKeywords(newTitle);

                _logger.LogDebug("[ChangeDetector] 🔄 Analiza zawartości tytułów:");
                _logger.LogDebug("[ChangeDetector]   Stary surowy: {OldRaw}", oldTitle);
                _logger.LogDebug("[ChangeDetector]   Stare słowa kluczowe: [{OldKeywords}]", string.Join(", ", oldKeywords));
                _logger.LogDebug("[ChangeDetector]   Nowy surowy: {NewRaw}", newTitle);
                _logger.LogDebug("[ChangeDetector]   Nowe słowa kluczowe: [{NewKeywords}]", string.Join(", ", newKeywords));

                // KROK 2: Oblicz podobieństwo zawartości (nie formatowania!)
                var similarity = CalculateContentSimilarity(oldKeywords, newKeywords);

                _logger.LogDebug("[ChangeDetector] 📝 Podobieństwo zawartości: {Similarity:P2} (próg: {Threshold:P2})",
                    similarity, TITLE_SIMILARITY_THRESHOLD);

                var hasSignificantChange = similarity < TITLE_SIMILARITY_THRESHOLD;

                if (hasSignificantChange)
                {
                    _logger.LogInformation("[ChangeDetector] 📝 Wykryto zmianę zawartości: podobieństwo {Similarity:P2}",
                        similarity);
                    _logger.LogInformation("[ChangeDetector]   Stara zawartość: {OldContent}", string.Join(" + ", oldKeywords));
                    _logger.LogInformation("[ChangeDetector]   Nowa zawartość: {NewContent}", string.Join(" + ", newKeywords));
                }
                else
                {
                    _logger.LogDebug("[ChangeDetector] ✅ Brak znaczących zmian zawartości");
                }

                return new TitleChangeInfo
                {
                    HasSignificantChange = hasSignificantChange,
                    Similarity = similarity
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ChangeDetector] ❌ Błąd podczas porównywania tytułów");
                return new TitleChangeInfo { HasSignificantChange = false, Similarity = 0.0 };
            }
        }





        /// <summary>
        /// Wyciąga kluczowe słowa z tytułu oferty (nazwy bohaterów, przedmiotów, gier).
        /// Ignoruje liczby, emoji, formatowanie i automatyczne parametry.
        /// </summary>
        private List<string> ExtractKeywords(string title)
        {
            if (string.IsNullOrWhiteSpace(title))
                return new List<string>();

            var keywords = new List<string>();

            // KROK 1: Usuń emoji i znaki specjalne
            var cleanTitle = RemoveEmojisAndSpecialChars(title);

            // KROK 2: Usuń automatyczne parametry (Account level, Legendary champions, etc.)
            cleanTitle = RemoveAutomaticParameters(cleanTitle);

            // KROK 3: Podziel na słowa i filtruj
            var words = cleanTitle.Split(new char[] { ' ', ',', '|', '+', '-', '(', ')', '[', ']', ':', ';' },
                StringSplitOptions.RemoveEmptyEntries);

            foreach (var word in words)
            {
                var cleanWord = word.Trim().ToLowerInvariant();

                // Ignoruj krótkie słowa (< 3 znaki)
                if (cleanWord.Length < 3) continue;

                // Ignoruj liczby i kombinacje liczb z literami (np. "5⭐", "100lvl")
                if (IsNumericOrLevel(cleanWord)) continue;

                // Ignoruj popularne słowa bez znaczenia
                if (IsStopWord(cleanWord)) continue;

                keywords.Add(cleanWord);
            }

            // Usuń duplikaty i posortuj
            return keywords.Distinct().OrderBy(k => k).ToList();
        }

        /// <summary>
        /// Oblicza podobieństwo zawartości na podstawie słów kluczowych.
        /// Używa algorytmu Jaccard similarity dla zbiorów słów.
        /// </summary>
        private double CalculateContentSimilarity(List<string> oldKeywords, List<string> newKeywords)
        {
            if (!oldKeywords.Any() && !newKeywords.Any())
                return 1.0; // Oba puste = identyczne

            if (!oldKeywords.Any() || !newKeywords.Any())
                return 0.0; // Jeden pusty = całkowicie różne

            // Algorytm Jaccard: |A ∩ B| / |A ∪ B|
            var intersection = oldKeywords.Intersect(newKeywords).Count();
            var union = oldKeywords.Union(newKeywords).Count();

            return union > 0 ? (double)intersection / union : 0.0;
        }

        /// <summary>
        /// Usuwa emoji i znaki specjalne z tytułu.
        /// </summary>
        private string RemoveEmojisAndSpecialChars(string title)
        {
            if (string.IsNullOrWhiteSpace(title))
                return string.Empty;

            var result = title;

            // Usuń popularne emoji
            var emojisToRemove = new[]
            {
                "🔥", "❤️‍🔥", "❤️", "💖", "📌", "🪑", "👑", "👹", "💰", "⛏", "⭐", "🌸", "💎", "🎁", "🏆", "🟣",
                "🔶", "😎", "👁️", "🌑", "🖤", "🔴", "🌟", "🎀", "💫", "⚡", "🔵", "🟢", "🟡", "🟠", "🟤", "⚫",
                "⚪", "🔺", "🔻", "🔸", "🔹", "🔷", "🔳", "🔲", "▪️", "▫️", "◾", "◽", "◼️", "◻️", "⬛", "⬜",
                "🚀", "✅", "❇️", "💯", "🎯", "🔝", "🆕", "🆓", "🆒", "🆙", "🔞", "💥", "🎊", "🎉"
            };
            foreach (var emoji in emojisToRemove)
            {
                result = result.Replace(emoji, " ");
            }

            // Usuń inne znaki specjalne
            result = result.Replace("【", " ").Replace("】", " ");

            // Usuń wszystkie pozostałe emoji Unicode
            result = System.Text.RegularExpressions.Regex.Replace(result,
                @"[\u1F600-\u1F64F\u1F300-\u1F5FF\u1F680-\u1F6FF\u1F1E0-\u1F1FF\u2600-\u26FF\u2700-\u27BF]", " ");

            // Usuń nadmiarowe spacje
            result = System.Text.RegularExpressions.Regex.Replace(result, @"\s+", " ");

            return result.Trim();
        }

        /// <summary>
        /// Sprawdza czy słowo to liczba lub poziom (np. "100", "5⭐", "lvl", "level").
        /// </summary>
        private bool IsNumericOrLevel(string word)
        {
            // Sprawdź czy to czysta liczba
            if (int.TryParse(word, out _)) return true;

            // Sprawdź wzorce poziomów
            var levelPatterns = new[] { "lvl", "level", "lv", "star", "stars" };
            if (levelPatterns.Any(pattern => word.Contains(pattern))) return true;

            // Sprawdź czy zawiera głównie cyfry
            var digitCount = word.Count(char.IsDigit);
            return digitCount > word.Length / 2;
        }

        /// <summary>
        /// Sprawdza czy słowo to popularne słowo bez znaczenia (stop word).
        /// </summary>
        private bool IsStopWord(string word)
        {
            var stopWords = new HashSet<string>
            {
                "the", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by",
                "start", "starter", "account", "top", "best", "good", "nice", "imba", "combo",
                "combination", "sell", "selling", "will", "can", "has", "have", "get", "all",
                "any", "some", "very", "much", "more", "most", "new", "old", "big", "small"
            };

            return stopWords.Contains(word);
        }

        /// <summary>
        /// Usuwa automatyczne parametry z tytułu (Account level, Legendary champions, etc.).
        /// </summary>
        private string RemoveAutomaticParameters(string title)
        {
            if (string.IsNullOrWhiteSpace(title))
                return string.Empty;

            var result = title;

            // Usuń parametry RSL
            var rslPatterns = new[]
            {
                @",\s*Account level:\s*\d+.*$",
                @",\s*Legendary champions:\s*\d+.*$",
                @",\s*Epic champions:\s*\d+.*$",
                @",\s*Rare champions:\s*\d+.*$",
                @",\s*Mythical champions:\s*\d+.*$"
            };

            foreach (var pattern in rslPatterns)
            {
                result = System.Text.RegularExpressions.Regex.Replace(result, pattern, "",
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            }

            // Usuń parametry Albion
            var albionPatterns = new[]
            {
                @",\s*Melee weapons,\s*Fame\s*\d+[kmKM]?",
                @",\s*Ranged weapons,\s*Fame\s*\d+[kmKM]?",
                @",\s*Magical weapons,\s*Fame\s*\d+[kmKM]?"
            };

            foreach (var pattern in albionPatterns)
            {
                result = System.Text.RegularExpressions.Regex.Replace(result, pattern, "",
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            }

            return result.Trim();
        }

        /// <summary>
        /// Oblicza podobieństwo dwóch stringów używając algorytmu Levenshteina.
        /// Zwraca wartość od 0.0 (całkowicie różne) do 1.0 (identyczne).
        /// </summary>
        private double CalculateLevenshteinSimilarity(string source, string target)
        {
            if (string.IsNullOrEmpty(source) && string.IsNullOrEmpty(target))
                return 1.0;
            
            if (string.IsNullOrEmpty(source) || string.IsNullOrEmpty(target))
                return 0.0;
            
            // Normalizacja - konwersja na małe litery i usunięcie nadmiarowych spacji
            source = NormalizeString(source);
            target = NormalizeString(target);
            
            if (source == target)
                return 1.0;
            
            var distance = CalculateLevenshteinDistance(source, target);
            var maxLength = Math.Max(source.Length, target.Length);
            
            // Konwersja dystansu na podobieństwo (0.0 - 1.0)
            var similarity = 1.0 - (double)distance / maxLength;
            
            return Math.Max(0.0, similarity);
        }

        /// <summary>
        /// Normalizuje string do porównania - małe litery, usunięcie nadmiarowych spacji.
        /// </summary>
        private string NormalizeString(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            // Konwersja na małe litery i usunięcie nadmiarowych spacji
            return System.Text.RegularExpressions.Regex.Replace(input.ToLowerInvariant().Trim(), @"\s+", " ");
        }



        /// <summary>
        /// Oblicza dystans Levenshteina między dwoma stringami.
        /// Algorytm dynamicznego programowania z optymalizacją pamięci.
        /// </summary>
        private int CalculateLevenshteinDistance(string source, string target)
        {
            if (string.IsNullOrEmpty(source))
                return target?.Length ?? 0;

            if (string.IsNullOrEmpty(target))
                return source.Length;

            var sourceLength = source.Length;
            var targetLength = target.Length;

            // Optymalizacja: jeśli jeden string jest znacznie dłuższy, wczesne przerwanie
            var lengthDifference = Math.Abs(sourceLength - targetLength);
            var maxLength = Math.Max(sourceLength, targetLength);

            // Jeśli różnica długości przekracza 50% dłuższego stringa,
            // prawdopodobnie są to bardzo różne stringi
            if (lengthDifference > maxLength * 0.5)
            {
                _logger.LogDebug("[ChangeDetector] ⚡ Wczesne przerwanie - duża różnica długości: {SourceLength} vs {TargetLength}",
                    sourceLength, targetLength);
                return Math.Max(sourceLength, targetLength);
            }

            // Użyj tylko dwóch wierszy zamiast całej macierzy (optymalizacja pamięci)
            var previousRow = new int[targetLength + 1];
            var currentRow = new int[targetLength + 1];

            // Inicjalizacja pierwszego wiersza
            for (int j = 0; j <= targetLength; j++)
            {
                previousRow[j] = j;
            }

            // Wypełnienie macierzy
            for (int i = 1; i <= sourceLength; i++)
            {
                currentRow[0] = i;

                for (int j = 1; j <= targetLength; j++)
                {
                    var cost = source[i - 1] == target[j - 1] ? 0 : 1;

                    currentRow[j] = Math.Min(
                        Math.Min(
                            currentRow[j - 1] + 1,     // Wstawienie
                            previousRow[j] + 1),       // Usunięcie
                        previousRow[j - 1] + cost);   // Zamiana
                }

                // Zamień wiersze
                var temp = previousRow;
                previousRow = currentRow;
                currentRow = temp;
            }

            return previousRow[targetLength];
        }

        /// <summary>
        /// Zwraca statystyki wydajności detektora zmian.
        /// </summary>
        public ChangeDetectorStats GetPerformanceStats()
        {
            return new ChangeDetectorStats
            {
                PriceChangeThreshold = SIGNIFICANT_PRICE_CHANGE_THRESHOLD,
                TitleSimilarityThreshold = TITLE_SIMILARITY_THRESHOLD
            };
        }
    }

    /// <summary>
    /// Statystyki wydajności detektora zmian.
    /// </summary>
    public class ChangeDetectorStats
    {
        public decimal PriceChangeThreshold { get; set; }
        public double TitleSimilarityThreshold { get; set; }
    }

    /// <summary>
    /// Informacje o zmianie ceny oferty.
    /// </summary>
    public class PriceChangeInfo
    {
        public bool HasSignificantChange { get; set; }
        public decimal NewPrice { get; set; }
        public double ChangePercent { get; set; }
    }

    /// <summary>
    /// Informacje o zmianie tytułu oferty.
    /// </summary>
    public class TitleChangeInfo
    {
        public bool HasSignificantChange { get; set; }
        public double Similarity { get; set; }
    }
}
