{"TotalOffers": 564, "AcceptedOffers": 63, "RejectedOffers": 501, "RejectionReasons": {"Cena ponizej minimum (3,27 < 15)": 4, "Duplikat oferty": 35, "Cena ponizej minimum (1,28 < 15)": 1, "Cena ponizej minimum (0,26 < 15)": 3, "Cena ponizej minimum (0,07 < 15)": 1, "Cena ponizej minimum (6,54 < 15)": 6, "Cena ponizej minimum (0,22 < 15)": 2, "Cena ponizej minimum (0,32 < 15)": 1, "Cena ponizej minimum (0,46 < 15)": 4, "Cena powyzej maximum (1138,42 > 99)": 1, "Cena powyzej maximum (196,28 > 99)": 6, "Cena ponizej minimum (13,09 < 15)": 8, "Cena ponizej minimum (4,58 < 15)": 3, "Cena ponizej minimum (1,57 < 15)": 1, "Cena ponizej minimum (3,93 < 15)": 9, "Cena powyzej maximum (183,19 > 99)": 7, "Cena ponizej minimum (14,39 < 15)": 2, "Cena ponizej minimum (9,67 < 15)": 1, "Cena powyzej maximum (206,75 > 99)": 1, "Cena powyzej maximum (135,27 > 99)": 1, "Cena powyzej maximum (112,72 > 99)": 1, "Cena ponizej minimum (1,14 < 15)": 4, "Cena ponizej minimum (10,47 < 15)": 7, "Cena ponizej minimum (1,64 < 15)": 3, "Cena ponizej minimum (1,31 < 15)": 15, "Cena ponizej minimum (5,23 < 15)": 5, "Cena ponizej minimum (2,46 < 15)": 1, "Cena ponizej minimum (5,89 < 15)": 4, "Cena powyzej maximum (105,64 > 99)": 1, "Cena powyzej maximum (104,74 > 99)": 1, "Cena powyzej maximum (105,34 > 99)": 2, "Cena powyzej maximum (226,33 > 99)": 1, "Cena powyzej maximum (366,22 > 99)": 1, "Cena powyzej maximum (103,53 > 99)": 1, "Cena powyzej maximum (971,75 > 99)": 1, "Cena powyzej maximum (103,23 > 99)": 2, "Cena powyzej maximum (105,95 > 99)": 2, "Cena powyzej maximum (102,59 > 99)": 1, "Cena powyzej maximum (101,65 > 99)": 1, "Cena powyzej maximum (103,50 > 99)": 1, "Cena ponizej minimum (8,66 < 15)": 3, "Ocena ponizej minimum (4 < 5)": 30, "Cena ponizej minimum (4,55 < 15)": 1, "Cena ponizej minimum (0,63 < 15)": 1, "Cena ponizej minimum (1,97 < 15)": 1, "Cena ponizej minimum (0,01 < 15)": 2, "Cena ponizej minimum (10,65 < 15)": 7, "Cena ponizej minimum (5,32 < 15)": 10, "Cena powyzej maximum (102,34 > 99)": 1, "Cena ponizej minimum (1,04 < 15)": 1, "Cena ponizej minimum (0,34 < 15)": 1, "Cena ponizej minimum (0,28 < 15)": 1, "Cena ponizej minimum (0,10 < 15)": 2, "Cena powyzej maximum (193,32 > 99)": 1, "Cena powyzej maximum (327,14 > 99)": 1, "Ocena ponizej minimum (0 < 5)": 37, "Cena ponizej minimum (0,23 < 15)": 1, "Cena ponizej minimum (0,21 < 15)": 2, "Cena ponizej minimum (0,20 < 15)": 4, "Cena ponizej minimum (0,19 < 15)": 1, "Cena ponizej minimum (0,18 < 15)": 1, "Cena ponizej minimum (0,17 < 15)": 1, "Cena ponizej minimum (0,16 < 15)": 1, "Cena ponizej minimum (0,15 < 15)": 1, "Cena ponizej minimum (0,14 < 15)": 1, "Cena ponizej minimum (0,12 < 15)": 1, "Cena powyzej maximum (654,27 > 99)": 3, "Cena ponizej minimum (0,98 < 15)": 5, "Cena ponizej minimum (14,54 < 15)": 2, "Cena ponizej minimum (7,85 < 15)": 1, "Cena powyzej maximum (202,82 > 99)": 2, "Cena ponizej minimum (1,85 < 15)": 1, "Cena ponizej minimum (0,65 < 15)": 2, "Cena powyzej maximum (392,56 > 99)": 7, "Cena ponizej minimum (0,47 < 15)": 3, "Cena ponizej minimum (0,92 < 15)": 2, "Cena powyzej maximum (209,37 > 99)": 3, "Cena powyzej maximum (190,61 > 99)": 1, "Cena ponizej minimum (0,57 < 15)": 15, "Cena ponizej minimum (1,16 < 15)": 1, "Cena ponizej minimum (3,08 < 15)": 1, "Cena ponizej minimum (0,91 < 15)": 1, "Cena ponizej minimum (2,56 < 15)": 1, "Cena ponizej minimum (1,96 < 15)": 1, "Cena ponizej minimum (2,47 < 15)": 1, "Cena ponizej minimum (2,88 < 15)": 1, "Cena ponizej minimum (1,05 < 15)": 9, "Cena ponizej minimum (8,51 < 15)": 4, "Cena powyzej maximum (261,71 > 99)": 5, "Cena powyzej maximum (353,30 > 99)": 2, "Cena ponizej minimum (0,29 < 15)": 1, "Cena powyzej maximum (143,94 > 99)": 4, "Cena ponizej minimum (1,13 < 15)": 1, "Cena ponizej minimum (1,32 < 15)": 1, "Cena ponizej minimum (1,45 < 15)": 1, "Cena powyzej maximum (117,77 > 99)": 2, "Cena powyzej maximum (222,45 > 99)": 1, "Cena powyzej maximum (123,05 > 99)": 1, "Cena powyzej maximum (104,68 > 99)": 4, "Cena ponizej minimum (13,07 < 15)": 2, "Cena ponizej minimum (11,78 < 15)": 4, "Cena ponizej minimum (11,12 < 15)": 1, "Cena powyzej maximum (921,69 > 99)": 1, "Cena powyzej maximum (292,60 > 99)": 1, "Cena powyzej maximum (263,34 > 99)": 2, "Cena powyzej maximum (395,01 > 99)": 1, "Cena powyzej maximum (237,01 > 99)": 3, "Cena powyzej maximum (146,30 > 99)": 1, "Cena powyzej maximum (355,51 > 99)": 1, "Cena powyzej maximum (223,84 > 99)": 2, "Cena powyzej maximum (329,18 > 99)": 1, "Cena powyzej maximum (302,84 > 99)": 1, "Cena powyzej maximum (336,48 > 99)": 1, "Cena powyzej maximum (605,68 > 99)": 1, "Cena ponizej minimum (0,13 < 15)": 1, "Cena powyzej maximum (235,54 > 99)": 2, "Cena powyzej maximum (113,70 > 99)": 1, "Cena ponizej minimum (1,09 < 15)": 1, "Cena powyzej maximum (484,16 > 99)": 1, "Cena ponizej minimum (4,45 < 15)": 1, "Cena powyzej maximum (588,84 > 99)": 1, "Cena ponizej minimum (0,90 < 15)": 1, "Cena powyzej maximum (165,44 > 99)": 1, "Cena powyzej maximum (319,42 > 99)": 2, "Cena powyzej maximum (244,89 > 99)": 1, "Cena powyzej maximum (404,60 > 99)": 1, "Cena ponizej minimum (11,72 < 15)": 1, "Cena ponizej minimum (11,74 < 15)": 1, "Cena ponizej minimum (11,75 < 15)": 1, "Cena ponizej minimum (11,76 < 15)": 1, "Cena powyzej maximum (327,13 > 99)": 3, "Cena ponizej minimum (10,93 < 15)": 1, "Cena ponizej minimum (5,50 < 15)": 1, "Cena powyzej maximum (110,57 > 99)": 1, "Cena powyzej maximum (1308,53 > 99)": 2, "Cena powyzej maximum (341,15 > 99)": 1, "Cena powyzej maximum (157,02 > 99)": 1, "Cena powyzej maximum (523,41 > 99)": 5, "Cena ponizej minimum (4,26 < 15)": 1, "Cena ponizej minimum (1,03 < 15)": 2, "Cena ponizej minimum (2,60 < 15)": 2, "Cena powyzej maximum (274,79 > 99)": 1, "Cena powyzej maximum (158,65 > 99)": 1, "Cena powyzej maximum (209,35 > 99)": 1, "Cena powyzej maximum (1177,68 > 99)": 1, "Cena powyzej maximum (106,47 > 99)": 1, "Cena powyzej maximum (170,11 > 99)": 2, "Cena powyzej maximum (163,57 > 99)": 1, "Cena powyzej maximum (130,85 > 99)": 5, "Cena powyzej maximum (300,96 > 99)": 1, "Cena ponizej minimum (7,20 < 15)": 3, "Cena powyzej maximum (248,62 > 99)": 2, "Cena powyzej maximum (212,95 > 99)": 1, "Cena powyzej maximum (287,72 > 99)": 1, "Cena ponizej minimum (0,33 < 15)": 1, "Cena powyzej maximum (340,22 > 99)": 1, "Ocena ponizej minimum (1 < 5)": 1, "Cena ponizej minimum (9,16 < 15)": 3, "Cena ponizej minimum (4,92 < 15)": 1, "Cena ponizej minimum (8,61 < 15)": 1, "Cena ponizej minimum (6,15 < 15)": 1, "Cena powyzej maximum (211,88 > 99)": 1, "Cena powyzej maximum (254,47 > 99)": 1, "Cena powyzej maximum (339,65 > 99)": 1, "Cena powyzej maximum (392,89 > 99)": 1, "Cena powyzej maximum (228,92 > 99)": 1, "Cena powyzej maximum (424,83 > 99)": 1, "Cena powyzej maximum (190,59 > 99)": 1, "Cena powyzej maximum (275,77 > 99)": 1, "Cena powyzej maximum (137,35 > 99)": 1, "Cena powyzej maximum (126,70 > 99)": 1, "Cena powyzej maximum (175,68 > 99)": 2, "Cena powyzej maximum (148,00 > 99)": 1, "Cena powyzej maximum (318,36 > 99)": 1, "Cena powyzej maximum (532,37 > 99)": 1, "Cena powyzej maximum (286,42 > 99)": 1, "Cena powyzej maximum (165,03 > 99)": 1, "Cena powyzej maximum (169,29 > 99)": 1, "Cena powyzej maximum (329,00 > 99)": 1, "Cena powyzej maximum (111,23 > 99)": 1, "Cena powyzej maximum (338,91 > 99)": 1, "Cena powyzej maximum (314,05 > 99)": 1, "Cena powyzej maximum (850,55 > 99)": 1, "Cena ponizej minimum (0,39 < 15)": 1, "Cena powyzej maximum (532,36 > 99)": 1, "Cena powyzej maximum (549,58 > 99)": 1, "Cena powyzej maximum (291,87 > 99)": 1, "Cena powyzej maximum (615,24 > 99)": 1, "Cena ponizej minimum (12,43 < 15)": 1, "Cena powyzej maximum (601,92 > 99)": 1, "Cena powyzej maximum (393,75 > 99)": 1, "Cena powyzej maximum (150,48 > 99)": 1, "Cena powyzej maximum (231,52 > 99)": 1, "Cena ponizej minimum (2,62 < 15)": 1, "Cena ponizej minimum (4,32 < 15)": 1, "Cena powyzej maximum (149,06 > 99)": 1, "Cena powyzej maximum (103,37 > 99)": 1, "Cena powyzej maximum (141,51 > 99)": 1, "Cena ponizej minimum (0,79 < 15)": 1, "Cena powyzej maximum (785,12 > 99)": 3, "Cena ponizej minimum (0,02 < 15)": 2, "Cena powyzej maximum (159,71 > 99)": 1, "Cena powyzej maximum (307,62 > 99)": 1, "Cena powyzej maximum (418,73 > 99)": 1, "Ocena ponizej minimum (3 < 5)": 1, "Cena powyzej maximum (693,55 > 99)": 1, "Cena ponizej minimum (9,81 < 15)": 1, "Cena ponizej minimum (2,22 < 15)": 1, "Cena powyzej maximum (249,65 > 99)": 1, "Cena powyzej maximum (915,97 > 99)": 1, "Cena ponizej minimum (7,97 < 15)": 1}, "GenerationTime": "2025-06-16T22:58:54.4519467+02:00"}