#nullable disable
using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ShopBot.Services;
using ShopBot.Services.Extensions;
using ShopBot.Config;
using Microsoft.Playwright;

namespace ShopBot.Tasks
{
    public class AnalyzeOffersTask : BaseTask
    {
        private readonly FunPayService _funPayService;
        private readonly ParserService _parserService;
        private readonly IBrowser _browser;
        private readonly IBrowserContext _context;
        private readonly OfferDescriptionAnalyzer _analyzer;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<AnalyzeOffersTask> _logger;

        public AnalyzeOffersTask(
            FunPayService funPayService,
            ParserService parserService,
            IBrowser browser,
            IBrowserContext context,
            OfferDescriptionAnalyzer analyzer,
            IServiceProvider serviceProvider,
            ILogger<AnalyzeOffersTask> logger)
        {
            _funPayService = funPayService;
            _parserService = parserService;
            _browser = browser;
            _context = context;
            _analyzer = analyzer;
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        public override async Task ExecuteAsync(CancellationToken cancellationToken)
        {
            try
            {
                Status = TaskStatus.Running;
                _logger.LogAnalysisStart();

                var (filter, _, _) = FilterConfig.LoadFromFile();
                var activeGames = GameConfig.GetActiveGames();

                foreach (var gameName in activeGames)
                {
                    if (cancellationToken.IsCancellationRequested)
                        break;

                    _logger.LogGameAnalysisStart(gameName);
                    IPage? page = null;
                    try
                    {
                        page = await _context.NewPageAsync();
                        
                        // Pobierz listę ofert
                        var funPayService = _serviceProvider.GetRequiredService<Func<IPage, FunPayService>>()(page);
                        var offers = await funPayService.GetFilteredOffers(filter, gameName);
                        _logger.LogOffersFoundForAnalysis(offers.Count);

                        // Pobierz szczegóły każdej oferty
                        foreach (var offer in offers)
                        {
                            if (cancellationToken.IsCancellationRequested)
                                break;

                            try
                            {
                                _logger.LogOfferDetailsRetrieved(offer.Id);
                                var details = await funPayService.GetOfferDetails(offer.Url);

                                var description = new OfferDescriptionAnalyzer.OfferDescription
                                {
                                    Id = offer.Id,
                                    GameName = gameName,
                                    ShortDescription = offer.Description,
                                    DetailedDescription = details.DetailedDescription
                                };

                                _analyzer.AddOrUpdateOffer(gameName, description);
                                _logger.LogOfferDescriptionAdded(offer.Id);

                                // Krótka przerwa między requestami
                                await Task.Delay(300, cancellationToken);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogOfferAnalysisError(offer.Id, ex.Message);
                            }
                        }

                        // Zapisz dane dla tej gry
                        await _analyzer.SaveGameData(gameName);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogGameAnalysisError(gameName, ex.Message);
                    }
                    finally
                    {
                        if (page != null)
                        {
                            await page.CloseAsync();
                        }
                    }
                }

                // Wygeneruj raport
                await _analyzer.GenerateAnalysisReport();

                // Wygeneruj analizę szablonów
                await _analyzer.AnalyzeTemplates();

                Status = TaskStatus.Completed;
                _logger.LogAnalysisComplete(_analyzer.GetTotalOffers());
            }
            catch (Exception ex)
            {
                Status = TaskStatus.Failed;
                ErrorMessage = ex.Message;
                _logger.LogError("[Analysis] ❌ Błąd krytyczny: {Error}", ex.Message);
                throw;
            }
        }
    }
} 