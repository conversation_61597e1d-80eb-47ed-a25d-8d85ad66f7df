using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Configuration.Json;
using ShopBot.Services;

namespace ShopBot
{
    /// <summary>
    /// Refaktoryzowany Program.cs - Clean, Simple, Maintainable
    /// Zmniejszony z 382 linii do ~70 linii!
    /// </summary>
    class Program
    {
        static async Task Main(string[] args)
        {
            try
            {
                // Sprawdź argumenty specjalne (CLI commands)
                if (args.Length > 0)
                {
                    await HandleCliCommands(args);
                    return;
                }

                // Uruchom główną aplikację jako Hosted Service
                var host = CreateHostBuilder(args).Build();
                await host.RunAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"💥 Krytyczny błąd aplikacji: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
                Environment.Exit(1);
            }
        }

        /// <summary>
        /// Konfiguracja Host Builder z Dependency Injection
        /// </summary>
        static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureServices((context, services) =>
                {
                    // Konfiguracja wszystkich serwisów przez DependencyConfig
                    DependencyConfig.ConfigureServices(services, context.Configuration);
                    
                    // Rejestracja głównego Hosted Service
                    services.AddHostedService<ShopBotHostedService>();
                })
                .ConfigureLogging(logging =>
                {
                    logging.ClearProviders();
                    logging.AddConsole();
                    logging.SetMinimumLevel(LogLevel.Information);
                })
                .UseConsoleLifetime();

        /// <summary>
        /// Obsługa poleceń CLI (delete, add, analyze, verify, test-dropbox)
        /// </summary>
        static async Task HandleCliCommands(string[] args)
        {
            Console.WriteLine($"[CLI] 🔧 Wykonuję polecenie: {string.Join(" ", args)}");

            // Special database test command
            if (args.Length > 0 && args[0].ToLower() == "test-db")
            {
                await DatabaseTest.TestDatabaseAsync();
                return;
            }

            // Tymczasowa konfiguracja serwisów dla CLI
            var services = new ServiceCollection();
            var configuration = new ConfigurationBuilder()
                .SetBasePath(System.IO.Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            DependencyConfig.ConfigureServices(services, configuration);
            var serviceProvider = services.BuildServiceProvider();

            // Deleguj do CLI Handler Service
            var cliHandler = serviceProvider.GetRequiredService<ICliCommandHandler>();
            await cliHandler.HandleCommandAsync(args);

            Console.WriteLine("[CLI] ✅ Polecenie wykonane");
        }
    }
}
