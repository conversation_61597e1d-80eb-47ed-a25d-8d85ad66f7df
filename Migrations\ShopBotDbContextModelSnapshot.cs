﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using ShopBot.Data;

#nullable disable

namespace ShopBotManager.Migrations
{
    [DbContext(typeof(ShopBotDbContext))]
    partial class ShopBotDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            modelBuilder.Entity("ShopBot.Models.ErrorLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ErrorMessage")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("ErrorType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("GameName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<Guid?>("OfferId")
                        .HasColumnType("char(36)");

                    b.Property<string>("SourceUrl")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<string>("StackTrace")
                        .HasColumnType("longtext");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("ErrorType");

                    b.HasIndex("GameName");

                    b.HasIndex("OfferId");

                    b.ToTable("ErrorLogs");
                });

            modelBuilder.Entity("ShopBot.Models.ImageUpload", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("DropboxUrl")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("longtext");

                    b.Property<long?>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<string>("ImgurUrl")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<Guid>("OfferId")
                        .HasColumnType("char(36)");

                    b.Property<string>("OriginalUrl")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<string>("UploadStatus")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<DateTime?>("UploadedAt")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("OfferId");

                    b.HasIndex("UploadStatus");

                    b.ToTable("ImageUploads");
                });

            modelBuilder.Entity("ShopBot.Models.Offer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<decimal>("ConvertedPrice")
                        .HasPrecision(10, 2)
                        .HasColumnType("decimal(10,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Currency")
                        .HasMaxLength(10)
                        .HasColumnType("varchar(10)");

                    b.Property<string>("Description")
                        .HasColumnType("longtext");

                    b.Property<string>("FailureReason")
                        .HasColumnType("longtext");

                    b.Property<string>("FunPayOfferId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("G2GOfferId")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("G2GUrl")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<string>("GameName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<int>("ImageCount")
                        .HasColumnType("int");

                    b.Property<string>("ImageUrls")
                        .HasColumnType("longtext");

                    b.Property<DateTime?>("LastAttemptAt")
                        .HasColumnType("datetime(6)");

                    b.Property<decimal>("OriginalPrice")
                        .HasPrecision(10, 2)
                        .HasColumnType("decimal(10,2)");

                    b.Property<DateTime>("ProcessedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("PublishedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("SourceUrl")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("FunPayOfferId")
                        .IsUnique();

                    b.HasIndex("Status");

                    b.HasIndex("GameName", "ProcessedAt");

                    b.ToTable("Offers");
                });

            modelBuilder.Entity("ShopBot.Models.ProcessingStat", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<int?>("AverageProcessingTimeMs")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("ErrorCount")
                        .HasColumnType("int");

                    b.Property<string>("GameName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("LastError")
                        .HasColumnType("longtext");

                    b.Property<int>("OffersFailed")
                        .HasColumnType("int");

                    b.Property<int>("OffersProcessed")
                        .HasColumnType("int");

                    b.Property<int>("OffersPublished")
                        .HasColumnType("int");

                    b.Property<int>("OffersScanned")
                        .HasColumnType("int");

                    b.Property<int>("OffersSkipped")
                        .HasColumnType("int");

                    b.Property<long?>("TotalProcessingTimeMs")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("Date");

                    b.HasIndex("Date", "GameName")
                        .IsUnique();

                    b.ToTable("ProcessingStats");
                });

            modelBuilder.Entity("ShopBot.Models.ErrorLog", b =>
                {
                    b.HasOne("ShopBot.Models.Offer", "Offer")
                        .WithMany("ErrorLogs")
                        .HasForeignKey("OfferId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Offer");
                });

            modelBuilder.Entity("ShopBot.Models.ImageUpload", b =>
                {
                    b.HasOne("ShopBot.Models.Offer", "Offer")
                        .WithMany("ImageUploads")
                        .HasForeignKey("OfferId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Offer");
                });

            modelBuilder.Entity("ShopBot.Models.Offer", b =>
                {
                    b.Navigation("ErrorLogs");

                    b.Navigation("ImageUploads");
                });
#pragma warning restore 612, 618
        }
    }
}
