using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using System.Text.Json.Nodes;

namespace ImgurUploader
{
    public class ImgurTokenManager
    {
        private readonly ImgurAuthService _authService;
        private readonly string _configPath;
        private string _accessToken;
        private string _refreshToken;
        private DateTime _tokenExpiryTime;
        private const int TOKEN_REFRESH_DAYS = 3; // Odświeżaj co 3 dni

        public ImgurTokenManager(ImgurAuthService authService, string configPath)
        {
            _authService = authService;
            _configPath = configPath;
        }

        public string AccessToken => _accessToken;

        public async Task InitializeAsync()
        {
            var jsonString = await File.ReadAllTextAsync(_configPath);
            var jsonObject = JsonNode.Parse(jsonString).AsObject();
            var refreshTokenNode = jsonObject["Imgur"]["refreshToken"];
            _refreshToken = refreshTokenNode?.GetValue<string>();

            if (string.IsNullOrEmpty(_refreshToken))
            {
                await RequestInitialAuthorizationAsync();
            }
            else
            {
                await RefreshAccessTokenAsync();
            }
        }

        private async Task RequestInitialAuthorizationAsync()
        {
            Console.WriteLine("Wymagana autoryzacja Imgur.");
            Console.WriteLine($"Otwórz ten URL w przeglądarce: {_authService.GetAuthorizationUrl()}");
            Console.WriteLine("Wprowadź otrzymany PIN:");
            string pin = Console.ReadLine();

            var authResponse = await _authService.GetAccessTokenWithPinAsync(pin);
            _accessToken = authResponse.AccessToken;
            _refreshToken = authResponse.RefreshToken;
            _tokenExpiryTime = DateTime.UtcNow.AddDays(TOKEN_REFRESH_DAYS);

            await SaveRefreshTokenAsync();
            Console.WriteLine($"[Imgur] Token zostanie odświeżony za {TOKEN_REFRESH_DAYS} dni");
        }

        private async Task RefreshAccessTokenAsync()
        {
            try
            {
                var authResponse = await _authService.RefreshAccessTokenAsync(_refreshToken);
                _accessToken = authResponse.AccessToken;
                _refreshToken = authResponse.RefreshToken;
                _tokenExpiryTime = DateTime.UtcNow.AddDays(TOKEN_REFRESH_DAYS);

                await SaveRefreshTokenAsync();
                Console.WriteLine($"[Imgur] Token został odświeżony. Następne odświeżenie za {TOKEN_REFRESH_DAYS} dni");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[Imgur] Błąd odświeżania tokena: {ex.Message}");
                await RequestInitialAuthorizationAsync();
            }
        }

        private async Task SaveRefreshTokenAsync()
        {
            try
            {
                var jsonString = await File.ReadAllTextAsync(_configPath);
                var jsonObject = JsonSerializer.Deserialize<JsonNode>(jsonString);
                var imgurObject = jsonObject["Imgur"].AsObject();

                // Zachowaj istniejące wartości
                var clientId = imgurObject["ClientId"]?.GetValue<string>();
                var clientSecret = imgurObject["clientSecret"]?.GetValue<string>();

                // Utwórz nowy obiekt z zachowanymi wartościami i nowym tokenem
                var newImgurObject = new JsonObject
                {
                    ["ClientId"] = clientId,
                    ["clientSecret"] = clientSecret,
                    ["refreshToken"] = _refreshToken
                };

                jsonObject["Imgur"] = newImgurObject;

                // Zapisz z wcięciami dla czytelności
                var options = new JsonSerializerOptions 
                { 
                    WriteIndented = true 
                };

                var updatedJson = JsonSerializer.Serialize(jsonObject, options);
                await File.WriteAllTextAsync(_configPath, updatedJson);
                
                Console.WriteLine($"[Imgur] Zapisano refresh token w {_configPath}");
                Console.WriteLine($"[Imgur] Następne odświeżenie tokena za {TOKEN_REFRESH_DAYS} dni");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[Imgur] ❌ Błąd podczas zapisywania refresh tokena: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"[Imgur] ❌ Inner exception: {ex.InnerException.Message}");
                }
                throw;
            }
        }

        public async Task EnsureValidTokenAsync()
        {
            if (DateTime.UtcNow >= _tokenExpiryTime.AddHours(-1))
            {
                await RefreshAccessTokenAsync();
            }
        }
    }
}
