{"TotalOffers": 557, "AcceptedOffers": 64, "RejectedOffers": 493, "RejectionReasons": {"Cena ponizej minimum (3,22 < 15)": 3, "Cena powyzej maximum (190,52 > 99)": 1, "Cena powyzej maximum (322,41 > 99)": 1, "Cena ponizej minimum (10,02 < 15)": 1, "Cena ponizej minimum (8,59 < 15)": 1, "Duplikat oferty": 34, "Cena ponizej minimum (1,26 < 15)": 1, "Cena ponizej minimum (0,26 < 15)": 4, "Cena ponizej minimum (0,06 < 15)": 1, "Cena ponizej minimum (0,91 < 15)": 1, "Cena ponizej minimum (3,86 < 15)": 1, "Cena powyzej maximum (103,17 > 99)": 6, "Cena ponizej minimum (0,56 < 15)": 15, "Cena ponizej minimum (0,22 < 15)": 2, "Cena powyzej maximum (109,62 > 99)": 4, "Cena ponizej minimum (5,80 < 15)": 3, "Cena ponizej minimum (0,32 < 15)": 2, "Ocena ponizej minimum (0 < 5)": 28, "Cena ponizej minimum (1,68 < 15)": 1, "Cena ponizej minimum (4,64 < 15)": 1, "Cena ponizej minimum (1,29 < 15)": 16, "Cena ponizej minimum (2,45 < 15)": 1, "Cena powyzej maximum (206,34 > 99)": 3, "Cena ponizej minimum (0,21 < 15)": 2, "Cena ponizej minimum (0,20 < 15)": 1, "Cena ponizej minimum (0,19 < 15)": 4, "Cena ponizej minimum (0,18 < 15)": 1, "Cena ponizej minimum (0,17 < 15)": 1, "Cena ponizej minimum (0,16 < 15)": 1, "Cena ponizej minimum (0,15 < 15)": 1, "Cena ponizej minimum (0,14 < 15)": 1, "Cena ponizej minimum (0,13 < 15)": 1, "Cena ponizej minimum (0,12 < 15)": 1, "Cena ponizej minimum (0,11 < 15)": 1, "Cena ponizej minimum (6,45 < 15)": 6, "Cena ponizej minimum (1,12 < 15)": 4, "Cena powyzej maximum (1121,96 > 99)": 1, "Cena powyzej maximum (193,44 > 99)": 7, "Cena ponizej minimum (0,90 < 15)": 2, "Cena ponizej minimum (0,28 < 15)": 2, "Cena ponizej minimum (0,10 < 15)": 2, "Cena powyzej maximum (135,27 > 99)": 1, "Cena powyzej maximum (112,72 > 99)": 1, "Cena powyzej maximum (180,55 > 99)": 8, "Cena ponizej minimum (14,19 < 15)": 1, "Cena ponizej minimum (12,90 < 15)": 7, "Ocena ponizej minimum (4 < 5)": 24, "Cena ponizej minimum (14,33 < 15)": 1, "Cena ponizej minimum (12,88 < 15)": 2, "Cena ponizej minimum (3,07 < 15)": 1, "Cena ponizej minimum (0,98 < 15)": 5, "Cena ponizej minimum (0,64 < 15)": 3, "Cena powyzej maximum (386,88 > 99)": 5, "Cena ponizej minimum (0,45 < 15)": 3, "Cena powyzej maximum (100,86 > 99)": 1, "Cena ponizej minimum (1,03 < 15)": 7, "Cena ponizej minimum (0,33 < 15)": 1, "Cena ponizej minimum (4,48 < 15)": 1, "Cena ponizej minimum (4,51 < 15)": 3, "Cena ponizej minimum (3,87 < 15)": 6, "Cena ponizej minimum (10,32 < 15)": 6, "Cena ponizej minimum (1,61 < 15)": 3, "Cena ponizej minimum (5,32 < 15)": 11, "Cena ponizej minimum (10,65 < 15)": 4, "Cena ponizej minimum (0,46 < 15)": 4, "Cena ponizej minimum (2,58 < 15)": 2, "Cena ponizej minimum (8,66 < 15)": 3, "Cena ponizej minimum (7,74 < 15)": 4, "Cena powyzej maximum (199,89 > 99)": 1, "Cena ponizej minimum (1,83 < 15)": 1, "Cena ponizej minimum (5,16 < 15)": 2, "Cena ponizej minimum (14,82 < 15)": 9, "Cena powyzej maximum (104,12 > 99)": 1, "Cena powyzej maximum (103,22 > 99)": 1, "Cena powyzej maximum (103,82 > 99)": 1, "Cena powyzej maximum (223,05 > 99)": 1, "Cena powyzej maximum (360,92 > 99)": 1, "Cena powyzej maximum (102,03 > 99)": 1, "Cena powyzej maximum (957,70 > 99)": 1, "Cena powyzej maximum (101,74 > 99)": 2, "Cena powyzej maximum (104,41 > 99)": 2, "Cena powyzej maximum (101,11 > 99)": 1, "Cena powyzej maximum (100,18 > 99)": 1, "Cena powyzej maximum (102,01 > 99)": 1, "Cena powyzej maximum (187,86 > 99)": 1, "Cena powyzej maximum (348,19 > 99)": 2, "Cena powyzej maximum (257,92 > 99)": 5, "Cena ponizej minimum (8,38 < 15)": 4, "Cena ponizej minimum (1,14 < 15)": 1, "Cena powyzej maximum (203,76 > 99)": 1, "Cena powyzej maximum (116,06 > 99)": 2, "Cena powyzej maximum (580,32 > 99)": 1, "Cena ponizej minimum (0,89 < 15)": 1, "Cena powyzej maximum (206,32 > 99)": 1, "Cena ponizej minimum (4,26 < 15)": 2, "Cena ponizej minimum (1,07 < 15)": 1, "Cena ponizej minimum (11,61 < 15)": 3, "Cena ponizej minimum (1,02 < 15)": 2, "Cena ponizej minimum (2,57 < 15)": 2, "Cena ponizej minimum (1,97 < 15)": 1, "Cena ponizej minimum (0,01 < 15)": 2, "Cena ponizej minimum (2,46 < 15)": 1, "Cena powyzej maximum (154,75 > 99)": 3, "Cena powyzej maximum (103,81 > 99)": 1, "Cena powyzej maximum (908,36 > 99)": 1, "Cena powyzej maximum (288,37 > 99)": 1, "Cena powyzej maximum (259,53 > 99)": 2, "Cena powyzej maximum (389,30 > 99)": 1, "Cena powyzej maximum (233,58 > 99)": 3, "Cena powyzej maximum (144,18 > 99)": 1, "Cena powyzej maximum (350,37 > 99)": 1, "Cena powyzej maximum (220,60 > 99)": 2, "Cena powyzej maximum (324,42 > 99)": 1, "Cena powyzej maximum (298,46 > 99)": 1, "Cena powyzej maximum (331,62 > 99)": 1, "Cena powyzej maximum (596,93 > 99)": 1, "Cena ponizej minimum (1,11 < 15)": 1, "Cena ponizej minimum (1,30 < 15)": 1, "Cena ponizej minimum (1,43 < 15)": 1, "Cena powyzej maximum (141,86 > 99)": 3, "Cena ponizej minimum (10,96 < 15)": 1, "Cena ponizej minimum (10,77 < 15)": 1, "Cena ponizej minimum (5,42 < 15)": 1, "Cena powyzej maximum (158,65 > 99)": 1, "Cena powyzej maximum (244,89 > 99)": 1, "Cena powyzej maximum (372,66 > 99)": 1, "Cena powyzej maximum (112,06 > 99)": 1, "Cena powyzej maximum (1418,57 > 99)": 1, "Cena powyzej maximum (122,98 > 99)": 1, "Cena powyzej maximum (1289,61 > 99)": 3, "Cena powyzej maximum (336,21 > 99)": 1, "Cena powyzej maximum (1160,65 > 99)": 1, "Cena powyzej maximum (644,80 > 99)": 2, "Cena powyzej maximum (213,34 > 99)": 1, "Cena ponizej minimum (2,84 < 15)": 1, "Cena powyzej maximum (106,47 > 99)": 1, "Cena powyzej maximum (322,40 > 99)": 2, "Cena powyzej maximum (122,51 > 99)": 1, "Cena ponizej minimum (9,53 < 15)": 1, "Cena powyzej maximum (186,99 > 99)": 1, "Cena powyzej maximum (477,16 > 99)": 1, "Cena powyzej maximum (167,65 > 99)": 2, "Cena powyzej maximum (161,20 > 99)": 1, "Cena powyzej maximum (1031,69 > 99)": 3, "Cena powyzej maximum (219,23 > 99)": 1, "Cena powyzej maximum (128,96 > 99)": 7, "Cena ponizej minimum (1,93 < 15)": 2, "Cena ponizej minimum (0,97 < 15)": 1, "Cena ponizej minimum (5,29 < 15)": 1, "Cena ponizej minimum (7,09 < 15)": 1, "Cena powyzej maximum (257,91 > 99)": 1, "Cena powyzej maximum (212,95 > 99)": 1, "Cena powyzej maximum (134,12 > 99)": 1, "Cena powyzej maximum (283,56 > 99)": 1, "Cena powyzej maximum (335,30 > 99)": 1, "Cena ponizej minimum (0,08 < 15)": 1, "Ocena ponizej minimum (1 < 5)": 1, "Cena powyzej maximum (165,44 > 99)": 1, "Cena powyzej maximum (319,42 > 99)": 3, "Cena powyzej maximum (368,95 > 99)": 1, "Cena ponizej minimum (9,03 < 15)": 3, "Cena ponizej minimum (4,92 < 15)": 1, "Cena ponizej minimum (8,61 < 15)": 1, "Cena ponizej minimum (6,15 < 15)": 1, "Cena powyzej maximum (211,88 > 99)": 1, "Cena powyzej maximum (254,47 > 99)": 1, "Cena powyzej maximum (339,65 > 99)": 1, "Cena powyzej maximum (392,89 > 99)": 1, "Cena powyzej maximum (228,92 > 99)": 1, "Cena powyzej maximum (424,83 > 99)": 1, "Cena powyzej maximum (190,59 > 99)": 1, "Cena powyzej maximum (275,77 > 99)": 1, "Cena powyzej maximum (137,35 > 99)": 1, "Cena powyzej maximum (126,70 > 99)": 1, "Cena powyzej maximum (175,68 > 99)": 2, "Cena powyzej maximum (148,00 > 99)": 1, "Cena powyzej maximum (318,36 > 99)": 1, "Cena powyzej maximum (532,37 > 99)": 1, "Cena powyzej maximum (286,42 > 99)": 1, "Cena powyzej maximum (165,03 > 99)": 1, "Cena powyzej maximum (108,33 > 99)": 1, "Cena powyzej maximum (334,01 > 99)": 1, "Cena powyzej maximum (309,51 > 99)": 1, "Cena ponizej minimum (3,61 < 15)": 1, "Cena powyzej maximum (838,25 > 99)": 1, "Cena powyzej maximum (532,36 > 99)": 1, "Cena powyzej maximum (541,64 > 99)": 1, "Cena powyzej maximum (291,87 > 99)": 1, "Cena powyzej maximum (515,84 > 99)": 3, "Cena powyzej maximum (614,92 > 99)": 1, "Cena powyzej maximum (173,55 > 99)": 1, "Cena ponizej minimum (12,25 < 15)": 1, "Cena powyzej maximum (593,22 > 99)": 1, "Cena powyzej maximum (388,06 > 99)": 1, "Cena powyzej maximum (148,31 > 99)": 1, "Cena powyzej maximum (245,03 > 99)": 1, "Cena powyzej maximum (232,13 > 99)": 2, "Cena powyzej maximum (149,06 > 99)": 1, "Cena powyzej maximum (167,64 > 99)": 1, "Cena powyzej maximum (554,53 > 99)": 1, "Cena powyzej maximum (166,03 > 99)": 1, "Cena ponizej minimum (0,77 < 15)": 1, "Cena powyzej maximum (111,80 > 99)": 1, "Cena ponizej minimum (0,02 < 15)": 2, "Cena powyzej maximum (307,46 > 99)": 1, "Ocena ponizej minimum (3 < 5)": 1, "Cena powyzej maximum (683,52 > 99)": 1, "Cena ponizej minimum (0,71 < 15)": 1, "Cena ponizej minimum (9,67 < 15)": 1, "Cena ponizej minimum (1,69 < 15)": 1, "Cena powyzej maximum (773,77 > 99)": 1, "Cena powyzej maximum (249,52 > 99)": 1, "Cena powyzej maximum (533,89 > 99)": 1, "Cena powyzej maximum (537,29 > 99)": 1, "Cena powyzej maximum (902,73 > 99)": 1, "Cena ponizej minimum (7,85 < 15)": 1}, "GenerationTime": "2025-06-14T23:05:32.0801616+02:00"}