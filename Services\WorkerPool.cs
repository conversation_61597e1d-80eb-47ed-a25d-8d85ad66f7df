using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;
using ShopBot.Services;
using ShopBot.Tasks;

namespace ShopBot.Services
{
    public class WorkerPool
    {
        private readonly string _poolName;
        private readonly int _maxWorkers;
        private readonly LoggerService _logger;
        private readonly ConcurrentQueue<BaseTask> _taskQueue = new();
        private readonly SemaphoreSlim _semaphore;
        private readonly CancellationTokenSource _cancellationTokenSource = new();
        private int _activeWorkers = 0;
        private bool _isRunning = true;

        public WorkerPool(string poolName, int maxWorkers, LoggerService logger)
        {
            _poolName = poolName;
            _maxWorkers = maxWorkers;
            _logger = logger;
            _semaphore = new SemaphoreSlim(maxWorkers, maxWorkers);
            
            // Uruchom workerów
            for (int i = 0; i < maxWorkers; i++)
            {
                _ = Task.Run(WorkerLoop);
            }
        }

        public void EnqueueTask(BaseTask task)
        {
            if (!_isRunning)
                return;

            _taskQueue.Enqueue(task);
            _logger.Log($"[{_poolName}] Dodano zadanie do kolejki: {task.GetType().Name} (ID: {task.Id})");
        }

        private async Task WorkerLoop()
        {
            while (_isRunning && !_cancellationTokenSource.Token.IsCancellationRequested)
            {
                try
                {
                    if (_taskQueue.TryDequeue(out var task))
                    {
                        await _semaphore.WaitAsync(_cancellationTokenSource.Token);
                        
                        try
                        {
                            Interlocked.Increment(ref _activeWorkers);
                            _logger.Log($"[{_poolName}] Rozpoczynam zadanie: {task.GetType().Name} (ID: {task.Id}), Aktywni: {_activeWorkers}/{_maxWorkers}");
                            
                            await task.ExecuteAsync(_cancellationTokenSource.Token);
                            
                            _logger.Log($"[{_poolName}] Zakończono zadanie: {task.GetType().Name} (ID: {task.Id})");
                        }
                        catch (Exception ex)
                        {
                            _logger.Log($"[{_poolName}] Błąd wykonania zadania {task.Id}: {ex.Message}", LoggerService.LogLevel.Error);
                        }
                        finally
                        {
                            Interlocked.Decrement(ref _activeWorkers);
                            _logger.Log($"[{_poolName}] Zwolniono workera. ID zadania: {task.Id}, Aktywni: {_activeWorkers}/{_maxWorkers}");
                            _semaphore.Release();
                        }
                    }
                    else
                    {
                        await Task.Delay(1000, _cancellationTokenSource.Token);
                    }
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    _logger.Log($"[{_poolName}] Błąd w pętli workera: {ex.Message}", LoggerService.LogLevel.Error);
                    await Task.Delay(5000);
                }
            }
        }

        public void Stop()
        {
            _isRunning = false;
            _cancellationTokenSource.Cancel();
            _logger.Log($"[{_poolName}] Zatrzymano pool workerów");
        }

        public async Task WaitForCompletion()
        {
            while (_activeWorkers > 0 || !_taskQueue.IsEmpty)
            {
                await Task.Delay(1000);
            }
        }

        public int GetActiveWorkers() => _activeWorkers;
        public int GetQueueLength() => _taskQueue.Count;
    }


}
