{"TotalOffers": 554, "AcceptedOffers": 68, "RejectedOffers": 486, "RejectionReasons": {"Cena ponizej minimum (3,92 < 15)": 7, "Cena ponizej minimum (0,26 < 15)": 3, "Cena ponizej minimum (0,20 < 15)": 4, "Cena powyzej maximum (209,16 > 99)": 4, "Ocena ponizej minimum (4 < 5)": 30, "Cena ponizej minimum (0,57 < 15)": 15, "Cena ponizej minimum (1,14 < 15)": 4, "Cena ponizej minimum (11,77 < 15)": 4, "Cena ponizej minimum (10,46 < 15)": 6, "Cena ponizej minimum (8,50 < 15)": 2, "Cena ponizej minimum (11,11 < 15)": 1, "Cena powyzej maximum (130,73 > 99)": 6, "Cena ponizej minimum (9,66 < 15)": 1, "Cena ponizej minimum (0,23 < 15)": 1, "Cena ponizej minimum (0,22 < 15)": 2, "Cena ponizej minimum (0,21 < 15)": 2, "Duplikat oferty": 33, "Cena ponizej minimum (0,19 < 15)": 1, "Cena ponizej minimum (0,18 < 15)": 1, "Cena ponizej minimum (0,17 < 15)": 1, "Cena ponizej minimum (0,16 < 15)": 1, "Cena ponizej minimum (0,15 < 15)": 1, "Cena ponizej minimum (0,14 < 15)": 1, "Cena ponizej minimum (0,12 < 15)": 1, "Cena ponizej minimum (0,11 < 15)": 1, "Cena ponizej minimum (0,13 < 15)": 1, "Cena ponizej minimum (0,10 < 15)": 2, "Cena ponizej minimum (11,71 < 15)": 1, "Cena ponizej minimum (11,73 < 15)": 1, "Cena ponizej minimum (11,74 < 15)": 1, "Cena ponizej minimum (11,75 < 15)": 1, "Cena ponizej minimum (1,16 < 15)": 1, "Cena ponizej minimum (0,92 < 15)": 2, "Cena ponizej minimum (1,31 < 15)": 15, "Cena ponizej minimum (3,27 < 15)": 4, "Cena ponizej minimum (0,46 < 15)": 4, "Cena ponizej minimum (0,65 < 15)": 2, "Cena ponizej minimum (1,05 < 15)": 8, "Cena ponizej minimum (1,09 < 15)": 1, "Cena powyzej maximum (196,09 > 99)": 6, "Cena powyzej maximum (457,54 > 99)": 1, "Cena ponizej minimum (1,63 < 15)": 2, "Cena ponizej minimum (13,07 < 15)": 10, "Cena ponizej minimum (14,52 < 15)": 2, "Cena ponizej minimum (13,06 < 15)": 1, "Cena powyzej maximum (104,58 > 99)": 4, "Cena powyzej maximum (261,45 > 99)": 5, "Cena powyzej maximum (183,02 > 99)": 8, "Cena powyzej maximum (193,13 > 99)": 1, "Cena powyzej maximum (326,82 > 99)": 4, "Cena ponizej minimum (1,28 < 15)": 1, "Cena ponizej minimum (0,07 < 15)": 1, "Ocena ponizej minimum (0 < 5)": 36, "Cena ponizej minimum (6,54 < 15)": 6, "Cena powyzej maximum (111,12 > 99)": 2, "Cena powyzej maximum (102,24 > 99)": 1, "Cena powyzej maximum (190,43 > 99)": 1, "Cena ponizej minimum (0,32 < 15)": 1, "Cena ponizej minimum (2,47 < 15)": 3, "Cena ponizej minimum (0,28 < 15)": 1, "Cena powyzej maximum (135,27 > 99)": 1, "Cena powyzej maximum (112,72 > 99)": 1, "Cena ponizej minimum (0,47 < 15)": 3, "Cena ponizej minimum (5,23 < 15)": 4, "Cena ponizej minimum (2,46 < 15)": 1, "Cena ponizej minimum (3,08 < 15)": 1, "Cena ponizej minimum (0,98 < 15)": 5, "Cena ponizej minimum (1,97 < 15)": 1, "Cena ponizej minimum (0,01 < 15)": 2, "Cena powyzej maximum (206,55 > 99)": 1, "Cena ponizej minimum (4,54 < 15)": 1, "Cena ponizej minimum (4,58 < 15)": 2, "Cena ponizej minimum (1,83 < 15)": 1, "Cena ponizej minimum (4,44 < 15)": 2, "Cena ponizej minimum (1,57 < 15)": 1, "Cena ponizej minimum (2,84 < 15)": 1, "Cena ponizej minimum (5,88 < 15)": 2, "Cena ponizej minimum (8,66 < 15)": 3, "Cena ponizej minimum (2,88 < 15)": 1, "Cena powyzej maximum (103,27 > 99)": 2, "Cena powyzej maximum (124,19 > 99)": 1, "Cena ponizej minimum (1,04 < 15)": 1, "Cena ponizej minimum (0,34 < 15)": 1, "Cena ponizej minimum (0,29 < 15)": 1, "Cena powyzej maximum (1137,32 > 99)": 1, "Cena ponizej minimum (1,12 < 15)": 1, "Cena ponizej minimum (1,32 < 15)": 1, "Cena ponizej minimum (1,45 < 15)": 1, "Cena ponizej minimum (5,32 < 15)": 3, "Cena powyzej maximum (222,23 > 99)": 1, "Cena ponizej minimum (10,92 < 15)": 1, "Cena ponizej minimum (5,49 < 15)": 1, "Cena powyzej maximum (588,27 > 99)": 2, "Cena powyzej maximum (113,59 > 99)": 1, "Cena powyzej maximum (105,54 > 99)": 1, "Cena powyzej maximum (104,64 > 99)": 1, "Cena powyzej maximum (105,24 > 99)": 1, "Cena powyzej maximum (226,11 > 99)": 1, "Cena powyzej maximum (365,86 > 99)": 1, "Cena powyzej maximum (103,43 > 99)": 1, "Cena powyzej maximum (970,81 > 99)": 1, "Cena powyzej maximum (103,13 > 99)": 2, "Cena powyzej maximum (105,84 > 99)": 2, "Cena powyzej maximum (102,49 > 99)": 1, "Cena powyzej maximum (101,56 > 99)": 1, "Cena powyzej maximum (103,40 > 99)": 1, "Cena powyzej maximum (117,65 > 99)": 2, "Cena powyzej maximum (165,44 > 99)": 1, "Cena powyzej maximum (319,42 > 99)": 2, "Cena ponizej minimum (14,38 < 15)": 1, "Cena powyzej maximum (585,61 > 99)": 1, "Cena ponizej minimum (7,19 < 15)": 3, "Cena powyzej maximum (105,23 > 99)": 1, "Cena powyzej maximum (920,80 > 99)": 1, "Cena powyzej maximum (292,31 > 99)": 1, "Cena powyzej maximum (263,09 > 99)": 2, "Cena powyzej maximum (394,63 > 99)": 1, "Cena powyzej maximum (236,78 > 99)": 3, "Cena powyzej maximum (146,16 > 99)": 1, "Cena powyzej maximum (355,17 > 99)": 1, "Cena powyzej maximum (223,62 > 99)": 2, "Cena powyzej maximum (328,86 > 99)": 1, "Cena powyzej maximum (302,55 > 99)": 1, "Cena powyzej maximum (336,16 > 99)": 1, "Cena powyzej maximum (605,10 > 99)": 1, "Cena powyzej maximum (483,69 > 99)": 1, "Cena powyzej maximum (300,67 > 99)": 1, "Cena powyzej maximum (158,65 > 99)": 1, "Cena ponizej minimum (0,90 < 15)": 1, "Cena ponizej minimum (9,15 < 15)": 3, "Cena powyzej maximum (244,89 > 99)": 1, "Cena powyzej maximum (404,60 > 99)": 1, "Cena ponizej minimum (10,65 < 15)": 2, "Cena ponizej minimum (4,26 < 15)": 2, "Cena powyzej maximum (163,41 > 99)": 1, "Cena powyzej maximum (653,63 > 99)": 3, "Cena ponizej minimum (7,84 < 15)": 1, "Cena powyzej maximum (202,63 > 99)": 2, "Cena ponizej minimum (1,85 < 15)": 1, "Cena ponizej minimum (1,96 < 15)": 1, "Cena powyzej maximum (352,96 > 99)": 2, "Cena powyzej maximum (123,09 > 99)": 1, "Cena powyzej maximum (392,18 > 99)": 6, "Cena powyzej maximum (1307,26 > 99)": 3, "Cena powyzej maximum (340,82 > 99)": 1, "Cena ponizej minimum (1,03 < 15)": 2, "Cena ponizej minimum (2,60 < 15)": 2, "Cena powyzej maximum (143,80 > 99)": 3, "Cena powyzej maximum (1176,54 > 99)": 1, "Cena powyzej maximum (106,47 > 99)": 1, "Cena powyzej maximum (169,94 > 99)": 2, "Cena powyzej maximum (248,38 > 99)": 2, "Cena powyzej maximum (212,95 > 99)": 1, "Cena powyzej maximum (287,44 > 99)": 1, "Cena ponizej minimum (0,33 < 15)": 1, "Cena powyzej maximum (339,89 > 99)": 1, "Ocena ponizej minimum (1 < 5)": 1, "Cena ponizej minimum (4,92 < 15)": 1, "Cena ponizej minimum (8,62 < 15)": 1, "Cena ponizej minimum (6,15 < 15)": 1, "Cena powyzej maximum (211,88 > 99)": 1, "Cena powyzej maximum (254,47 > 99)": 1, "Cena powyzej maximum (339,65 > 99)": 1, "Cena powyzej maximum (392,89 > 99)": 1, "Cena powyzej maximum (228,92 > 99)": 1, "Cena powyzej maximum (424,83 > 99)": 1, "Cena powyzej maximum (190,59 > 99)": 1, "Cena powyzej maximum (275,77 > 99)": 1, "Cena powyzej maximum (137,35 > 99)": 1, "Cena powyzej maximum (126,70 > 99)": 1, "Cena powyzej maximum (175,68 > 99)": 2, "Cena powyzej maximum (148,00 > 99)": 1, "Cena powyzej maximum (318,36 > 99)": 1, "Cena powyzej maximum (532,37 > 99)": 1, "Cena powyzej maximum (286,42 > 99)": 1, "Cena powyzej maximum (165,03 > 99)": 1, "Cena powyzej maximum (169,29 > 99)": 1, "Cena powyzej maximum (329,00 > 99)": 1, "Cena powyzej maximum (431,40 > 99)": 1, "Cena powyzej maximum (338,58 > 99)": 1, "Cena powyzej maximum (313,74 > 99)": 1, "Cena powyzej maximum (849,72 > 99)": 1, "Cena ponizej minimum (0,39 < 15)": 1, "Cena powyzej maximum (266,19 > 99)": 1, "Cena powyzej maximum (532,36 > 99)": 1, "Cena powyzej maximum (549,05 > 99)": 1, "Cena powyzej maximum (291,87 > 99)": 1, "Cena powyzej maximum (522,90 > 99)": 2, "Cena powyzej maximum (615,43 > 99)": 1, "Cena powyzej maximum (212,18 > 99)": 1, "Cena ponizej minimum (12,42 < 15)": 1, "Cena powyzej maximum (601,34 > 99)": 1, "Cena powyzej maximum (393,37 > 99)": 1, "Cena powyzej maximum (150,34 > 99)": 1, "Cena powyzej maximum (231,29 > 99)": 1, "Cena ponizej minimum (2,61 < 15)": 1, "Cena ponizej minimum (4,31 < 15)": 1, "Cena powyzej maximum (149,06 > 99)": 1, "Cena ponizej minimum (5,54 < 15)": 1, "Cena powyzej maximum (562,12 > 99)": 1, "Cena powyzej maximum (141,55 > 99)": 1, "Cena ponizej minimum (0,78 < 15)": 1, "Cena powyzej maximum (784,36 > 99)": 3, "Cena ponizej minimum (0,02 < 15)": 2, "Cena powyzej maximum (159,71 > 99)": 1, "Cena powyzej maximum (258,84 > 99)": 1, "Cena powyzej maximum (307,71 > 99)": 1, "Cena powyzej maximum (418,32 > 99)": 1, "Ocena ponizej minimum (3 < 5)": 1, "Cena powyzej maximum (692,88 > 99)": 1, "Cena powyzej maximum (1045,81 > 99)": 1, "Cena ponizej minimum (9,80 < 15)": 1, "Cena ponizej minimum (1,71 < 15)": 1, "Cena powyzej maximum (235,31 > 99)": 1, "Cena powyzej maximum (249,73 > 99)": 1, "Cena powyzej maximum (915,08 > 99)": 1, "Cena ponizej minimum (7,96 < 15)": 1}, "GenerationTime": "2025-06-17T16:43:01.9461885+02:00"}