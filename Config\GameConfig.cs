using System.Text.Json;
using System.Text.Json.Serialization;

namespace ShopBot.Config
{
    public class GameConfig
    {
        private static Dictionary<string, GameData> _games;
        private static string[] _activeGames;

        static GameConfig()
        {
            LoadConfig();
        }

        private static void LoadConfig(string path = "gamesconfig.json")
        {
            if (!File.Exists(path))
            {
                // Domyślna konfiguracja
                var defaultConfig = new GameConfigData
                {
                    ActiveGames = new[] { "RaidShadowLegends" },
                    GamesData = new Dictionary<string, GameData>
                    {
                        {"RaidShadowLegends", new GameData { Id = "566", PriceRange = new PriceRange(), ListAttributes = new[] { "mhero", "lhero", "ehero", "rhero", "level" } } }
                    }
                };
                SaveConfig(defaultConfig, path);
            }

            var json = File.ReadAllText(path);
            var config = JsonSerializer.Deserialize<GameConfigData>(json);
            
            _games = config?.GamesData ?? new Dictionary<string, GameData>();
            _activeGames = config?.ActiveGames ?? new[] { "RaidShadowLegends" };
        }

        private static void SaveConfig(GameConfigData config, string path)
        {
            var json = JsonSerializer.Serialize(config, new JsonSerializerOptions { WriteIndented = true });
            File.WriteAllText(path, json);
        }

        public static string[] GetActiveGames()
        {
            return _activeGames;
        }

        public static string GetGameId(string gameName)
        {
            if (_games.TryGetValue(gameName, out var gameData))
            {
                return gameData.Id;
            }
            
            throw new Exception($"Game '{gameName}' not found in configuration!");
        }

        public static string GetGameUrl(string gameName)
        {
            var gameId = GetGameId(gameName);
            if (string.IsNullOrEmpty(gameId))
            {
                throw new Exception($"Invalid game ID for game: {gameName}");
            }
            return $"https://funpay.com/en/lots/{gameId}/";
        }

        public static string[] GetGameAttributes(string gameName)
        {
            return _games.TryGetValue(gameName, out var gameData) ? gameData.ListAttributes : Array.Empty<string>();
        }

        public static PriceRange GetGamePriceRange(string gameName)
        {
            return _games.TryGetValue(gameName, out var gameData) 
                ? gameData.PriceRange 
                : new PriceRange();
        }

        private class GameConfigData
        {
            public string[] ActiveGames { get; set; } = Array.Empty<string>();
            public Dictionary<string, GameData> GamesData { get; set; } = new();
        }

        public class GameData
        {
            [JsonPropertyName("id")]
            public string Id { get; set; } = string.Empty;
            
            [JsonPropertyName("priceRange")]
            public PriceRange PriceRange { get; set; } = new();
            
            [JsonPropertyName("listAttributes")]
            public string[] ListAttributes { get; set; } = Array.Empty<string>();
        }

        public class PriceRange
        {
            [JsonPropertyName("min")]
            public decimal Min { get; set; } = 10;
            
            [JsonPropertyName("max")]
            public decimal Max { get; set; } = 200;
        }
    }
} 