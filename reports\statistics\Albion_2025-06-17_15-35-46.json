{"TotalOffers": 558, "AcceptedOffers": 69, "RejectedOffers": 489, "RejectionReasons": {"Cena ponizej minimum (3,93 < 15)": 7, "Cena ponizej minimum (8,51 < 15)": 4, "Duplikat oferty": 34, "Cena powyzej maximum (261,82 > 99)": 5, "Cena powyzej maximum (183,28 > 99)": 9, "Cena powyzej maximum (193,40 > 99)": 1, "Cena powyzej maximum (327,28 > 99)": 4, "Cena ponizej minimum (3,27 < 15)": 4, "Cena ponizej minimum (1,28 < 15)": 1, "Cena ponizej minimum (0,26 < 15)": 3, "Cena ponizej minimum (0,07 < 15)": 1, "Ocena ponizej minimum (0 < 5)": 35, "Cena ponizej minimum (13,09 < 15)": 10, "Cena ponizej minimum (0,46 < 15)": 4, "Cena ponizej minimum (1,31 < 15)": 15, "Cena ponizej minimum (6,55 < 15)": 6, "Cena powyzej maximum (111,27 > 99)": 2, "Cena powyzej maximum (102,39 > 99)": 1, "Cena powyzej maximum (190,70 > 99)": 1, "Cena ponizej minimum (0,32 < 15)": 1, "Cena ponizej minimum (2,47 < 15)": 3, "Ocena ponizej minimum (4 < 5)": 30, "Cena ponizej minimum (0,28 < 15)": 1, "Cena ponizej minimum (0,10 < 15)": 2, "Cena powyzej maximum (135,27 > 99)": 1, "Cena powyzej maximum (112,72 > 99)": 1, "Cena ponizej minimum (0,22 < 15)": 2, "Cena ponizej minimum (0,47 < 15)": 3, "Cena ponizej minimum (5,24 < 15)": 5, "Cena ponizej minimum (2,46 < 15)": 1, "Cena ponizej minimum (1,14 < 15)": 4, "Cena ponizej minimum (3,07 < 15)": 1, "Cena ponizej minimum (0,98 < 15)": 5, "Cena ponizej minimum (1,97 < 15)": 1, "Cena ponizej minimum (0,01 < 15)": 2, "Cena powyzej maximum (206,84 > 99)": 1, "Cena ponizej minimum (4,55 < 15)": 1, "Cena ponizej minimum (4,58 < 15)": 2, "Cena ponizej minimum (1,05 < 15)": 8, "Cena ponizej minimum (1,83 < 15)": 1, "Cena ponizej minimum (4,45 < 15)": 2, "Cena ponizej minimum (1,57 < 15)": 1, "Cena ponizej minimum (5,89 < 15)": 2, "Cena ponizej minimum (0,23 < 15)": 1, "Cena ponizej minimum (0,21 < 15)": 2, "Cena ponizej minimum (0,20 < 15)": 4, "Cena ponizej minimum (0,19 < 15)": 1, "Cena ponizej minimum (0,18 < 15)": 1, "Cena ponizej minimum (0,17 < 15)": 1, "Cena ponizej minimum (0,16 < 15)": 1, "Cena ponizej minimum (0,15 < 15)": 1, "Cena ponizej minimum (0,14 < 15)": 1, "Cena ponizej minimum (0,13 < 15)": 2, "Cena ponizej minimum (0,12 < 15)": 1, "Cena ponizej minimum (0,11 < 15)": 1, "Cena ponizej minimum (8,66 < 15)": 3, "Cena ponizej minimum (9,67 < 15)": 1, "Cena ponizej minimum (0,57 < 15)": 15, "Cena ponizej minimum (2,88 < 15)": 1, "Cena powyzej maximum (103,42 > 99)": 2, "Cena powyzej maximum (124,37 > 99)": 1, "Cena ponizej minimum (1,04 < 15)": 1, "Cena ponizej minimum (0,34 < 15)": 1, "Cena ponizej minimum (1,09 < 15)": 1, "Cena ponizej minimum (0,29 < 15)": 1, "Cena powyzej maximum (1138,92 > 99)": 1, "Cena powyzej maximum (196,37 > 99)": 6, "Cena ponizej minimum (10,47 < 15)": 7, "Cena ponizej minimum (1,64 < 15)": 2, "Cena ponizej minimum (1,13 < 15)": 1, "Cena ponizej minimum (1,32 < 15)": 1, "Cena ponizej minimum (1,45 < 15)": 1, "Cena ponizej minimum (5,32 < 15)": 3, "Cena powyzej maximum (209,46 > 99)": 4, "Cena ponizej minimum (1,16 < 15)": 1, "Cena powyzej maximum (222,55 > 99)": 1, "Cena ponizej minimum (0,65 < 15)": 2, "Cena ponizej minimum (10,93 < 15)": 1, "Cena ponizej minimum (5,50 < 15)": 1, "Cena powyzej maximum (589,10 > 99)": 2, "Cena ponizej minimum (14,54 < 15)": 2, "Cena ponizej minimum (13,08 < 15)": 1, "Cena ponizej minimum (0,92 < 15)": 2, "Cena powyzej maximum (113,75 > 99)": 1, "Cena powyzej maximum (105,69 > 99)": 1, "Cena powyzej maximum (104,78 > 99)": 1, "Cena powyzej maximum (105,39 > 99)": 1, "Cena powyzej maximum (226,43 > 99)": 1, "Cena powyzej maximum (366,38 > 99)": 1, "Cena powyzej maximum (103,58 > 99)": 1, "Cena powyzej maximum (972,18 > 99)": 1, "Cena powyzej maximum (103,28 > 99)": 2, "Cena powyzej maximum (105,99 > 99)": 2, "Cena powyzej maximum (102,63 > 99)": 1, "Cena powyzej maximum (101,70 > 99)": 1, "Cena powyzej maximum (103,55 > 99)": 1, "Cena powyzej maximum (117,82 > 99)": 2, "Cena powyzej maximum (165,44 > 99)": 1, "Cena powyzej maximum (319,42 > 99)": 2, "Cena ponizej minimum (11,73 < 15)": 1, "Cena ponizej minimum (11,74 < 15)": 1, "Cena ponizej minimum (11,76 < 15)": 1, "Cena ponizej minimum (11,77 < 15)": 1, "Cena ponizej minimum (11,78 < 15)": 4, "Cena ponizej minimum (14,40 < 15)": 1, "Cena powyzej maximum (585,61 > 99)": 1, "Cena ponizej minimum (7,20 < 15)": 3, "Cena powyzej maximum (105,38 > 99)": 1, "Cena powyzej maximum (922,10 > 99)": 1, "Cena powyzej maximum (292,73 > 99)": 1, "Cena powyzej maximum (263,46 > 99)": 2, "Cena powyzej maximum (395,19 > 99)": 1, "Cena powyzej maximum (237,11 > 99)": 3, "Cena powyzej maximum (146,36 > 99)": 1, "Cena powyzej maximum (355,67 > 99)": 1, "Cena powyzej maximum (223,94 > 99)": 2, "Cena powyzej maximum (329,32 > 99)": 1, "Cena powyzej maximum (302,98 > 99)": 1, "Cena powyzej maximum (336,63 > 99)": 1, "Cena powyzej maximum (605,95 > 99)": 1, "Cena powyzej maximum (484,37 > 99)": 1, "Cena powyzej maximum (104,73 > 99)": 4, "Cena powyzej maximum (301,09 > 99)": 1, "Cena powyzej maximum (158,65 > 99)": 1, "Cena ponizej minimum (0,90 < 15)": 1, "Cena ponizej minimum (9,16 < 15)": 3, "Cena powyzej maximum (244,89 > 99)": 1, "Cena powyzej maximum (404,60 > 99)": 1, "Cena ponizej minimum (10,65 < 15)": 2, "Cena ponizej minimum (4,26 < 15)": 2, "Cena powyzej maximum (163,64 > 99)": 1, "Cena powyzej maximum (654,55 > 99)": 3, "Cena ponizej minimum (7,85 < 15)": 1, "Cena powyzej maximum (202,91 > 99)": 2, "Cena ponizej minimum (1,85 < 15)": 1, "Cena powyzej maximum (392,73 > 99)": 8, "Cena ponizej minimum (1,96 < 15)": 1, "Cena powyzej maximum (353,46 > 99)": 2, "Cena powyzej maximum (122,99 > 99)": 1, "Cena ponizej minimum (11,13 < 15)": 1, "Cena powyzej maximum (1309,11 > 99)": 3, "Cena powyzej maximum (341,30 > 99)": 1, "Cena ponizej minimum (1,03 < 15)": 2, "Cena ponizej minimum (2,61 < 15)": 2, "Cena powyzej maximum (144,00 > 99)": 3, "Cena powyzej maximum (1178,20 > 99)": 1, "Cena powyzej maximum (106,47 > 99)": 1, "Cena powyzej maximum (170,18 > 99)": 2, "Cena powyzej maximum (130,91 > 99)": 5, "Cena powyzej maximum (248,73 > 99)": 2, "Cena powyzej maximum (212,95 > 99)": 1, "Cena powyzej maximum (287,84 > 99)": 1, "Cena ponizej minimum (0,33 < 15)": 1, "Cena powyzej maximum (340,37 > 99)": 1, "Ocena ponizej minimum (1 < 5)": 1, "Cena ponizej minimum (4,92 < 15)": 1, "Cena ponizej minimum (8,61 < 15)": 1, "Cena ponizej minimum (6,15 < 15)": 1, "Cena powyzej maximum (211,88 > 99)": 1, "Cena powyzej maximum (254,47 > 99)": 1, "Cena powyzej maximum (339,65 > 99)": 1, "Cena powyzej maximum (392,89 > 99)": 1, "Cena powyzej maximum (228,92 > 99)": 1, "Cena powyzej maximum (424,83 > 99)": 1, "Cena powyzej maximum (190,59 > 99)": 1, "Cena powyzej maximum (275,77 > 99)": 1, "Cena powyzej maximum (137,35 > 99)": 1, "Cena powyzej maximum (126,70 > 99)": 1, "Cena powyzej maximum (175,68 > 99)": 2, "Cena powyzej maximum (148,00 > 99)": 1, "Cena powyzej maximum (318,36 > 99)": 1, "Cena powyzej maximum (532,37 > 99)": 1, "Cena powyzej maximum (286,42 > 99)": 1, "Cena powyzej maximum (165,03 > 99)": 1, "Cena powyzej maximum (169,29 > 99)": 1, "Cena powyzej maximum (329,00 > 99)": 1, "Cena powyzej maximum (432,01 > 99)": 1, "Cena powyzej maximum (339,06 > 99)": 1, "Cena powyzej maximum (314,19 > 99)": 1, "Cena powyzej maximum (850,92 > 99)": 1, "Cena ponizej minimum (0,39 < 15)": 1, "Cena powyzej maximum (266,19 > 99)": 1, "Cena powyzej maximum (532,36 > 99)": 1, "Cena powyzej maximum (549,83 > 99)": 1, "Cena powyzej maximum (291,87 > 99)": 1, "Cena powyzej maximum (523,64 > 99)": 2, "Cena powyzej maximum (614,97 > 99)": 1, "Cena powyzej maximum (212,48 > 99)": 1, "Cena ponizej minimum (12,44 < 15)": 1, "Cena powyzej maximum (602,19 > 99)": 1, "Cena powyzej maximum (393,93 > 99)": 1, "Cena powyzej maximum (150,55 > 99)": 1, "Cena powyzej maximum (231,62 > 99)": 1, "Cena ponizej minimum (2,62 < 15)": 1, "Cena ponizej minimum (4,32 < 15)": 1, "Cena powyzej maximum (149,06 > 99)": 1, "Cena ponizej minimum (5,53 < 15)": 1, "Cena powyzej maximum (141,44 > 99)": 1, "Cena ponizej minimum (0,79 < 15)": 1, "Cena powyzej maximum (785,46 > 99)": 3, "Cena ponizej minimum (0,02 < 15)": 2, "Cena powyzej maximum (159,71 > 99)": 1, "Cena powyzej maximum (307,49 > 99)": 1, "Cena powyzej maximum (418,91 > 99)": 1, "Ocena ponizej minimum (3 < 5)": 1, "Cena powyzej maximum (693,85 > 99)": 1, "Cena powyzej maximum (1047,29 > 99)": 1, "Cena ponizej minimum (9,82 < 15)": 1, "Cena ponizej minimum (1,71 < 15)": 1, "Cena powyzej maximum (235,64 > 99)": 1, "Cena powyzej maximum (249,54 > 99)": 1, "Cena powyzej maximum (916,38 > 99)": 1, "Cena ponizej minimum (7,97 < 15)": 1}, "GenerationTime": "2025-06-17T15:35:46.1869173+02:00"}