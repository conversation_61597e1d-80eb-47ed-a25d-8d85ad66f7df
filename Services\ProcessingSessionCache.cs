using Microsoft.Extensions.Logging;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

namespace ShopBot.Services
{
    /// <summary>
    /// In-Memory Processing Session Cache
    /// Tracks offers processed in current session for performance optimization
    /// Prevents duplicate processing within single bot cycle
    /// </summary>
    public interface IProcessingSessionCache
    {
        bool WasProcessedInSession(string funPayOfferId);
        void MarkAsProcessed(string funPayOfferId);
        void MarkAsCurrentlyProcessing(string funPayOfferId);
        void MarkAsFinishedProcessing(string funPayOfferId);
        bool IsCurrentlyProcessing(string funPayOfferId);
        void ClearSession();
        int GetProcessedCount();
        int GetCurrentlyProcessingCount();
        List<string> GetProcessedOffers();
        List<string> GetCurrentlyProcessingOffers();
    }

    public class ProcessingSessionCache : IProcessingSessionCache
    {
        private readonly ConcurrentHashSet<string> _processedInSession = new();
        private readonly ConcurrentHashSet<string> _currentlyProcessing = new();
        private readonly ILogger<ProcessingSessionCache> _logger;
        private readonly object _lockObject = new object();

        public ProcessingSessionCache(ILogger<ProcessingSessionCache> logger)
        {
            _logger = logger;
        }

        public bool WasProcessedInSession(string funPayOfferId)
        {
            var wasProcessed = _processedInSession.Contains(funPayOfferId);
            
            if (wasProcessed)
            {
                _logger.LogDebug("[SessionCache] 🔍 Offer {OfferId} was processed in current session", funPayOfferId);
            }
            
            return wasProcessed;
        }

        public void MarkAsProcessed(string funPayOfferId)
        {
            lock (_lockObject)
            {
                _processedInSession.Add(funPayOfferId);
                _currentlyProcessing.Remove(funPayOfferId); // Remove from currently processing
                
                _logger.LogDebug("[SessionCache] ✅ Marked offer {OfferId} as processed in session", funPayOfferId);
            }
        }

        public void MarkAsCurrentlyProcessing(string funPayOfferId)
        {
            lock (_lockObject)
            {
                _currentlyProcessing.Add(funPayOfferId);
                _logger.LogDebug("[SessionCache] ⏳ Marked offer {OfferId} as currently processing", funPayOfferId);
            }
        }

        public void MarkAsFinishedProcessing(string funPayOfferId)
        {
            lock (_lockObject)
            {
                _currentlyProcessing.Remove(funPayOfferId);
                _processedInSession.Add(funPayOfferId);
                
                _logger.LogDebug("[SessionCache] 🏁 Finished processing offer {OfferId}", funPayOfferId);
            }
        }

        public bool IsCurrentlyProcessing(string funPayOfferId)
        {
            var isProcessing = _currentlyProcessing.Contains(funPayOfferId);
            
            if (isProcessing)
            {
                _logger.LogDebug("[SessionCache] ⏳ Offer {OfferId} is currently being processed", funPayOfferId);
            }
            
            return isProcessing;
        }

        public void ClearSession()
        {
            lock (_lockObject)
            {
                var processedCount = _processedInSession.Count;
                var processingCount = _currentlyProcessing.Count;
                
                _processedInSession.Clear();
                _currentlyProcessing.Clear();
                
                _logger.LogInformation("[SessionCache] 🔄 Cleared session cache: {ProcessedCount} processed, {ProcessingCount} were processing", 
                    processedCount, processingCount);
            }
        }

        public int GetProcessedCount()
        {
            return _processedInSession.Count;
        }

        public int GetCurrentlyProcessingCount()
        {
            return _currentlyProcessing.Count;
        }

        public List<string> GetProcessedOffers()
        {
            return _processedInSession.ToList();
        }

        public List<string> GetCurrentlyProcessingOffers()
        {
            return _currentlyProcessing.ToList();
        }
    }

    /// <summary>
    /// Thread-safe HashSet implementation
    /// </summary>
    public class ConcurrentHashSet<T> : IDisposable
    {
        private readonly HashSet<T> _hashSet = new HashSet<T>();
        private readonly ReaderWriterLockSlim _lock = new ReaderWriterLockSlim();

        public bool Add(T item)
        {
            _lock.EnterWriteLock();
            try
            {
                return _hashSet.Add(item);
            }
            finally
            {
                _lock.ExitWriteLock();
            }
        }

        public bool Remove(T item)
        {
            _lock.EnterWriteLock();
            try
            {
                return _hashSet.Remove(item);
            }
            finally
            {
                _lock.ExitWriteLock();
            }
        }

        public bool Contains(T item)
        {
            _lock.EnterReadLock();
            try
            {
                return _hashSet.Contains(item);
            }
            finally
            {
                _lock.ExitReadLock();
            }
        }

        public void Clear()
        {
            _lock.EnterWriteLock();
            try
            {
                _hashSet.Clear();
            }
            finally
            {
                _lock.ExitWriteLock();
            }
        }

        public int Count
        {
            get
            {
                _lock.EnterReadLock();
                try
                {
                    return _hashSet.Count;
                }
                finally
                {
                    _lock.ExitReadLock();
                }
            }
        }

        public List<T> ToList()
        {
            _lock.EnterReadLock();
            try
            {
                return new List<T>(_hashSet);
            }
            finally
            {
                _lock.ExitReadLock();
            }
        }

        public void Dispose()
        {
            _lock?.Dispose();
        }
    }
}
