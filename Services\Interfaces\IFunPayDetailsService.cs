using System;
using System.Threading.Tasks;
using ShopBot.Models;

namespace ShopBot.Services.Interfaces
{
    /// <summary>
    /// Interfejs dla serwisu pobierania szczegółów ofert z FunPay
    /// Używa dedykowanej zakładki z kontrolowanymi opóźnieniami
    /// </summary>
    public interface IFunPayDetailsService : IAsyncDisposable
    {
        /// <summary>
        /// Pobiera szczegóły oferty asynchronicznie
        /// Dodaje żądanie do kolejki i zwraca Task który zostanie ukończony gdy szczegóły będą gotowe
        /// </summary>
        /// <param name="offerId">ID oferty</param>
        /// <param name="offerUrl">URL oferty</param>
        /// <param name="gameName">Nazwa gry</param>
        /// <returns>Szczegóły oferty</returns>
        Task<OfferDetails> GetOfferDetailsAsync(string offerId, string offerUrl, string gameName);
        
        /// <summary>
        /// Uruchamia serwis (inicjalizuje dedykowaną zakładkę i timer)
        /// </summary>
        Task StartAsync();
        
        /// <summary>
        /// Zatrzymuje serwis (zamyka zakładkę i timer)
        /// </summary>
        Task StopAsync();
        
        /// <summary>
        /// Sprawdza czy serwis jest uruchomiony
        /// </summary>
        bool IsRunning { get; }
        
        /// <summary>
        /// Liczba oczekujących żądań w kolejce
        /// </summary>
        int PendingRequestsCount { get; }
        
        /// <summary>
        /// Statystyki serwisu
        /// </summary>
        FunPayDetailsServiceStats GetStats();
    }
    
    /// <summary>
    /// Statystyki serwisu FunPayDetailsService
    /// </summary>
    public class FunPayDetailsServiceStats
    {
        public int TotalRequests { get; set; }
        public int SuccessfulRequests { get; set; }
        public int FailedRequests { get; set; }
        public int TimeoutRequests { get; set; }
        public int PendingRequests { get; set; }
        public DateTime LastRequestAt { get; set; }
        public TimeSpan AverageProcessingTime { get; set; }
        public double SuccessRate => TotalRequests > 0 ? (double)SuccessfulRequests / TotalRequests * 100 : 0;
    }
}
