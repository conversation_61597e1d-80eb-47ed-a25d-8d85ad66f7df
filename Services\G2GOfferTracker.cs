using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace ShopBot.Services
{
    public static class G2GOfferTracker
    {
        private const string TrackingFile = "g2g_offers.json";
        private static readonly object _fileLock = new object();
        private static List<TrackedOffer> _cachedOffers = null;
        private static DateTime _cacheExpiry = DateTime.MinValue;
        private static readonly ProcessedOffersTracker _processedOffersTracker = new ProcessedOffersTracker();

        public class TrackedOffer
        {
            public string FunPayId { get; set; }
            public string G2GOfferId { get; set; }
            public string GameName { get; set; }
        }

        public static async Task<bool> TryAddOffer(TrackedOffer offer)
        {
            if (await _processedOffersTracker.TryProcessOffer(offer.FunPayId))
            {
                AddOrUpdateOffer(offer);
                return true;
            }
            return false;
        }

        public static void AddOrUpdateOffer(TrackedOffer offer)
        {
            if (string.IsNullOrEmpty(offer.GameName))
            {
                throw new ArgumentException("GameName nie może być puste");
            }
            if (string.IsNullOrEmpty(offer.FunPayId))
            {
                throw new ArgumentException("FunPayId nie może być puste");
            }
            if (string.IsNullOrEmpty(offer.G2GOfferId))
            {
                throw new ArgumentException("G2GOfferId nie może być puste");
            }

            lock (_fileLock)
            {
                var offers = LoadAllOffers();
                
                // Sprawdź czy oferta już istnieje
                var existingByFunPay = offers.FirstOrDefault(o => o.FunPayId == offer.FunPayId);
                var existingByG2G = offers.FirstOrDefault(o => o.G2GOfferId == offer.G2GOfferId);

                if (existingByFunPay != null)
                {
                    existingByFunPay.G2GOfferId = offer.G2GOfferId;
                    existingByFunPay.GameName = offer.GameName;
                }
                else if (existingByG2G != null)
                {
                    existingByG2G.FunPayId = offer.FunPayId;
                    existingByG2G.GameName = offer.GameName;
                }
                else
                {
                    offers.Add(offer);
                }

                SaveOffers(offers);
                _cachedOffers = null; // Invalidate cache
            }
        }

        public static List<TrackedOffer> LoadAllOffers(bool bypassCache = false)
        {
            if (bypassCache || _cachedOffers == null || DateTime.UtcNow > _cacheExpiry)
            {
                _cachedOffers = LoadOffersFromFile();
                _cacheExpiry = DateTime.UtcNow.AddMinutes(15); // Cache na 15 minut
            }

            return _cachedOffers;
        }

        private static List<TrackedOffer> LoadOffersFromFile()
        {
            lock (_fileLock)
            {
                if (!File.Exists(TrackingFile))
                {
                    SaveOffers(new List<TrackedOffer>());
                    return new List<TrackedOffer>();
                }

                try
                {
                    var jsonContent = File.ReadAllText(TrackingFile);
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };
                    
                    var offers = JsonSerializer.Deserialize<List<TrackedOffer>>(jsonContent, options);
                    
                    if (offers == null)
                    {
                        return new List<TrackedOffer>();
                    }

                    // Walidacja ofert
                    var validOffers = offers.Where(o => 
                        !string.IsNullOrEmpty(o.FunPayId) && 
                        !string.IsNullOrEmpty(o.G2GOfferId) && 
                        !string.IsNullOrEmpty(o.GameName)).ToList();

                    return validOffers;
                }
                catch (JsonException ex)
                {
                    Console.WriteLine($"[G2G Tracker] Błąd podczas parsowania ofert: {ex.Message}");
                    return new List<TrackedOffer>();
                }
            }
        }

        public static void RemoveOffer(string g2gOfferId)
        {
            lock (_fileLock)
            {
                var offers = LoadAllOffers(bypassCache: true); // Zawsze używaj świeżych danych
                var initialCount = offers.Count;
                offers.RemoveAll(o => o.G2GOfferId == g2gOfferId);
                var removedCount = initialCount - offers.Count;
                
                if (removedCount > 0)
                {
                    SaveOffers(offers);
                    _cachedOffers = null; // Invalidate cache
                }
                else
                {
                    Console.WriteLine($"[G2G Tracker] ⚠️ Nie znaleziono oferty {g2gOfferId} do usunięcia");
                }
            }
        }

        private static void SaveOffers(List<TrackedOffer> offers)
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true
                };

                var jsonContent = JsonSerializer.Serialize(offers, options);
                
                // Formatujemy JSON ręcznie dla zachowania spójnego stylu
                var formattedLines = new List<string>();
                formattedLines.Add("[");
                
                var offerLines = jsonContent.TrimStart('[').TrimEnd(']')
                    .Split(new[] { "},", "}" }, StringSplitOptions.RemoveEmptyEntries)
                    .Select(line => line.Trim().TrimStart('{'))
                    .Where(line => !string.IsNullOrWhiteSpace(line))
                    .ToList();

                for (int i = 0; i < offerLines.Count; i++)
                {
                    var line = "    {" + offerLines[i];
                    if (!line.EndsWith("}")) line += "}";
                    if (i < offerLines.Count - 1) line += ",";
                    formattedLines.Add(line);
                }
                
                formattedLines.Add("]");

                // Upewnij się, że katalog istnieje
                var directory = Path.GetDirectoryName(TrackingFile);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                File.WriteAllLines(TrackingFile, formattedLines);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[G2G Tracker] ❌ Błąd podczas zapisu ofert: {ex.Message}");
                throw;
            }
        }

        public static void ClearAllOffers()
        {
            lock (_fileLock)
            {
                if (File.Exists(TrackingFile))
                {
                    File.WriteAllText(TrackingFile, JsonSerializer.Serialize(new { ProcessedOffers = new List<TrackedOffer>() }, 
                        new JsonSerializerOptions { WriteIndented = true }));
                }
                _cachedOffers = null;
            }
        }
    }
}