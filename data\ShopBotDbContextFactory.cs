using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace ShopBot.Data
{
    public class ShopBotDbContextFactory : IDesignTimeDbContextFactory<ShopBotDbContext>
    {
        public ShopBotDbContext CreateDbContext(string[] args)
        {
            var optionsBuilder = new DbContextOptionsBuilder<ShopBotDbContext>();

            // Use default connection string for migrations
            var connectionString = "Server=localhost;Database=shopbotdb;Uid=root;Pwd=**********;";

            optionsBuilder.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));

            return new ShopBotDbContext(optionsBuilder.Options);
        }
    }
}
