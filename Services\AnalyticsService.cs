using Microsoft.Extensions.Logging;
using ShopBot.Services.Interfaces;
using ShopBot.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ShopBot.Services
{
    /// <summary>
    /// Analytics Service - wykorzystuje moc bazy danych do analizy danych
    /// Zapewnia insights, raporty i metryki wydajności
    /// </summary>
    public interface IAnalyticsService
    {
        Task<DashboardData> GetDashboardDataAsync();
        Task<ProcessingPerformanceReport> GetPerformanceReportAsync(int days = 30);
        Task<List<GameAnalytics>> GetGameAnalyticsAsync();
        Task<List<ErrorAnalytics>> GetTopErrorsAsync(int limit = 10);
        Task<PriceAnalytics> GetPriceAnalyticsAsync(string gameName);
        Task<List<DailyStats>> GetDailyStatsAsync(int days = 7);
    }

    public class AnalyticsService : IAnalyticsService
    {
        private readonly IShopBotRepository _repository;
        private readonly ILogger<AnalyticsService> _logger;

        public AnalyticsService(
            IShopBotRepository repository,
            ILogger<AnalyticsService> logger)
        {
            _repository = repository;
            _logger = logger;
        }

        public async Task<DashboardData> GetDashboardDataAsync()
        {
            try
            {
                _logger.LogInformation("[Analytics] 📊 Generating dashboard data");

                var offers = await _repository.Offers.GetAllAsync();
                var recentOffers = await _repository.Offers.GetRecentAsync(50);
                var errorLogs = await _repository.ErrorLogs.GetRecentErrorsAsync(10);

                var dashboardData = new DashboardData
                {
                    TotalOffers = offers.Count,
                    OffersByGame = offers.GroupBy(o => o.GameName)
                        .ToDictionary(g => g.Key, g => g.Count()),
                    OffersByStatus = offers.GroupBy(o => o.Status)
                        .ToDictionary(g => g.Key, g => g.Count()),
                    RecentOffers = recentOffers.Take(10).ToList(),
                    RecentErrors = errorLogs,
                    TotalRevenue = offers.Where(o => o.Status == "Published")
                        .Sum(o => o.ConvertedPrice),
                    SuccessRate = offers.Any() ? 
                        (double)offers.Count(o => o.Status == "Published") / offers.Count * 100 : 0
                };

                _logger.LogInformation("[Analytics] ✅ Dashboard data generated: {TotalOffers} offers, {SuccessRate:F1}% success rate", 
                    dashboardData.TotalOffers, dashboardData.SuccessRate);

                return dashboardData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[Analytics] ❌ Error generating dashboard data");
                throw;
            }
        }

        public async Task<ProcessingPerformanceReport> GetPerformanceReportAsync(int days = 30)
        {
            try
            {
                _logger.LogInformation("[Analytics] ⚡ Generating performance report for {Days} days", days);

                var fromDate = DateTime.UtcNow.AddDays(-days);
                var offers = await _repository.Offers.GetByDateRangeAsync(fromDate, DateTime.UtcNow);
                var stats = await _repository.ProcessingStats.GetDailyStatsAsync(days);

                var report = new ProcessingPerformanceReport
                {
                    TotalOffersProcessed = offers.Count,
                    SuccessfulOffers = offers.Count(o => o.Status == "Published"),
                    FailedOffers = offers.Count(o => o.Status == "Failed"),
                    PendingOffers = offers.Count(o => o.Status == "Pending"),
                    SuccessRate = offers.Any() ? 
                        (double)offers.Count(o => o.Status == "Published") / offers.Count * 100 : 0,
                    AverageProcessingTime = stats.Any() ? 
                        stats.Average(s => s.AverageProcessingTimeMs ?? 0) : 0,
                    TotalRevenue = offers.Where(o => o.Status == "Published")
                        .Sum(o => o.ConvertedPrice),
                    AverageOfferValue = offers.Where(o => o.Status == "Published").Any() ?
                        offers.Where(o => o.Status == "Published").Average(o => o.ConvertedPrice) : 0,
                    DailyStats = stats
                };

                _logger.LogInformation("[Analytics] ✅ Performance report generated: {SuccessRate:F1}% success, {Revenue:C} revenue", 
                    report.SuccessRate, report.TotalRevenue);

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[Analytics] ❌ Error generating performance report");
                throw;
            }
        }

        public async Task<List<GameAnalytics>> GetGameAnalyticsAsync()
        {
            try
            {
                _logger.LogInformation("[Analytics] 🎮 Generating game analytics");

                var offers = await _repository.Offers.GetAllAsync();
                var gameAnalytics = offers.GroupBy(o => o.GameName)
                    .Select(g => new GameAnalytics
                    {
                        GameName = g.Key,
                        TotalOffers = g.Count(),
                        PublishedOffers = g.Count(o => o.Status == "Published"),
                        FailedOffers = g.Count(o => o.Status == "Failed"),
                        PendingOffers = g.Count(o => o.Status == "Pending"),
                        SuccessRate = g.Any() ? (double)g.Count(o => o.Status == "Published") / g.Count() * 100 : 0,
                        TotalRevenue = g.Where(o => o.Status == "Published").Sum(o => o.ConvertedPrice),
                        AveragePrice = g.Where(o => o.Status == "Published").Any() ?
                            g.Where(o => o.Status == "Published").Average(o => o.ConvertedPrice) : 0,
                        MinPrice = g.Where(o => o.Status == "Published").Any() ?
                            g.Where(o => o.Status == "Published").Min(o => o.ConvertedPrice) : 0,
                        MaxPrice = g.Where(o => o.Status == "Published").Any() ?
                            g.Where(o => o.Status == "Published").Max(o => o.ConvertedPrice) : 0
                    })
                    .OrderByDescending(g => g.TotalRevenue)
                    .ToList();

                _logger.LogInformation("[Analytics] ✅ Game analytics generated for {GameCount} games", gameAnalytics.Count);
                return gameAnalytics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[Analytics] ❌ Error generating game analytics");
                throw;
            }
        }

        public async Task<List<ErrorAnalytics>> GetTopErrorsAsync(int limit = 10)
        {
            try
            {
                _logger.LogInformation("[Analytics] 🚨 Analyzing top {Limit} errors", limit);

                var errorLogs = await _repository.ErrorLogs.GetRecentErrorsAsync(1000);
                var errorAnalytics = errorLogs
                    .GroupBy(e => e.ErrorMessage)
                    .Select(g => new ErrorAnalytics
                    {
                        ErrorMessage = g.Key,
                        Count = g.Count(),
                        LastOccurrence = g.Max(e => e.CreatedAt),
                        AffectedGames = g.Select(e => e.GameName).Where(gn => !string.IsNullOrEmpty(gn)).Distinct().ToList()
                    })
                    .OrderByDescending(e => e.Count)
                    .Take(limit)
                    .ToList();

                _logger.LogInformation("[Analytics] ✅ Top errors analyzed: {ErrorCount} unique error types", errorAnalytics.Count);
                return errorAnalytics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[Analytics] ❌ Error analyzing top errors");
                throw;
            }
        }

        public async Task<PriceAnalytics> GetPriceAnalyticsAsync(string gameName)
        {
            try
            {
                _logger.LogInformation("[Analytics] 💰 Analyzing prices for game: {GameName}", gameName);

                var offers = await _repository.Offers.GetByGameAsync(gameName, 1000);
                var publishedOffers = offers.Where(o => o.Status == "Published" && o.ConvertedPrice > 0).ToList();

                if (!publishedOffers.Any())
                {
                    return new PriceAnalytics { GameName = gameName };
                }

                var priceAnalytics = new PriceAnalytics
                {
                    GameName = gameName,
                    TotalOffers = publishedOffers.Count,
                    MinPrice = publishedOffers.Min(o => o.ConvertedPrice),
                    MaxPrice = publishedOffers.Max(o => o.ConvertedPrice),
                    AveragePrice = publishedOffers.Average(o => o.ConvertedPrice),
                    MedianPrice = GetMedian(publishedOffers.Select(o => o.ConvertedPrice).ToList()),
                    TotalRevenue = publishedOffers.Sum(o => o.ConvertedPrice),
                    PriceRanges = GetPriceRanges(publishedOffers)
                };

                _logger.LogInformation("[Analytics] ✅ Price analytics for {GameName}: Avg={AvgPrice:C}, Range={MinPrice:C}-{MaxPrice:C}", 
                    gameName, priceAnalytics.AveragePrice, priceAnalytics.MinPrice, priceAnalytics.MaxPrice);

                return priceAnalytics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[Analytics] ❌ Error analyzing prices for game {GameName}", gameName);
                throw;
            }
        }

        public async Task<List<DailyStats>> GetDailyStatsAsync(int days = 7)
        {
            try
            {
                _logger.LogInformation("[Analytics] 📅 Generating daily stats for {Days} days", days);

                var fromDate = DateTime.UtcNow.AddDays(-days);
                var offers = await _repository.Offers.GetByDateRangeAsync(fromDate, DateTime.UtcNow);

                var dailyStats = offers
                    .GroupBy(o => o.ProcessedAt.Date)
                    .Select(g => new DailyStats
                    {
                        Date = g.Key,
                        TotalOffers = g.Count(),
                        PublishedOffers = g.Count(o => o.Status == "Published"),
                        FailedOffers = g.Count(o => o.Status == "Failed"),
                        PendingOffers = g.Count(o => o.Status == "Pending"),
                        Revenue = g.Where(o => o.Status == "Published").Sum(o => o.ConvertedPrice),
                        SuccessRate = g.Any() ? (double)g.Count(o => o.Status == "Published") / g.Count() * 100 : 0
                    })
                    .OrderBy(s => s.Date)
                    .ToList();

                _logger.LogInformation("[Analytics] ✅ Daily stats generated for {Days} days", dailyStats.Count);
                return dailyStats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[Analytics] ❌ Error generating daily stats");
                throw;
            }
        }

        private decimal GetMedian(List<decimal> values)
        {
            if (!values.Any()) return 0;
            
            var sorted = values.OrderBy(x => x).ToList();
            var count = sorted.Count;
            
            if (count % 2 == 0)
            {
                return (sorted[count / 2 - 1] + sorted[count / 2]) / 2;
            }
            else
            {
                return sorted[count / 2];
            }
        }

        private Dictionary<string, int> GetPriceRanges(List<Offer> offers)
        {
            return new Dictionary<string, int>
            {
                ["$0-10"] = offers.Count(o => o.ConvertedPrice >= 0 && o.ConvertedPrice < 10),
                ["$10-25"] = offers.Count(o => o.ConvertedPrice >= 10 && o.ConvertedPrice < 25),
                ["$25-50"] = offers.Count(o => o.ConvertedPrice >= 25 && o.ConvertedPrice < 50),
                ["$50-100"] = offers.Count(o => o.ConvertedPrice >= 50 && o.ConvertedPrice < 100),
                ["$100+"] = offers.Count(o => o.ConvertedPrice >= 100)
            };
        }
    }

    // Data Transfer Objects for Analytics
    public class DashboardData
    {
        public int TotalOffers { get; set; }
        public Dictionary<string, int> OffersByGame { get; set; } = new();
        public Dictionary<string, int> OffersByStatus { get; set; } = new();
        public List<Offer> RecentOffers { get; set; } = new();
        public List<ErrorLog> RecentErrors { get; set; } = new();
        public decimal TotalRevenue { get; set; }
        public double SuccessRate { get; set; }
    }

    public class ProcessingPerformanceReport
    {
        public int TotalOffersProcessed { get; set; }
        public int SuccessfulOffers { get; set; }
        public int FailedOffers { get; set; }
        public int PendingOffers { get; set; }
        public double SuccessRate { get; set; }
        public double AverageProcessingTime { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal AverageOfferValue { get; set; }
        public List<ProcessingStat> DailyStats { get; set; } = new();
    }

    public class GameAnalytics
    {
        public string GameName { get; set; } = string.Empty;
        public int TotalOffers { get; set; }
        public int PublishedOffers { get; set; }
        public int FailedOffers { get; set; }
        public int PendingOffers { get; set; }
        public double SuccessRate { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal AveragePrice { get; set; }
        public decimal MinPrice { get; set; }
        public decimal MaxPrice { get; set; }
    }

    public class ErrorAnalytics
    {
        public string ErrorMessage { get; set; } = string.Empty;
        public int Count { get; set; }
        public DateTime LastOccurrence { get; set; }
        public List<string> AffectedGames { get; set; } = new();
    }

    public class PriceAnalytics
    {
        public string GameName { get; set; } = string.Empty;
        public int TotalOffers { get; set; }
        public decimal MinPrice { get; set; }
        public decimal MaxPrice { get; set; }
        public decimal AveragePrice { get; set; }
        public decimal MedianPrice { get; set; }
        public decimal TotalRevenue { get; set; }
        public Dictionary<string, int> PriceRanges { get; set; } = new();
    }

    public class DailyStats
    {
        public DateTime Date { get; set; }
        public int TotalOffers { get; set; }
        public int PublishedOffers { get; set; }
        public int FailedOffers { get; set; }
        public int PendingOffers { get; set; }
        public decimal Revenue { get; set; }
        public double SuccessRate { get; set; }
    }
}
