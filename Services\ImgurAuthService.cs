using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace ImgurUploader
{
    public class ImgurAuthResponse
    {
        [JsonPropertyName("access_token")]
        public string AccessToken { get; set; }

        [JsonPropertyName("refresh_token")]
        public string RefreshToken { get; set; }

        [JsonPropertyName("expires_in")]
        public int ExpiresIn { get; set; }

        [JsonPropertyName("token_type")]
        public string TokenType { get; set; }

        [JsonPropertyName("account_username")]
        public string AccountUsername { get; set; }
    }

    public class ImgurAuthService
    {
        private readonly HttpClient _httpClient;
        private readonly string _clientId;
        private readonly string _clientSecret;
        private const string AuthUrl = "https://api.imgur.com/oauth2/authorize";
        private const string TokenUrl = "https://api.imgur.com/oauth2/token";

        public ImgurAuthService(string clientId, string clientSecret)
        {
            _clientId = clientId;
            _clientSecret = clientSecret;
            _httpClient = new HttpClient();
        }

        public string GetAuthorizationUrl()
        {
            // Response type "pin" dla autoryzacji PIN-based
            return $"{AuthUrl}?client_id={_clientId}&response_type=pin";
        }

        public async Task<ImgurAuthResponse> GetAccessTokenWithPinAsync(string pin)
        {
            var content = new FormUrlEncodedContent(new Dictionary<string, string>
            {
                { "client_id", _clientId },
                { "client_secret", _clientSecret },
                { "grant_type", "pin" },
                { "pin", pin }
            });

            try
            {
                var response = await _httpClient.PostAsync(TokenUrl, content);
                var jsonResponse = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"[Imgur Debug] Token response: {jsonResponse}");

                if (!response.IsSuccessStatusCode)
                {
                    throw new Exception($"Failed to get access token. Status: {response.StatusCode}. Response: {jsonResponse}");
                }

                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                var authResponse = JsonSerializer.Deserialize<ImgurAuthResponse>(jsonResponse, options);
                if (authResponse == null)
                {
                    throw new Exception("Failed to parse authentication response");
                }

                if (string.IsNullOrEmpty(authResponse.RefreshToken))
                {
                    throw new Exception("No refresh token in response");
                }

                Console.WriteLine($"[Imgur Debug] Got refresh token: {authResponse.RefreshToken}");
                return authResponse;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[Imgur Debug] Authentication error: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"[Imgur Debug] Inner exception: {ex.InnerException.Message}");
                }
                throw new Exception($"Authentication failed: {ex.Message}", ex);
            }
        }

        public async Task<ImgurAuthResponse> RefreshAccessTokenAsync(string refreshToken)
        {
            var content = new FormUrlEncodedContent(new Dictionary<string, string>
            {
                { "client_id", _clientId },
                { "client_secret", _clientSecret },
                { "grant_type", "refresh_token" },
                { "refresh_token", refreshToken }
            });

            try
            {
                var response = await _httpClient.PostAsync(TokenUrl, content);
                var jsonResponse = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    throw new Exception($"Failed to refresh token. Status: {response.StatusCode}. Response: {jsonResponse}");
                }

                var authResponse = JsonSerializer.Deserialize<ImgurAuthResponse>(jsonResponse);
                if (authResponse == null)
                {
                    throw new Exception("Failed to parse refresh token response");
                }

                return authResponse;
            }
            catch (Exception ex)
            {
                throw new Exception($"Token refresh failed: {ex.Message}", ex);
            }
        }
    }
}
