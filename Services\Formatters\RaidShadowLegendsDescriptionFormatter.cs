using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using ShopBot;

namespace ShopBot.Services.Formatters
{
    public class RaidShadowLegendsDescriptionFormatter : BaseDescriptionFormatter
    {
        public RaidShadowLegendsDescriptionFormatter(ILogger<RaidShadowLegendsDescriptionFormatter> logger) : base(logger)
        {
        }
        
        public override string FormatDescription(OfferDetails offerDetails, Dictionary<string, string> parameters = null)
        {
            if (offerDetails == null)
            {
                _logger.LogWarning("OfferDetails is null in RaidShadowLegends formatter");
                return string.Empty;
            }
            
            _logger.LogInformation("Formatting Raid Shadow Legends description for offer {Id}", offerDetails.Id);
            
            var description = new StringBuilder();
            
            // Formatowanie głównego opisu
            var cleanDesc = CleanAndFormatRaidDescription(offerDetails.DetailedDescription);
            
            if (!string.IsNullOrEmpty(cleanDesc))
            {
                description.AppendLine(cleanDesc);
            }
            
            // Dodaj standardowy tekst
            description.AppendLine("\n===============================================================");
            description.AppendLine("Got questions? Contact me on G2G chat before buying! Offers like this go fast – reserve your ideal account now!");
            
            // Dodaj ID
            if (parameters != null && parameters.TryGetValue("Id", out var id))
            {
                _logger.LogInformation("Adding ID to description: {Id}", id);
                description.AppendLine($"\n({id})");
            }

            var result = description.ToString();
            _logger.LogInformation("Final formatted description:\n{Description}", result);

            return result;
        }
        
        private string CleanAndFormatRaidDescription(string description)
        {
            if (string.IsNullOrEmpty(description))
                return string.Empty;
            
            // Usuń emoji i zamień na bullet points
            var cleanDesc = RemoveEmoji(description);

            // Usuń zabronione frazy
            cleanDesc = RemoveBannedPhrases(cleanDesc);

            // Jeśli tekst ma separatory |, użyj ich. Jeśli nie, podziel inteligentnie
            string[] lines;
            if (cleanDesc.Contains("|"))
            {
                lines = cleanDesc.Split(new[] { '|' }, StringSplitOptions.RemoveEmptyEntries);
                _logger.LogInformation("Split by | into {Count} lines", lines.Length);
            }
            else
            {
                // Inteligentne dzielenie na podstawie słów kluczowych
                lines = SplitIntelligently(cleanDesc);
                _logger.LogInformation("Split intelligently into {Count} lines", lines.Length);
            }
            var formattedLines = new List<string>();

            foreach (var line in lines)
            {
                var trimmedLine = line.Trim();
                if (string.IsNullOrEmpty(trimmedLine)) continue;

                // Usuń pozostałe | na początku linii
                trimmedLine = trimmedLine.TrimStart('|').Trim();

                // Pomiń linie ze standardowym tekstem
                if (IsStandardText(trimmedLine)) continue;

                // Formatuj linię (zachowaj separatory |)
                var formattedLine = FormatRaidLine(trimmedLine);
                if (!string.IsNullOrEmpty(formattedLine))
                {
                    formattedLines.Add(formattedLine);
                }
            }

            // Połącz z separatorami | i dodaj bullet point na początku
            var result = "• " + string.Join(" | ", formattedLines);
            return result;
        }
        
        private string FormatRaidLine(string line)
        {
            if (string.IsNullOrEmpty(line)) return string.Empty;
            
            // Kapitalizuj pierwszą literę
            line = char.ToUpper(line[0]) + line.Substring(1).ToLower();
            
            // Specjalne formatowanie dla różnych typów informacji
            
            // Heroes/Champions
            if (line.Contains("heroes") || line.Contains("champions"))
            {
                line = FormatHeroesLine(line);
            }
            
            // Clan Boss
            if (line.Contains("clanboss") || line.Contains("clan boss"))
            {
                line = FormatClanBossLine(line);
            }
            
            // Arena
            if (line.Contains("arena"))
            {
                line = FormatArenaLine(line);
            }
            
            // Dungeons
            if (line.Contains("dungeons") || line.Contains("dungeon"))
            {
                line = FormatDungeonsLine(line);
            }
            
            // Mail/Binding
            if (line.Contains("mail") || line.Contains("bind"))
            {
                line = FormatMailLine(line);
            }
            
            return line;
        }
        
        private string FormatHeroesLine(string line)
        {
            // Przykład: "s tier arena heroes: galathir_rotos_wukong_vitrius etc."
            // Wynik: "S-Tier Arena Heroes: Galathir_Rotos_Wukong_Vitrius etc."

            line = line.Replace("s tier", "S-Tier");
            // Zachowaj _ w nazwach bohaterów - to jest standard w opisach gier
            line = Regex.Replace(line, @"\b\w", m => m.Value.ToUpper()); // Kapitalizuj każde słowo

            return line;
        }
        
        private string FormatClanBossLine(string line)
        {
            // Przykład: "clanboss nightmare(5) 2 key - brutal(4) 1 key"
            // Wynik: "Clan Boss: Nightmare (5) 2-key, Brutal (4) 1-key"
            
            line = line.Replace("clanboss", "Clan Boss:");
            line = line.Replace("nightmare", "Nightmare");
            line = line.Replace("brutal", "Brutal");
            line = line.Replace(" key", "-key");
            line = line.Replace(" - ", ", ");
            
            return line;
        }
        
        private string FormatArenaLine(string line)
        {
            // Przykład: "farming on gold 4-5 in classic arena"
            // Wynik: "Classic Arena: Gold 4-5"
            
            if (line.Contains("classic arena"))
            {
                var match = Regex.Match(line, @"gold\s+(\d+(?:-\d+)?)", RegexOptions.IgnoreCase);
                if (match.Success)
                {
                    return $"Classic Arena: Gold {match.Groups[1].Value}";
                }
            }
            
            line = Regex.Replace(line, @"\b\w", m => m.Value.ToUpper());
            return line;
        }
        
        private string FormatDungeonsLine(string line)
        {
            // Przykład: "all dungeons stage 20 ready to auto farm"
            // Wynik: "All Dungeons: Stage 20 (Auto-farm ready)"
            
            if (line.Contains("stage") && line.Contains("20"))
            {
                return "All Dungeons: Stage 20 (Auto-farm ready)";
            }
            
            line = Regex.Replace(line, @"\b\w", m => m.Value.ToUpper());
            return line;
        }
        
        private string FormatMailLine(string line)
        {
            // Przykład: "replaceable mail & without any bind (no fb or apple)"
            // Wynik: "Replaceable Email & No Bindings (Facebook/Apple)"
            
            line = line.Replace("mail", "Email");
            line = line.Replace("bind", "Bindings");
            line = line.Replace("fb", "Facebook");
            line = line.Replace("no facebook or apple", "No Facebook/Apple");
            line = line.Replace("without any bindings", "No Bindings");
            
            line = Regex.Replace(line, @"\b\w", m => m.Value.ToUpper());
            return line;
        }
        
        private string[] SplitIntelligently(string text)
        {
            // Słowa kluczowe do dzielenia tekstu na sekcje
            var splitKeywords = new[]
            {
                "everything is on the screenshots",
                "unfortunately",
                "you can protect your account",
                "everything closes on auto",
                "hydra",
                "chimera",
                "difficult games",
                "the life arena"
            };

            var result = new List<string>();
            var currentText = text.ToLower();
            var originalText = text;
            var lastIndex = 0;

            foreach (var keyword in splitKeywords)
            {
                var index = currentText.IndexOf(keyword, lastIndex);
                if (index >= 0)
                {
                    // Dodaj tekst przed słowem kluczowym
                    if (index > lastIndex)
                    {
                        var beforeKeyword = originalText.Substring(lastIndex, index - lastIndex).Trim();
                        if (!string.IsNullOrEmpty(beforeKeyword))
                        {
                            result.Add(beforeKeyword);
                        }
                    }

                    // Znajdź koniec tej sekcji (do następnego słowa kluczowego lub końca tekstu)
                    var nextKeywordIndex = currentText.Length;
                    foreach (var nextKeyword in splitKeywords)
                    {
                        var nextIndex = currentText.IndexOf(nextKeyword, index + keyword.Length);
                        if (nextIndex >= 0 && nextIndex < nextKeywordIndex)
                        {
                            nextKeywordIndex = nextIndex;
                        }
                    }

                    // Dodaj sekcję ze słowem kluczowym
                    var section = originalText.Substring(index, nextKeywordIndex - index).Trim();
                    if (!string.IsNullOrEmpty(section))
                    {
                        result.Add(section);
                    }

                    lastIndex = nextKeywordIndex;
                }
            }

            // Dodaj pozostały tekst
            if (lastIndex < originalText.Length)
            {
                var remaining = originalText.Substring(lastIndex).Trim();
                if (!string.IsNullOrEmpty(remaining))
                {
                    result.Add(remaining);
                }
            }

            // Jeśli nie udało się podzielić, zwróć oryginalny tekst
            if (result.Count == 0)
            {
                result.Add(text);
            }

            return result.ToArray();
        }

        private bool IsStandardText(string line)
        {
            var standardPhrases = new[]
            {
                "got questions",
                "contact me",
                "offers like this",
                "all details are in",
                "screenshots below",
                "please check",
                "send message",
                "write me only",
                "safe and trusted",
                "please write me only in english"
            };

            var lowerLine = line.ToLower();
            return standardPhrases.Any(phrase => lowerLine.Contains(phrase));
        }
        
        public override string FormatTitle(OfferDetails offerDetails)
        {
            if (offerDetails == null)
            {
                _logger.LogWarning("OfferDetails is null in RaidShadowLegends title formatter");
                return string.Empty;
            }
            
            // Wyczyść tytuł z emoji i niepotrzebnych znaków
            var title = RemoveEmoji(offerDetails.ShortDescription);
            title = title.Replace("🔥", "").Trim();
            
            // Usuń wielokrotne spacje i znaki
            title = Regex.Replace(title, @"\s+", " ");
            title = Regex.Replace(title, @"[|]+", " | ");
            
            return title.Trim();
        }
    }
}
