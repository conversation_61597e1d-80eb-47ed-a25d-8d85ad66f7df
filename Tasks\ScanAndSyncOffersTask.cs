using System;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using ShopBot.Services;
using ShopBot.Services.Interfaces;
using ShopBot.Config;
using ShopBot.Models;
using Microsoft.Playwright;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.IO;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ShopBot.Services.Formatters;
using ShopBot.Services.Extensions;

namespace ShopBot.Tasks
{
    public class ScanAndSyncOffersTask : BaseTask
    {
        private const int G2G_OFFERS_LIMIT = 1010; // TYMCZASOWO ZWIĘKSZONE DLA TESTU
        private readonly FunPayService _funPayService;
        private readonly TaskScheduler _taskScheduler;
        private readonly ProcessedOffersTracker _processedOffersTracker;
        private readonly ParserService _parserService;
        private readonly IBrowser _browser;
        private readonly IBrowserContext _context;
        private readonly ImageUploadService _imageUploadService;
        private readonly G2GOfferLimitChecker _limitChecker;
        private readonly IServiceProvider _serviceProvider;
        private readonly G2GOfferManager _offerManager;
        private readonly IConfigurationRepository _configRepository;
        private readonly IShopBotRepository _repository;
        private readonly ILogger<ScanAndSyncOffersTask> _logger;
        private readonly OfferContentChangeDetector _changeDetector;

        // Flaga, aby zapobiec ciągłemu uruchamianiu weryfikacji
        private static bool _verificationPerformedInCurrentCycle = false;
        private static readonly object _verificationLock = new object();
        
        // Metoda do resetowania flagi przy uruchomieniu nowego cyklu
        public static void ResetVerificationFlag()
        {
            lock (_verificationLock)
            {
                _verificationPerformedInCurrentCycle = false;
            }
        }

        public ScanAndSyncOffersTask(
            FunPayService funPayService,
            TaskScheduler taskScheduler,
            ProcessedOffersTracker processedOffersTracker,
            ParserService parserService,
            IBrowser browser,
            IBrowserContext context,
            ImageUploadService imageUploadService,
            IServiceProvider serviceProvider,
            IConfigurationRepository configRepository,
            IShopBotRepository repository,
            ILogger<ScanAndSyncOffersTask> logger,
            OfferContentChangeDetector changeDetector)
        {
            _funPayService = funPayService;
            _taskScheduler = taskScheduler;
            _processedOffersTracker = processedOffersTracker;
            _parserService = parserService;
            _browser = browser;
            _context = context;
            _imageUploadService = imageUploadService;
            _limitChecker = new G2GOfferLimitChecker(browser, context);
            _serviceProvider = serviceProvider;
            _offerManager = new G2GOfferManager(browser, context);
            _configRepository = configRepository;
            _repository = repository;
            _logger = logger;
            _changeDetector = changeDetector;
        }

        public override async Task ExecuteAsync(CancellationToken cancellationToken)
        {
            try
            {
                Status = TaskStatus.Running;
                _logger.LogSyncStart();

                // 1. Pobierz aktywne oferty z g2g_offers.json
                var g2gOffers = G2GOfferTracker.LoadAllOffers();
                var totalOffersCount = g2gOffers.Count;
                _logger.LogInitialOfferCount(totalOffersCount, G2G_OFFERS_LIMIT);

                // Sprawdź aktualną liczbę ofert przez API
                var actualG2GCount = await _offerManager.GetCurrentG2GOfferCount();

                // Sprawdź czy jest rozbieżność
                if (Math.Abs(actualG2GCount - totalOffersCount) > 1)
                {
                    _logger.LogOfferCountMismatch(actualG2GCount, totalOffersCount);
                    
                    // Sprawdź czy weryfikacja już była wykonana
                    bool shouldPerformVerification = false;
                    lock (_verificationLock)
                    {
                        if (!_verificationPerformedInCurrentCycle)
                        {
                            _verificationPerformedInCurrentCycle = true;
                            shouldPerformVerification = true;
                        }
                    }
                    
                    if (shouldPerformVerification)
                    {
                        _logger.LogInformation("[Sync] 🔍 Uruchamiam pełną weryfikację...");

                        var verificationTask = new G2GOfferVerificationTask(
                            _browser,
                            _context,
                            _processedOffersTracker,
                            _serviceProvider,
                            _taskScheduler
                        );
                        await _taskScheduler.ScheduleTask(verificationTask);

                        // Poczekaj na zakończenie weryfikacji
                        while (verificationTask.Status != TaskStatus.Completed &&
                               verificationTask.Status != TaskStatus.Failed)
                        {
                            await Task.Delay(1000);
                        }

                        if (verificationTask.Status == TaskStatus.Failed)
                        {
                            _logger.LogError("[Sync] ❌ Weryfikacja nie powiodła się: {ErrorMessage}", verificationTask.ErrorMessage);
                            return;
                        }
                    }
                    else
                    {
                        _logger.LogInformation("[Sync] ℹ️ Pomijam weryfikację - już została wykonana w tym cyklu");
                    }

                    // Załaduj ponownie oferty po weryfikacji
                    g2gOffers = G2GOfferTracker.LoadAllOffers();
                    totalOffersCount = g2gOffers.Count;
                    _logger.LogActualOfferCount(totalOffersCount, G2G_OFFERS_LIMIT);
                }

                if (!await _offerManager.CanAddOffer())
                {
                    _logger.LogWarning("[Sync] ⚠️ Osiągnięto limit ofert na G2G - pomijam dodawanie nowych ofert");
                }

                // 2. Grupuj oferty G2G według gier
                var offersByGame = g2gOffers.GroupBy(o => o.GameName)
                                          .ToDictionary(g => g.Key, g => g.ToList());

                // 3. Wczytaj konfigurację przez Repository Pattern
                var filter = await _configRepository.GetFilterConfigAsync();
                var activeGames = await _configRepository.GetActiveGamesAsync();

                if (!activeGames.Any())
                {
                    _logger.LogNoActiveGames();
                    return;
                }

                // 4. Przetwarzaj gry równolegle, maksymalnie 3 na raz
                var semaphore = new SemaphoreSlim(3);
                var tasks = activeGames.Select(async gameToProcess =>
                {
                    await semaphore.WaitAsync();
                    try
                    {
                        _logger.LogSyncGameProcessingStart(gameToProcess);

                        var gameOffers = offersByGame.GetValueOrDefault(gameToProcess, new List<G2GOfferTracker.TrackedOffer>());
                        _logger.LogCurrentGameOfferCount(gameOffers.Count);

                        List<OfferListItem> funPayOffers;
                        IPage page = null;
                        try
                        {
                            page = await _context.NewPageAsync();
                            var funPayService = _serviceProvider.GetRequiredService<Func<IPage, FunPayService>>()(page);
                            funPayOffers = await funPayService.GetFilteredOffers(filter, gameToProcess);
                            _logger.LogInformation("[Sync] 🔍 Znaleziono {OfferCount} ofert na FunPay dla {GameName}", funPayOffers.Count, gameToProcess);
                        }
                        finally
                        {
                            if (page != null)
                            {
                                await page.CloseAsync();
                            }
                        }

                        if (funPayOffers != null)
                        {
                            // 🛡️ ZABEZPIECZENIE: Sprawdź czy FunPay zwrócił jakiekolwiek oferty
                            if (funPayOffers.Count == 0 && gameOffers.Count > 0)
                            {
                                _logger.LogWarning("[Sync] ⚠️ ZABEZPIECZENIE: FunPay zwrócił 0 ofert dla {GameName}, ale mamy {ExistingCount} ofert G2G. Pomijam usuwanie aby uniknąć masowego usunięcia!",
                                    gameToProcess, gameOffers.Count);
                                return; // Pomiń tę grę
                            }

                            // Zapisz opisy ofert do pliku
                            await SaveOffersDescriptions(gameToProcess, funPayOffers);

                            // Zbierz ID ofert
                            var funPayIds = funPayOffers.Select(o => o.Id).ToHashSet();
                            var g2gFunPayIds = gameOffers.Select(o => o.FunPayId).ToHashSet();

                            // Znajdź nieaktywne oferty
                            var inactiveOffers = gameOffers.Where(o => !funPayIds.Contains(o.FunPayId)).ToList();

                            // 🛡️ DODATKOWE ZABEZPIECZENIE: Sprawdź czy nie usuwamy zbyt wielu ofert
                            if (inactiveOffers.Count > gameOffers.Count * 0.8) // Jeśli > 80% ofert do usunięcia
                            {
                                _logger.LogWarning("[Sync] ⚠️ ZABEZPIECZENIE: Wykryto {InactiveCount}/{TotalCount} ({Percentage:F1}%) ofert do usunięcia dla {GameName}. To może być błąd - pomijam masowe usuwanie!",
                                    inactiveOffers.Count, gameOffers.Count, (double)inactiveOffers.Count / gameOffers.Count * 100, gameToProcess);
                                return; // Pomiń tę grę
                            }

                            // Znajdź nowe oferty
                            var newOffers = funPayOffers.Where(o => !g2gFunPayIds.Contains(o.Id)).ToList();

                            // NOWA FUNKCJONALNOŚĆ: Wykrywanie zmian zawartości ofert
                            var offersWithContentChanges = await DetectContentChangesAsync(gameOffers, funPayOffers, gameToProcess);

                            if (offersWithContentChanges.Any())
                            {
                                _logger.LogInformation("[Sync] 🔄 Wykryto {ChangedCount} ofert ze znaczącymi zmianami zawartości dla {GameName}",
                                    offersWithContentChanges.Count, gameToProcess);

                                // Dodaj oferty ze zmianami do listy nieaktywnych (do usunięcia)
                                inactiveOffers.AddRange(offersWithContentChanges.Select(c => c.ExistingOffer));

                                // Dodaj nowe wersje ofert do listy nowych ofert (do dodania)
                                newOffers.AddRange(offersWithContentChanges.Select(c => c.NewOffer));
                            }

                            // Usuń nieaktywne oferty
                            if (inactiveOffers.Any())
                            {
                                _logger.LogInformation("[Sync] 🗑️ Znaleziono {InactiveCount} nieaktywnych ofert do usunięcia dla {GameName}", inactiveOffers.Count, gameToProcess);
                                foreach (var inactiveOffer in inactiveOffers)
                                {
                                    _logger.LogInformation("[Sync] ❌ Usuwam ofertę: FunPay({FunPayId}) -> G2G({G2GOfferId})", inactiveOffer.FunPayId, inactiveOffer.G2GOfferId);
                                    var deleteTask = new DeleteOfferTask(
                                        inactiveOffer.G2GOfferId,
                                        new DeleteService(_browser, _context, _processedOffersTracker)
                                    );
                                    await _taskScheduler.ScheduleTask(deleteTask);
                                    Interlocked.Decrement(ref totalOffersCount);
                                }
                            }

                            // Sprawdź limit ofert tylko jeśli mamy nowe oferty do dodania
                            if (newOffers.Any())
                            {
                                // Sprawdź aktualną liczbę ofert
                                if (!await _offerManager.CanAddOffer())
                                {
                                    _logger.LogWarning("[Sync] ⚠️ Pomijam {NewOfferCount} nowych ofert dla {GameName} - osiągnięto limit na G2G", newOffers.Count, gameToProcess);
                                }
                                else
                                {
                                    var availableSlots = G2G_OFFERS_LIMIT - totalOffersCount;
                                    var offersToAdd = newOffers.Where(offer =>
                                        !_processedOffersTracker.WasOfferProcessed(offer.Id) &&
                                        !g2gOffers.Any(g2gOffer => g2gOffer.FunPayId == offer.Id)
                                    ).Take(availableSlots).ToList();

                                    if (offersToAdd.Any())
                                    {
                                        _logger.LogNewOffersFound(offersToAdd.Count, gameToProcess);
                                        foreach (var newOffer in offersToAdd)
                                        {
                                            try
                                            {
                                                _logger.LogInformation("[Sync] ➕ Dodaję nową ofertę: {OfferId}", newOffer.Id);

                                                // Save to database first (dual write pattern)
                                                await SaveOfferToDatabaseAsync(newOffer, gameToProcess);

                                                var addTask = new AddOfferTask(
                                                    newOffer,
                                                    gameToProcess,
                                                    _context,
                                                    _processedOffersTracker,
                                                    _parserService,
                                                    _imageUploadService,
                                                    _serviceProvider.GetRequiredService<DescriptionFormatterFactory>(),
                                                    _serviceProvider.GetRequiredService<IFunPayDetailsService>(),
                                                    _serviceProvider.GetRequiredService<IOfferRepository>(),
                                                    _serviceProvider.GetRequiredService<ILogger<AddOfferTask>>(),
                                                    _serviceProvider.GetRequiredService<G2GPublisherService>(),
                                                    _serviceProvider.GetRequiredService<G2GOfferManager>()
                                                );
                                                await _taskScheduler.ScheduleTask(addTask);
                                                Interlocked.Increment(ref totalOffersCount);

                                                // Sprawdź limit po każdym dodaniu
                                                if (!await _offerManager.CanAddOffer())
                                                {
                                                    _logger.LogWarning("[Sync] ⚠️ Osiągnięto limit ofert - przerywam dodawanie");
                                                    break;
                                                }
                                            }
                                            catch (Exception ex)
                                            {
                                                _logger.LogError("[Sync] ❌ Błąd podczas dodawania oferty {OfferId}: {Error}", newOffer.Id, ex.Message);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    finally
                    {
                        semaphore.Release();
                    }
                });

                await Task.WhenAll(tasks);

                Status = TaskStatus.Completed;
                _logger.LogSyncComplete();
            }
            catch (Exception ex)
            {
                Status = TaskStatus.Failed;
                ErrorMessage = ex.Message;
                _logger.LogSyncError(ex.Message);
                throw;
            }
        }

        private async Task SaveOffersDescriptions(string gameName, List<OfferListItem> offers)
        {
            try
            {
                var descriptionsDir = "offers_descriptions";
                if (!Directory.Exists(descriptionsDir))
                {
                    Directory.CreateDirectory(descriptionsDir);
                }

                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var filePath = Path.Combine(descriptionsDir, $"{gameName}_descriptions_{timestamp}.json");

                var offerDescriptions = new
                {
                    Game = gameName,
                    ExportDate = DateTime.Now,
                    TotalOffers = offers.Count,
                    Offers = offers.Select(o => new
                    {
                        Id = o.Id,
                        ShortDescription = o.Description,
                        Price = o.Price,
                        SellerRating = o.SellerRating,
                        ReviewCount = o.ReviewCount,
                        IsOnline = o.IsOnline,
                        Attributes = o.Attributes
                    }).ToList()
                };

                var jsonOptions = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };

                var json = JsonSerializer.Serialize(offerDescriptions, jsonOptions);
                await File.WriteAllTextAsync(filePath, json);
                _logger.LogOfferDescriptionsSaved(filePath);

                // Dodatkowo zapisz do zbiorczego pliku
                await UpdateMasterDescriptionsFile(gameName, offerDescriptions);
            }
            catch (Exception ex)
            {
                _logger.LogOfferDescriptionsSaveError(ex.Message);
            }
        }

        private async Task UpdateMasterDescriptionsFile(string gameName, object newDescriptions)
        {
            try
            {
                var masterFilePath = "all_offers_descriptions.json";
                Dictionary<string, object> masterData;

                // Wczytaj istniejący plik lub utwórz nowy
                if (File.Exists(masterFilePath))
                {
                    var existingJson = await File.ReadAllTextAsync(masterFilePath);
                    masterData = JsonSerializer.Deserialize<Dictionary<string, object>>(existingJson) 
                               ?? new Dictionary<string, object>();
                }
                else
                {
                    masterData = new Dictionary<string, object>();
                }

                // Aktualizuj lub dodaj nowe dane
                masterData[gameName] = newDescriptions;

                // Zapisz zaktualizowany plik
                var jsonOptions = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };

                var updatedJson = JsonSerializer.Serialize(masterData, jsonOptions);
                await File.WriteAllTextAsync(masterFilePath, updatedJson);
                _logger.LogInformation("[Sync] 📝 Zaktualizowano zbiorczy plik opisów ofert");
            }
            catch (Exception ex)
            {
                _logger.LogWarning("[Sync] ⚠️ Błąd podczas aktualizacji zbiorczego pliku opisów: {Error}", ex.Message);
            }
        }

        private async Task SaveOfferToDatabaseAsync(OfferListItem funPayOffer, string gameName)
        {
            try
            {
                // Check if offer already exists
                var existingOffer = await _repository.Offers.GetByFunPayIdAsync(funPayOffer.Id);
                if (existingOffer != null)
                {
                    _logger.LogWarning("[DB] ⚠️ Offer {OfferId} already exists in database", funPayOffer.Id);
                    return;
                }

                // Serialize image URLs and calculate count
                string? imageUrlsJson = null;
                int imageCount = 0;

                if (funPayOffer.Attributes != null)
                {
                    imageUrlsJson = System.Text.Json.JsonSerializer.Serialize(funPayOffer.Attributes);
                    imageCount = funPayOffer.Attributes.Count;
                }

                // Create new offer entity
                var offer = new Models.Offer
                {
                    FunPayOfferId = funPayOffer.Id,
                    GameName = gameName,
                    Title = funPayOffer.Description ?? "No title",
                    Description = funPayOffer.Description,
                    OriginalPrice = (decimal)funPayOffer.Price,
                    ConvertedPrice = (decimal)funPayOffer.Price, // Will be updated later with actual conversion
                    Currency = "USDT", // FunPay default
                    Status = "Pending",
                    ProcessedAt = DateTime.UtcNow,
                    SourceUrl = $"https://funpay.com/lots/offer?id={funPayOffer.Id}",
                    ImageUrls = imageUrlsJson,
                    ImageCount = imageCount
                };

                await _repository.Offers.CreateAsync(offer);
                _logger.LogInformation("[DB] ✅ Saved offer {OfferId} to database", funPayOffer.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError("[DB] ❌ Failed to save offer {OfferId} to database: {Error}", funPayOffer.Id, ex.Message);
                // Don't throw - continue with existing logic
            }
        }

        /// <summary>
        /// Wykrywa oferty, które mają znaczące zmiany zawartości (cena, tytuł) przy zachowaniu tego samego ID.
        /// </summary>
        private async Task<List<OfferContentChange>> DetectContentChangesAsync(
            List<G2GOfferTracker.TrackedOffer> existingG2GOffers,
            List<OfferListItem> currentFunPayOffers,
            string gameName)
        {
            var contentChanges = new List<OfferContentChange>();

            try
            {
                _logger.LogContentChangeDetectionStart(existingG2GOffers.Count, gameName);

                var startTime = DateTime.UtcNow;
                var checkedOffers = 0;
                var detectedChanges = 0;

                foreach (var existingG2GOffer in existingG2GOffers)
                {
                    // Znajdź odpowiadającą ofertę w aktualnych ofertach FunPay
                    var currentFunPayOffer = currentFunPayOffers.FirstOrDefault(o => o.Id == existingG2GOffer.FunPayId);

                    if (currentFunPayOffer != null)
                    {
                        // Pobierz szczegóły istniejącej oferty z bazy danych
                        var existingOfferInDb = await _repository.Offers.GetByFunPayIdAsync(existingG2GOffer.FunPayId);

                        if (existingOfferInDb != null)
                        {
                            // Utwórz tymczasowy obiekt OfferDetails z aktualnych danych FunPay
                            var currentOfferDetails = new OfferDetails
                            {
                                Id = currentFunPayOffer.Id,
                                ShortDescription = currentFunPayOffer.Description,
                                ActualOfferPrice = currentFunPayOffer.Price.ToString("F2")
                            };

                            // Sprawdź czy są znaczące zmiany
                            if (_changeDetector.HasSignificantChanges(existingOfferInDb, currentOfferDetails))
                            {
                                _logger.LogContentChangeDetected(existingG2GOffer.FunPayId,
                                    existingOfferInDb.Title, existingOfferInDb.OriginalPrice,
                                    currentFunPayOffer.Description, currentFunPayOffer.Price);

                                contentChanges.Add(new OfferContentChange
                                {
                                    ExistingOffer = existingG2GOffer,
                                    NewOffer = currentFunPayOffer,
                                    ChangeType = "ContentChange"
                                });

                                detectedChanges++;
                            }
                        }
                    }

                    checkedOffers++;
                }

                var processingTime = DateTime.UtcNow - startTime;
                _logger.LogContentChangeDetectionComplete(checkedOffers, detectedChanges, processingTime.TotalMilliseconds);

                return contentChanges;
            }
            catch (Exception ex)
            {
                _logger.LogContentChangeDetectionError(gameName, ex.Message);
                return new List<OfferContentChange>();
            }
        }
    }

    /// <summary>
    /// Reprezentuje wykrytą zmianę zawartości oferty.
    /// </summary>
    public class OfferContentChange
    {
        public G2GOfferTracker.TrackedOffer ExistingOffer { get; set; } = null!;
        public OfferListItem NewOffer { get; set; } = null!;
        public string ChangeType { get; set; } = string.Empty;
    }
}