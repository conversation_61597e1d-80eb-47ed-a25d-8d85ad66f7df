using ShopBot.Tasks;
using System;
using System.Threading.Tasks;

namespace ShopBot.Services
{
    /// <summary>
    /// Interfejs dla schedulera zadań
    /// </summary>
    public interface ITaskScheduler
    {
        /// <summary>
        /// Dodaje zadanie do kolejki
        /// </summary>
        Task ScheduleTask(BaseTask task);

        /// <summary>
        /// Dodaje zadanie cykliczne
        /// </summary>
        void ScheduleRecurringTask(BaseTask task, TimeSpan interval, Action? beforeExecution = null);

        /// <summary>
        /// Zatrzymuje wszystkie zadania
        /// </summary>
        void StopAllTasks();

        /// <summary>
        /// Pobiera status schedulera
        /// </summary>
        (int activeWorkers, int queueLength, int activeSyncWorkers, int syncQueueLength) GetStatus();

        /// <summary>
        /// Sprawdza czy scheduler jest aktywny
        /// </summary>
        bool IsRunning { get; }

        /// <summary>
        /// Pobiera statystyki wykonanych zadań
        /// </summary>
        TaskStatistics GetStatistics();
    }

    /// <summary>
    /// Statystyki wykonanych zadań
    /// </summary>
    public class TaskStatistics
    {
        public int TotalTasksExecuted { get; set; }
        public int SuccessfulTasks { get; set; }
        public int FailedTasks { get; set; }
        public TimeSpan AverageExecutionTime { get; set; }
        public DateTime LastTaskExecution { get; set; }
        public double SuccessRate => TotalTasksExecuted > 0 ? (double)SuccessfulTasks / TotalTasksExecuted * 100 : 0;
    }
}
