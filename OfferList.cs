namespace ShopBot
{
    public class OfferListItem
    {
        public string Id { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public decimal Price { get; set; }
        public string SellerName { get; set; } = string.Empty;
        public int SellerRating { get; set; }
        public int ReviewCount { get; set; }
        public bool IsOnline { get; set; }
        public Dictionary<string, string> Attributes { get; set; } = new Dictionary<string, string>();

        // Dodatkowe właściwości dla kompatybilności z parserami
        public string Title { get; set; } = string.Empty;
        public string DeliveryTime { get; set; } = string.Empty;
    }

    public class GameSpecificFilter
    {
        public Dictionary<string, bool> Enabled { get; set; } = new();
        public Dictionary<string, string> Values { get; set; } = new();
    }

    public class OfferFilter
    {
        public decimal? MinPrice { get; set; }
        public decimal? MaxPrice { get; set; }
        public int? MinRating { get; set; }
        public int? MinReviews { get; set; }
        public bool? OnlineOnly { get; set; }
        public List<string> IgnoredServers { get; set; } = new();
        public List<string> IgnoredPlatforms { get; set; } = new();
        public List<string> IgnoredShortDescriptions { get; set; } = new();
        public Dictionary<string, string> RequiredAttributes { get; set; } = new();
        public Dictionary<string, GameSpecificFilter> GameSpecificFilters { get; set; } = new();
    }
} 