# Wykrywanie Zmian Zawartości Ofert - Dokumentacja Implementacji

## Przegląd

Zaimplementowano system wykrywania znaczących zmian w zawartości ofert FunPay, który rozwiązuje problem gdy oferta o tym samym ID zmienia swoją zawartość (tytuł, cenę, opis). System automatycznie wykrywa takie zmiany i traktuje je jako nowe oferty, usuwając stare wersje z G2G.

## Komponenty Implementacji

### 1. OfferContentChangeDetector

**Lokalizacja:** `Services/OfferContentChangeDetector.cs`

Główny serwis odpowiedzialny za wykrywanie zmian zawartości ofert.

#### Kluczowe funkcjonalności:
- **Wykrywanie zmian ceny:** Sprawdza czy cena zmieniła się o więcej niż 15%
- **Wykrywanie zmian tytułu:** Używa algorytmu Levenshteina do obliczania podobieństwa (próg: 70%)
- **Algorytm Levenshteina:** Zoptymalizowana implementacja z wczesnym przerywaniem
- **Normalizacja stringów:** Konwersja na małe litery i usunięcie nadmiarowych spacji

#### Progi wykrywania:
```csharp
private const decimal SIGNIFICANT_PRICE_CHANGE_THRESHOLD = 0.15m; // 15% zmiany ceny
private const double TITLE_SIMILARITY_THRESHOLD = 0.7; // 70% podobieństwa tytułu
```

### 2. Integracja z ScanAndSyncOffersTask

**Lokalizacja:** `Tasks/ScanAndSyncOffersTask.cs`

Zintegrowano detektor zmian z głównym procesem synchronizacji ofert.

#### Proces wykrywania:
1. Dla każdej istniejącej oferty w G2G sprawdza odpowiadającą ofertę w FunPay
2. Pobiera szczegóły oferty z bazy danych
3. Porównuje zawartość używając `OfferContentChangeDetector`
4. Oferty ze znaczącymi zmianami dodaje do listy nieaktywnych (do usunięcia)
5. Nowe wersje ofert dodaje do listy nowych ofert (do dodania)

### 3. Rozszerzenia Logowania

**Lokalizacja:** `Services/Extensions/TaskLoggingExtensions.cs`

Dodano dedykowane metody logowania dla detektora zmian:
- `LogContentChangeDetectionStart`
- `LogContentChangeDetected`
- `LogPriceChangeDetected`
- `LogTitleChangeDetected`
- `LogContentChangeDetectionComplete`
- `LogContentChangeDetectionError`

### 4. Test Runner

**Lokalizacja:** `Services/OfferContentChangeDetectorTestRunner.cs`

Prosty test runner do weryfikacji poprawności implementacji:
- Test znaczącej zmiany ceny (100% wzrost)
- Test nieznacznej zmiany ceny (5% wzrost)
- Test znaczącej zmiany tytułu (WoW → Dota 2)
- Test nieznacznej zmiany tytułu (dodanie "with extras")
- Test braku zmian
- Test obsługi nieprawidłowej ceny
- Test progów algorytmu

## Analiza Wydajności

### Złożoność Obliczeniowa
- **Porównanie ceny:** O(1) - stała złożoność
- **Porównanie tytułu:** O(n*m) - gdzie n i m to długości tytułów
- **Łączna złożoność:** O(N * L²), gdzie N = liczba ofert, L = średnia długość tytułu

### Optymalizacje
1. **Wczesne przerywanie dla cen:** Jeśli cena różni się o więcej niż 15%, nie porównuje tytułów
2. **Optymalizacja algorytmu Levenshteina:** 
   - Użycie tylko dwóch wierszy zamiast całej macierzy (oszczędność pamięci)
   - Wczesne przerywanie dla bardzo różnych długości stringów
3. **Normalizacja stringów:** Efektywne porównywanie po konwersji na małe litery

### Szacunkowy Wpływ na Wydajność
- **Dla 500 ofert:** ~50-100ms dodatkowego czasu przetwarzania
- **Wpływ na całkowity proces:** <1% całkowitego czasu synchronizacji (~161s zamiast ~160s)

## Konfiguracja DI

Dodano rejestrację serwisów w `Services/DependencyConfig.cs`:
```csharp
// Offer Content Change Detector - wykrywanie zmian zawartości ofert
services.AddScoped<OfferContentChangeDetector>();

// Test Runner dla OfferContentChangeDetector
services.AddScoped<OfferContentChangeDetectorTestRunner>();
```

Zaktualizowano `Services/Factories/TaskFactory.cs` o nowy parametr konstruktora.

## Przykłady Użycia

### Uruchomienie Testów
```csharp
var testRunner = serviceProvider.GetRequiredService<OfferContentChangeDetectorTestRunner>();
testRunner.RunAllTests();
```

### Ręczne Sprawdzenie Zmian
```csharp
var detector = serviceProvider.GetRequiredService<OfferContentChangeDetector>();
var hasChanges = detector.HasSignificantChanges(existingOffer, newOfferDetails);
```

## Scenariusze Wykrywania

### Przykład 1: Znacząca Zmiana Ceny
- **Stara oferta:** "Konto WoW 60 lvl" - $50
- **Nowa oferta:** "Konto WoW 60 lvl" - $100
- **Wynik:** Wykryto zmianę (100% wzrost ceny > 15% próg)

### Przykład 2: Znacząca Zmiana Tytułu
- **Stara oferta:** "World of Warcraft Account Level 60" - $50
- **Nowa oferta:** "Dota 2 Account with Rare Items" - $50
- **Wynik:** Wykryto zmianę (podobieństwo tytułu < 70% próg)

### Przykład 3: Nieznaczne Zmiany
- **Stara oferta:** "World of Warcraft Account Level 60" - $50
- **Nowa oferta:** "World of Warcraft Account Level 60 with extras" - $52
- **Wynik:** Brak znaczących zmian (4% wzrost ceny < 15% próg, wysokie podobieństwo tytułu)

## Logowanie

System generuje szczegółowe logi:
```
[ChangeDetector] 🔍 Rozpoczynam wykrywanie zmian zawartości dla 150 ofert w grze RaidShadowLegends
[ChangeDetector] 💰 Wykryto zmianę ceny w ofercie 123456: $50.00 → $100.00 (zmiana: 100.00%)
[ChangeDetector] 🔄 Wykryto znaczące zmiany w ofercie 123456: 'Konto WoW 60 lvl' ($50.00) → 'Konto Dota 60 poziom' ($100.00)
[ChangeDetector] ✅ Sprawdzono 150 ofert w 75.2ms, wykryto 3 zmian zawartości
```

## Korzyści Implementacji

1. **Automatyczne wykrywanie:** System automatycznie identyfikuje oferty, które zmieniły zawartość
2. **Zapobieganie błędom:** Eliminuje publikowanie nieprawidłowych ofert na G2G
3. **Lepsza jakość danych:** Zapewnia spójność między systemami FunPay i G2G
4. **Minimalna wydajność:** Dodaje <1% czasu do procesu synchronizacji
5. **Konfigurowalne progi:** Łatwa modyfikacja progów wykrywania zmian
6. **Szczegółowe logowanie:** Pełna transparentność procesu wykrywania

## Przyszłe Ulepszenia

1. **Konfigurowalne progi:** Możliwość ustawienia progów per gra
2. **Buforowanie wyników:** Cache dla często porównywanych tytułów
3. **Statystyki wydajności:** Metryki czasu wykonania i wykrytych zmian
4. **Zaawansowane algorytmy:** Inne metryki podobieństwa stringów (Jaro-Winkler, Cosine similarity)
