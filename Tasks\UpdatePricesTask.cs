using System;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Playwright;
using ShopBot.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ShopBot.Services.Formatters;
using ShopBot.Services.Extensions;

namespace ShopBot.Tasks
{
    public class UpdatePricesTask : BaseTask
    {
        private readonly IBrowser _browser;
        private readonly IBrowserContext _context;
        private readonly ProcessedOffersTracker _processedOffersTracker;
        private readonly ParserService _parserService;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<UpdatePricesTask> _logger;

        public UpdatePricesTask(
            IBrowser browser,
            IBrowserContext context,
            ProcessedOffersTracker processedOffersTracker,
            ParserService parserService,
            IServiceProvider serviceProvider,
            ILogger<UpdatePricesTask> logger)
        {
            _browser = browser;
            _context = context;
            _processedOffersTracker = processedOffersTracker;
            _parserService = parserService;
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        public override async Task ExecuteAsync(CancellationToken cancellationToken)
        {
            try
            {
                Status = TaskStatus.Running;
                _logger.LogPriceUpdateStart();

                var trackedOffers = G2GOfferTracker.LoadAllOffers();
                var gamesToProcess = trackedOffers.Select(o => o.GameName).Distinct();

                foreach (var game in gamesToProcess)
                {
                    if (cancellationToken.IsCancellationRequested)
                        break;

                    try
                    {
                        _logger.LogGamePriceUpdateStart(game);
                        var gameOffers = trackedOffers.Where(o => o.GameName == game).ToList();
                        
                        var page = await _context.NewPageAsync();
                        try
                        {
                            var funPayService = _serviceProvider.GetRequiredService<Func<IPage, FunPayService>>()(page);
                            
                            foreach (var offer in gameOffers)
                            {
                                try
                                {
                                    // Pobierz aktualne szczegóły oferty z FunPay
                                    var offerDetails = await funPayService.GetOfferDetails($"https://funpay.com/lots/offer?id={offer.FunPayId}");
                                    
                                    // Oblicz nową cenę z limitami per gra i 25% marżą
                                    var originalPrice = decimal.Parse(offerDetails.ActualOfferPrice.Replace("$", ""));
                                    var calculatedPrice = PriceCalculator.CalculatePriceWithGameLimits(originalPrice, game);
                                    
                                    // Aktualizuj cenę na G2G
                                    var g2gPublisher = new G2GPublisherService(_browser, _context, null, _serviceProvider.GetRequiredService<DescriptionFormatterFactory>());
                                    await g2gPublisher.UpdateOfferPrice(offer.G2GOfferId, calculatedPrice);

                                    _logger.LogPriceUpdateSuccess(offer.FunPayId, offer.G2GOfferId, originalPrice, calculatedPrice);
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogTaskPriceUpdateError(offer.FunPayId, ex.Message);
                                }
                                
                                // Odczekaj między aktualizacjami
                                await Task.Delay(2000, cancellationToken);
                            }
                        }
                        finally
                        {
                            await page.CloseAsync();
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogGamePriceUpdateError(game, ex.Message);
                    }
                }

                _logger.LogPriceUpdateComplete();
                Status = TaskStatus.Completed;
            }
            catch (Exception ex)
            {
                Status = TaskStatus.Failed;
                ErrorMessage = ex.Message;
                _logger.LogError("[Update] Błąd podczas aktualizacji cen: {Error}", ex.Message);
                throw;
            }
        }
    }
} 