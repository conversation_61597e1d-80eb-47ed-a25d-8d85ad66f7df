using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ShopBot.Data;
using ShopBot.Models;
using ShopBot.Services.Interfaces;

namespace ShopBot.Services
{
    public class ErrorLogRepository : IErrorLogRepository
    {
        private readonly ShopBotDbContext _context;
        private readonly ILogger<ErrorLogRepository> _logger;

        public ErrorLogRepository(ShopBotDbContext context, ILogger<ErrorLogRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<ErrorLog> CreateAsync(ErrorLog errorLog)
        {
            _logger.LogDebug("[ErrorLogRepo] Creating error log: {ErrorType} - {ErrorMessage}", errorLog.ErrorType, errorLog.ErrorMessage);
            
            _context.ErrorLogs.Add(errorLog);
            await _context.SaveChangesAsync();
            
            _logger.LogDebug("[ErrorLogRepo] ✅ Created error log: {Id}", errorLog.Id);
            return errorLog;
        }

        public async Task<List<ErrorLog>> GetRecentErrorsAsync(int limit = 100)
        {
            return await _context.ErrorLogs
                .Include(e => e.Offer)
                .OrderByDescending(e => e.CreatedAt)
                .Take(limit)
                .ToListAsync();
        }

        public async Task<List<ErrorLog>> GetErrorsByTypeAsync(string errorType, int limit = 100)
        {
            return await _context.ErrorLogs
                .Where(e => e.ErrorType == errorType)
                .OrderByDescending(e => e.CreatedAt)
                .Take(limit)
                .ToListAsync();
        }

        public async Task<List<ErrorLog>> GetErrorsByGameAsync(string gameName, int limit = 100)
        {
            return await _context.ErrorLogs
                .Where(e => e.GameName == gameName)
                .OrderByDescending(e => e.CreatedAt)
                .Take(limit)
                .ToListAsync();
        }

        public async Task<List<ErrorLog>> GetErrorsByOfferAsync(Guid offerId)
        {
            return await _context.ErrorLogs
                .Where(e => e.OfferId == offerId)
                .OrderByDescending(e => e.CreatedAt)
                .ToListAsync();
        }

        public async Task<Dictionary<string, int>> GetErrorCountsByTypeAsync(DateTime from, DateTime to)
        {
            return await _context.ErrorLogs
                .Where(e => e.CreatedAt >= from && e.CreatedAt <= to)
                .GroupBy(e => e.ErrorType)
                .Select(g => new { Type = g.Key, Count = g.Count() })
                .ToDictionaryAsync(x => x.Type, x => x.Count);
        }

        public async Task<int> DeleteOldErrorsAsync(DateTime olderThan)
        {
            var oldErrors = await _context.ErrorLogs
                .Where(e => e.CreatedAt < olderThan)
                .ToListAsync();

            _context.ErrorLogs.RemoveRange(oldErrors);
            await _context.SaveChangesAsync();

            _logger.LogInformation("[ErrorLogRepo] 🗑️ Deleted {Count} old error logs", oldErrors.Count);
            return oldErrors.Count;
        }
    }
}
