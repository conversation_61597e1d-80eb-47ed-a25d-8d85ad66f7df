using HtmlAgilityPack;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ShopBot.Services.Parsers
{
    /// <summary>
    /// Parser odpowiedzialny za parsowanie list ofert
    /// </summary>
    public class OfferListParser : IOfferListParser
    {
        private readonly ILogger<OfferListParser> _logger;
        private readonly IDataExtractor _dataExtractor;
        private readonly IUrlValidator _urlValidator;

        public OfferListParser(
            ILogger<OfferListParser> logger,
            IDataExtractor dataExtractor,
            IUrlValidator urlValidator)
        {
            _logger = logger;
            _dataExtractor = dataExtractor;
            _urlValidator = urlValidator;
        }

        public List<OfferListItem> ParseOfferList(HtmlDocument document)
        {
            var offers = new List<OfferListItem>();

            try
            {
                _logger.LogDebug("📋 Rozpoczynam parsowanie listy ofert");

                // Różne selektory dla ofert w zależności od struktury strony
                var offerSelectors = new[]
                {
                    "//div[contains(@class, 'offer-item')]",
                    "//div[contains(@class, 'tc-item')]",
                    "//a[contains(@class, 'tc-item')]",
                    "//div[contains(@class, 'lot-item')]"
                };

                foreach (var selector in offerSelectors)
                {
                    var offerNodes = document.DocumentNode.SelectNodes(selector);
                    if (offerNodes != null && offerNodes.Count > 0)
                    {
                        _logger.LogDebug("🎯 Znaleziono {Count} ofert z selektorem: {Selector}", offerNodes.Count, selector);

                        foreach (var offerNode in offerNodes)
                        {
                            var offer = ParseSingleOfferFromList(offerNode);
                            if (offer != null && !string.IsNullOrEmpty(offer.Url))
                            {
                                offers.Add(offer);
                            }
                        }

                        // Jeśli znaleźliśmy oferty, nie szukaj dalej
                        if (offers.Count > 0)
                            break;
                    }
                }

                _logger.LogInformation("✅ Sparsowano {Count} ofert z listy", offers.Count);
                return offers;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Błąd podczas parsowania listy ofert");
                return offers;
            }
        }

        public OfferListItem ParseSingleOfferFromList(HtmlNode offerNode)
        {
            try
            {
                if (offerNode == null)
                    return null;

                var offer = new OfferListItem();

                // Wyciągnij URL oferty
                offer.Url = ExtractOfferUrl(offerNode);
                if (string.IsNullOrEmpty(offer.Url))
                {
                    _logger.LogTrace("⚠️ Nie znaleziono URL w węźle oferty");
                    return null;
                }

                // Wyciągnij ID z URL
                offer.Id = _urlValidator.ExtractIdFromUrl(offer.Url);

                // Wyciągnij tytuł
                offer.Title = ExtractOfferTitle(offerNode);

                // Wyciągnij cenę
                offer.Price = ExtractOfferPrice(offerNode);

                // Ustaw Title jako alias dla Description
                offer.Title = offer.Description;

                // Wyciągnij informacje o sprzedawcy
                offer.SellerName = ExtractSellerName(offerNode);
                offer.SellerRating = (int)ExtractSellerRating(offerNode);

                // Wyciągnij dodatkowe informacje
                offer.IsOnline = ExtractSellerOnlineStatus(offerNode);
                offer.DeliveryTime = ExtractDeliveryTime(offerNode);

                _logger.LogTrace("📦 Sparsowano ofertę: {Id} - {Title} - ${Price}", offer.Id, offer.Title, offer.Price);
                return offer;
            }
            catch (Exception ex)
            {
                _logger.LogTrace(ex, "⚠️ Błąd parsowania pojedynczej oferty");
                return null;
            }
        }

        private string ExtractOfferUrl(HtmlNode offerNode)
        {
            // Różne sposoby wyciągnięcia URL oferty
            var urlSelectors = new[]
            {
                ".//a[contains(@class, 'tc-item')]/@href",
                ".//a[contains(@href, '/lots/')]/@href",
                ".//a[contains(@href, '/offer/')]/@href",
                "./@href",
                ".//a/@href"
            };

            foreach (var selector in urlSelectors)
            {
                var urlNode = offerNode.SelectSingleNode(selector);
                if (urlNode != null)
                {
                    var url = urlNode.GetAttributeValue("href", "") ?? urlNode.InnerText;
                    if (!string.IsNullOrEmpty(url))
                    {
                        return _urlValidator.ValidateAndFixUrl(url);
                    }
                }
            }

            return "";
        }

        private string ExtractOfferTitle(HtmlNode offerNode)
        {
            var titleSelectors = new[]
            {
                ".//div[contains(@class, 'tc-desc-text')]",
                ".//div[contains(@class, 'offer-title')]",
                ".//h3",
                ".//h4",
                ".//span[contains(@class, 'title')]",
                ".//div[contains(@class, 'description')]"
            };

            foreach (var selector in titleSelectors)
            {
                var title = _dataExtractor.ExtractText(offerNode, selector);
                if (!string.IsNullOrEmpty(title))
                {
                    return title;
                }
            }

            return "Brak tytułu";
        }

        private decimal ExtractOfferPrice(HtmlNode offerNode)
        {
            var priceSelectors = new[]
            {
                ".//div[contains(@class, 'tc-price')]",
                ".//span[contains(@class, 'price')]",
                ".//div[contains(@class, 'offer-price')]",
                ".//span[contains(text(), '$')]",
                ".//div[contains(text(), '$')]"
            };

            foreach (var selector in priceSelectors)
            {
                var priceText = _dataExtractor.ExtractText(offerNode, selector);
                if (!string.IsNullOrEmpty(priceText))
                {
                    var price = _dataExtractor.ExtractNumber(priceText);
                    if (price > 0)
                    {
                        return price;
                    }
                }
            }

            return 0;
        }

        private string ExtractSellerName(HtmlNode offerNode)
        {
            var sellerSelectors = new[]
            {
                ".//div[contains(@class, 'media-user-name')]",
                ".//span[contains(@class, 'seller-name')]",
                ".//div[contains(@class, 'user-name')]",
                ".//a[contains(@class, 'seller')]"
            };

            foreach (var selector in sellerSelectors)
            {
                var sellerName = _dataExtractor.ExtractText(offerNode, selector);
                if (!string.IsNullOrEmpty(sellerName))
                {
                    return sellerName;
                }
            }

            return "";
        }

        private decimal ExtractSellerRating(HtmlNode offerNode)
        {
            var ratingSelectors = new[]
            {
                ".//div[contains(@class, 'media-user-rating')]",
                ".//span[contains(@class, 'rating')]",
                ".//div[contains(@class, 'seller-rating')]"
            };

            foreach (var selector in ratingSelectors)
            {
                var ratingText = _dataExtractor.ExtractText(offerNode, selector);
                if (!string.IsNullOrEmpty(ratingText))
                {
                    var rating = _dataExtractor.ExtractNumber(ratingText);
                    if (rating > 0)
                    {
                        return rating;
                    }
                }
            }

            return 0;
        }

        private bool ExtractSellerOnlineStatus(HtmlNode offerNode)
        {
            var onlineIndicators = new[]
            {
                ".//div[contains(@class, 'online')]",
                ".//span[contains(@class, 'online')]",
                ".//i[contains(@class, 'online')]"
            };

            foreach (var selector in onlineIndicators)
            {
                var onlineNode = offerNode.SelectSingleNode(selector);
                if (onlineNode != null)
                {
                    return true;
                }
            }

            return false;
        }

        private string ExtractDeliveryTime(HtmlNode offerNode)
        {
            var deliverySelectors = new[]
            {
                ".//div[contains(@class, 'delivery-time')]",
                ".//span[contains(@class, 'delivery')]",
                ".//div[contains(text(), 'min')]",
                ".//div[contains(text(), 'hour')]"
            };

            foreach (var selector in deliverySelectors)
            {
                var deliveryTime = _dataExtractor.ExtractText(offerNode, selector);
                if (!string.IsNullOrEmpty(deliveryTime))
                {
                    return deliveryTime;
                }
            }

            return "";
        }
    }
}
