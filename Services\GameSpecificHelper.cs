using System.Text.RegularExpressions;
using System.Web;
using System.Text;

namespace ShopBot.Services
{
    public static class GameSpecificHelper
    {
        // Mapowanie serwerów LoL
        public static string MapLoLServer(string server)
        {
            server = HttpUtility.HtmlDecode(server);
            
            return server.ToUpper() switch
            {
                "EU NORDIC & EAST" => "EUNE",
                "EU NORDIC &AMP; EAST" => "EUNE",
                "EU WEST" => "EUW",
                "JAPAN" => "JP",
                "LATIN AMERICA NORTH" => "LAN",
                "LATIN AMERICA SOUTH" => "LAS",
                "NORTH AMERICA" => "NA",
                "RUSSIA" => "RU",
                "TURKEY" => "TR",
                "BRAZIL" => "BR",
                "OCEANIA" => "OCE",
                "KOREA" => "KR",
                "PHILIPPINES" => "PH",
                "TAIWAN" => "TW",
                "THAILAND" => "TH",
                "VIETNAM" => "VN",
                "SINGAPORE" => "SG",
                "PUBLIC BETA ENVIRONMENT" => "PBE",
                "MIDDLE EAST" => "ME",
                _ => server  // zachowaj oryginalną nazwę jeśli nie ma mapowania
            };
        }

        // Określanie zakresu poziomów dla Tarkov
        public static string DetermineTarkovLevel(int level)
        {
            if (level >= 65) return "65+";
            if (level >= 60) return "60+";
            if (level >= 55) return "55+";
            if (level >= 50) return "50+";
            if (level >= 45) return "45+";
            if (level >= 40) return "40+";
            if (level >= 35) return "35+";
            if (level >= 30) return "30+";
            if (level >= 25) return "25+";
            if (level >= 20) return "20+";
            return "19 or below";
        }

        // Określanie zakresu championów dla League of Legends
        public static string DetermineLoLChampionsRange(int champions)
        {
            if (champions >= 160) return "160+";
            if (champions >= 130) return "130+";
            if (champions >= 100) return "100+";
            if (champions >= 50) return "50+";
            if (champions >= 30) return "30+";
            if (champions >= 10) return "10+";
            return "9 or below";
        }

        // Określanie zakresu skinów dla League of Legends
        public static string DetermineLoLSkinsRange(int skins)
        {
            if (skins >= 1000) return "1000+";
            if (skins >= 500) return "500+";
            if (skins >= 300) return "300+";
            if (skins >= 100) return "100+";
            if (skins >= 50) return "50+";
            if (skins >= 10) return "10+";
            return "9 or below";
        }

        // Określanie zakresu skinów (dla innych gier)
        public static string DetermineSkinsRange(int skins)
        {
            if (skins >= 1000) return "1000+";
            if (skins >= 500) return "500+";
            if (skins >= 300) return "300+";
            if (skins >= 150) return "150+";
            if (skins >= 100) return "100+";
            if (skins >= 50) return "50+";
            if (skins >= 10) return "10+";
            return "9 or below";
        }

        // Określanie zakresu MMR dla Dota 2
        public static string GetMMRRange(int mmr)
        {
            if (mmr >= 5000) return "5000+";
            if (mmr >= 4000) return "4000+";
            if (mmr >= 3000) return "3000+";
            if (mmr >= 2000) return "2000+";
            if (mmr >= 1000) return "1000+";
            if (mmr >= 500) return "500+";
            return "499 or below";
        }

        // Określanie zakresu Behavior Score dla Dota 2
        public static string GetBehaviorScoreRange(int behaviorScore)
        {
            if (behaviorScore == 10000) return "10000";
            if (behaviorScore >= 9000) return "9000+";
            if (behaviorScore >= 7000) return "7000+";
            if (behaviorScore >= 5000) return "5000+";
            if (behaviorScore >= 3000) return "3000+";
            if (behaviorScore >= 2000) return "2000+";
            if (behaviorScore >= 1000) return "1000+";
            return "999 or below";
        }

        // Określanie zakresu agentów dla Valorant
        public static string DetermineAgentsRange(int agents)
        {
            if (agents >= 20) return "20+";
            if (agents >= 15) return "15+";
            if (agents >= 10) return "10+";
            if (agents >= 5) return "5+";
            return "1-4";  // Domyślny zakres dla małej liczby agentów
        }

        // Mapowanie serwerów Albion
        public static string MapAlbionServer(string server)
        {
            return server switch
            {
                "America (Washington)" => "Albion America",
                "Asia (Singapore)" => "Albion Asia",
                "Europe (Amsterdam)" => "Albion Europe",
                _ => "Albion America"
            };
        }

        // Określanie edycji gry
        public static string DetermineGameEdition(string description)
        {
            description = description.ToLower();
            
            var editionKeywords = new Dictionary<string, string>
            {
                { "edge of darkness", "Edge of Darkness" },
                { "eod", "Edge of Darkness" },
                { "left behind", "Left Behind" },
                { "prepare for escape", "Prepare for Escape" },
                { "the unheard", "The Unheard" },
                { "tue", "The Unheard" },
                { "standard edition", "Standard" },
                { "standard", "Standard" },
                { "standart", "Standard" },
                { "standart edition", "Standard" }
            };

            foreach (var keyword in editionKeywords)
            {
                if (description.Contains(keyword.Key))
                {
                    return keyword.Value;
                }
            }

            if (description.Contains("gamma container") || description.Contains("gamma case"))
                return "Edge of Darkness";
            
            return "Standard";
        }

        public static string MapRankToG2G(string rank)
        {
            var rankMapping = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
            {
                {"Нет ранга", "UnRanked"},
                {"Unranked", "UnRanked"},
                {"Железо", "Iron"},
                {"Iron", "Iron"},
                {"Бронза", "Bronze"},
                {"Bronze", "Bronze"},
                {"Серебро", "Silver"},
                {"Silver", "Silver"},
                {"Золото", "Gold"},
                {"Gold", "Gold"},
                {"Платина", "Platinum"},
                {"Platinum", "Platinum"},
                {"Изумруд", "Emerald"},
                {"Emerald", "Emerald"},
                {"Алмаз", "Diamond"},
                {"Diamond", "Diamond"},
                {"Мастер", "Master"},
                {"Master", "Master"},
                {"Грандмастер", "Grandmaster"},
                {"Grandmaster", "Grandmaster"},
                {"Претендент", "Challenger"},
                {"Challenger", "Challenger"}
            };

            return rankMapping.GetValueOrDefault(rank, "UnRanked");
        }

        public static string GetRankFromMMR(int mmr)
        {
            if (mmr >= 5620) return "Immortal";
            if (mmr >= 4620) return "Divine";
            if (mmr >= 3850) return "Ancient";
            if (mmr >= 3080) return "Legend";
            if (mmr >= 2310) return "Archon";
            if (mmr >= 1540) return "Crusader";
            if (mmr >= 770) return "Guardian";
            return "Herald";
        }

        public static string GetPreciseSelector(string range)
        {
            // Specjalne przypadki dla zakresów liczbowych z "+"
            if (range.EndsWith("+") && int.TryParse(range.TrimEnd('+'), out int value))
            {
                // Zakresy zaczynające się od 1000
                if (value >= 1000)
                {
                    return $"div.q-item:has-text('{range}')";
                }
                
                // Zakresy zaczynające się od 500
                if (value >= 500)
                {
                    string notSelector = CreateNotSelector(range, new[] { 1000 });
                    return $"div.q-item:has-text('{range}'){notSelector}";
                }
                
                // Zakresy zaczynające się od 300
                if (value >= 300)
                {
                    string notSelector = CreateNotSelector(range, new[] { 500, 1000 });
                    return $"div.q-item:has-text('{range}'){notSelector}";
                }
                
                // Zakresy zaczynające się od 200
                if (value >= 200)
                {
                    string notSelector = CreateNotSelector(range, new[] { 300, 500, 1000 });
                    return $"div.q-item:has-text('{range}'){notSelector}";
                }
                
                // Zakresy zaczynające się od 150
                if (value >= 150)
                {
                    string notSelector = CreateNotSelector(range, new[] { 200, 300, 500, 1000 });
                    return $"div.q-item:has-text('{range}'){notSelector}";
                }
                
                // Zakresy zaczynające się od 100
                if (value >= 100)
                {
                    string notSelector = CreateNotSelector(range, new[] { 150, 200, 300, 500, 1000 });
                    return $"div.q-item:has-text('{range}'){notSelector}";
                }
                
                // Zakresy zaczynające się od 50
                if (value >= 50)
                {
                    string notSelector = CreateNotSelector(range, new[] { 100, 150, 200, 300, 500, 1000 });
                    return $"div.q-item:has-text('{range}'){notSelector}";
                }
                
                // Zakresy zaczynające się od 30
                if (value >= 30)
                {
                    string notSelector = CreateNotSelector(range, new[] { 50, 100, 130, 150, 200, 300, 500, 1000 });
                    return $"div.q-item:has-text('{range}'){notSelector}";
                }
                
                // Zakresy zaczynające się od 20
                if (value >= 20)
                {
                    string notSelector = CreateNotSelector(range, new[] { 30, 50, 100, 120, 200, 300, 500, 1000 });
                    return $"div.q-item:has-text('{range}'){notSelector}";
                }
                
                // Zakresy zaczynające się od 10
                if (value >= 10)
                {
                    string notSelector = CreateNotSelector(range, new[] { 20, 30, 40, 50, 100, 110, 210, 310, 1000 });
                    return $"div.q-item:has-text('{range}'){notSelector}";
                }
                
                // Zakresy zaczynające się od 1-9
                string smallNotSelector = CreateNotSelector(range, new[] { 10, 20, 30, 40, 50, 100, 1000 });
                return $"div.q-item:has-text('{range}'){smallNotSelector}";
            }
            
            // Specjalne przypadki dla zakresów "X or below"
            if (range.EndsWith(" or below"))
            {
                return $"div.q-item:has-text('{range}')";
            }
            
            // Domyślny selektor dla wszystkich innych przypadków
            return $"div.q-item:has-text('{range}')";
        }
        
        // Pomocnicza metoda do tworzenia selektorów :not()
        private static string CreateNotSelector(string range, int[] valuesToExclude)
        {
            StringBuilder builder = new StringBuilder();
            
            foreach (int value in valuesToExclude)
            {
                builder.Append($":not(:has-text('{value}+'))");
            }
            
            return builder.ToString();
        }

        public static string DetermineSkinsOption(int skinsCount)
        {
            if (skinsCount >= 300) return "300+";
            if (skinsCount >= 200) return "200+";
            if (skinsCount >= 100) return "100+";
            if (skinsCount >= 50) return "50+";
            if (skinsCount >= 10) return "10+";
            if (skinsCount >= 1) return "1+";
            return "10+";
        }

        public static string DetermineHoursOption(int hours)
        {
            if (hours >= 1500) return "1500+";
            if (hours >= 1000) return "1000+";
            if (hours >= 500) return "500+";
            if (hours >= 300) return "300+";
            if (hours >= 100) return "100+";
            if (hours >= 10) return "10+";
            if (hours >= 1) return "1+";
            if (hours == 0) return "0";
            return "100+";
        }

        public static string MapApexRank(string rank)
        {
            // Najpierw normalizujemy tekst (usuwamy spacje, wielkość liter itp.)
            rank = rank?.Trim().ToLower() ?? "unranked";

            return rank switch
            {
                "apex predator" => "Apex Predator",
                "master" => "Master",
                "diamond" => "Diamond",
                "platinum" => "Platinum",
                "gold" => "Gold",
                "silver" => "Silver",
                "bronze" => "Bronze",
                "rookie" => "Rookie",
                "unranked" => "Fresh New",  // Zmienione z "Rank Ready" na "Fresh New"
                "no rank" => "Fresh New",    // Zmienione z "Rank Ready" na "Fresh New"
                "" => "Fresh New",
                _ => "Fresh New"              // Domyślnie też "Fresh New"
            };
        }

        public static string MapApexLevel(int level)
        {
            if (level >= 500) return "500";
            if (level >= 400) return "400+";
            if (level >= 300) return "300+";
            if (level >= 200) return "200+";
            if (level >= 100) return "100+";
            if (level >= 50) return "50+";
            if (level >= 10) return "10+";
            return "9 or below";
        }

        public static string MapApexSkins(int skins)
        {
            if (skins >= 100) return "100+";
            if (skins >= 50) return "50+";
            if (skins >= 10) return "10+";
            return "9 or below";
        }

        public static string DetermineRustSkinsRange(int skins)
        {
            if (skins >= 300) return "300+";
            if (skins >= 200) return "200+";
            if (skins >= 100) return "100+";
            if (skins >= 50) return "50+";
            if (skins >= 10) return "10+";
            if (skins >= 1) return "1+";
            return "0";
        }

        public static string DetermineARRange(int ar)
        {
            if (ar >= 60) return "60";
            if (ar >= 55) return "55+";
            if (ar >= 50) return "50+";
            if (ar >= 45) return "45+";
            if (ar >= 40) return "40+";
            if (ar >= 35) return "35+";
            if (ar >= 30) return "30+";
            return "29 or below";
        }

        public static string DetermineGenshinCharactersRange(int characters)
        {
            if (characters >= 50) return "50+";
            if (characters >= 30) return "30+";
            if (characters >= 25) return "25+";
            if (characters >= 20) return "20+";
            if (characters >= 15) return "15+";
            if (characters >= 10) return "10+";
            if (characters >= 6) return "6+";
            return "5 or below";
        }

        public static string DetermineGenshinWeaponsRange(string weaponInfo)
        {
            // Próbujemy wyciągnąć liczbę z weaponInfo
            var match = Regex.Match(weaponInfo, @"(\d+)");
            if (match.Success && int.TryParse(match.Groups[1].Value, out int weapons))
            {
                if (weapons >= 50) return "50+";
                if (weapons >= 30) return "30+";
                if (weapons >= 25) return "25+";
                if (weapons >= 20) return "20+";
                if (weapons >= 15) return "15+";
                if (weapons >= 10) return "10+";
                if (weapons >= 6) return "6+";
                return "5 or below";
            }
            return "5 or below";
        }

        public static string DetermineWoWLevel(int level)
        {
            if (level >= 70) return "70";
            if (level >= 60) return "60+";
            if (level >= 50) return "50+";
            if (level >= 40) return "40+";
            if (level >= 30) return "30+";
            if (level >= 20) return "20+";
            if (level >= 10) return "10+";
            return "1+";
        }

        public static string MapWoWRace(string race)
        {
            return race?.ToLower() switch
            {
                "human" or "человек" => "Human",
                "dwarf" or "дворф" => "Dwarf",
                "night elf" or "ночной эльф" => "Night Elf",
                "gnome" or "гном" => "Gnome",
                "draenei" or "дреней" => "Draenei",
                "worgen" or "ворген" => "Worgen",
                "orc" or "орк" => "Orc",
                "undead" or "нежить" => "Undead",
                "tauren" or "таурен" => "Tauren",
                "troll" or "тролль" => "Troll",
                "blood elf" or "эльф крови" => "Blood Elf",
                "goblin" or "гоблин" => "Goblin",
                _ => "Human" // domyślnie Human
            };
        }

        public static string MapWoWClass(string characterClass)
        {
            return characterClass?.ToLower() switch
            {
                "warrior" or "воин" => "Warrior",
                "paladin" or "паладин" => "Paladin",
                "hunter" or "охотник" => "Hunter",
                "rogue" or "разбойник" => "Rogue",
                "priest" or "жрец" => "Priest",
                "shaman" or "шаман" => "Shaman",
                "mage" or "маг" => "Mage",
                "warlock" or "чернокнижник" => "Warlock",
                "monk" or "монах" => "Monk",
                "druid" or "друид" => "Druid",
                "demon hunter" or "охотник на демонов" => "Demon Hunter",
                "death knight" or "рыцарь смерти" => "Death Knight",
                _ => "Warrior" // domyślnie Warrior
            };
        }

        public static string MapCoDPlatform(string platform)
        {
            return platform.ToUpper() switch
            {
                "PC" => "PC",
                "PS" => "PSN",
                "XBOX" => "Xbox",
                _ => "PC" // domyślnie PC
            };
        }
        
        public static string MapFC25Platform(string platform)
        {
            string normalizedPlatform = platform?.Trim().ToUpperInvariant() ?? "PC";
            return normalizedPlatform switch
            {
                "PC" => "PC",
                "PS" => "Playstation",
                "PS4" => "Playstation",
                "PS5" => "Playstation",
                "XBOX" => "Xbox",
                "SWITCH" => "Switch",
                _ => "PC" // domyślnie PC
            };
        }
    }
} 