{"TotalOffers": 626, "AcceptedOffers": 217, "RejectedOffers": 409, "RejectionReasons": {"Cena ponizej minimum (3,93 < 15)": 11, "Ocena ponizej minimum (0 < 5)": 153, "Cena ponizej minimum (1,14 < 15)": 1, "Cena ponizej minimum (1,44 < 15)": 1, "Cena ponizej minimum (0,03 < 15)": 1, "Cena ponizej minimum (13,09 < 15)": 32, "Cena ponizej minimum (7,20 < 15)": 5, "Cena ponizej minimum (5,89 < 15)": 3, "Cena ponizej minimum (3,27 < 15)": 8, "Cena ponizej minimum (2,62 < 15)": 4, "Cena ponizej minimum (10,47 < 15)": 14, "Cena ponizej minimum (5,24 < 15)": 10, "Cena ponizej minimum (9,16 < 15)": 21, "Cena ponizej minimum (2,84 < 15)": 1, "Cena ponizej minimum (4,55 < 15)": 2, "Cena ponizej minimum (7,85 < 15)": 11, "Cena ponizej minimum (3,41 < 15)": 3, "Cena ponizej minimum (9,82 < 15)": 6, "Cena ponizej minimum (6,55 < 15)": 17, "Cena ponizej minimum (2,28 < 15)": 1, "Cena ponizej minimum (11,78 < 15)": 14, "Cena ponizej minimum (7,38 < 15)": 1, "Ocena ponizej minimum (4 < 5)": 7, "Cena ponizej minimum (4,58 < 15)": 6, "Cena ponizej minimum (8,51 < 15)": 2, "Cena ponizej minimum (5,58 < 15)": 1, "Cena ponizej minimum (5,69 < 15)": 3, "Cena ponizej minimum (6,83 < 15)": 3, "Cena ponizej minimum (7,96 < 15)": 2, "Cena ponizej minimum (13,65 < 15)": 1, "Zawiera zabronioną frazę (hardcoded): your choice": 3, "Cena ponizej minimum (12,86 < 15)": 2, "Cena ponizej minimum (14,02 < 15)": 2, "Cena ponizej minimum (11,69 < 15)": 2, "Cena ponizej minimum (10,48 < 15)": 1, "Cena ponizej minimum (8,18 < 15)": 1, "Cena ponizej minimum (11,13 < 15)": 4, "Cena ponizej minimum (1,05 < 15)": 1, "Cena ponizej minimum (5,12 < 15)": 1, "Cena ponizej minimum (1,37 < 15)": 6, "Cena ponizej minimum (1,71 < 15)": 1, "Duplikat oferty": 1, "Cena ponizej minimum (13,08 < 15)": 1, "Cena ponizej minimum (6,81 < 15)": 1, "Cena ponizej minimum (11,37 < 15)": 1, "Cena ponizej minimum (10,65 < 15)": 1, "Cena ponizej minimum (3,67 < 15)": 1, "Cena ponizej minimum (4,32 < 15)": 1, "Cena ponizej minimum (1,31 < 15)": 1, "Cena ponizej minimum (0,79 < 15)": 2, "Cena ponizej minimum (7,59 < 15)": 1, "Cena ponizej minimum (6,28 < 15)": 1, "Cena ponizej minimum (14,40 < 15)": 2, "Cena ponizej minimum (5,18 < 15)": 1, "Cena ponizej minimum (6,49 < 15)": 1, "Cena ponizej minimum (9,27 < 15)": 1, "Cena ponizej minimum (4,37 < 15)": 1, "Cena ponizej minimum (5,85 < 15)": 1, "Cena ponizej minimum (7,49 < 15)": 1, "Cena ponizej minimum (6,94 < 15)": 1, "Cena powyzej maximum (3259,68 > 1000)": 1, "Ocena ponizej minimum (3 < 5)": 1, "Cena ponizej minimum (2,77 < 15)": 1, "Cena ponizej minimum (7,70 < 15)": 1, "Cena ponizej minimum (5,63 < 15)": 1, "Cena ponizej minimum (1,11 < 15)": 1, "Ocena ponizej minimum (1 < 5)": 1, "Cena ponizej minimum (6,17 < 15)": 1, "Cena ponizej minimum (3,19 < 15)": 4, "Cena ponizej minimum (5,32 < 15)": 1, "Cena powyzej maximum (1570,93 > 1000)": 1, "Cena ponizej minimum (0,39 < 15)": 1, "Cena ponizej minimum (0,01 < 15)": 1, "Ocena ponizej minimum (2 < 5)": 1, "Cena ponizej minimum (1,17 < 15)": 1, "Cena ponizej minimum (3,51 < 15)": 1}, "GenerationTime": "2025-06-17T15:35:44.9002101+02:00"}