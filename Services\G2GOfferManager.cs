using System;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Playwright;

namespace ShopBot.Services
{
    public class G2GOfferManager
    {
        private readonly IBrowser _browser;
        private readonly IBrowserContext _context;
        private const string G2G_API_URL = "https://sls.g2g.com/offer/seller/7670672/brands?group_by=service&root_id=5830014a-b974-45c6-9672-b51e83112fb7&service_id=f6a1aba5-473a-4044-836a-8968bbab16d7&include_out_of_stock=1&include_inactive=0";

        private int _cachedOfferCount;
        private DateTime _lastCheckTime;
        private readonly TimeSpan _cacheValidityPeriod = TimeSpan.FromMinutes(15);
        private readonly SemaphoreSlim _syncLock = new SemaphoreSlim(1);

        public G2GOfferManager(IBrowser browser, IBrowserContext context)
        {
            _browser = browser;
            _context = context;
        }

        public async Task<int> GetCurrentG2GOfferCount()
        {
            IPage page = null;
            try
            {
                page = await _context.NewPageAsync();
                Console.WriteLine("[G2G] 🌐 Pobieram statystyki ofert z G2G...");
                
                await page.GotoAsync(G2G_API_URL);
                var content = await page.TextContentAsync("pre");
                
                if (string.IsNullOrEmpty(content))
                {
                    Console.WriteLine("[G2G] ℹ️ Brak ofert na G2G");
                    return 0;
                }

                var apiData = JsonSerializer.Deserialize<ApiResponse>(content);
                if (apiData?.Payload?.Results == null || !apiData.Payload.Results.Any())
                {
                    Console.WriteLine("[G2G] ℹ️ Brak ofert na G2G");
                    return 0;
                }

                var result = apiData.Payload.Results.First();
                return result.Total_result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[G2G] ℹ️ Brak ofert na G2G (błąd: {ex.Message})");
                return 0;
            }
            finally
            {
                if (page != null)
                {
                    await page.CloseAsync();
                }
            }
        }

        public async Task<bool> CanAddOffer()
        {
            await _syncLock.WaitAsync();
            try
            {
                // Używaj cache jeśli jest aktualny
                if (DateTime.Now - _lastCheckTime < _cacheValidityPeriod)
                {
                    var canAdd = _cachedOfferCount < 1000; // TYMCZASOWO ZWIĘKSZONE DLA TESTU
                    Console.WriteLine($"[G2G] Cache: {_cachedOfferCount}/1000 ofert {(canAdd ? "✅ Można dodać" : "❌ Limit osiągnięty")}");
                    return canAdd;
                }

                // Pobierz aktualną liczbę ofert z retry
                int currentOffers = await WithRetry(GetCurrentG2GOfferCount);
                
                // Aktualizuj cache
                _cachedOfferCount = currentOffers;
                _lastCheckTime = DateTime.Now;

                var canAddOffer = currentOffers < 1000; // TYMCZASOWO ZWIĘKSZONE DLA TESTU
                Console.WriteLine($"[G2G] Aktualizacja: {currentOffers}/1000 ofert {(canAddOffer ? "✅ Można dodać" : "❌ Limit osiągnięty")}");
                return canAddOffer;
            }
            finally
            {
                _syncLock.Release();
            }
        }

        private async Task<T> WithRetry<T>(Func<Task<T>> action, int maxRetries = 3)
        {
            for (int i = 0; i < maxRetries; i++)
            {
                try
                {
                    return await action();
                }
                catch (Exception ex) when (i < maxRetries - 1)
                {
                    Console.WriteLine($"[G2G] Próba {i + 1} nie powiodła się: {ex.Message}");
                    await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, i))); // Exponential backoff
                }
            }
            throw new Exception($"Operacja nie powiodła się po {maxRetries} próbach");
        }

        public async Task ForceRefreshOfferCount()
        {
            await _syncLock.WaitAsync();
            try
            {
                Console.WriteLine("[G2G] 🔄 Wymuszenie odświeżenia liczby ofert...");
                _cachedOfferCount = await GetCurrentG2GOfferCount();
                _lastCheckTime = DateTime.Now;
            }
            finally
            {
                _syncLock.Release();
            }
        }

        private class ApiResponse
        {
            [JsonPropertyName("code")]
            public int Code { get; set; }
            
            [JsonPropertyName("messages")]
            public List<ApiMessage> Messages { get; set; }
            
            [JsonPropertyName("payload")]
            public ApiPayload Payload { get; set; }
        }

        private class ApiPayload
        {
            [JsonPropertyName("results")]
            public List<ApiResult> Results { get; set; }
        }

        private class ApiResult
        {
            [JsonPropertyName("total_result")]
            public int Total_result { get; set; }
        }

        private class ApiMessage
        {
            [JsonPropertyName("code")]
            public int Code { get; set; }

            [JsonPropertyName("type")]
            public string Type { get; set; }

            [JsonPropertyName("text")]
            public string Text { get; set; }
        }
    }
} 