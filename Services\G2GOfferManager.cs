using System;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Playwright;

namespace ShopBot.Services
{
    public class G2GOfferManager
    {
        private readonly IBrowser _browser;
        private readonly IBrowserContext _context;
        private const string G2G_API_URL = "https://sls.g2g.com/offer/seller/7670672/brands?group_by=service&root_id=5830014a-b974-45c6-9672-b51e83112fb7&service_id=f6a1aba5-473a-4044-836a-8968bbab16d7&include_out_of_stock=1&include_inactive=0";

        private int _cachedOfferCount;
        private DateTime _lastCheckTime;
        private readonly TimeSpan _cacheValidityPeriod = TimeSpan.FromMinutes(15);
        private readonly SemaphoreSlim _syncLock = new SemaphoreSlim(1);

        public G2GOfferManager(IBrowser browser, IBrowserContext context)
        {
            _browser = browser;
            _context = context;
        }

        public async Task<int> GetCurrentG2GOfferCount()
        {
            IPage page = null;
            try
            {
                page = await _context.NewPageAsync();
                Console.WriteLine("[G2G] 🌐 Pobieram statystyki ofert z G2G...");

                await page.GotoAsync(G2G_API_URL);
                var content = await page.TextContentAsync("pre");

                if (string.IsNullOrEmpty(content))
                {
                    Console.WriteLine("[G2G] ℹ️ Brak ofert na G2G");
                    return 0;
                }

                var apiData = JsonSerializer.Deserialize<ApiResponse>(content);
                if (apiData?.Payload?.Results == null || !apiData.Payload.Results.Any())
                {
                    Console.WriteLine("[G2G] ℹ️ Brak ofert na G2G");
                    return 0;
                }

                var result = apiData.Payload.Results.First();
                return result.Total_result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[G2G] ℹ️ Brak ofert na G2G (błąd: {ex.Message})");
                return 0;
            }
            finally
            {
                if (page != null)
                {
                    await page.CloseAsync();
                }
            }
        }

        /// <summary>
        /// Pobiera rzeczywiste oferty z G2G API (nie tylko liczbę)
        /// </summary>
        public async Task<List<G2GRealOffer>> GetRealG2GOffers()
        {
            IPage page = null;
            try
            {
                page = await _context.NewPageAsync();
                Console.WriteLine("[G2G] 🌐 Pobieram rzeczywiste oferty z G2G API...");

                await page.GotoAsync(G2G_API_URL);
                var content = await page.TextContentAsync("pre");

                if (string.IsNullOrEmpty(content))
                {
                    Console.WriteLine("[G2G] ℹ️ Brak ofert na G2G");
                    return new List<G2GRealOffer>();
                }

                var apiData = JsonSerializer.Deserialize<ApiResponse>(content);
                if (apiData?.Payload?.Results == null || !apiData.Payload.Results.Any())
                {
                    Console.WriteLine("[G2G] ℹ️ Brak ofert na G2G");
                    return new List<G2GRealOffer>();
                }

                var result = apiData.Payload.Results.First();
                var realOffers = new List<G2GRealOffer>();

                // Pobierz oferty z każdej gry/brand
                if (result.Brands != null)
                {
                    foreach (var brand in result.Brands)
                    {
                        var offers = await GetOffersForBrand(brand.Brand_id);
                        realOffers.AddRange(offers);
                    }
                }

                Console.WriteLine($"[G2G] ✅ Pobrano {realOffers.Count} rzeczywistych ofert z G2G");
                return realOffers;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[G2G] ❌ Błąd podczas pobierania rzeczywistych ofert: {ex.Message}");
                return new List<G2GRealOffer>();
            }
            finally
            {
                if (page != null)
                {
                    await page.CloseAsync();
                }
            }
        }

        private async Task<List<G2GRealOffer>> GetOffersForBrand(string brandId)
        {
            IPage page = null;
            try
            {
                page = await _context.NewPageAsync();

                // ✅ NOWE PODEJŚCIE: Web scraping publicznej strony sklepu TrustCraft
                Console.WriteLine($"[G2G] 🔍 Scrapuję oferty z publicznej strony TrustCraft dla brand: {brandId}...");

                // Idź na publiczną stronę sklepu TrustCraft
                var storeUrl = "https://www.g2g.com/stores/trustcraft";
                await page.GotoAsync(storeUrl);

                // Czekaj na załadowanie ofert
                await page.WaitForTimeoutAsync(5000);

                // Znajdź wszystkie oferty na publicznej stronie sklepu
                var offerElements = await page.QuerySelectorAllAsync(".offer-card, .product-card, .item-card, .offer-item, [data-testid*='offer'], .card");

                Console.WriteLine($"[G2G] 🔍 Znaleziono {offerElements.Count} elementów ofert na stronie");

                var offers = new List<G2GRealOffer>();

                foreach (var element in offerElements)
                {
                    try
                    {
                        // Wyciągnij tytuł oferty - różne możliwe selektory
                        var titleElement = await element.QuerySelectorAsync("h3, h4, .title, .offer-title, .product-title, .card-title, .item-title");
                        var title = titleElement != null ? await titleElement.TextContentAsync() : "";

                        // Wyciągnij ID oferty z różnych atrybutów
                        var offerId = await element.GetAttributeAsync("data-offer-id") ??
                                     await element.GetAttributeAsync("data-id") ??
                                     await element.GetAttributeAsync("id") ?? "";

                        // Wyciągnij cenę
                        var priceElement = await element.QuerySelectorAsync(".price, .offer-price, .amount, .cost, .value, .usd");
                        var priceText = priceElement != null ? await priceElement.TextContentAsync() : "0";

                        // Wyciągnij opis (może zawierać FunPay ID)
                        var descElement = await element.QuerySelectorAsync(".description, .desc, .offer-desc, .content");
                        var description = descElement != null ? await descElement.TextContentAsync() : "";

                        if (!string.IsNullOrEmpty(title) || !string.IsNullOrEmpty(description))
                        {
                            offers.Add(new G2GRealOffer
                            {
                                OfferId = string.IsNullOrEmpty(offerId) ? Guid.NewGuid().ToString() : offerId,
                                Title = (title + " " + description).Trim(),
                                GameName = GetGameNameFromBrandId(brandId),
                                BrandId = brandId,
                                Price = ParsePrice(priceText),
                                Status = "Active"
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"[G2G] ⚠️ Błąd parsowania elementu oferty: {ex.Message}");
                    }
                }

                Console.WriteLine($"[G2G] ✅ Znaleziono {offers.Count} ofert przez web scraping");

                // Loguj szczegóły ofert
                foreach (var offer in offers.Take(3))
                {
                    Console.WriteLine($"[G2G] 📋 Oferta: {offer.OfferId} - {offer.Title}");
                }

                return offers;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[G2G] ❌ Błąd podczas scrapowania ofert: {ex.Message}");
                return new List<G2GRealOffer>();
            }
            finally
            {
                if (page != null)
                {
                    await page.CloseAsync();
                }
            }
        }

        private decimal ParsePrice(string priceText)
        {
            if (string.IsNullOrEmpty(priceText)) return 0;

            // Usuń wszystkie znaki oprócz cyfr i kropki
            var cleanPrice = System.Text.RegularExpressions.Regex.Replace(priceText, @"[^\d.]", "");

            return decimal.TryParse(cleanPrice, out var price) ? price : 0;
        }

        private string GetGameNameFromBrandId(string brandId)
        {
            // ✅ MAPOWANIE brand_id na nazwy gier
            var mapping = new Dictionary<string, string>
            {
                {"lgc_game_26258", "RaidShadowLegends"},  // Potwierdzone z API
                {"lgc_game_26259", "WatcherOfRealms"},    // Prawdopodobne
                {"lgc_game_26260", "Albion"},             // Prawdopodobne
                {"lgc_game_26261", "LeagueOfLegends"},    // Prawdopodobne
                {"lgc_game_26262", "Valorant"},           // Prawdopodobne
                {"lgc_game_26263", "GenshinImpact"},      // Prawdopodobne
                {"lgc_game_26264", "PUBG"},               // Prawdopodobne
                {"lgc_game_26265", "Rust"},               // Prawdopodobne
                {"lgc_game_26266", "EscapeFromTarkov"},   // Prawdopodobne
                {"lgc_game_26267", "WorldOfWarcraft"},    // Prawdopodobne
                {"lgc_game_26268", "Dota2"},              // Prawdopodobne
                {"lgc_game_26269", "ApexLegends"}         // Prawdopodobne
            };

            return mapping.TryGetValue(brandId, out var gameName) ? gameName : $"Unknown_{brandId}";
        }

        private string GetGameUrlName(string gameName)
        {
            var mapping = new Dictionary<string, string>
            {
                {"RaidShadowLegends", "raid-shadow-legends-account"},
                {"WatcherOfRealms", "watcher-of-realms-account"},
                {"LeagueOfLegends", "league-of-legends-account"},
                {"Valorant", "valorant-account"},
                {"GenshinImpact", "genshin-impact-account"},
                {"PUBG", "playerunknowns-battlegrounds-account"},
                {"Rust", "rust-account"},
                {"EscapeFromTarkov", "escape-from-tarkov-account"},
                {"WorldOfWarcraft", "world-of-warcraft-account"},
                {"Dota2", "dota-2-account"},
                {"ApexLegends", "apex-legends-account"}
            };

            return mapping.TryGetValue(gameName, out var urlName) ? urlName : gameName.ToLower();
        }

        public async Task<bool> CanAddOffer()
        {
            await _syncLock.WaitAsync();
            try
            {
                // Używaj cache jeśli jest aktualny
                if (DateTime.Now - _lastCheckTime < _cacheValidityPeriod)
                {
                    var canAdd = _cachedOfferCount < 1000; // TYMCZASOWO ZWIĘKSZONE DLA TESTU
                    Console.WriteLine($"[G2G] Cache: {_cachedOfferCount}/1000 ofert {(canAdd ? "✅ Można dodać" : "❌ Limit osiągnięty")}");
                    return canAdd;
                }

                // Pobierz aktualną liczbę ofert z retry
                int currentOffers = await WithRetry(GetCurrentG2GOfferCount);
                
                // Aktualizuj cache
                _cachedOfferCount = currentOffers;
                _lastCheckTime = DateTime.Now;

                var canAddOffer = currentOffers < 1000; // TYMCZASOWO ZWIĘKSZONE DLA TESTU
                Console.WriteLine($"[G2G] Aktualizacja: {currentOffers}/1000 ofert {(canAddOffer ? "✅ Można dodać" : "❌ Limit osiągnięty")}");
                return canAddOffer;
            }
            finally
            {
                _syncLock.Release();
            }
        }

        private async Task<T> WithRetry<T>(Func<Task<T>> action, int maxRetries = 3)
        {
            for (int i = 0; i < maxRetries; i++)
            {
                try
                {
                    return await action();
                }
                catch (Exception ex) when (i < maxRetries - 1)
                {
                    Console.WriteLine($"[G2G] Próba {i + 1} nie powiodła się: {ex.Message}");
                    await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, i))); // Exponential backoff
                }
            }
            throw new Exception($"Operacja nie powiodła się po {maxRetries} próbach");
        }

        public async Task ForceRefreshOfferCount()
        {
            await _syncLock.WaitAsync();
            try
            {
                Console.WriteLine("[G2G] 🔄 Wymuszenie odświeżenia liczby ofert...");
                _cachedOfferCount = await GetCurrentG2GOfferCount();
                _lastCheckTime = DateTime.Now;
            }
            finally
            {
                _syncLock.Release();
            }
        }

        public class G2GRealOffer
        {
            public string OfferId { get; set; }
            public string Title { get; set; }
            public string GameName { get; set; }
            public string BrandId { get; set; }
            public decimal Price { get; set; }
            public string Status { get; set; }
            public string FunPayId { get; set; } // Jeśli można wyciągnąć z opisu
        }

        private class ApiResponse
        {
            [JsonPropertyName("code")]
            public int Code { get; set; }

            [JsonPropertyName("messages")]
            public List<ApiMessage> Messages { get; set; }

            [JsonPropertyName("payload")]
            public ApiPayload Payload { get; set; }
        }

        private class ApiPayload
        {
            [JsonPropertyName("results")]
            public List<ApiResult> Results { get; set; }
        }

        private class ApiResult
        {
            [JsonPropertyName("total_result")]
            public int Total_result { get; set; }

            [JsonPropertyName("brands")]
            public List<ApiBrand> Brands { get; set; }
        }

        private class ApiBrand
        {
            [JsonPropertyName("brand_id")]
            public string Brand_id { get; set; }

            [JsonPropertyName("total_result")]
            public int Total_result { get; set; }
        }

        private class ApiMessage
        {
            [JsonPropertyName("code")]
            public int Code { get; set; }

            [JsonPropertyName("type")]
            public string Type { get; set; }

            [JsonPropertyName("text")]
            public string Text { get; set; }
        }

        private class SearchApiResponse
        {
            [JsonPropertyName("code")]
            public int Code { get; set; }

            [JsonPropertyName("payload")]
            public SearchPayload Payload { get; set; }
        }

        private class SearchPayload
        {
            [JsonPropertyName("total_result")]
            public int TotalResult { get; set; }

            [JsonPropertyName("results")]
            public List<SearchResult> Results { get; set; }
        }

        private class SearchResult
        {
            [JsonPropertyName("offer_id")]
            public string OfferId { get; set; }

            [JsonPropertyName("title")]
            public string Title { get; set; }

            [JsonPropertyName("unit_price")]
            public decimal UnitPrice { get; set; }

            [JsonPropertyName("description")]
            public string Description { get; set; }
        }
    }
} 