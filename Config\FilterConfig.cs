#nullable disable
using System.IO;
using System.Text.Json;

namespace ShopBot.Config
{
    public class FilterConfig
    {
        public static (OfferFilter Filter, ImgurConfig? Imgur, FlickrConfig? Flickr) LoadFromFile(string path = "config.json")
        {
            if (!File.Exists(path))
            {
                var defaultConfig = new ConfigWrapper
                {
                    Filter = new OfferFilter
                    {
                        MinPrice = 10,
                        MaxPrice = 200,
                        MinRating = 4,
                        MinReviews = 3,
                        OnlineOnly = false
                    },
                    Imgur = new ImgurConfig
                    {
                        ClientId = "9e830302bbd7760"
                    },
                    Flickr = new FlickrConfig
                    {
                        ApiKey = "86f5cb7a6307997b1aa61aba103cb78f",
                        ApiSecret = "74f6b75886506aa4"
                    }
                };

                SaveToFile(defaultConfig, path);
                return (defaultConfig.Filter, defaultConfig.Imgur, defaultConfig.Flickr);
            }

            var json = File.ReadAllText(path);
            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
            var config = JsonSerializer.Deserialize<ConfigWrapper>(json, options);
            return (config?.Filter ?? new OfferFilter(), config?.Imgur, config?.Flickr);
        }

        public static void SaveToFile(ConfigWrapper config, string path = "config.json")
        {
            var json = JsonSerializer.Serialize(config, new JsonSerializerOptions 
            { 
                WriteIndented = true 
            });
            File.WriteAllText(path, json);
        }

        public class ConfigWrapper
        {
            public OfferFilter? Filter { get; set; }
            public ImgurConfig? Imgur { get; set; }
            public FlickrConfig? Flickr { get; set; }
        }

        public class ImgurConfig
        {
            public string? ClientId { get; set; }
            public string? ClientSecret { get; set; }
        }

        public class FlickrConfig
        {
            public string? ApiKey { get; set; }
            public string? ApiSecret { get; set; }
        }
    }
}