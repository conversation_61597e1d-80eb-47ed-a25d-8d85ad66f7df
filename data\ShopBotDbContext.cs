using Microsoft.EntityFrameworkCore;
using ShopBot.Models;

namespace ShopBot.Data
{
    public class ShopBotDbContext : DbContext
    {
        public ShopBotDbContext(DbContextOptions<ShopBotDbContext> options) : base(options)
        {
        }

        public DbSet<Offer> Offers { get; set; }
        public DbSet<ProcessingStat> ProcessingStats { get; set; }
        public DbSet<ImageUpload> ImageUploads { get; set; }
        public DbSet<ErrorLog> ErrorLogs { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Offer configuration
            modelBuilder.Entity<Offer>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.FunPayOfferId).IsUnique();
                entity.HasIndex(e => new { e.GameName, e.ProcessedAt });
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.CreatedAt);

                entity.Property(e => e.OriginalPrice).HasPrecision(10, 2);
                entity.Property(e => e.ConvertedPrice).HasPrecision(10, 2);

                // Configure relationships
                entity.HasMany(e => e.ImageUploads)
                      .WithOne(e => e.Offer)
                      .HasForeignKey(e => e.OfferId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasMany(e => e.ErrorLogs)
                      .WithOne(e => e.Offer)
                      .HasForeignKey(e => e.OfferId)
                      .OnDelete(DeleteBehavior.SetNull);
            });

            // ProcessingStat configuration
            modelBuilder.Entity<ProcessingStat>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => new { e.Date, e.GameName }).IsUnique();
                entity.HasIndex(e => e.Date);
            });

            // ImageUpload configuration
            modelBuilder.Entity<ImageUpload>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.OfferId);
                entity.HasIndex(e => e.UploadStatus);
                entity.HasIndex(e => e.CreatedAt);
            });

            // ErrorLog configuration
            modelBuilder.Entity<ErrorLog>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.OfferId);
                entity.HasIndex(e => e.ErrorType);
                entity.HasIndex(e => e.CreatedAt);
                entity.HasIndex(e => e.GameName);
            });
        }

        public override int SaveChanges()
        {
            UpdateTimestamps();
            return base.SaveChanges();
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateTimestamps();
            return await base.SaveChangesAsync(cancellationToken);
        }

        private void UpdateTimestamps()
        {
            var entries = ChangeTracker.Entries()
                .Where(e => e.Entity is Offer && e.State == EntityState.Modified);

            foreach (var entry in entries)
            {
                if (entry.Entity is Offer offer)
                {
                    offer.UpdatedAt = DateTime.UtcNow;
                }
            }
        }
    }
}
