using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ShopBot.Services.Interfaces
{
    /// <summary>
    /// Unified interface for offer tracking - supports both legacy JSON and new Database systems
    /// </summary>
    public interface IOfferTracker
    {
        // Synchronous methods (for backward compatibility with legacy code)
        bool WasOfferProcessed(string funPayOfferId);
        void AddProcessedOffer(string funPayOfferId);
        List<string> GetAllOfferIds();
        DateTime GetLastProcessingTime();
        void SynchronizeWithActiveOffers(ISet<string> verifiedOffers);
        
        // Asynchronous methods (for new database-powered functionality)
        Task<bool> WasOfferProcessedAsync(string funPayOfferId);
        Task AddProcessedOfferAsync(string funPayOfferId, string gameName);
        Task<List<string>> GetAllOfferIdsAsync();
        Task<DateTime> GetLastProcessingTimeAsync();
        Task<bool> TryProcessOfferAsync(string funPayOfferId, string gameName);
        Task SynchronizeWithActiveOffersAsync(ISet<string> verifiedOffers);
        Task<int> GetProcessedOffersCountAsync();
        Task<Dictionary<string, int>> GetOfferCountsByGameAsync();
        
        // Migration support
        Task MigrateFromJsonToDatabaseAsync();

        // Enhanced status management (for SmartOfferTracker)
        Task MarkAsPublishedAsync(string funPayOfferId, string g2gOfferId);
        Task MarkAsFailedAsync(string funPayOfferId, string reason);
        Task MarkAsRejectedAsync(string funPayOfferId, string reason);
        Task MarkAsLimitReachedAsync(string funPayOfferId);
        Task<Dictionary<string, int>> GetStatusCountsAsync();
        void ClearSessionCache();
    }
}
