using Microsoft.Extensions.Logging;

namespace ShopBot.Services.Extensions
{
    /// <summary>
    /// Extension methods dla G2GPublisherService structured logging
    /// Zastępuje Console.WriteLine z structured logging
    /// </summary>
    public static class G2GLoggingExtensions
    {
        // ===== PRICE OPERATIONS =====
        public static void LogPriceUpdate(this ILogger logger, string offerId, decimal newPrice)
            => logger.LogInformation("[G2G] 💰 Updating price for offer {OfferId} to ${NewPrice}", offerId, newPrice);
        
        public static void LogPriceUpdateSuccess(this ILogger logger, string offerId)
            => logger.LogInformation("[G2G] ✅ Successfully updated price for offer {OfferId}", offerId);
        
        public static void LogPriceUpdateError(this ILogger logger, string offerId, string error)
            => logger.LogError("[G2G] ❌ Error updating price for offer {OfferId}: {Error}", offerId, error);

        public static void LogPriceParsingError(this ILogger logger, string priceString)
            => logger.LogWarning("[G2G Publisher] ⚠️ Nie udało się sparsować ceny: {PriceString}", priceString);

        public static void LogInvalidPriceType(this ILogger logger, string typeName)
            => logger.LogWarning("[G2G Publisher] ⚠️ Nieprawidłowy typ ceny: {TypeName}", typeName);

        // ===== IMAGE OPERATIONS =====
        public static void LogImageUploadStart(this ILogger logger, int imageCount)
            => logger.LogInformation("[G2G] 🖼️ Rozpoczynam upload {ImageCount} obrazów do Dropbox", imageCount);
        
        public static void LogImageUploadResult(this ILogger logger, int successful, int total)
            => logger.LogInformation("[G2G] 📊 Pomyślnie zauploadowano {Successful}/{Total} obrazów", successful, total);
        
        public static void LogImageUploadError(this ILogger logger, string error)
            => logger.LogError("[G2G] ❌ Błąd podczas uploadu obrazów: {Error}", error);

        public static void LogNoImagesToUpload(this ILogger logger)
            => logger.LogInformation("[G2G] ℹ️ Brak obrazów do uploadu");

        public static void LogNoSuccessfulUploads(this ILogger logger)
            => logger.LogWarning("[G2G] ⚠️ Nie udało się zauploadować żadnego obrazu");

        // ===== GAME PROCESSING =====
        public static void LogGameProcessingStart(this ILogger logger, string gameName)
            => logger.LogInformation("[G2G] 🎮 Rozpoczynam przetwarzanie gry: {GameName}", gameName);
        
        public static void LogGameProcessingEnd(this ILogger logger, string gameName)
            => logger.LogInformation("[G2G] ✅ Zakończono przetwarzanie gry: {GameName}", gameName);

        public static void LogGameAccountDetails(this ILogger logger, string gameName, object details)
            => logger.LogInformation("[{GameName}] 📋 Processing account details: {@Details}", gameName, details);

        public static void LogGameProcessingFinished(this ILogger logger, string gameName)
            => logger.LogInformation("[{GameName}] ✅ Finished processing account", gameName);

        // ===== FORM OPERATIONS =====
        public static void LogButtonClick(this ILogger logger, string buttonText)
            => logger.LogDebug("[G2G] 🖱️ Klikam przycisk: {ButtonText}", buttonText);
        
        public static void LogFieldFill(this ILogger logger, string label, string value)
            => logger.LogDebug("[G2G] ✏️ Wypełniam pole {Label}: {Value}", label, value);
        
        public static void LogDropdownSelection(this ILogger logger, string label, string option)
            => logger.LogDebug("[G2G] 📋 Wybieram opcję dla {Label}: {Option}", label, option);

        public static void LogDropdownFallback(this ILogger logger, string label)
            => logger.LogWarning("[G2G] ⚠️ Standardowy selektor nie zadziałał dla '{Label}', próbuję alternatywnego", label);

        public static void LogSelectorUsage(this ILogger logger, string selector)
            => logger.LogDebug("[G2G] 🔍 Używam selektora: {Selector}", selector);

        // ===== PLATFORM SELECTION =====
        public static void LogPlatformSelectionStart(this ILogger logger, string gameName)
            => logger.LogInformation("[G2G] 🎮 Rozpoczynam wybór Platform dla {GameName}", gameName);

        public static void LogPlatformSelectionSuccess(this ILogger logger, string platform)
            => logger.LogInformation("[G2G] ✅ Pomyślnie wybrano Platform: {Platform}", platform);

        public static void LogPlatformSelectionError(this ILogger logger, string error)
            => logger.LogError("[G2G] ❌ Błąd przy wyborze Platform: {Error}", error);

        public static void LogPlatformFallbackStart(this ILogger logger)
            => logger.LogInformation("[G2G] 🔄 Próbuję bezpośredniego selektora dla Mobile Legends");

        public static void LogPlatformFallbackSuccess(this ILogger logger)
            => logger.LogInformation("[G2G] ✅ Fallback zakończony pomyślnie");

        public static void LogPlatformFallbackError(this ILogger logger, string error)
            => logger.LogError("[G2G] ❌ Fallback również nie powiódł się: {Error}", error);

        // ===== MEDIA OPERATIONS =====
        public static void LogMediaUploadStart(this ILogger logger, int imageCount)
            => logger.LogInformation("[G2G] 📸 Dodaję {ImageCount} obrazów do oferty", imageCount);
        
        public static void LogMediaUploadProgress(this ILogger logger, int current, int total, string imageUrl)
            => logger.LogDebug("[G2G] 📤 Dodaję obraz {Current}/{Total}: {ImageUrl}", current, total, imageUrl);
        
        public static void LogMediaUploadSuccess(this ILogger logger, int imageCount)
            => logger.LogInformation("[G2G] ✅ Pomyślnie dodano {ImageCount} obrazów przez sekcję Media", imageCount);

        public static void LogMediaUploadFallback(this ILogger logger)
            => logger.LogWarning("[G2G] ⚠️ Nie udało się dodać przez sekcję media, dodaję linki do opisu");

        public static void LogMediaUploadError(this ILogger logger, string error)
            => logger.LogError("[G2G] ❌ Błąd podczas dodawania obrazów do oferty: {Error}", error);

        public static void LogMediaSectionStart(this ILogger logger)
            => logger.LogInformation("[G2G] 🎬 Próbuję dodać obrazy przez sekcję Media");

        public static void LogMediaFieldsNotFound(this ILogger logger, int imageIndex)
            => logger.LogWarning("[G2G] ⚠️ Nie znaleziono pustych pól dla obrazu {ImageIndex}", imageIndex + 1);

        public static void LogMediaFieldsDebug(this ILogger logger, bool titleFound, bool urlFound)
            => logger.LogDebug("[G2G] 🔍 Debug: titleInput={TitleFound}, urlInput={UrlFound}", titleFound, urlFound);

        public static void LogMediaFieldsRetrySuccess(this ILogger logger)
            => logger.LogInformation("[G2G] ✅ Znaleziono pola po ponownej próbie");

        public static void LogMediaFieldsRetryFailed(this ILogger logger)
            => logger.LogError("[G2G] ❌ Nadal brak pustych pól - przerywam dodawanie przez Media");

        public static void LogAddMediaButtonClick(this ILogger logger)
            => logger.LogInformation("[G2G] ✅ Kliknięto 'Add media' dla następnego obrazu");

        public static void LogAddMediaButtonDisabled(this ILogger logger)
            => logger.LogWarning("[G2G] ⚠️ Przycisk 'Add media' jest wyłączony");

        public static void LogAddMediaButtonNotFound(this ILogger logger)
            => logger.LogWarning("[G2G] ⚠️ Nie znaleziono przycisku 'Add media'");

        // ===== URL VALIDATION =====
        public static void LogEmptyImageUrl(this ILogger logger)
            => logger.LogWarning("[G2G] ⚠️ Pusty URL obrazu");

        public static void LogUrlValidation(this ILogger logger, string originalUrl, string cleanedUrl)
            => logger.LogDebug("[G2G] 🔧 Wyczyszczono URL: {OriginalUrl} -> {CleanedUrl}", originalUrl, cleanedUrl);

        public static void LogInvalidImageUrl(this ILogger logger, string cleanedUrl)
            => logger.LogWarning("[G2G] ⚠️ Nieprawidłowy URL obrazu: {CleanedUrl}", cleanedUrl);

        public static void LogUrlValidationError(this ILogger logger, string imageUrl, string error)
            => logger.LogWarning("[G2G] ⚠️ Błąd walidacji URL {ImageUrl}: {Error}", imageUrl, error);

        // ===== DESCRIPTION OPERATIONS =====
        public static void LogDescriptionLinkAddError(this ILogger logger, string error)
            => logger.LogError("[G2G] ❌ Błąd podczas dodawania linków do opisu: {Error}", error);
    }
}
