using Microsoft.EntityFrameworkCore;
using ShopBot.Data;
using ShopBot.Models;
using System;
using System.Threading.Tasks;

namespace ShopBot
{
    public class DatabaseTest
    {
        public static async Task TestDatabaseAsync()
        {
            var connectionString = "Server=localhost;Database=shopbotdb;Uid=root;Pwd=**********;";

            var options = new DbContextOptionsBuilder<ShopBotDbContext>()
                .UseMySql(connectionString, ServerVersion.AutoDetect(connectionString))
                .Options;

            try
            {
                using var context = new ShopBotDbContext(options);
                
                Console.WriteLine("🔍 Testing database connection...");
                
                // Test connection
                var canConnect = await context.Database.CanConnectAsync();
                Console.WriteLine($"Can connect: {canConnect}");
                
                if (!canConnect)
                {
                    Console.WriteLine("❌ Cannot connect to database");
                    return;
                }
                
                // Apply migrations
                Console.WriteLine("🔄 Applying migrations...");
                await context.Database.MigrateAsync();
                Console.WriteLine("✅ Migrations applied");
                
                // Test data operations
                Console.WriteLine("📊 Testing data operations...");
                
                // Count existing records
                var offerCount = await context.Offers.CountAsync();
                var statsCount = await context.ProcessingStats.CountAsync();
                var imageCount = await context.ImageUploads.CountAsync();
                var errorCount = await context.ErrorLogs.CountAsync();
                
                Console.WriteLine($"📈 Current data in database:");
                Console.WriteLine($"  • Offers: {offerCount}");
                Console.WriteLine($"  • Processing Stats: {statsCount}");
                Console.WriteLine($"  • Image Uploads: {imageCount}");
                Console.WriteLine($"  • Error Logs: {errorCount}");
                
                // Test insert
                Console.WriteLine("➕ Testing insert operation...");
                var testOffer = new Offer
                {
                    FunPayOfferId = $"TEST_{DateTime.Now:yyyyMMddHHmmss}",
                    GameName = "TestGame",
                    Title = "Test Offer",
                    Description = "This is a test offer created by DatabaseTest",
                    OriginalPrice = 10.50m,
                    ConvertedPrice = 12.00m,
                    Currency = "USD",
                    Status = "Test",
                    ProcessedAt = DateTime.UtcNow,
                    SourceUrl = "https://test.com"
                };
                
                context.Offers.Add(testOffer);
                await context.SaveChangesAsync();
                Console.WriteLine($"✅ Test offer created with ID: {testOffer.Id}");
                
                // Test query
                Console.WriteLine("🔍 Testing query operation...");
                var recentOffers = await context.Offers
                    .Where(o => o.CreatedAt >= DateTime.UtcNow.AddMinutes(-5))
                    .OrderByDescending(o => o.CreatedAt)
                    .Take(5)
                    .ToListAsync();
                
                Console.WriteLine($"📋 Recent offers (last 5 minutes): {recentOffers.Count}");
                foreach (var offer in recentOffers)
                {
                    Console.WriteLine($"  • {offer.FunPayOfferId} - {offer.GameName} - {offer.Title}");
                }
                
                // Test statistics
                Console.WriteLine("📊 Testing statistics...");
                var gameStats = await context.Offers
                    .GroupBy(o => o.GameName)
                    .Select(g => new { Game = g.Key, Count = g.Count(), AvgPrice = g.Average(o => o.ConvertedPrice) })
                    .OrderByDescending(s => s.Count)
                    .ToListAsync();
                
                Console.WriteLine($"🎮 Game statistics:");
                foreach (var stat in gameStats)
                {
                    Console.WriteLine($"  • {stat.Game}: {stat.Count} offers, avg price: ${stat.AvgPrice:F2}");
                }
                
                Console.WriteLine("✅ Database test completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Database test failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
