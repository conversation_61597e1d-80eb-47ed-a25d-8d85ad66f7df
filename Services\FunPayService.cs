using Microsoft.Playwright;
using Microsoft.Extensions.Logging;
using ShopBot.Config;
using ShopBot.Services.Extensions;

namespace ShopBot.Services
{
    public class FunPayService : IFunPayService
    {
        private readonly ParserService _parserService;
        private readonly IBrowser _browser;
        private readonly IBrowserContext _context;
        private IPage _page;
        private readonly ProcessedOffersTracker _tracker;
        private readonly ILogger<FunPayService> _logger;

        public FunPayService(ParserService parserService, IBrowser browser, IBrowserContext context, IPage page, ProcessedOffersTracker tracker, ILogger<FunPayService> logger)
        {
            _parserService = parserService;
            _browser = browser;
            _context = context;
            _page = page;
            _tracker = tracker;
            _logger = logger;
        }

        public async Task<List<OfferListItem>> GetOffers(string gameName)
        {
            var gameId = GameConfig.GetGameId(gameName);
            
            if (string.IsNullOrEmpty(gameId))
            {
                throw new Exception($"Invalid game ID for game: {gameName}");
            }
            
            var gameUrl = $"https://funpay.com/en/lots/{gameId}/";
            await _page.GotoAsync(gameUrl);
            
            try
            {
                await _page.WaitForLoadStateAsync(LoadState.NetworkIdle);
                
                await Task.Delay(500); // Czekaj 1 sekundę przed sprawdzeniem przycisku
                var saleButton = await _page.QuerySelectorAsync("button.btn.btn-gray[value='Продажа']");

                if (saleButton != null)
                {
                    await saleButton.ClickAsync();
                    await Task.Delay(50); // Krótkie opóźnienie po kliknięciu
                }
            }
            catch (Exception ex)
            {
                _logger.LogSaleButtonError(ex.Message);
            }

            var html = await _page.ContentAsync();
            var offers = _parserService.ParseOfferList(html);
            _logger.LogOffersFound(offers.Count, gameName);

            return offers;
        }

        public async Task<List<OfferListItem>> GetFilteredOffers(OfferFilter filter, string gameName)
        {
            var offers = await GetOffers(gameName);
            var gameAttributes = GameConfig.GetGameAttributes(gameName);
            var priceRange = GameConfig.GetGamePriceRange(gameName);
            
            var gameFilter = new OfferFilter
            {
                MinPrice = priceRange.Min,
                MaxPrice = priceRange.Max,
                MinRating = filter.MinRating,
                MinReviews = filter.MinReviews,
                OnlineOnly = filter.OnlineOnly,
                RequiredAttributes = filter.RequiredAttributes,
                IgnoredShortDescriptions = filter.IgnoredShortDescriptions,
                IgnoredServers = filter.IgnoredServers,
                IgnoredPlatforms = filter.IgnoredPlatforms,
                GameSpecificFilters = filter.GameSpecificFilters
            };

            if (filter.GameSpecificFilters.TryGetValue(gameName, out var gameSpecificFilter))
            {
                foreach (var (key, isEnabled) in gameSpecificFilter.Enabled)
                {
                    if (gameAttributes.Contains(key) && 
                        isEnabled && 
                        gameSpecificFilter.Values.TryGetValue(key, out var value))
                    {
                        gameFilter.RequiredAttributes[key] = value;
                    }
                }
            }

            var filteredOffers = _parserService.FilterOffers(offers, gameFilter, gameName);
            _logger.LogOffersFiltered(filteredOffers.Count, offers.Count);
            return filteredOffers;
        }

        public async Task<OfferDetails> GetOfferDetails(string offerUrl)
        {
            var id = _parserService.ExtractIdFromUrl(offerUrl);
            int maxAttempts = 3;
            int attempt = 0;

            while (attempt < maxAttempts)
            {
                try
                {
                    var processUrl = $"https://funpay.com/en/lots/offer?id={id}";
                    await _page.GotoAsync(processUrl);
                    string content = await _page.ContentAsync();

                    if (content.Contains("429 Too Many Requests"))
                    {
                        attempt++;
                        _logger.LogRateLimitHit(id, attempt, maxAttempts);
                        await Task.Delay(10000);
                        continue;
                    }

                    await _page.WaitForSelectorAsync("div.param-item", new() { Timeout = 10000 });

                    var parameters = new Dictionary<string, string>
                    {
                        { "Id", id },
                        { "OriginalUrl", offerUrl }
                    };

                    return _parserService.ParseOfferHtml(content, parameters);
                }
                catch (Exception ex)
                {
                    attempt++;
                    _logger.LogOfferFetchError(id, ex.Message, attempt, maxAttempts);
                    if (attempt >= maxAttempts)
                    {
                        throw;
                    }
                    await Task.Delay(10000);
                }
            }

            throw new Exception($"Nie udało się pobrać szczegółów oferty {id} po {maxAttempts} próbach.");
        }

        public async Task<List<OfferDetails>> GetAllOfferDetails(List<OfferListItem> offers, int delayBetweenRequests = 2000)
        {
            var results = new List<OfferDetails>();
            foreach (var offer in offers)
            {
                try
                {
                    if (string.IsNullOrEmpty(offer.Id))
                    {
                        continue;
                    }

                    if (_tracker.WasOfferProcessed(offer.Id))
                    {
                        continue;
                    }

                    var details = await GetOfferDetails(offer.Url);
                    
                    if (!string.IsNullOrEmpty(details.Id))
                    {
                        _tracker.AddProcessedOffer(details.Id);
                        results.Add(details);
                    }
                    
                    await Task.Delay(delayBetweenRequests);
                }
                catch (Exception ex)
                {
                    _logger.LogOfferProcessingError(offer.Id, ex.Message);
                }
            }
            return results;
        }

        public async ValueTask DisposeAsync()
        {
            if (_page != null)
                await _page.CloseAsync();
        }

        // Implementacja brakujących metod z interfejsu IFunPayService
        public async Task<bool> IsOfferActive(string offerId)
        {
            try
            {
                var offerDetails = await GetOfferDetails($"https://funpay.com/en/lots/offer?id={offerId}");
                return !string.IsNullOrEmpty(offerDetails.Id);
            }
            catch
            {
                return false;
            }
        }

        public async Task<List<OfferListItem>> GetOffersFromPage(string gameUrl, int pageNumber)
        {
            try
            {
                var url = $"{gameUrl}?page={pageNumber}";
                await _page.GotoAsync(url);
                var html = await _page.ContentAsync();
                return _parserService.ParseOfferList(html);
            }
            catch
            {
                return new List<OfferListItem>();
            }
        }

        public async Task<bool> IsServiceAvailable()
        {
            try
            {
                await _page.GotoAsync("https://funpay.com");
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}