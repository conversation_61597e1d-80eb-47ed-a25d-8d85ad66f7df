using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Playwright;
using ShopBot.Tasks;
using ShopBot.Services.Formatters;
using ShopBot.Services.Factories;
using ShopBot.Services.Interfaces;
using ShopBot.Config;
using ShopBot.Exceptions;
using System.Linq;

namespace ShopBot.Services
{
    /// <summary>
    /// Obsługuje polecenia CLI (delete, add, analyze, verify, test-dropbox)
    /// </summary>
    public class CliCommandHandler : ICliCommandHandler
    {
        private readonly ILogger<CliCommandHandler> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly IBrowserService _browserService;
        private readonly IFunPayService _funPayService;
        private readonly IParserService _parserService;
        private readonly IImageUploadService _imageUploadService;
        private readonly ProcessedOffersTracker _processedOffersTracker;
        private readonly DropboxService _dropboxService;
        private readonly ITaskFactory _taskFactory;

        public CliCommandHandler(
            ILogger<CliCommandHandler> logger,
            IServiceProvider serviceProvider,
            IBrowserService browserService,
            IFunPayService funPayService,
            IParserService parserService,
            IImageUploadService imageUploadService,
            ProcessedOffersTracker processedOffersTracker,
            ITaskFactory taskFactory,
            DropboxService dropboxService = null)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
            _browserService = browserService;
            _funPayService = funPayService;
            _parserService = parserService;
            _imageUploadService = imageUploadService;
            _processedOffersTracker = processedOffersTracker;
            _dropboxService = dropboxService;
            _taskFactory = taskFactory;
        }

        public async Task HandleCommandAsync(string[] args)
        {
            if (args.Length == 0)
            {
                _logger.LogWarning("Brak argumentów CLI");
                return;
            }

            var command = args[0].ToLower();

            try
            {
                switch (command)
                {
                    case "delete":
                        await HandleDeleteCommand(args);
                        break;

                    case "add":
                        await HandleAddCommand(args);
                        break;

                    case "analyze":
                        await HandleAnalyzeCommand(args);
                        break;

                    case "analytics":
                        await HandleAnalyticsCommand(args);
                        break;

                    case "tracker-status":
                        await HandleTrackerStatusCommand(args);
                        break;

                    case "test-dropbox":
                        await HandleTestDropboxCommand(args);
                        break;

                    case "check-db":
                        await HandleCheckDatabaseCommand(args);
                        break;

                    case "verify":
                        await HandleVerifyCommand(args);
                        break;

                    case "test-change-detector":
                        await HandleTestChangeDetectorCommand(args);
                        break;

                    case "test-change-detector-all":
                        await HandleTestChangeDetectorAllCommand(args);
                        break;

                    default:
                        _logger.LogWarning("Nieznane polecenie: {Command}", command);
                        ShowHelp();
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Błąd podczas wykonywania polecenia {Command}", command);
                throw;
            }
        }

        private async Task HandleDeleteCommand(string[] args)
        {
            if (args.Length < 2)
            {
                _logger.LogError("Użycie: delete <offerId>");
                return;
            }

            var offerId = args[1];
            _logger.LogInformation("🗑️ Usuwam ofertę: {OfferId}", offerId);

            var browser = await _browserService.InitializeBrowserAsync();
            var context = await _browserService.GetBrowserContextAsync();
            var deleteService = new DeleteService(browser, context, _processedOffersTracker);

            await deleteService.DeleteOffer(offerId);
            _logger.LogInformation("✅ Oferta {OfferId} została usunięta", offerId);
        }

        private async Task HandleAddCommand(string[] args)
        {
            if (args.Length < 3)
            {
                _logger.LogError("Użycie: add <funPayId> <gameName>");
                return;
            }

            var funPayId = args[1];
            var gameName = args[2];
            _logger.LogInformation("➕ Dodaję ofertę FunPay ID: {FunPayId}, Gra: {GameName}", funPayId, gameName);

            try
            {
                var browser = await _browserService.InitializeBrowserAsync();
                var context = await _browserService.GetBrowserContextAsync();

                var offer = await _funPayService.GetOfferDetails($"https://funpay.com/lots/{funPayId}/");
                if (offer != null)
                {
                    var addTask = _taskFactory.CreateAddOfferTask(
                        new OfferListItem
                        {
                            Id = funPayId,
                            Url = $"https://funpay.com/lots/{funPayId}/",
                            Description = offer.ShortDescription
                        },
                        gameName
                    );

                    await addTask.ExecuteAsync(CancellationToken.None);
                    _logger.LogInformation("✅ Oferta {FunPayId} została dodana", funPayId);
                }
                else
                {
                    _logger.LogError("❌ Nie znaleziono oferty {FunPayId} na FunPay", funPayId);
                }
            }
            catch (OfferNotFoundException ex)
            {
                _logger.LogWarning("🗑️ Oferta {FunPayId} została usunięta z FunPay", funPayId);
                Console.WriteLine($"❌ Oferta {funPayId} została usunięta z FunPay lub nigdy nie istniała");
            }
        }

        private async Task HandleAnalyzeCommand(string[] args)
        {
            _logger.LogInformation("🔍 Rozpoczynam analizę ofert...");

            var analyzeTask = _taskFactory.CreateAnalyzeOffersTask();
            await analyzeTask.ExecuteAsync(CancellationToken.None);
            _logger.LogInformation("✅ Analiza ofert zakończona");
        }

        private async Task HandleAnalyticsCommand(string[] args)
        {
            _logger.LogInformation("📊 Generuję raport analytics...");

            var analyticsService = _serviceProvider.GetRequiredService<IAnalyticsService>();

            try
            {
                // Dashboard data
                var dashboard = await analyticsService.GetDashboardDataAsync();
                Console.WriteLine("\n📊 === DASHBOARD ANALYTICS ===");
                Console.WriteLine($"📈 Total Offers: {dashboard.TotalOffers}");
                Console.WriteLine($"💰 Total Revenue: ${dashboard.TotalRevenue:F2}");
                Console.WriteLine($"✅ Success Rate: {dashboard.SuccessRate:F1}%");

                Console.WriteLine("\n🎮 Offers by Game:");
                foreach (var game in dashboard.OffersByGame.OrderByDescending(kv => kv.Value))
                {
                    Console.WriteLine($"  • {game.Key}: {game.Value} offers");
                }

                Console.WriteLine("\n📊 Offers by Status:");
                foreach (var status in dashboard.OffersByStatus)
                {
                    Console.WriteLine($"  • {status.Key}: {status.Value} offers");
                }

                // Performance report
                var performance = await analyticsService.GetPerformanceReportAsync(30);
                Console.WriteLine("\n⚡ === PERFORMANCE REPORT (30 days) ===");
                Console.WriteLine($"📈 Total Processed: {performance.TotalOffersProcessed}");
                Console.WriteLine($"✅ Successful: {performance.SuccessfulOffers}");
                Console.WriteLine($"❌ Failed: {performance.FailedOffers}");
                Console.WriteLine($"⏳ Pending: {performance.PendingOffers}");
                Console.WriteLine($"📊 Success Rate: {performance.SuccessRate:F1}%");
                Console.WriteLine($"💰 Total Revenue: ${performance.TotalRevenue:F2}");
                Console.WriteLine($"💵 Average Offer Value: ${performance.AverageOfferValue:F2}");

                // Game analytics
                var gameAnalytics = await analyticsService.GetGameAnalyticsAsync();
                Console.WriteLine("\n🎮 === GAME ANALYTICS ===");
                foreach (var game in gameAnalytics.Take(5))
                {
                    Console.WriteLine($"🎯 {game.GameName}:");
                    Console.WriteLine($"  📈 Total: {game.TotalOffers} | Published: {game.PublishedOffers} | Success: {game.SuccessRate:F1}%");
                    Console.WriteLine($"  💰 Revenue: ${game.TotalRevenue:F2} | Avg Price: ${game.AveragePrice:F2}");
                }

                // Top errors
                var topErrors = await analyticsService.GetTopErrorsAsync(5);
                if (topErrors.Any())
                {
                    Console.WriteLine("\n🚨 === TOP ERRORS ===");
                    foreach (var error in topErrors)
                    {
                        Console.WriteLine($"❌ {error.ErrorMessage.Substring(0, Math.Min(error.ErrorMessage.Length, 80))}...");
                        Console.WriteLine($"   Count: {error.Count} | Last: {error.LastOccurrence:yyyy-MM-dd HH:mm}");
                    }
                }

                _logger.LogInformation("✅ Analytics report generated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error generating analytics report");
            }
        }

        private async Task HandleTrackerStatusCommand(string[] args)
        {
            _logger.LogInformation("📊 Sprawdzam status Smart Offer Tracker...");

            var offerTracker = _serviceProvider.GetRequiredService<IOfferTracker>();

            try
            {
                Console.WriteLine("\n🎯 === SMART OFFER TRACKER STATUS ===");

                // Status counts
                if (offerTracker is SmartOfferTracker smartTracker)
                {
                    var statusCounts = await smartTracker.GetStatusCountsAsync();
                    Console.WriteLine("\n📊 Status Counts:");
                    foreach (var status in statusCounts.OrderByDescending(kv => kv.Value))
                    {
                        Console.WriteLine($"  • {status.Key}: {status.Value} offers");
                    }

                    // Session cache info
                    var sessionCache = _serviceProvider.GetRequiredService<IProcessingSessionCache>();
                    Console.WriteLine("\n🔄 Session Cache:");
                    Console.WriteLine($"  • Processed in session: {sessionCache.GetProcessedCount()}");
                    Console.WriteLine($"  • Currently processing: {sessionCache.GetCurrentlyProcessingCount()}");

                    if (sessionCache.GetProcessedCount() > 0)
                    {
                        Console.WriteLine("\n📋 Recently processed offers:");
                        var recentOffers = sessionCache.GetProcessedOffers().Take(10);
                        foreach (var offerId in recentOffers)
                        {
                            Console.WriteLine($"  • {offerId}");
                        }
                    }

                    if (sessionCache.GetCurrentlyProcessingCount() > 0)
                    {
                        Console.WriteLine("\n⏳ Currently processing offers:");
                        var processingOffers = sessionCache.GetCurrentlyProcessingOffers();
                        foreach (var offerId in processingOffers)
                        {
                            Console.WriteLine($"  • {offerId}");
                        }
                    }
                }

                // General stats
                var totalOffers = await offerTracker.GetProcessedOffersCountAsync();
                var offersByGame = await offerTracker.GetOfferCountsByGameAsync();
                var lastProcessing = await offerTracker.GetLastProcessingTimeAsync();

                Console.WriteLine("\n📈 General Statistics:");
                Console.WriteLine($"  • Total active offers: {totalOffers}");
                Console.WriteLine($"  • Last processing: {lastProcessing:yyyy-MM-dd HH:mm:ss}");

                Console.WriteLine("\n🎮 Offers by Game:");
                foreach (var game in offersByGame.OrderByDescending(kv => kv.Value))
                {
                    Console.WriteLine($"  • {game.Key}: {game.Value} offers");
                }

                _logger.LogInformation("✅ Tracker status report generated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error generating tracker status report");
            }
        }

        private async Task HandleTestDropboxCommand(string[] args)
        {
            _logger.LogInformation("🔧 Testowanie Dropbox System...");

            try
            {
                Console.WriteLine("\n🔐 === DROPBOX SYSTEM TEST ===");

                var dropboxService = _serviceProvider.GetRequiredService<DropboxService>();
                var appSettings = _serviceProvider.GetRequiredService<AppSettings>();

                // Sprawdź jaki system jest używany
                if (!string.IsNullOrEmpty(appSettings.Dropbox.AccessToken))
                {
                    Console.WriteLine("📋 System: Static Access Token (Legacy Mode)");
                    Console.WriteLine($"   Token: {appSettings.Dropbox.AccessToken.Substring(0, 20)}...");
                }
                else
                {
                    Console.WriteLine("📋 System: OAuth Refresh Token System");
                    var dropboxTokenManager = _serviceProvider.GetRequiredService<DropboxTokenManager>();

                    Console.WriteLine("1. Inicjalizacja Dropbox Token Manager...");
                    await dropboxTokenManager.InitializeAsync();

                    Console.WriteLine("2. Pobieranie ważnego access token...");
                    var accessToken = await dropboxTokenManager.GetValidAccessTokenAsync();
                    Console.WriteLine($"   ✅ Access token: {accessToken.Substring(0, 20)}...");
                }

                // Test automatycznego systemu tokenów
                Console.WriteLine("\n3. Test automatycznego systemu tokenów...");
                try
                {
                    // Sprawdź czy refresh token został zapisany
                    if (System.IO.File.Exists("dropbox_refresh_token.json"))
                    {
                        var tokenContent = await System.IO.File.ReadAllTextAsync("dropbox_refresh_token.json");
                        Console.WriteLine($"   ✅ Refresh token zapisany!");
                        Console.WriteLine($"   📄 Token file size: {tokenContent.Length} chars");

                        // Sprawdź czy token manager działa
                        var dropboxTokenManager = _serviceProvider.GetRequiredService<DropboxTokenManager>();
                        var currentToken = await dropboxTokenManager.GetValidAccessTokenAsync();
                        Console.WriteLine($"   ✅ Current access token: {currentToken.Substring(0, 20)}...");
                        Console.WriteLine($"   🔄 System automatycznie odświeży token za ~3 godziny");
                    }
                    else
                    {
                        Console.WriteLine($"   ❌ Refresh token file not found");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ❌ Token test failed: {ex.Message}");
                }

                Console.WriteLine("\n🎉 === DROPBOX TEST COMPLETED ===");
                _logger.LogInformation("✅ Dropbox test completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n❌ Dropbox test failed: {ex.Message}");
                _logger.LogError(ex, "❌ Error during Dropbox test");
            }
        }

        private async Task HandleVerifyCommand(string[] args)
        {
            _logger.LogInformation("🔍 Rozpoczynam weryfikację ofert G2G...");

            var browser = await _browserService.InitializeBrowserAsync();
            var context = await _browserService.GetBrowserContextAsync();
            var taskScheduler = _serviceProvider.GetRequiredService<ITaskScheduler>();
            var deleteService = new DeleteService(browser, context, _processedOffersTracker);

            var verificationService = new G2GOfferVerificationService(
                browser,
                context,
                (ShopBot.Tasks.TaskScheduler)taskScheduler,
                deleteService
            );

            if (args.Length >= 2 && args[1] == "full")
            {
                await verificationService.VerifyOffers();
                _logger.LogInformation("✅ Pełna weryfikacja ofert zakończona");
            }
            else
            {
                await verificationService.TestApiRequest();
                _logger.LogInformation("✅ Test API zakończony");
            }
        }

        private async Task HandleCheckDatabaseCommand(string[] args)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var offerRepository = scope.ServiceProvider.GetRequiredService<IOfferRepository>();

                Console.WriteLine("\n🗄️ === DATABASE CHECK ===");

                if (args.Length > 1)
                {
                    // Sprawdź konkretną ofertę
                    var offerId = args[1];
                    Console.WriteLine($"🔍 Sprawdzam ofertę: {offerId}");

                    var offer = await offerRepository.GetByFunPayIdAsync(offerId);
                    if (offer != null)
                    {
                        Console.WriteLine($"✅ Znaleziono ofertę w bazie:");
                        Console.WriteLine($"   📋 ID: {offer.Id}");
                        Console.WriteLine($"   🎮 FunPay ID: {offer.FunPayOfferId}");
                        Console.WriteLine($"   🎯 Gra: {offer.GameName}");
                        Console.WriteLine($"   📝 Tytuł: {offer.Title}");
                        Console.WriteLine($"   💰 Cena oryginalna: {offer.OriginalPrice} {offer.Currency}");
                        Console.WriteLine($"   💵 Cena przeliczona: {offer.ConvertedPrice} USD");
                        Console.WriteLine($"   📊 Status: {offer.Status}");
                        Console.WriteLine($"   🕐 Utworzono: {offer.CreatedAt:yyyy-MM-dd HH:mm:ss}");
                        Console.WriteLine($"   🕑 Przetworzono: {offer.ProcessedAt:yyyy-MM-dd HH:mm:ss}");
                        Console.WriteLine($"   📤 Opublikowano: {offer.PublishedAt?.ToString("yyyy-MM-dd HH:mm:ss") ?? "Nie"}");
                        Console.WriteLine($"   🔗 G2G ID: {offer.G2GOfferId ?? "Brak"}");
                        Console.WriteLine($"   🖼️ Liczba obrazów: {offer.ImageCount}");

                        if (!string.IsNullOrEmpty(offer.Description))
                        {
                            Console.WriteLine($"   📄 Opis: {offer.Description.Substring(0, Math.Min(100, offer.Description.Length))}...");
                        }
                    }
                    else
                    {
                        Console.WriteLine($"❌ Nie znaleziono oferty {offerId} w bazie danych");
                    }
                }
                else
                {
                    // Pokaż statystyki ogólne
                    Console.WriteLine("📊 Statystyki bazy danych:");

                    var totalOffers = await offerRepository.GetTotalOffersCountAsync();
                    Console.WriteLine($"   📋 Łączna liczba ofert: {totalOffers}");

                    var recentOffers = await offerRepository.GetRecentAsync(10);
                    Console.WriteLine($"   🕐 Ostatnie 10 ofert:");

                    foreach (var offer in recentOffers)
                    {
                        Console.WriteLine($"      • {offer.FunPayOfferId} - {offer.GameName} - {offer.Status} - {offer.CreatedAt:MM-dd HH:mm}");
                    }

                    var offersByGame = await offerRepository.GetOfferCountsByGameAsync();
                    Console.WriteLine($"   🎮 Oferty według gier:");

                    foreach (var game in offersByGame.Take(10))
                    {
                        Console.WriteLine($"      • {game.Key}: {game.Value} ofert");
                    }
                }

                Console.WriteLine("\n✅ Database check completed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Błąd sprawdzania bazy danych: {ex.Message}");
                _logger.LogError(ex, "[CLI] Database check failed");
            }
        }

        private async Task HandleTestChangeDetectorCommand(string[] args)
        {
            _logger.LogInformation("🧪 Testowanie OfferContentChangeDetector...");

            try
            {
                Console.WriteLine("\n🔍 === OFFER CONTENT CHANGE DETECTOR TEST ===");

                var testRunner = _serviceProvider.GetRequiredService<OfferContentChangeDetectorTestRunner>();

                Console.WriteLine("🚀 Uruchamiam wszystkie testy...\n");
                testRunner.RunAllTests();

                // Dodatkowy test z rzeczywistymi danymi jeśli podano argumenty
                if (args.Length >= 2)
                {
                    var funPayId = args[1];
                    Console.WriteLine($"\n🔍 === TEST Z RZECZYWISTYMI DANYMI ===");
                    Console.WriteLine($"📋 Sprawdzam ofertę FunPay ID: {funPayId}");

                    await TestWithRealData(funPayId);
                }

                Console.WriteLine("\n🎉 === TEST CHANGE DETECTOR COMPLETED ===");
                _logger.LogInformation("✅ Test change detector completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n❌ Test change detector failed: {ex.Message}");
                _logger.LogError(ex, "❌ Error during change detector test");
            }
        }

        private async Task HandleTestChangeDetectorAllCommand(string[] args)
        {
            _logger.LogInformation("🧪 Testowanie OfferContentChangeDetector na wszystkich ofertach...");

            try
            {
                Console.WriteLine("\n🔍 === MASS OFFER CONTENT CHANGE DETECTOR TEST ===");
                Console.WriteLine("🚀 Testowanie wszystkich ofert z bazy danych...\n");

                using var scope = _serviceProvider.CreateScope();
                var offerRepository = scope.ServiceProvider.GetRequiredService<IOfferRepository>();
                var changeDetector = scope.ServiceProvider.GetRequiredService<OfferContentChangeDetector>();

                // Pobierz wszystkie oferty z bazy
                Console.WriteLine("📊 Pobieranie ofert z bazy danych...");
                var allOffers = await offerRepository.GetAllAsync();
                Console.WriteLine($"✅ Znaleziono {allOffers.Count} ofert w bazie danych\n");

                if (allOffers.Count == 0)
                {
                    Console.WriteLine("❌ Brak ofert w bazie danych do testowania");
                    return;
                }

                // Statystyki
                var totalOffers = allOffers.Count;
                var processedOffers = 0;
                var offersWithChanges = 0;
                var offersWithErrors = 0;
                var startTime = DateTime.UtcNow;

                // Limit testowania (żeby nie przeciążyć systemu)
                var maxTestOffers = args.Length >= 2 && int.TryParse(args[1], out var limit) ? limit : 50;
                var offersToTest = allOffers.Take(maxTestOffers).ToList();

                Console.WriteLine($"🎯 Testowanie pierwszych {offersToTest.Count} ofert (limit: {maxTestOffers})");
                Console.WriteLine("📋 Format: [ID] Status - Gra - Tytuł (skrócony)\n");

                foreach (var offer in offersToTest)
                {
                    try
                    {
                        processedOffers++;
                        var progress = (processedOffers * 100) / offersToTest.Count;

                        Console.Write($"[{processedOffers:D3}/{offersToTest.Count:D3}] ({progress:D2}%) ");
                        Console.Write($"{offer.FunPayOfferId} - {offer.GameName} - ");

                        var shortTitle = offer.Title?.Length > 50 ? offer.Title.Substring(0, 47) + "..." : offer.Title;
                        Console.Write($"{shortTitle}");

                        // Pobierz aktualne dane z FunPay
                        var currentOffer = await _funPayService.GetOfferDetails($"https://funpay.com/lots/{offer.FunPayOfferId}/");

                        if (currentOffer == null)
                        {
                            Console.WriteLine(" ❌ Błąd pobierania");
                            offersWithErrors++;
                            continue;
                        }

                        // Sprawdź zmiany
                        var hasChanges = changeDetector.HasSignificantChanges(offer, currentOffer);

                        if (hasChanges)
                        {
                            Console.WriteLine(" 🔄 ZMIANY");
                            offersWithChanges++;
                        }
                        else
                        {
                            Console.WriteLine(" ✅ OK");
                        }

                        // Krótka przerwa żeby nie przeciążyć FunPay
                        await Task.Delay(500);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($" ❌ BŁĄD: {ex.Message}");
                        offersWithErrors++;
                        _logger.LogWarning(ex, "Error testing offer {FunPayId}", offer.FunPayOfferId);
                    }
                }

                // Podsumowanie
                var endTime = DateTime.UtcNow;
                var duration = endTime - startTime;

                Console.WriteLine($"\n📊 === PODSUMOWANIE TESTÓW ===");
                Console.WriteLine($"⏱️  Czas wykonania: {duration.TotalMinutes:F1} minut");
                Console.WriteLine($"📋 Przetestowane oferty: {processedOffers}/{totalOffers}");
                Console.WriteLine($"🔄 Oferty ze zmianami: {offersWithChanges} ({(offersWithChanges * 100.0 / processedOffers):F1}%)");
                Console.WriteLine($"✅ Oferty bez zmian: {processedOffers - offersWithChanges - offersWithErrors} ({((processedOffers - offersWithChanges - offersWithErrors) * 100.0 / processedOffers):F1}%)");
                Console.WriteLine($"❌ Błędy: {offersWithErrors} ({(offersWithErrors * 100.0 / processedOffers):F1}%)");

                // Statystyki detektora
                var stats = changeDetector.GetPerformanceStats();
                Console.WriteLine($"\n🔧 Ustawienia detektora:");
                Console.WriteLine($"   💰 Próg zmiany ceny: {stats.PriceChangeThreshold:P2}");
                Console.WriteLine($"   📝 Próg podobieństwa tytułu: {stats.TitleSimilarityThreshold:P2}");

                Console.WriteLine($"\n🎉 === MASS TEST COMPLETED ===");
                _logger.LogInformation("✅ Mass change detector test completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n❌ Mass test failed: {ex.Message}");
                _logger.LogError(ex, "❌ Error during mass change detector test");
            }
        }

        private async Task TestWithRealData(string funPayId)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var offerRepository = scope.ServiceProvider.GetRequiredService<IOfferRepository>();
                var changeDetector = scope.ServiceProvider.GetRequiredService<OfferContentChangeDetector>();

                // Pobierz ofertę z bazy danych
                var existingOffer = await offerRepository.GetByFunPayIdAsync(funPayId);
                if (existingOffer == null)
                {
                    Console.WriteLine($"❌ Nie znaleziono oferty {funPayId} w bazie danych");
                    return;
                }

                Console.WriteLine($"✅ Znaleziono ofertę w bazie:");
                Console.WriteLine($"   📝 Tytuł: {existingOffer.Title}");
                Console.WriteLine($"   💰 Cena: {existingOffer.OriginalPrice} {existingOffer.Currency}");
                Console.WriteLine($"   🕐 Utworzono: {existingOffer.CreatedAt:yyyy-MM-dd HH:mm:ss}");

                // Pobierz aktualne dane z FunPay
                Console.WriteLine($"\n🔍 Pobieranie aktualnych danych z FunPay...");
                var currentOffer = await _funPayService.GetOfferDetails($"https://funpay.com/lots/{funPayId}/");

                if (currentOffer == null)
                {
                    Console.WriteLine($"❌ Nie można pobrać aktualnych danych oferty z FunPay");
                    return;
                }

                Console.WriteLine($"✅ Pobrano aktualne dane:");
                Console.WriteLine($"   📝 Tytuł: {currentOffer.ShortDescription}");
                Console.WriteLine($"   💰 Cena: {currentOffer.ActualOfferPrice}");

                // Sprawdź zmiany
                Console.WriteLine($"\n🔍 Sprawdzanie zmian zawartości...");
                var hasChanges = changeDetector.HasSignificantChanges(existingOffer, currentOffer);

                if (hasChanges)
                {
                    Console.WriteLine($"🔄 WYKRYTO ZNACZĄCE ZMIANY!");
                    Console.WriteLine($"   📊 Oferta zostałaby usunięta z G2G i dodana ponownie");
                }
                else
                {
                    Console.WriteLine($"✅ Brak znaczących zmian");
                    Console.WriteLine($"   📊 Oferta pozostałaby bez zmian");
                }

                // Pokaż statystyki detektora
                var stats = changeDetector.GetPerformanceStats();
                Console.WriteLine($"\n📊 Progi detektora:");
                Console.WriteLine($"   💰 Próg zmiany ceny: {stats.PriceChangeThreshold:P2}");
                Console.WriteLine($"   📝 Próg podobieństwa tytułu: {stats.TitleSimilarityThreshold:P2}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Błąd podczas testu z rzeczywistymi danymi: {ex.Message}");
                _logger.LogError(ex, "Error testing with real data for offer {FunPayId}", funPayId);
            }
        }

        private void ShowHelp()
        {
            Console.WriteLine("🔧 Dostępne polecenia CLI:");
            Console.WriteLine("  delete <offerId>              - Usuń ofertę");
            Console.WriteLine("  add <funPayId> <gameName>     - Dodaj ofertę");
            Console.WriteLine("  analyze                       - Analizuj oferty");
            Console.WriteLine("  analytics                     - Pokaż raport analytics");
            Console.WriteLine("  tracker-status                - Status Smart Offer Tracker");
            Console.WriteLine("  test-dropbox                  - Test Dropbox Refresh Token System");
            Console.WriteLine("  check-db [offerId]            - Sprawdź bazę danych");
            Console.WriteLine("  verify [full]                 - Weryfikuj oferty G2G");
            Console.WriteLine("  test-change-detector [id]     - Test wykrywania zmian zawartości ofert");
            Console.WriteLine("  test-change-detector-all [n]  - Test wszystkich ofert z bazy (limit: n, domyślnie 50)");

        }
    }
}
