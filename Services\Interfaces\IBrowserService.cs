using Microsoft.Playwright;
using ShopBot.Config;
using System.Threading.Tasks;

namespace ShopBot.Services
{
    /// <summary>
    /// Interfejs dla zarządzania przeglądarką
    /// </summary>
    public interface IBrowserService
    {
        /// <summary>
        /// Inicjalizuje połączenie z przeglądarką
        /// </summary>
        Task<IBrowser> InitializeBrowserAsync();

        /// <summary>
        /// Pobiera kontekst przeglądarki
        /// </summary>
        Task<IBrowserContext> GetBrowserContextAsync();

        /// <summary>
        /// Tworzy nową stronę
        /// </summary>
        Task<IPage> CreateNewPageAsync();

        /// <summary>
        /// Zamyka przeglądarkę
        /// </summary>
        Task CloseBrowserAsync();

        /// <summary>
        /// Sprawdza czy przeglądarka jest dostępna
        /// </summary>
        Task<bool> IsBrowserAvailableAsync();
    }

    /// <summary>
    /// Implementacja serwisu przeglądarki
    /// </summary>
    public class BrowserService : IBrowserService
    {
        private IBrowser? _browser;
        private IBrowserContext? _context;
        private readonly AppSettings _appSettings;

        public BrowserService()
        {
            _appSettings = AppSettings.Instance;
        }

        public async Task<IBrowser> InitializeBrowserAsync()
        {
            if (_browser == null)
            {
                _browser = await Browser.ConnectToExistingBrowserAsync(_appSettings.Application.BrowserId, false);
            }
            return _browser;
        }

        public async Task<IBrowserContext> GetBrowserContextAsync()
        {
            if (_context == null)
            {
                var browser = await InitializeBrowserAsync();
                _context = browser.Contexts[0];
            }
            return _context;
        }

        public async Task<IPage> CreateNewPageAsync()
        {
            var context = await GetBrowserContextAsync();
            return await context.NewPageAsync();
        }

        public async Task CloseBrowserAsync()
        {
            if (_browser != null)
            {
                await _browser.CloseAsync();
                _browser = null;
                _context = null;
            }
        }

        public async Task<bool> IsBrowserAvailableAsync()
        {
            try
            {
                var browser = await InitializeBrowserAsync();
                return browser != null && browser.IsConnected;
            }
            catch
            {
                return false;
            }
        }
    }
}
