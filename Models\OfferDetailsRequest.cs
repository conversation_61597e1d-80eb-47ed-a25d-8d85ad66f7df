using System;
using System.Threading.Tasks;

namespace ShopBot.Models
{
    /// <summary>
    /// Model żądania szczegółów oferty dla FunPayDetailsService
    /// </summary>
    public class OfferDetailsRequest
    {
        public string OfferId { get; set; } = string.Empty;
        public string OfferUrl { get; set; } = string.Empty;
        public string GameName { get; set; } = string.Empty;
        public DateTime RequestedAt { get; set; } = DateTime.UtcNow;
        public TaskCompletionSource<OfferDetails> CompletionSource { get; set; } = new();
        
        /// <summary>
        /// Timeout dla żądania (domyślnie 30 sekund)
        /// </summary>
        public TimeSpan Timeout { get; set; } = TimeSpan.FromSeconds(30);
        
        /// <summary>
        /// Liczba prób pobrania szczegółów
        /// </summary>
        public int AttemptCount { get; set; } = 0;
        
        /// <summary>
        /// Maksymalna liczba prób
        /// </summary>
        public int MaxAttempts { get; set; } = 3;
        
        /// <summary>
        /// Sprawdza czy żądanie wygasło
        /// </summary>
        public bool IsExpired => DateTime.UtcNow - RequestedAt > Timeout;
        
        /// <summary>
        /// Sprawdza czy można ponowić próbę
        /// </summary>
        public bool CanRetry => AttemptCount < MaxAttempts && !IsExpired;
    }
}
