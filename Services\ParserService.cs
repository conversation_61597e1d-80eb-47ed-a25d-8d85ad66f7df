#nullable disable
using HtmlAgilityPack;
using Microsoft.Extensions.Logging;
using System.Text.RegularExpressions;
using System.Globalization;
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Playwright;
using System.Threading.Tasks;
using ShopBot.Services.Formatters;
using ShopBot.Services.Extensions;

namespace ShopBot.Services
{
    public class ParserService : IParserService
    {
        private readonly ILogger<ParserService> _logger;
        private readonly Dictionary<string, string> _rankMapping = new()
        {
            // Wspólne rangi dla wielu gier
            {"бронза", "Bronze"},
            {"серебро", "Silver"},
            {"золото", "Gold"},
            {"платина", "Platinum"},
            {"алмаз", "Diamond"},
            {"мастер", "Master"},
            {"нет ранга", "Unranked"},
            // League of Legends specyficzne
            {"грандмастер", "Grandmaster"},
            {"челленджер", "Challenger"},
            {"железо", "Iron"},
            // Dota 2 specyficzne
            {"рекрут", "Herald"},
            {"страж", "Guardian"},
            {"рыцарь", "Crusader"},
            {"архонт", "Archon"},
            {"леенда", "Legend"},
            {"властелн", "Ancient"},
            {"божество", "Divine"},
            {"титан", "Immortal"},
            // Apex Legends specyficzne
            {"новичок", "Rookie"},
            {"высший хищник", "Apex Predator"},
            {"%без ранга-no rank", "No rank"},
            {"без ранга", "No rank"},
            
            // Skróty rang Apex Legends
            {"нов", "Rookie"},
            {"бр", "Bronze"},
            {"ср", "Silver"},
            {"зл", "Gold"},
            {"пл", "Platinum"},
            {"ал", "Diamond"},
            {"мс", "Master"},
            {"вх", "Apex Predator"},
            {"хищ", "Apex Predator"}
        };

        private static readonly Dictionary<string, string> ServerMapping = new()
        {
            {"411", "Russia"},
            {"405", "EU West"},
            {"406", "EU Nordic & East"},
            {"404", "North America"},
            {"413", "Japan"},
            {"407", "Latin America North"},
            {"408", "Latin America South"},
            {"410", "Turkey"}
        };

        private readonly HashSet<string> _bannedPhrases = new(StringComparer.OrdinalIgnoreCase)
        {
            "account with 1 leg to choose",
            "account with one leg to choose",
            // Frazy wskazujące na katalogi kont
            "myths to choose",
            "starting account with a legendary to choose",
            "large selection",
            "all accounts can be viewed here",
            "to find out the price, send me the selected account id",
            "there are several accounts available",
            "you can see available accounts here", 
            "to find out the price send me the id",
            "write me the id of the account",
            "send me the selected account",
            "wide choice",
            "available accounts",
            "check our catalog",
            "view catalog",
            "more accounts at",
            "accounts catalog",
            "check available accounts",
            "view accounts here",
            "all accounts available at",
            // Frazy wskazujące na wybór postaci/zawartości
            "with any mythic",
            "with ankil of your choice",
            "with any login legend",
            "account with one leg to choose",
            "starting account with a legend to choose from",
            "your choice",
            "of your choice", 
            "with any",
            "to choose from",
            "with any links",
            "with mythical hero of your choice",
            "with legendary hero of your choice",
            "with any mythical hero and resources",
            "fresh start with legendary of your choice",
            "starting account with legendary of choice",
            // Frazy wskazujące na customizację
            "custom order",
            "starting account with legendary of choice",    
            "available in different variants",        
            "with legendary of choice",
            "with legendary of your choice",
            "with lega of choice",
            // Frazy wskazujące na masową sprzedaż
            "starter accounts",
            "starter accounts with any legendary heroes",
            "starting accounts", 
            "accounts with",
            "multiple accounts",
            "bulk accounts",
            "we will select",
            "choose account",
            "start account",
            "starter account",
            // Domeny katalogów zewnętrznych
            "durmanplay.shop",
            "raid.bar",
            "milostev.store",
            "lanalins.acc2s.shop",
            "rslmirage.fun",
            "uxxu.acc2s.shop",
            // Warzone
            "warzone 3",
            "warzone3",
            "warzone 3.0",
            "warzone3.0",
            "wz3",
            "wz 3",
            "wz 3.0",
            "wz3.0",
            "warzone iii",
            "warzone three",
            "cod warzone 3",
            "call of duty warzone 3",
            "cod wz3",
            "cod wz 3",
            "cod warzone 3.0",
            "call of duty warzone 3.0",
        };

        private readonly DescriptionFormatterFactory _formatterFactory;

        public ParserService(ILogger<ParserService> logger, DescriptionFormatterFactory formatterFactory = null)
        {
            _logger = logger;
            _formatterFactory = formatterFactory;
        }

        public OfferDetails ParseOfferHtml(string html, Dictionary<string, string>? initialParameters = default)
        {
            var document = new HtmlAgilityPack.HtmlDocument();
            document.LoadHtml(html);

            // Używamy przekazanych parametrów lub tworzymy nowe
            var parameters = initialParameters ?? new Dictionary<string, string>();
            
            // ID powinno być już przekazane w parametrach
            var offerId = parameters.GetValueOrDefault("Id", "");
            
            // Znajdujemy potrzebne elementy
            var linkNode = document.DocumentNode.SelectSingleNode("//a[contains(@class, 'tc-item')]");
            var ratingStarsNode = document.DocumentNode.SelectSingleNode(".//div[contains(@class, 'rating-stars')]");
            
            // Parsujemy parametry oferty
            var additionalParams = ParseParameters(document);
            foreach (var param in additionalParams)
            {
                parameters[param.Key] = param.Value;
            }

            _logger.LogFC25Details(
                parameters.GetValueOrDefault("Squad price", parameters.GetValueOrDefault("Squad price (k)", "Not found")),
                parameters.GetValueOrDefault("Coins on balance", parameters.GetValueOrDefault("Coins on balance (k)", "Not found")),
                parameters.GetValueOrDefault("Web App", parameters.GetValueOrDefault("Web app", "Not found")),
                parameters.GetValueOrDefault("Rating", "Not found")
            );

            // Parsowanie winrate
            var winrate = 0;
            if (parameters.TryGetValue("Winrate", out var winrateStr))
            {
                winrateStr = winrateStr.Replace("%", "").Trim();
                if (int.TryParse(winrateStr, out int parsedWinrate))
                {
                    winrate = parsedWinrate;
                }
            }

            var level = ExtractNumber(parameters.GetValueOrDefault("Account level", 
                    parameters.GetValueOrDefault("Level",
                    parameters.GetValueOrDefault("Character level", "0"))));

            // Parsowanie ceny z opcji płatności
            string price = ParsePriceFromPaymentOptions(document);

            // Tworzenie obiektu OfferDetails
            var offerDetails = BuildOfferDetails(offerId, price, parameters, document, linkNode, ratingStarsNode, level, winrate);

            // Post-processing oferty
            PostProcessOffer(offerDetails, parameters);

            _logger.LogOfferCreated(offerDetails.Id);
            return offerDetails;
        }

        private string ExtractPrice(string content)
        {
            if (string.IsNullOrEmpty(content)) return "Brak ceny";

            try
            {
                // Szukamy ceny w formacie "from X $"
                var match = Regex.Match(content, @"from\s*([\d.]+)\s*\$");
                if (match.Success && match.Groups.Count > 1)
                {
                    return $"{match.Groups[1].Value} $";
                }
            }
            catch (Exception ex)
            {
                _logger.LogPriceError("ExtractPrice", ex.Message);
            }

            return "Brak ceny";
        }

        private Dictionary<string, string> ParseParameters(HtmlDocument document)
        {
            var parameters = new Dictionary<string, string>();
            
            // Najpierw wyciągnij ID z URL
            var linkNode = document.DocumentNode.SelectSingleNode("//a[contains(@class, 'tc-item')]");
            if (linkNode != null)
            {
                var url = ValidateAndFixUrl(linkNode.GetAttributeValue("href", ""));
                var id = ExtractIdFromUrl(url);
                if (!string.IsNullOrEmpty(id))
                {
                    parameters["Id"] = id;
                }
            }
            
            var paramItems = document.DocumentNode.SelectNodes("//div[contains(@class, 'param-item')]");
            if (paramItems != null)
            {
                foreach (var paramItem in paramItems)
                {
                    var titleNode = paramItem.SelectSingleNode(".//h5");
                    var valueNode = paramItem.SelectSingleNode(".//div[@class='text-bold']");
                    
                    if (titleNode != null && valueNode != null)
                    {
                        var key = titleNode.InnerText.Trim();
                        var value = valueNode.InnerText.Trim();
                        
                        // Normalizacja kluczy dla FC 25
                        if (key == "Squad price (k)")
                            key = "Squad price";
                        else if (key == "Coins on balance (k)")
                            key = "Coins on balance";
                        else if (key.ToLower() == "web app")
                            key = "Web App";
                        
                        _logger.LogParameterFound(key, value);

                        // Specjalne logowanie dla parametru Rating
                        if (key == "Rating")
                        {
                            _logger.LogRatingFound(value);
                        }
                        
                        parameters[key] = value;
                    }
                }
            }

            // Parsowanie opisów
            var shortDescNode = document.DocumentNode.SelectSingleNode("//div[contains(@class, 'param-item')]/h5[text()='Short description']/following-sibling::div");
            if (shortDescNode != null)
            {
                var shortDesc = shortDescNode.InnerText.Trim();
                if (_formatterFactory != null)
                {
                    var formatter = _formatterFactory.GetFormatter(null);
                    parameters["Short description"] = formatter.CleanDescription(shortDesc);
                }
                else
                {
                    parameters["Short description"] = shortDesc;
                }
            }

            var detailedDescNode = document.DocumentNode.SelectSingleNode("//div[contains(@class, 'param-item')]/h5[text()='Detailed description']/following-sibling::div");
            if (detailedDescNode != null)
            {
                var detailedDesc = detailedDescNode.InnerText.Trim();
                if (_formatterFactory != null)
                {
                    var formatter = _formatterFactory.GetFormatter(null);
                    parameters["Detailed description"] = formatter.FormatDescription(
                        new OfferDetails { DetailedDescription = detailedDesc }, 
                        parameters);
                }
                else
                {
                    parameters["Detailed description"] = detailedDesc;
                }
            }

            // Sprawdzenie czy znaleziono wszystkie wymagane parametry
            var requiredParams = new[] { "Server", "Level", "Champions", "Skins", "Rank" };
            foreach (var param in requiredParams)
            {
                if (!parameters.ContainsKey(param))
                {
                    _logger.LogParameterMissing(param);
                }
            }

            return parameters;
        }

        private decimal ExtractDecimal(string text)
        {
            if (string.IsNullOrEmpty(text)) return 0;
            if (decimal.TryParse(text.Replace(",", "."), NumberStyles.Any, CultureInfo.InvariantCulture, out decimal result))
            {
                return result;
            }
            return 0;
        }

        private int ExtractNumber(string? text)
        {
            if (string.IsNullOrEmpty(text)) return 0;

            try
            {
                var match = Regex.Match(text, @"\d+");
                if (match.Success && int.TryParse(match.Value, out int result))
                {
                    return result;
                }
            }
            catch (Exception ex)
            {
                _logger.LogUnexpectedError($"parsowania liczby z tekstu '{text}'", ex.Message);
            }

            return 0;
        }

        private string ExtractRatingFromDiv(HtmlNode? ratingStarsNode)
        {
            if (ratingStarsNode == null) return "0";
            var starElements = ratingStarsNode.SelectNodes(".//i");
            if (starElements == null) return "0";
            int filledStars = starElements.Count(star => star.GetAttributeValue("class", "").Contains("fas"));
            return filledStars.ToString();
        }

        public string ValidateAndFixUrl(string url)
        {
            if (string.IsNullOrEmpty(url))
            {
                return string.Empty;
            }

            try
            {
                if (Uri.TryCreate(url, UriKind.Absolute, out Uri? uriResult) 
                    && uriResult != null
                    && (uriResult.Scheme == Uri.UriSchemeHttp || uriResult.Scheme == Uri.UriSchemeHttps))
                {
                    return url;
                }
                else if (url.StartsWith("//"))
                {
                    return "https:" + url;
                }
                else if (!url.StartsWith("http"))
                {
                    return "https://funpay.com" + (url.StartsWith("/") ? "" : "/") + url;
                }
            }
            catch (Exception)
            {
                // Cichy błąd - nie logujemy
            }

            return url;
        }

        public string ExtractIdFromUrl(string url)
        {
            if (string.IsNullOrEmpty(url))
            {
                return string.Empty;
            }

            try
            {
                // Dodajemy nowy wzorzec wyszukiwania ID
                var idMatch = Regex.Match(url, @"id=(\d+)");
                if (idMatch.Success && idMatch.Groups.Count > 1)
                {
                    return idMatch.Groups[1].Value;
                }

                // Zachowujemy też stary wzorzec dla kompatybilności
                if (url.Contains("/lots/"))
                {
                    var lotsMatch = Regex.Match(url, @"/lots/(\d+)/");
                    if (lotsMatch.Success && lotsMatch.Groups.Count > 1)
                    {
                        return lotsMatch.Groups[1].Value;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogUnexpectedError($"wyciągania ID z URL '{url}'", ex.Message);
            }

            return string.Empty;
        }

        private List<string> ParseImages(HtmlDocument document)
        {
            var images = new List<string>();
            
            // Pobieranie obrazków z sekcji Images
            var imageNodes = document.DocumentNode.SelectNodes("//div[contains(@class, 'param-item')]/h5[text()='Images']/following-sibling::div//li[contains(@class, 'attachments-item')]/a");
            if (imageNodes != null)
            {
                foreach (var imageNode in imageNodes)
                {
                    var href = imageNode.GetAttributeValue("href", "");
                    if (!string.IsNullOrEmpty(href))
                    {
                        images.Add(href);
                    }
                }
            }
            
            // Pobieranie obrazków z opisu
            var descriptionNode = document.DocumentNode.SelectSingleNode("//div[contains(@class, 'param-item')]/h5[text()='Detailed description']/following-sibling::div");
            if (descriptionNode != null)
            {
                var description = descriptionNode.InnerText;
                var urlMatches = Regex.Matches(description, @"https?://[^\s<>""]+?(?:jpg|jpeg|png|gif)");
                foreach (Match match in urlMatches)
                {
                    images.Add(match.Value);
                }
            }
            
            return images;
        }

        public List<OfferListItem> ParseOfferList(string html)
        {
            var offers = new List<OfferListItem>();
            var document = new HtmlAgilityPack.HtmlDocument();
            document.LoadHtml(html);

            var offerNodes = document.DocumentNode.SelectNodes("//a[contains(@class, 'tc-item')]");
            if (offerNodes == null) return offers;

            foreach (var offerNode in offerNodes)
            {
                // Debug - pokaż wszystkie atrybuty
                _logger.LogDebug("[Parser] Raw attributes:");
                foreach (var attr in offerNode.Attributes)
                {
                    _logger.LogDebug("- {AttributeName}: {AttributeValue}", attr.Name, attr.Value);
                }

                var url = ValidateAndFixUrl(offerNode.GetAttributeValue("href", ""));
                var id = ExtractIdFromUrl(url);
                
                if (string.IsNullOrEmpty(id))
                {
                    continue;
                }

                var offer = new OfferListItem
                {
                    Id = id,
                    Url = url,
                    Description = offerNode.SelectSingleNode(".//div[contains(@class, 'tc-desc-text')]")?.InnerText.Trim() ?? "",
                    IsOnline = offerNode.GetAttributeValue("data-online", "") == "1",
                    SellerName = offerNode.SelectSingleNode(".//div[contains(@class, 'media-user-name')]//span")?.InnerText.Trim() ?? "",
                };

                // Parsuj atrybuty data-f-*
                _logger.LogDebug("[Parser] Parsing data-f-* attributes:");
                foreach (var attribute in offerNode.Attributes.Where(a => a.Name.StartsWith("data-f-")))
                {
                    var key = attribute.Name.Replace("data-f-", "");
                    offer.Attributes[key] = attribute.Value;
                    _logger.LogDebug("- Added {Key}: {Value}", key, attribute.Value);
                }

                // Dodaj specyficzne atrybuty
                _logger.LogDebug("[Parser] Adding specific attributes:");
                var specificAttributes = new[] { "server", "online", "user" };
                foreach (var attrName in specificAttributes)
                {
                    var value = offerNode.GetAttributeValue($"data-{attrName}", null);
                    if (value != null)
                    {
                        offer.Attributes[attrName] = value;
                        _logger.LogDebug("- Added {AttributeName}: {Value}", attrName, value);
                    }
                    else
                    {
                        _logger.LogDebug("- Not found: data-{AttributeName}", attrName);
                    }
                }

                // Debug - pokaż końcowe atrybuty
                _logger.LogDebug("[Parser] Final offer attributes:");
                foreach (var attr in offer.Attributes)
                {
                    _logger.LogDebug("- {Key}: {Value}", attr.Key, attr.Value);
                }

                var priceNode = offerNode.SelectSingleNode(".//div[contains(@class, 'tc-price')]");
                if (priceNode != null)
                {
                    var priceDataS = priceNode.GetAttributeValue("data-s", "0");
                    if (decimal.TryParse(priceDataS, NumberStyles.Any, CultureInfo.InvariantCulture, out decimal price))
                    {
                        offer.Price = Math.Round(price * 0.9726m, 2);
                    }
                }

                var ratingNode = offerNode.SelectSingleNode(".//div[contains(@class, 'rating-stars')]");
                if (ratingNode != null)
                {
                    int.TryParse(ExtractRatingFromDiv(ratingNode), out int rating);
                    offer.SellerRating = rating;
                }

                var reviewCountNode = offerNode.SelectSingleNode(".//span[contains(@class, 'rating-mini-count')]");
                if (reviewCountNode != null)
                {
                    int.TryParse(reviewCountNode.InnerText.Trim(), out int reviewCount);
                    offer.ReviewCount = reviewCount;
                }

                offers.Add(offer);
            }

            return offers;
        }

        private string MapServerCode(string serverCode)
        {
            return ServerMapping.GetValueOrDefault(serverCode, "Unknown Server");
        }

        private bool ValidateRustOffer(OfferListItem offer, out string reason)
        {
            reason = string.Empty;
            
            // Sprawdź serwer
            if (!offer.Attributes.TryGetValue("server", out var serverValue) || serverValue != "1223")
            {
                reason = "Niewlasciwy serwer";
                return false;
            }

            // Sprawdź godziny i skiny
            var (hours, skins) = ExtractRustDetails(offer.Description);
            
            // Sprawdź minimalne wymagania
            if (hours == 0)
            {
                reason = "Brak informacji o godzinach gry";
                return false;
            }
            
            if (hours < 100) // przykładowy próg
            {
                reason = $"Za malo godzin gry: {hours}h";
                return false;
            }

            return true;
        }

        public List<OfferListItem> FilterOffers(List<OfferListItem> offers, OfferFilter filter, string gameName)
        {
            var report = new FilteringReport(gameName);
            var uniqueOffers = new HashSet<string>();

            // Logowanie konfiguracji filtrów dla Raid Shadow Legends
            if (gameName == "RaidShadowLegends")
            {
                _logger.LogInformation("[Filter] 🔍 Filtrowanie {Count} ofert dla gry {GameName}", offers.Count, gameName);
                if (filter.IgnoredShortDescriptions != null && filter.IgnoredShortDescriptions.Count > 0)
                {
                    _logger.LogInformation("[Filter] 📋 Załadowano {Count} zabronionych fraz z config.json:", filter.IgnoredShortDescriptions.Count);
                    foreach (var phrase in filter.IgnoredShortDescriptions)
                    {
                        _logger.LogDebug("[Filter]   - \"{Phrase}\"", phrase);
                    }
                }
                else
                {
                    _logger.LogWarning("[Filter] ⚠️ Brak zabronionych fraz z config.json!");
                }
            }

            foreach (var offer in offers)
            {
                // Logowanie dla debugowania
                if (gameName == "Rust")
                {
                    _logger.LogDebug("[Debug Rust] Analyzing offer:");
                    _logger.LogDebug("- Server: {Server}", offer.Attributes.GetValueOrDefault("server", "none"));
                    _logger.LogDebug("- Type: {Type}", offer.Attributes.GetValueOrDefault("type", "none"));
                    _logger.LogDebug("- Description: {Description}", offer.Description);
                    _logger.LogDebug("- Price: {Price}", offer.Price);
                }

                // Sprawdź typ oferty
                if (offer.Attributes.TryGetValue("type", out var offerType) && offerType == "аренда")
                {
                    report.AddRejectedOffer(offer, "Oferta typu wynajem");
                    continue;
                }

                var offerKey = $"{offer.Description}_{offer.Price}";
                if (uniqueOffers.Contains(offerKey))
                {
                    report.AddRejectedOffer(offer, "Duplikat oferty");
                    continue;
                }
                uniqueOffers.Add(offerKey);

                // Specjalna logika dla Rust
                if (gameName == "Rust")
                {
                    string rustReason;
                    if (!ValidateRustOffer(offer, out rustReason))
                    {
                        report.AddRejectedOffer(offer, $"Rust: {rustReason}");
                        continue;
                    }
                }

                // Sprawdzenie ceny
                if (filter.MinPrice.HasValue && offer.Price < filter.MinPrice)
                {
                    report.AddRejectedOffer(offer, $"Cena ponizej minimum ({offer.Price} < {filter.MinPrice})");
                    continue;
                }
                if (filter.MaxPrice.HasValue && offer.Price > filter.MaxPrice)
                {
                    report.AddRejectedOffer(offer, $"Cena powyzej maximum ({offer.Price} > {filter.MaxPrice})");
                    continue;
                }

                // Sprawdzenie oceny sprzedawcy
                if (filter.MinRating.HasValue && offer.SellerRating < filter.MinRating)
                {
                    report.AddRejectedOffer(offer, $"Ocena ponizej minimum ({offer.SellerRating} < {filter.MinRating})");
                    continue;
                }

                // Sprawdzenie liczby recenzji
                if (filter.MinReviews.HasValue && offer.ReviewCount < filter.MinReviews)
                {
                    report.AddRejectedOffer(offer, $"Liczba recenzji ponizej minimum ({offer.ReviewCount} < {filter.MinReviews})");
                    continue;
                }

                // Sprawdzenie statusu online
                if (filter.OnlineOnly.HasValue && filter.OnlineOnly.Value && !offer.IsOnline)
                {
                    report.AddRejectedOffer(offer, "Sprzedawca offline");
                    continue;
                }

                // Sprawdzenie zabronionych fraz (hardcoded)
                bool containsBannedPhrase = false;
                string bannedPhrase = string.Empty;

                foreach (var phrase in _bannedPhrases)
                {
                    if (offer.Description.Contains(phrase, StringComparison.OrdinalIgnoreCase))
                    {
                        containsBannedPhrase = true;
                        bannedPhrase = phrase;
                        break;
                    }
                }

                if (containsBannedPhrase)
                {
                    report.AddRejectedOffer(offer, $"Zawiera zabronioną frazę (hardcoded): {bannedPhrase}");
                    continue;
                }

                // Sprawdzenie zabronionych fraz z config.json
                if (filter.IgnoredShortDescriptions != null && filter.IgnoredShortDescriptions.Count > 0)
                {
                    foreach (var ignoredPhrase in filter.IgnoredShortDescriptions)
                    {
                        // Sprawdź czy opis zaczyna się od zabronionej frazy (dla MYTHIC, START🏆Any LEGENDARY)
                        if (offer.Description.StartsWith(ignoredPhrase, StringComparison.OrdinalIgnoreCase))
                        {
                            // Specjalne logowanie dla nowych filtrów Raid Shadow Legends
                            if (gameName == "RaidShadowLegends" && (ignoredPhrase == "start with MYTHIC" || ignoredPhrase == "starting MYTHIC" || ignoredPhrase == "START🏆Any LEGENDARY"))
                            {
                                _logger.LogInformation("[Filter] 🚫 ZABLOKOWANO ofertę {OfferId}: \"{Description}\" - zaczyna się od \"{IgnoredPhrase}\"", offer.Id, offer.Description, ignoredPhrase);
                            }
                            report.AddRejectedOffer(offer, $"Opis zaczyna się od zabronionej frazy: {ignoredPhrase}");
                            containsBannedPhrase = true;
                            break;
                        }
                        // Sprawdź czy opis zawiera zabronioną frazę (dla pozostałych)
                        else if (offer.Description.Contains(ignoredPhrase, StringComparison.OrdinalIgnoreCase))
                        {
                            // Specjalne logowanie dla nowych filtrów Raid Shadow Legends
                            if (gameName == "RaidShadowLegends" && (ignoredPhrase == "start with MYTHIC" || ignoredPhrase == "starting MYTHIC" || ignoredPhrase == "START🏆Any LEGENDARY"))
                            {
                                _logger.LogInformation("[Filter] 🚫 ZABLOKOWANO ofertę {OfferId}: \"{Description}\" - zawiera \"{IgnoredPhrase}\"", offer.Id, offer.Description, ignoredPhrase);
                            }
                            report.AddRejectedOffer(offer, $"Zawiera zabronioną frazę (config): {ignoredPhrase}");
                            containsBannedPhrase = true;
                            break;
                        }
                    }

                    if (containsBannedPhrase)
                    {
                        continue;
                    }
                }

                // Jeśli oferta przeszła wszystkie filtry, dodaj ją do zaakceptowanych
                report.AddAcceptedOffer(offer);
            }

            // Zapisz raport
            report.SaveReport();

            if (gameName == "Rust")
            {
                _logger.LogDebug("[Debug Rust] Total filtered offers: {AcceptedOffers}", report.Statistics.AcceptedOffers);
            }

            return report.AcceptedOffers;
        }

        private string ExtractServerFromDescription(string description)
        {
            description = description.ToLower();
            if (description.Contains("euw") || description.Contains("eu west")) return "EUW";
            if (description.Contains("eune") || description.Contains("eu nordic")) return "EUNE";
            if (description.Contains("na ") || description.Contains("north america")) return "NA";
            // ... dodaj więcej serwerów
            return string.Empty;
        }

        private (int hours, int skins) ExtractRustDetails(string description)
        {
            int hours = 0;
            int skins = 0;
            
            description = description.ToLower();

            // Definicje wzorców dla godzin
            var hoursPatterns = new[]
            {
                @"(\d+)\+?\s*(?:hours?\s*(?:boosted|idle)?)",    // "2000+ hours", "500+ Hours Boosted"
                @"(\d+)(?:\s*k)?\s*h(?:ours?)?",                 // "2k hours", "2000h"
                @"with\s*(\d+)\+?\s*hours",                      // "with 2000+ hours"
                @"(\d+)\s*hours?\s*(?:in|on)?\s*(?:game|rust)",  // "2000 hours in game"
                @"account\s*of\s*(\d+)\+?\s*hours",              // "account of 1000+ hours"
                @"clock\s*is\s*wound\s*up.*?(\d+)",              // dla przypadków z "clock is wound up"
                @"0\s*hours?\s*(?:in\s*(?:the\s*)?game)?",      // "0 hours in the game"
                @"\(0\s*hours?\)"                                // "(0 hours)"
            };

            // Definicje wzorców dla skinów
            var skinsPatterns = new[]
            {
                @"(\d+)(?:\+|\s*-\s*\d+)?\s*(?:rust\s*)?skins?",              // "63+ RUST SKINS", "29-72 RUST SKINS"
                @"skins\s*:?\s*(\d+)(?:\+|-\d+)?",                            // "skins: 150+", "skins 29-72"
                @"(\d+)\s*(?:twitch\s*)?(?:drops?|items)",                    // "247 TWITCH ITEMS"
                @"(\d+)(?:\s*-\s*\d+)?(?:\+\d+)*\s*(?:rounds?|drops?)",      // "24-35 sets"
                @"inventory\s*:?\s*(\d+)(?:\+|-\d+)?",                        // "inventory: 150+"
                @"twitch\s*items?\s*(\d+)",                                   // "twitch items 142"
                @"(\d+)\s*items?\s*in\s*total",                              // "142 items in total"
                @"(\d+)\s*sets?\s*of\s*twitch\s*drops?"                      // "24-35 sets of twitch drops"
            };
            
            // Najpierw sprawdzamy czy to jest konto z 0 godzin
            var zeroHoursMatch = description.Contains("0 hours") || 
                                description.Contains("(0 hours)") || 
                                description.Contains("0 hours in the game");
            
            if (zeroHoursMatch)
            {
                hours = 0;
                
                // Sprawdzamy czy są wzmianki o Twitch Drops/Items
                var hasTwitchItems = description.Contains("twitch") && 
                    (description.Contains("drops") || description.Contains("items"));
                    
                if (!hasTwitchItems)
                {
                    // Jeśli 0 godzin i brak wzmianki o Twitch Drops, ustawiamy też 0 skinów
                    skins = 0;
                    return (hours, skins);
                }
            }

            // Jeśli nie jest to konto z 0 godzin lub ma Twitch Drops, szukamy godzin normalnie
            if (!zeroHoursMatch)
            {
                foreach (var pattern in hoursPatterns)
                {
                    try
                    {
                        var match = Regex.Match(description, pattern, RegexOptions.IgnoreCase);
                        if (match.Success && match.Groups.Count > 1 && int.TryParse(match.Groups[1].Value, out int foundHours))
                        {
                            hours = foundHours;
                            break;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning("[Parser] ⚠️ Błąd podczas parsowania godzin z wzorcem '{Pattern}': {Error}", pattern, ex.Message);
                    }
                }
            }

            // Szukamy skinów tylko jeśli nie jest to konto z 0 godzin bez Twitch Drops
            foreach (var pattern in skinsPatterns)
            {
                try
                {
                    var match = Regex.Match(description, pattern, RegexOptions.IgnoreCase);
                    if (match.Success && match.Groups.Count > 1 && int.TryParse(match.Groups[1].Value, out int foundSkins))
                    {
                        if (foundSkins > skins) // bierzemy największą znalezioną wartość
                        {
                            skins = foundSkins;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning("[Parser] ⚠️ Błąd podczas parsowania skinów z wzorcem '{Pattern}': {Error}", pattern, ex.Message);
                }
            }

            return (hours, skins);
        }

        /// <summary>
        /// Tworzy obiekt OfferDetails z parsowanych parametrów
        /// </summary>
        private OfferDetails BuildOfferDetails(string offerId, string price, Dictionary<string, string> parameters,
            HtmlDocument document, HtmlNode linkNode, HtmlNode ratingStarsNode, int level, int winrate)
        {
            return new OfferDetails
            {
                Id = offerId,
                Url = parameters.GetValueOrDefault("OriginalUrl", ""),
                FilteringPrice = price,
                ActualOfferPrice = price,
                RatingStars = ExtractRatingFromDiv(ratingStarsNode),
                DataFType = linkNode?.GetAttributeValue("data-f-type", string.Empty) ?? string.Empty,

                // Podstawowe informacje
                ShortDescription = parameters.GetValueOrDefault("Short description", ""),
                DetailedDescription = parameters.GetValueOrDefault("Detailed description", ""),

                // Informacje o koncie
                Level = level,
                Rank = parameters.GetValueOrDefault("Rank", "UnRanked"),
                Heroes = ExtractNumber(parameters.GetValueOrDefault("Heroes", "0")),
                Skins = ExtractNumber(parameters.GetValueOrDefault("Skins", "0")),
                Winrate = winrate,

                // Champions
                MythicalChampions = ExtractNumber(parameters.GetValueOrDefault("Mythical champions", "0")),
                LegendaryChampions = ExtractNumber(parameters.GetValueOrDefault("Legendary champions", "0")),
                EpicChampions = ExtractNumber(parameters.GetValueOrDefault("Epic champions", "0")),
                RareChampions = ExtractNumber(parameters.GetValueOrDefault("Rare champions", "0")),
                Champions = ExtractNumber(parameters.GetValueOrDefault("Champions", "0")),

                // Parametry FC 25
                SquadPrice = parameters.GetValueOrDefault("Squad price", parameters.GetValueOrDefault("Squad price (k)", "")),
                CoinsOnBalance = parameters.GetValueOrDefault("Coins on balance", parameters.GetValueOrDefault("Coins on balance (k)", "")),
                WebApp = parameters.GetValueOrDefault("Web App", parameters.GetValueOrDefault("Web app", "")),
                RatingFC25 = parameters.GetValueOrDefault("Rating", ""),

                // Statystyki
                MMR = ExtractNumber(parameters.GetValueOrDefault("MMR", "0")),
                BehaviorScore = ExtractNumber(parameters.GetValueOrDefault("Behavior score", "0")),
                PveFame = ExtractDecimal(parameters.GetValueOrDefault("PvE Fame", "0")),
                Trophies = ExtractNumber(parameters.GetValueOrDefault("Trophies", "0")),

                // Przedmioty i postacie
                Brawlers = ExtractNumber(parameters.GetValueOrDefault("Brawlers", "0")),
                SkinsQuantity = ExtractNumber(parameters.GetValueOrDefault("Skins quantity", "0")),
                AgentsQuantity = ExtractNumber(parameters.GetValueOrDefault("Agents quantity", "0")),

                // Dodatkowe informacje
                WeaponType = parameters.GetValueOrDefault("Weapon type", ""),
                BattlegroundsPlus = parameters.ContainsKey("BATTLEGROUNDS Plus") &&
                                    parameters["BATTLEGROUNDS Plus"].ToUpper() == "YES" ? "Yes" : "No",

                Images = ParseImages(document),
                Edition = GameSpecificHelper.DetermineGameEdition(parameters.GetValueOrDefault("Detailed description", "") +
                                     parameters.GetValueOrDefault("Short description", "")),

                // Dodajemy parsowanie serwera z różnych źródeł
                Server = parameters.GetValueOrDefault("Server",
                       parameters.GetValueOrDefault("Region",
                       ExtractServerFromDescription(parameters.GetValueOrDefault("Short description", "") +
                                                 parameters.GetValueOrDefault("Detailed description", "")))),
                Platform = parameters.GetValueOrDefault("Platform", "PC"),
                HoursPlayed = ExtractNumber(parameters.GetValueOrDefault("Hours played", "0")),
                AR = ExtractNumber(parameters.GetValueOrDefault("AR", "0")),
                Primogems = ExtractNumber(parameters.GetValueOrDefault("Primogems", "0")),

                // Dodatkowe parametry
                MythicSkins = ExtractNumber(parameters.GetValueOrDefault("Mythic skins", "0")),
                KillChats = ExtractNumber(parameters.GetValueOrDefault("Kill chats", "0")),
                SportCars = ExtractNumber(parameters.GetValueOrDefault("Sport cars", "0")),
                Stage = ExtractNumber(parameters.GetValueOrDefault("Stage", "0"))
            };
        }

        /// <summary>
        /// Post-processing oferty - specjalne przetwarzanie, walidacja, mapowanie serwerów
        /// </summary>
        private void PostProcessOffer(OfferDetails offerDetails, Dictionary<string, string> parameters)
        {
            // Specjalne przetwarzanie dla Rust
            if (offerDetails.DataFType == "продажа" && // sprawdzamy czy to oferta sprzedaży
                (offerDetails.ShortDescription + offerDetails.DetailedDescription).ToLower().Contains("rust"))
            {
                var (hours, skins) = ExtractRustDetails(
                    offerDetails.ShortDescription + " " + offerDetails.DetailedDescription
                );
                offerDetails.HoursPlayed = hours;
                offerDetails.Skins = skins;
            }

            // Sprawdzenie czy znaleziono wszystkie wymagane parametry
            var requiredParams = new[] { "Server", "Level", "Champions", "Skins", "Rank" };
            foreach (var param in requiredParams)
            {
                if (!parameters.ContainsKey(param))
                {
                    _logger.LogParameterMissing(param);
                }
            }

            // Mapowanie serwerów LoL na nazwy używane w G2G
            if (!string.IsNullOrEmpty(offerDetails.Server))
            {
                offerDetails.Server = GameSpecificHelper.MapLoLServer(offerDetails.Server);
            }
            else
            {
                // Domyślnie ustawiamy EUW jeśli nie znaleziono serwera
                offerDetails.Server = "EUW";
                _logger.LogDefaultServer("EUW");
            }
        }

        /// <summary>
        /// Parsuje cenę z opcji płatności w dokumencie HTML
        /// </summary>
        private string ParsePriceFromPaymentOptions(HtmlDocument document)
        {
            // Szukamy różnych opcji płatności (Litecoin, USDT, etc.)
            var paymentOptions = document.DocumentNode.SelectNodes("//select[@name='method']//option");

            string price = "Brak ceny";

            if (paymentOptions != null)
            {
                _logger.LogPaymentOptions(paymentOptions.Count);

                foreach (var option in paymentOptions)
                {
                    var optionText = option.InnerText;
                    var dataContent = option.GetAttributeValue("data-content", "");

                    _logger.LogPaymentOption(optionText, dataContent);

                    // Sprawdź USDT
                    if (optionText.Contains("USDT") || dataContent.Contains("USDT"))
                    {
                        try
                        {
                            var usdtMatch = Regex.Match(dataContent, @"([\d.,]+)\s*USDT");
                            if (usdtMatch.Success)
                            {
                                var usdtValue = usdtMatch.Groups[1].Value;
                                _logger.LogPriceFound("USDT", usdtValue);
                                price = $"{usdtValue} USDT";
                                break; // Preferuj USDT jeśli dostępne
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogPriceError("USDT", ex.Message);
                        }
                    }

                    // Sprawdź Litecoin (fallback)
                    if (optionText.Contains("Litecoin"))
                    {
                        try
                        {
                            var priceMatch = Regex.Match(dataContent, @"(\d+\.?\d*)\s*\$");
                            if (priceMatch.Success && priceMatch.Groups.Count > 1)
                            {
                                _logger.LogPriceFound("Litecoin", $"{priceMatch.Groups[1].Value} $");
                                if (price == "Brak ceny") // Tylko jeśli nie znaleziono USDT
                                {
                                    price = $"{priceMatch.Groups[1].Value} $";
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogPriceError("Litecoin", ex.Message);
                        }
                    }
                }
            }

            _logger.LogFinalPrice(price);
            return price;
        }

        // Implementacja interfejsu IParserService
        public OfferDetails ParseOfferDetails(HtmlDocument document, string url)
        {
            var html = document.DocumentNode.OuterHtml;
            var parameters = new Dictionary<string, string> { { "OriginalUrl", url } };
            return ParseOfferHtml(html, parameters);
        }

        public List<OfferListItem> ParseOfferList(HtmlDocument document)
        {
            var html = document.DocumentNode.OuterHtml;
            return ParseOfferList(html);
        }
    }
}