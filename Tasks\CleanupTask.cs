using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using ShopBot.Services;

namespace ShopBot.Tasks
{
    public class CleanupTask : BaseTask
    {
        private const string FilteredOffersDir = "filtered_offers";
        private const int MaxLogAgeDays = 7;
        private readonly HashSet<string> _protectedFiles = new HashSet<string> 
        { 
            "offers_tracking.json",
            "g2g_offers.json",
            "config.json",
            "gamesconfig.json"
        };

        public override async Task ExecuteAsync(CancellationToken cancellationToken)
        {
            try
            {
                Status = TaskStatus.Running;
                Console.WriteLine("\n=== Rozpoczynam czyszczenie starych plików ===");

                // Czyszczenie starych plików z filtered_offers
                if (Directory.Exists(FilteredOffersDir))
                {
                    var files = Directory.GetFiles(FilteredOffersDir, "*.json");
                    var cutoffDate = DateTime.Now.AddDays(-MaxLogAgeDays);

                    foreach (var file in files)
                    {
                        if (cancellationToken.IsCancellationRequested)
                            break;

                        var fileInfo = new FileInfo(file);
                        if (fileInfo.LastWriteTime < cutoffDate)
                        {
                            try
                            {
                                File.Delete(file);
                                Console.WriteLine($"[Cleanup] Usunięto stary plik: {fileInfo.Name}");
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"[Cleanup] Błąd podczas usuwania pliku {fileInfo.Name}: {ex.Message}");
                            }
                        }
                    }
                }

                // Czyszczenie nieaktywnych ofert starszych niż 7 dni
                if (File.Exists("inactive_g2g_offers.json"))
                {
                    try
                    {
                        var inactiveOffers = await File.ReadAllTextAsync("inactive_g2g_offers.json");
                        // TODO: Implementacja czyszczenia starych nieaktywnych ofert
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"[Cleanup] Błąd podczas czyszczenia nieaktywnych ofert: {ex.Message}");
                    }
                }

                // Czyszczenie plików logów
                var logFiles = Directory.GetFiles(".", "*.log");
                foreach (var logFile in logFiles)
                {
                    if (cancellationToken.IsCancellationRequested)
                        break;

                    var fileInfo = new FileInfo(logFile);
                    var fileName = Path.GetFileName(logFile);

                    // Pomijamy chronione pliki
                    if (_protectedFiles.Contains(fileName))
                    {
                        Console.WriteLine($"[Cleanup] Pomijam chroniony plik: {fileName}");
                        continue;
                    }

                    if (fileInfo.LastWriteTime < DateTime.Now.AddDays(-MaxLogAgeDays))
                    {
                        try
                        {
                            File.Delete(logFile);
                            Console.WriteLine($"[Cleanup] Usunięto stary plik logu: {fileInfo.Name}");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"[Cleanup] Błąd podczas usuwania pliku logu {fileInfo.Name}: {ex.Message}");
                        }
                    }
                }

                Status = TaskStatus.Completed;
                Console.WriteLine("[Cleanup] Zakończono czyszczenie starych plików");
            }
            catch (Exception ex)
            {
                Status = TaskStatus.Failed;
                ErrorMessage = ex.Message;
                Console.WriteLine($"[Cleanup] Błąd podczas czyszczenia: {ex.Message}");
                throw;
            }
        }
    }
} 