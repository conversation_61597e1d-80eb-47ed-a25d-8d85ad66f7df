{"TotalOffers": 1698, "AcceptedOffers": 493, "RejectedOffers": 1205, "RejectionReasons": {"Cena ponizej minimum (5,76 < 15)": 5, "Cena ponizej minimum (3,84 < 15)": 2, "Cena ponizej minimum (4,80 < 15)": 6, "Cena ponizej minimum (6,72 < 15)": 3, "Cena ponizej minimum (7,68 < 15)": 1, "Duplikat oferty": 7, "Ocena ponizej minimum (0 < 5)": 325, "Cena ponizej minimum (11,78 < 15)": 24, "Cena ponizej minimum (5,29 < 15)": 16, "Zawiera zabronioną frazę (hardcoded): start account": 11, "Zawiera zabronioną frazę (hardcoded): with any mythic": 8, "Zawiera zabronioną frazę (hardcoded): with any": 5, "Cena ponizej minimum (7,85 < 15)": 24, "Cena ponizej minimum (7,20 < 15)": 11, "Cena ponizej minimum (9,16 < 15)": 25, "Cena ponizej minimum (8,51 < 15)": 8, "Cena ponizej minimum (13,09 < 15)": 64, "Zawiera zabronioną frazę (hardcoded): starting accounts": 2, "Cena ponizej minimum (7,72 < 15)": 1, "Cena ponizej minimum (6,59 < 15)": 2, "Cena ponizej minimum (8,79 < 15)": 1, "Cena ponizej minimum (10,98 < 15)": 2, "Cena ponizej minimum (7,69 < 15)": 3, "Cena ponizej minimum (9,88 < 15)": 2, "Cena ponizej minimum (13,18 < 15)": 1, "Cena ponizej minimum (14,28 < 15)": 1, "Cena ponizej minimum (5,49 < 15)": 1, "Cena ponizej minimum (6,42 < 15)": 2, "Cena ponizej minimum (12,75 < 15)": 1, "Cena ponizej minimum (14,39 < 15)": 21, "Zawiera zabronioną frazę (hardcoded): accounts with": 8, "Zawiera zabronioną frazę (hardcoded): your choice": 7, "Zawiera zabronioną frazę (hardcoded): with ankil of your choice": 1, "Zawiera zabronioną frazę (hardcoded): with any login legend": 2, "Cena ponizej minimum (14,99 < 15)": 1, "Cena ponizej minimum (8,57 < 15)": 3, "Cena ponizej minimum (11,77 < 15)": 1, "Cena ponizej minimum (8,56 < 15)": 1, "Cena ponizej minimum (12,85 < 15)": 1, "Cena ponizej minimum (4,28 < 15)": 3, "Zawiera zabronioną frazę (hardcoded): starter account": 34, "Cena ponizej minimum (9,54 < 15)": 1, "Cena ponizej minimum (7,58 < 15)": 1, "Cena ponizej minimum (9,34 < 15)": 1, "Cena ponizej minimum (5,32 < 15)": 4, "Cena ponizej minimum (10,47 < 15)": 26, "Cena ponizej minimum (8,64 < 15)": 3, "Cena ponizej minimum (6,94 < 15)": 9, "Cena ponizej minimum (13,74 < 15)": 1, "Cena ponizej minimum (9,81 < 15)": 8, "Cena ponizej minimum (6,41 < 15)": 2, "Cena ponizej minimum (5,35 < 15)": 2, "Cena ponizej minimum (3,21 < 15)": 1, "Cena ponizej minimum (7,48 < 15)": 2, "Cena ponizej minimum (2,62 < 15)": 14, "Cena ponizej minimum (10,70 < 15)": 1, "Cena ponizej minimum (4,81 < 15)": 1, "Cena ponizej minimum (6,96 < 15)": 1, "Cena ponizej minimum (4,50 < 15)": 1, "Cena ponizej minimum (2,57 < 15)": 1, "Zawiera zabronioną frazę (hardcoded): to choose from": 13, "Cena powyzej maximum (1308,53 > 1000)": 6, "Cena ponizej minimum (5,10 < 15)": 4, "Cena ponizej minimum (12,69 < 15)": 1, "Cena ponizej minimum (10,08 < 15)": 1, "Cena ponizej minimum (7,46 < 15)": 1, "Cena ponizej minimum (9,42 < 15)": 7, "Cena ponizej minimum (4,84 < 15)": 2, "Cena ponizej minimum (11,91 < 15)": 1, "Cena ponizej minimum (11,12 < 15)": 3, "Cena ponizej minimum (9,55 < 15)": 5, "Cena ponizej minimum (9,29 < 15)": 1, "Cena ponizej minimum (12,43 < 15)": 7, "Cena ponizej minimum (6,54 < 15)": 47, "Cena powyzej maximum (1490,64 > 1000)": 1, "Cena ponizej minimum (0,32 < 15)": 1, "Cena powyzej maximum (1064,74 > 1000)": 1, "Cena ponizej minimum (6,39 < 15)": 1, "Cena ponizej minimum (11,71 < 15)": 3, "Zawiera zabronioną frazę (config): START🏆Any LEGENDARY": 1, "Zawiera zabronioną frazę (config): start with MYTHIC": 2, "Cena ponizej minimum (14,77 < 15)": 2, "Cena ponizej minimum (5,78 < 15)": 1, "Cena ponizej minimum (13,54 < 15)": 2, "Cena ponizej minimum (5,23 < 15)": 23, "Cena ponizej minimum (10,71 < 15)": 1, "Zawiera zabronioną frazę (hardcoded): with lega of choice": 8, "Cena ponizej minimum (12,30 < 15)": 1, "Cena ponizej minimum (5,89 < 15)": 15, "Cena ponizej minimum (7,65 < 15)": 1, "Ocena ponizej minimum (4 < 5)": 58, "Cena ponizej minimum (8,52 < 15)": 1, "Cena powyzej maximum (1439,39 > 1000)": 1, "Cena ponizej minimum (2,25 < 15)": 1, "Cena ponizej minimum (0,72 < 15)": 2, "Cena ponizej minimum (1,96 < 15)": 9, "Cena ponizej minimum (4,58 < 15)": 21, "Cena ponizej minimum (1,31 < 15)": 8, "Cena ponizej minimum (0,39 < 15)": 1, "Ocena ponizej minimum (3 < 5)": 1, "Cena ponizej minimum (0,98 < 15)": 1, "Cena ponizej minimum (0,52 < 15)": 23, "Cena ponizej minimum (7,33 < 15)": 1, "Cena ponizej minimum (4,71 < 15)": 1, "Cena ponizej minimum (11,65 < 15)": 1, "Cena ponizej minimum (4,19 < 15)": 1, "Cena ponizej minimum (11,38 < 15)": 1, "Cena ponizej minimum (3,40 < 15)": 2, "Cena ponizej minimum (3,53 < 15)": 1, "Cena ponizej minimum (6,28 < 15)": 2, "Cena powyzej maximum (2617,06 > 1000)": 3, "Cena powyzej maximum (2747,92 > 1000)": 1, "Cena powyzej maximum (3925,60 > 1000)": 4, "Cena ponizej minimum (8,11 < 15)": 2, "Cena ponizej minimum (3,93 < 15)": 26, "Cena ponizej minimum (6,35 < 15)": 2, "Zawiera zabronioną frazę (hardcoded): starting account with a legendary to choose": 1, "Zawiera zabronioną frazę (hardcoded): account with one leg to choose": 2, "Zawiera zabronioną frazę (hardcoded): account with 1 leg to choose": 2, "Cena ponizej minimum (10,57 < 15)": 1, "Cena ponizej minimum (2,20 < 15)": 2, "Cena ponizej minimum (10,21 < 15)": 1, "Cena ponizej minimum (10,86 < 15)": 1, "Cena ponizej minimum (10,99 < 15)": 1, "Cena ponizej minimum (14,54 < 15)": 2, "Cena ponizej minimum (10,17 < 15)": 3, "Cena ponizej minimum (2,50 < 15)": 1, "Cena ponizej minimum (7,07 < 15)": 1, "Cena ponizej minimum (3,27 < 15)": 23, "Cena ponizej minimum (5,50 < 15)": 1, "Cena powyzej maximum (2224,50 > 1000)": 3, "Cena ponizej minimum (6,20 < 15)": 1, "Cena ponizej minimum (14,79 < 15)": 1, "Cena ponizej minimum (2,75 < 15)": 5, "Cena ponizej minimum (2,36 < 15)": 1, "Cena ponizej minimum (13,07 < 15)": 2, "Cena ponizej minimum (8,90 < 15)": 1, "Cena ponizej minimum (8,71 < 15)": 1, "Cena ponizej minimum (13,23 < 15)": 1, "Cena powyzej maximum (1384,16 > 1000)": 1, "Cena powyzej maximum (1168,96 > 1000)": 1, "Cena ponizej minimum (9,32 < 15)": 1, "Cena ponizej minimum (9,53 < 15)": 1, "Cena ponizej minimum (5,86 < 15)": 1, "Cena ponizej minimum (4,68 < 15)": 1, "Cena ponizej minimum (7,67 < 15)": 1, "Cena ponizej minimum (0,44 < 15)": 1, "Cena ponizej minimum (2,04 < 15)": 1, "Cena ponizej minimum (14,98 < 15)": 1, "Cena ponizej minimum (4,97 < 15)": 2, "Cena ponizej minimum (3,01 < 15)": 1, "Cena ponizej minimum (5,41 < 15)": 1, "Cena ponizej minimum (9,05 < 15)": 1, "Cena powyzej maximum (1046,83 > 1000)": 3, "Cena ponizej minimum (1,04 < 15)": 1, "Cena powyzej maximum (1177,68 > 1000)": 4, "Cena ponizej minimum (4,20 < 15)": 1, "Cena powyzej maximum (1863,30 > 1000)": 1, "Cena powyzej maximum (1171,22 > 1000)": 1, "Cena powyzej maximum (1701,09 > 1000)": 1, "Cena ponizej minimum (6,02 < 15)": 1, "Cena powyzej maximum (2486,21 > 1000)": 1, "Cena powyzej maximum (1150,20 > 1000)": 1, "Cena ponizej minimum (6,60 < 15)": 1, "Cena ponizej minimum (7,45 < 15)": 1, "Cena ponizej minimum (10,60 < 15)": 1, "Cena ponizej minimum (4,92 < 15)": 3, "Cena ponizej minimum (6,15 < 15)": 2, "Cena ponizej minimum (0,65 < 15)": 2, "Cena ponizej minimum (0,54 < 15)": 2, "Cena ponizej minimum (3,75 < 15)": 1, "Cena powyzej maximum (3297,50 > 1000)": 1, "Cena ponizej minimum (4,32 < 15)": 2, "Cena powyzej maximum (2132,91 > 1000)": 1, "Cena ponizej minimum (3,66 < 15)": 2, "Cena ponizej minimum (2,22 < 15)": 1, "Cena ponizej minimum (10,34 < 15)": 1, "Cena ponizej minimum (1,54 < 15)": 1, "Cena ponizej minimum (14,16 < 15)": 1, "Cena ponizej minimum (5,22 < 15)": 1, "Cena ponizej minimum (2,31 < 15)": 1, "Cena ponizej minimum (1,76 < 15)": 1, "Cena ponizej minimum (13,84 < 15)": 1, "Cena ponizej minimum (5,63 < 15)": 1, "Cena powyzej maximum (1292,01 > 1000)": 1, "Cena ponizej minimum (1,05 < 15)": 1, "Cena ponizej minimum (9,84 < 15)": 2, "Cena ponizej minimum (3,89 < 15)": 1, "Cena ponizej minimum (5,81 < 15)": 1, "Cena ponizej minimum (3,28 < 15)": 1, "Cena ponizej minimum (5,54 < 15)": 1, "Cena ponizej minimum (0,49 < 15)": 1, "Cena ponizej minimum (6,55 < 15)": 1, "Cena ponizej minimum (2,13 < 15)": 1, "Cena ponizej minimum (1,83 < 15)": 1, "Cena ponizej minimum (2,88 < 15)": 1, "Cena powyzej maximum (1962,80 > 1000)": 1, "Cena ponizej minimum (2,45 < 15)": 1, "Cena powyzej maximum (2093,65 > 1000)": 1, "Cena ponizej minimum (13,48 < 15)": 1, "Cena powyzej maximum (1373,96 > 1000)": 1, "Cena ponizej minimum (9,41 < 15)": 1, "Cena powyzej maximum (1360,87 > 1000)": 1, "Cena powyzej maximum (1347,79 > 1000)": 1, "Cena ponizej minimum (3,19 < 15)": 1, "Cena ponizej minimum (4,25 < 15)": 1, "Cena ponizej minimum (6,90 < 15)": 1}, "GenerationTime": "2025-06-16T22:46:39.8793035+02:00"}