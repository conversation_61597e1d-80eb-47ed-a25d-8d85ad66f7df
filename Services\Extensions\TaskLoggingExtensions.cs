using Microsoft.Extensions.Logging;

namespace ShopBot.Services.Extensions
{
    /// <summary>
    /// Extension methods dla Tasks structured logging
    /// Zastępuje Console.WriteLine z structured logging w Tasks
    /// </summary>
    public static class TaskLoggingExtensions
    {
        // ===== ADD OFFER TASK =====
        public static void LogOfferLimitReached(this ILogger logger, string offerId)
            => logger.LogWarning("[AddOffer] ⚠️ Pomijam dodawanie oferty {OfferId} - osiągnięto limit na G2G", offerId);

        public static void LogOfferAddStart(this ILogger logger, string offerId)
            => logger.LogInformation("[AddOffer] 🔄 Rozpoczynam dodawanie oferty {OfferId}", offerId);

        public static void LogOfferAddSuccess(this ILogger logger, string offerId, string publishedOfferId)
            => logger.LogInformation("[AddOffer] ✅ Pomyślnie opublikowano ofertę {OfferId} -> {PublishedOfferId}", offerId, publishedOfferId);

        public static void LogOfferLimitExceeded(this ILogger logger, string message)
            => logger.LogWarning("[AddOffer] ⚠️ {Message}", message);

        public static void LogOfferAddError(this ILogger logger, string offerId, string error)
            => logger.LogError("[AddOffer] ❌ Błąd podczas dodawania oferty {OfferId}: {Error}", offerId, error);

        // ===== SCAN FUNPAY TASK =====
        public static void LogScanStart(this ILogger logger)
            => logger.LogInformation("=== Rozpoczynam skanowanie FunPay ===");

        public static void LogGameScanStart(this ILogger logger, string gameName)
            => logger.LogInformation("=== Skanowanie gry {GameName} ===", gameName);

        public static void LogGameScanComplete(this ILogger logger, string gameName, int offerCount)
            => logger.LogInformation("[Scan] ✅ Zakończono skanowanie {GameName}: {OfferCount} ofert", gameName, offerCount);

        public static void LogScanComplete(this ILogger logger)
            => logger.LogInformation("=== Zakończono skanowanie FunPay ===");

        public static void LogScanError(this ILogger logger, string gameName, string error)
            => logger.LogError("[Scan] ❌ Błąd podczas skanowania {GameName}: {Error}", gameName, error);

        // ===== UPDATE PRICES TASK =====
        public static void LogPriceUpdateStart(this ILogger logger)
            => logger.LogInformation("=== Rozpoczynam aktualizację cen ofert ===");

        public static void LogGamePriceUpdateStart(this ILogger logger, string gameName)
            => logger.LogInformation("=== Aktualizacja cen dla gry {GameName} ===", gameName);

        public static void LogPriceUpdateSuccess(this ILogger logger, string funPayId, string g2gOfferId, decimal oldPrice, decimal newPrice)
            => logger.LogInformation("[Update] ✅ Zaktualizowano cenę oferty {FunPayId}: G2G ID: {G2GOfferId}, Stara cena: ${OldPrice}, Nowa cena: ${NewPrice}", 
                funPayId, g2gOfferId, oldPrice, newPrice);

        public static void LogTaskPriceUpdateError(this ILogger logger, string funPayId, string error)
            => logger.LogError("[Update] ❌ Błąd aktualizacji ceny oferty {FunPayId}: {Error}", funPayId, error);

        public static void LogGamePriceUpdateError(this ILogger logger, string gameName, string error)
            => logger.LogError("[Update] Błąd podczas aktualizacji cen dla {GameName}: {Error}", gameName, error);

        public static void LogPriceUpdateComplete(this ILogger logger)
            => logger.LogInformation("=== Zakończono aktualizację cen ofert ===");

        // ===== ANALYZE OFFERS TASK =====
        public static void LogAnalysisStart(this ILogger logger)
            => logger.LogInformation("=== Rozpoczynam analizę opisów ofert ===");

        public static void LogGameAnalysisStart(this ILogger logger, string gameName)
            => logger.LogInformation("[Analysis] 🎮 Analizuję grę: {GameName}", gameName);

        public static void LogOffersFoundForAnalysis(this ILogger logger, int offerCount)
            => logger.LogInformation("[Analysis] 📝 Znaleziono {OfferCount} ofert do analizy", offerCount);

        public static void LogOfferDetailsRetrieved(this ILogger logger, string offerId)
            => logger.LogDebug("[Analysis] 🔍 Pobieram szczegóły oferty {OfferId}", offerId);

        public static void LogOfferDescriptionAdded(this ILogger logger, string offerId)
            => logger.LogDebug("[Analysis] ✓ Dodano opis oferty {OfferId}", offerId);

        public static void LogOfferAnalysisError(this ILogger logger, string offerId, string error)
            => logger.LogWarning("[Analysis] ⚠️ Błąd podczas pobierania szczegółów oferty {OfferId}: {Error}", offerId, error);

        public static void LogGameAnalysisError(this ILogger logger, string gameName, string error)
            => logger.LogError("[Analysis] ❌ Błąd podczas analizy gry {GameName}: {Error}", gameName, error);

        public static void LogAnalysisComplete(this ILogger logger, int totalOffers)
            => logger.LogInformation("=== Zakończono analizę opisów ofert === Łącznie przeanalizowano {TotalOffers} ofert", totalOffers);

        // ===== SYNC OFFERS TASK =====
        public static void LogSyncStart(this ILogger logger)
            => logger.LogInformation("=== Rozpoczynam synchronizację ofert ===");

        public static void LogInitialOfferCount(this ILogger logger, int totalOffers, int limit)
            => logger.LogInformation("[Sync] 📊 Stan początkowy: {TotalOffers}/{Limit} ofert na G2G", totalOffers, limit);

        public static void LogActualOfferCount(this ILogger logger, int actualCount, int trackedCount)
            => logger.LogInformation("[Sync] 🔍 Rzeczywista liczba ofert na G2G: {ActualCount} (tracked: {TrackedCount})", actualCount, trackedCount);

        public static void LogOfferCountMismatch(this ILogger logger, int actualCount, int trackedCount)
            => logger.LogWarning("[Sync] ⚠️ Rozbieżność w liczbie ofert! API: {ActualCount}, Tracked: {TrackedCount}", actualCount, trackedCount);

        public static void LogNoActiveGames(this ILogger logger)
            => logger.LogWarning("[Sync] ⚠️ Brak aktywnych gier w konfiguracji");

        public static void LogSyncGameProcessingStart(this ILogger logger, string gameName)
            => logger.LogInformation("[Sync] 🎮 Przetwarzam grę: {GameName}", gameName);

        public static void LogCurrentGameOfferCount(this ILogger logger, int offerCount)
            => logger.LogInformation("[Sync] 📝 Aktualna liczba ofert na G2G: {OfferCount}", offerCount);

        public static void LogNewOffersFound(this ILogger logger, int newOfferCount, string gameName)
            => logger.LogInformation("[Sync] 🆕 Znaleziono {NewOfferCount} nowych ofert dla {GameName}", newOfferCount, gameName);

        public static void LogOfferDescriptionsSaved(this ILogger logger, string filePath)
            => logger.LogInformation("[Sync] 📝 Zapisano opisy ofert do pliku: {FilePath}", filePath);

        public static void LogOfferDescriptionsSaveError(this ILogger logger, string error)
            => logger.LogWarning("[Sync] ⚠️ Błąd podczas zapisywania opisów ofert: {Error}", error);

        public static void LogSyncComplete(this ILogger logger)
            => logger.LogInformation("=== Zakończono synchronizację ofert ===");

        public static void LogSyncError(this ILogger logger, string error)
            => logger.LogError("[Sync] ❌ Błąd podczas synchronizacji: {Error}", error);

        // ===== GENERAL TASK OPERATIONS =====
        public static void LogTaskStart(this ILogger logger, string taskName)
            => logger.LogInformation("🚀 Rozpoczynam zadanie: {TaskName}", taskName);

        public static void LogTaskComplete(this ILogger logger, string taskName)
            => logger.LogInformation("✅ Zakończono zadanie: {TaskName}", taskName);

        public static void LogTaskError(this ILogger logger, string taskName, string error)
            => logger.LogError("❌ Błąd w zadaniu {TaskName}: {Error}", taskName, error);

        public static void LogTaskCancelled(this ILogger logger, string taskName)
            => logger.LogWarning("⚠️ Anulowano zadanie: {TaskName}", taskName);

        // ===== PERFORMANCE METRICS =====
        public static void LogTaskDuration(this ILogger logger, string taskName, double durationMs)
            => logger.LogInformation("⏱️ Zadanie {TaskName} wykonano w {DurationMs}ms", taskName, durationMs);

        public static void LogOfferProcessingRate(this ILogger logger, int processedCount, int totalCount, double elapsedSeconds)
            => logger.LogInformation("📊 Przetworzono {ProcessedCount}/{TotalCount} ofert w {ElapsedSeconds:F1}s ({Rate:F1} ofert/s)",
                processedCount, totalCount, elapsedSeconds, processedCount / elapsedSeconds);

        // ===== OFFER CONTENT CHANGE DETECTION =====
        public static void LogContentChangeDetectionStart(this ILogger logger, int offerCount, string gameName)
            => logger.LogDebug("[ChangeDetector] 🔍 Rozpoczynam wykrywanie zmian zawartości dla {OfferCount} ofert w grze {GameName}", offerCount, gameName);

        public static void LogContentChangeDetected(this ILogger logger, string offerId, string oldTitle, decimal oldPrice, string newTitle, decimal newPrice)
            => logger.LogInformation("[ChangeDetector] 🔄 Wykryto znaczące zmiany w ofercie {OfferId}: '{OldTitle}' (${OldPrice}) → '{NewTitle}' (${NewPrice})",
                offerId, oldTitle, oldPrice, newTitle, newPrice);

        public static void LogPriceChangeDetected(this ILogger logger, string offerId, decimal oldPrice, decimal newPrice, double changePercent)
            => logger.LogInformation("[ChangeDetector] 💰 Wykryto zmianę ceny w ofercie {OfferId}: ${OldPrice} → ${NewPrice} (zmiana: {ChangePercent:P2})",
                offerId, oldPrice, newPrice, changePercent);

        public static void LogTitleChangeDetected(this ILogger logger, string offerId, string oldTitle, string newTitle, double similarity)
            => logger.LogInformation("[ChangeDetector] 📝 Wykryto zmianę tytułu w ofercie {OfferId}: podobieństwo {Similarity:P2}",
                offerId, similarity);

        public static void LogContentChangeDetectionComplete(this ILogger logger, int checkedOffers, int detectedChanges, double processingTimeMs)
            => logger.LogInformation("[ChangeDetector] ✅ Sprawdzono {CheckedOffers} ofert w {ProcessingTimeMs:F1}ms, wykryto {DetectedChanges} zmian zawartości",
                checkedOffers, processingTimeMs, detectedChanges);

        public static void LogContentChangeDetectionError(this ILogger logger, string gameName, string error)
            => logger.LogError("[ChangeDetector] ❌ Błąd podczas wykrywania zmian zawartości dla gry {GameName}: {Error}", gameName, error);

        public static void LogOfferContentChangeProcessed(this ILogger logger, string offerId, string changeType)
            => logger.LogInformation("[ChangeDetector] ♻️ Przetworzono zmianę zawartości oferty {OfferId}: {ChangeType}", offerId, changeType);

        public static void LogLevenshteinCalculation(this ILogger logger, string offerId, string oldTitle, string newTitle, double similarity, int distance)
            => logger.LogDebug("[ChangeDetector] 🧮 Algorytm Levenshteina dla oferty {OfferId}: dystans={Distance}, podobieństwo={Similarity:P2}",
                offerId, distance, similarity);
    }
}
