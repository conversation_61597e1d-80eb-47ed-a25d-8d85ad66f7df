using System;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using ShopBot;

namespace ShopBot.Services.Formatters
{
    public abstract class BaseDescriptionFormatter : IDescriptionFormatter
    {
        protected readonly ILogger _logger;
        
        private static readonly HashSet<string> _bannedPhrases = new(StringComparer.OrdinalIgnoreCase)
        {
            "warranty",
            "guarantee",
            "lifetime",
            "eternal",
            "100%",
            "you can pay at any time without a response from the seller",                     
            "free", 
            "rent",
            "rental",
            "fun pay",
            "autodelivery",
            "funpay",
            "fanpay",
            "fanpey",
            "nigga",
            "niger",
            "nyga",
            "auto release",
            "auto-release",
            "auto-issuance",
            "auto-issue",
            "auto delivery",
            "auto-delivery",
            "auto-display",
            "auto auto issue",
            "autopay",
            "automatic issue",
            "auto issue",
            "auto output",
            "[[auto-release",
            "auto - release",
            "instant delivery",
            "instant-delivery",
            "instant access",
            "instant-access",
            "auto-delivery",
            "24/7",
            "24/7 support",
            "full access",
            "full-access",
            "safe deal",
            "safe trade",
            "money back",
            "money-back",
            "refund",
            "cheapest",
            "lowest price",
            "best price",
            "best offer",
            "no ban",
            "no-ban",
            "no risk",
            "no-risk",
            "fast delivery",
            "fast-delivery",
            "guaranteed account",
            "guaranteed delivery",
            "safe account",
            "secure account",
            "secure delivery",
            "verified account",
            "trusted account",
            "100% safe",
            "100% secure",
            "100% legit",
            "100% working",
            "100% genuine",
            "satisfaction guaranteed",
            "money-back guarantee",
            "risk-free",
            "risk free",
            "no risk guarantee",
            "secure transaction",
            "safe transaction",
            "trusted transaction",
            "verified transaction",
            "guaranteed transaction",
            "guaranteed transfer",
            "safe transfer",
            "autoissue",
            "secure transfer",
            "auto-delivery",
            "auto issue",
            "auto-issue",
            "auto output",
            "auto-output",
            "auto release",
            "auto-release",
            "auto issuance",
            "auto-issuance",
            "autopromotion",
            "auto promotion",
            "auto-promotion",
            "automatic delivery",
            "automatic issue",
            "automatic release",
            "automatic output",
            "automatic issuance",
            "automatic promotion",
            "(automatic)",
            "full access",
            "full-access",
            "change mail",
            "change email",
            "auto-delivery",
            "instant delivery",
            "instant-delivery",
            "24/7 delivery",
            "24/7 support",
            "lifetime warranty",
            "no blocking",            
            "life warranty",
            "ban free",
            "no ban risk",
            "ban risk free",
            "account with no shadow banned",
            "no shadow ban",
            "shadow ban free",
            "not shadow banned",
            "without shadow ban",
            "best quality",
            "highest quality",
            "top quality",
            "successful transactions",
            "honest reviews",
            "positive reviews",
            "verified reviews",
            "trusted reviews",
            "successful deals",
            "completed deals",
            "completed transactions",
            "satisfied customers",
            "happy customers",
            "years of experience",
            "years experience",
            "professional seller",
            "experienced seller",
            "top seller",
            "best seller",
            "premium seller",
            "elite seller",
            "vip seller",
            "transactions",
            "the best",
            "best quality",
            "best service",
            "best accounts",
            "best prices",
            "on the site",
            "on the market",
            "in the market",
            "on funpay",
            "on the platform",
            "no modded",
            "not modded",
            "no mods",
            "no hack",
            "not hacked",
            "no hacks",
            "no duped",
            "not duped",
            "no dupes",
            "no duplicates",
            "no cheats",
            "not cheated",
            "no exploits",
            "no glitch",
            "no glitches",
            "no bugs",
            "auto-send",         
            "no exploited",
            "no bot",
            "no bots",
            "no macro",
            "fully automated",
            "no macros",
            "no scripts",
            "no automation",
            "no third party",
            "no 3rd party",
            "no banned",
            "never banned",
            "ban free",
            "clean account",
            "legit account",
            "legitimate account",
            "no violations",
            "no reports",
            "no warnings",
            "no restrictions",
            "no limitations",
            "no suspensions",
            "account safety",
            "safe account",
            "secure account",
            "trusted account",
            "verified account",
            "delivery info",
            "account information will be sent",
            "will be sent to your order",
            "sent instantly",
            "allow up to 8 hours",
            "allow up to 24 hours",
            "allow up to 12 hours",
            "allow up to",
            "delivery time",
            "delivery period",
            "delivery duration",
            "instant account delivery",
            "instant account transfer",
            "instant information",
            "instant details",
            "account details will be sent",
            "information will be provided",
            "details will be provided",
            "delivery within",
            "within hours",
            "within minutes",
            "processing time",
            "processing period",
            "order processing"
        };
        
        public BaseDescriptionFormatter(ILogger logger)
        {
            _logger = logger;
        }
        
        // Wspólne metody pomocnicze do wykorzystania przez wszystkie formatterzy
        protected string RemoveEmoji(string text)
        {
            if (string.IsNullOrEmpty(text)) return text;

            // Mapa zamiany emoji na tekst
            var emojiMap = new Dictionary<string, string>
            {
                {"⭐", "|"},
                {"🔴", "|"},
                {"🟠", "|"},
                {"🟣", "|"},
                {"🔵", "|"},
                {"🟢", "|"},
                {"⬜", "|"},
                {"➖", "|"},
                {"💸", "|"},
                {"⚡️", "|"},
                {"💎", "|"},
                {"🥂", "|"},
                {"🐓", "|"},
                {"📖", "|"},
                {"🔥", "|"},
                {"📌", "|"},
                {"🅰️", "|"},
                {"⚡", "|"}
            };

            // Zamień emoji na |
            foreach (var emoji in emojiMap)
            {
                text = text.Replace(emoji.Key, emoji.Value);
            }

            // Zamień wszystkie pozostałe znaki unicode na |
            text = Regex.Replace(text, @"[^\u0000-\u007F]+", "|");

            // Usuń wielokrotne | obok siebie
            text = Regex.Replace(text, @"\|+", "|");

            // Usuń | na początku i końcu
            text = text.Trim('|');

            // Usuń wielokrotne spacje
            text = Regex.Replace(text, @"\s+", " ");
            
            // Usuń spacje na początku i końcu
            return text.Trim();
        }

        protected string RemoveBannedPhrases(string text)
        {
            if (string.IsNullOrEmpty(text)) return text;

            var result = text.ToLower();
            foreach (var phrase in _bannedPhrases)
            {
                result = Regex.Replace(result, 
                    Regex.Escape(phrase.ToLower()), 
                    "", 
                    RegexOptions.IgnoreCase);
            }

            // Usuń wielokrotne spacje powstałe po usunięciu fraz
            result = Regex.Replace(result, @"\s+", " ").Trim();
            
            return result;
        }

        protected string NormalizeDelimiters(string text)
        {
            if (string.IsNullOrEmpty(text)) return text;

            // Zamień przecinki na |
            text = text.Replace(",", " | ");
            
            // Normalizuj spacje wokół |
            text = Regex.Replace(text, @"\s*\|\s*", " | ");
            
            // Usuń podwójne spacje
            text = Regex.Replace(text, @"\s+", " ");
            
            // Usuń spacje na początku i końcu
            text = text.Trim();
            
            // Usuń puste separatory (| | lub | na końcu/początku)
            text = Regex.Replace(text, @"\|\s*\|", "|");
            text = Regex.Replace(text, @"^\s*\|\s*|\s*\|\s*$", "");
            
            return text;
        }
        
        // Implementacja interfejsu z domyślnym zachowaniem
        public virtual string FormatDescription(OfferDetails offerDetails, Dictionary<string, string> parameters = null)
        {
            if (offerDetails == null)
            {
                _logger.LogWarning("OfferDetails is null in FormatDescription");
                return string.Empty;
            }

            var cleanDesc = CleanDescription(offerDetails.DetailedDescription);
            
            // Standardowy tekst i separator
            var standardText = "Got questions? Contact me on G2G chat before buying! Offers like this go fast – reserve your ideal account now!";
            var separator = "===============================================================";
            
            // Zawsze dodaj standardowy tekst, nawet jeśli opis jest pusty
            var formattedDesc = string.IsNullOrEmpty(cleanDesc) 
                ? standardText 
                : cleanDesc + $"\n\n{separator}\n{standardText}";
            
            // Dodaj ID jeśli dostępne
            if (parameters != null && parameters.TryGetValue("Id", out var id))
            {
                _logger.LogInformation("Adding ID to description: {Id}", id);
                formattedDesc += $"\n\n({id})";
            }
            else
            {
                _logger.LogWarning("No ID found in parameters!");
            }
            
            return formattedDesc;
        }
        
        public virtual string FormatAdditionalInfo(OfferDetails offerDetails)
        {
            // Domyślna implementacja dla dodatkowych informacji
            return string.Empty;
        }
        
        public virtual string FormatTitle(OfferDetails offerDetails)
        {
            if (offerDetails == null)
            {
                _logger.LogWarning("OfferDetails is null in FormatTitle");
                return string.Empty;
            }
            
            return RemoveEmoji(offerDetails.ShortDescription);
        }
        
        // Podstawowe formatowanie opisu, które może być wspólne dla wielu gier
        protected string FormatDetailedDescription(string description)
        {
            if (string.IsNullOrEmpty(description))
                return string.Empty;

            var formattedDesc = RemoveEmoji(description);
            formattedDesc = RemoveBannedPhrases(formattedDesc);
            formattedDesc = NormalizeDelimiters(formattedDesc);
            
            // Standardowy tekst
            var standardText = "Got questions? Contact me on G2G chat before buying! Offers like this go fast – reserve your ideal account now!";
            var separator = "===============================================================";
            
            // Usuń istniejący standardowy tekst i separator, ale zachowaj resztę opisu
            var parts = formattedDesc.Split(new[] { separator }, StringSplitOptions.RemoveEmptyEntries);
            formattedDesc = parts[0].Trim();
            
            // Usuń standardowy tekst jeśli istnieje
            formattedDesc = formattedDesc.Replace(standardText, "").Trim();
            
            // Usuń wielokrotne nowe linie i spacje
            formattedDesc = Regex.Replace(formattedDesc, @"\n\s*\n\s*\n+", "\n\n");
            formattedDesc = formattedDesc.Trim();
            
            // Dodaj standardowy tekst na końcu
            formattedDesc += $"\n\n{separator}\n";
            formattedDesc += standardText;
            
            return formattedDesc;
        }
        
        protected string FormatShortDescription(string description)
        {
            if (string.IsNullOrEmpty(description))
                return string.Empty;

            var formattedDesc = RemoveEmoji(description);
            formattedDesc = RemoveBannedPhrases(formattedDesc);
            return formattedDesc;
        }

        public string CleanDescription(string description)
        {
            if (string.IsNullOrEmpty(description))
                return string.Empty;

            var formattedDesc = RemoveEmoji(description);
            formattedDesc = RemoveBannedPhrases(formattedDesc);
            formattedDesc = NormalizeDelimiters(formattedDesc);
            
            // Usuń standardowy tekst i separator jeśli istnieją
            var standardText = "Got questions? Contact me on G2G chat before buying! Offers like this go fast – reserve your ideal account now!";
            var separator = "===============================================================";
            
            // Usuń istniejący standardowy tekst i separator, ale zachowaj resztę opisu
            var parts = formattedDesc.Split(new[] { separator }, StringSplitOptions.RemoveEmptyEntries);
            formattedDesc = parts[0].Trim();
            
            // Usuń standardowy tekst jeśli istnieje
            formattedDesc = formattedDesc.Replace(standardText, "").Trim();
            
            // Usuń wielokrotne nowe linie i spacje
            formattedDesc = Regex.Replace(formattedDesc, @"\n\s*\n\s*\n+", "\n\n");
            
            return formattedDesc.Trim();
        }

        protected string ExtractKeyInformation(string description)
        {
            var keyInfo = new List<string>();

            // Szukaj informacji o poziomie
            var levelMatch = Regex.Match(description, @"level[:\s]+(\d+)", RegexOptions.IgnoreCase);
            if (levelMatch.Success)
            {
                keyInfo.Add($"Level: {levelMatch.Groups[1].Value}");
            }

            // Szukaj informacji o randze
            var rankMatch = Regex.Match(description, @"rank[:\s]+(\w+)", RegexOptions.IgnoreCase);
            if (rankMatch.Success)
            {
                keyInfo.Add($"Rank: {rankMatch.Groups[1].Value}");
            }

            // Szukaj informacji o skinach
            var skinsMatch = Regex.Match(description, @"skins?[:\s]+(\d+)", RegexOptions.IgnoreCase);
            if (skinsMatch.Success)
            {
                keyInfo.Add($"Skins: {skinsMatch.Groups[1].Value}");
            }

            // Szukaj informacji o championach
            var championsMatch = Regex.Match(description, @"champions?[:\s]+(\d+)", RegexOptions.IgnoreCase);
            if (championsMatch.Success)
            {
                keyInfo.Add($"Champions: {championsMatch.Groups[1].Value}");
            }

            var result = string.Join(" | ", keyInfo);
            return NormalizeDelimiters(result);
        }
    }
} 