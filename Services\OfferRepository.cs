using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ShopBot.Data;
using ShopBot.Models;
using ShopBot.Services.Interfaces;

namespace ShopBot.Services
{
    public class OfferRepository : IOfferRepository
    {
        private readonly ShopBotDbContext _context;
        private readonly ILogger<OfferRepository> _logger;

        public OfferRepository(ShopBotDbContext context, ILogger<OfferRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

        // Basic CRUD operations
        public async Task<Offer?> GetByIdAsync(Guid id)
        {
            return await _context.Offers
                .Include(o => o.ImageUploads)
                .Include(o => o.ErrorLogs)
                .FirstOrDefaultAsync(o => o.Id == id);
        }

        public async Task<Offer?> GetByFunPayIdAsync(string funPayOfferId)
        {
            return await _context.Offers
                .Include(o => o.ImageUploads)
                .FirstOrDefaultAsync(o => o.FunPayOfferId == funPayOfferId);
        }

        public async Task<List<Offer>> GetAllAsync()
        {
            return await _context.Offers
                .Include(o => o.ImageUploads)
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public async Task<Offer> CreateAsync(Offer offer)
        {
            _logger.LogDebug("[OfferRepo] Creating offer: {FunPayId} for game: {GameName}", offer.FunPayOfferId, offer.GameName);
            
            _context.Offers.Add(offer);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("[OfferRepo] ✅ Created offer: {Id}", offer.Id);
            return offer;
        }

        public async Task<Offer> UpdateAsync(Offer offer)
        {
            _logger.LogDebug("[OfferRepo] Updating offer: {Id}", offer.Id);
            
            offer.UpdatedAt = DateTime.UtcNow;
            _context.Offers.Update(offer);
            await _context.SaveChangesAsync();
            
            _logger.LogDebug("[OfferRepo] ✅ Updated offer: {Id}", offer.Id);
            return offer;
        }

        public async Task DeleteAsync(Guid id)
        {
            var offer = await _context.Offers.FindAsync(id);
            if (offer != null)
            {
                _logger.LogDebug("[OfferRepo] Deleting offer: {Id}", id);
                _context.Offers.Remove(offer);
                await _context.SaveChangesAsync();
                _logger.LogDebug("[OfferRepo] ✅ Deleted offer: {Id}", id);
            }
        }

        // Query operations
        public async Task<List<Offer>> GetByGameAsync(string gameName, int limit = 100)
        {
            return await _context.Offers
                .Where(o => o.GameName == gameName)
                .OrderByDescending(o => o.CreatedAt)
                .Take(limit)
                .ToListAsync();
        }

        public async Task<List<Offer>> GetByStatusAsync(string status, int limit = 100)
        {
            return await _context.Offers
                .Where(o => o.Status == status)
                .OrderByDescending(o => o.CreatedAt)
                .Take(limit)
                .ToListAsync();
        }

        public async Task<List<Offer>> GetRecentAsync(int limit = 50)
        {
            return await _context.Offers
                .OrderByDescending(o => o.CreatedAt)
                .Take(limit)
                .ToListAsync();
        }

        public async Task<List<Offer>> GetByDateRangeAsync(DateTime from, DateTime to)
        {
            return await _context.Offers
                .Where(o => o.CreatedAt >= from && o.CreatedAt <= to)
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<Offer>> GetFailedOffersAsync(int limit = 100)
        {
            return await _context.Offers
                .Where(o => o.Status == "Failed")
                .OrderByDescending(o => o.CreatedAt)
                .Take(limit)
                .ToListAsync();
        }

        // Business logic queries
        public async Task<bool> IsOfferProcessedAsync(string funPayOfferId)
        {
            return await _context.Offers
                .AnyAsync(o => o.FunPayOfferId == funPayOfferId);
        }

        public async Task<List<Offer>> GetOffersForRetryAsync(int limit = 10)
        {
            return await _context.Offers
                .Where(o => o.Status == "Failed" && 
                           o.ProcessedAt < DateTime.UtcNow.AddHours(-1)) // Failed more than 1 hour ago
                .OrderBy(o => o.ProcessedAt)
                .Take(limit)
                .ToListAsync();
        }

        public async Task<List<Offer>> GetUnpublishedOffersAsync(string gameName, int limit = 50)
        {
            return await _context.Offers
                .Where(o => o.GameName == gameName && 
                           o.Status == "Processed" && 
                           o.PublishedAt == null)
                .OrderByDescending(o => o.ProcessedAt)
                .Take(limit)
                .ToListAsync();
        }

        // Statistics
        public async Task<int> GetTotalOffersCountAsync()
        {
            return await _context.Offers.CountAsync();
        }

        public async Task<int> GetOffersCountByGameAsync(string gameName)
        {
            return await _context.Offers
                .CountAsync(o => o.GameName == gameName);
        }

        public async Task<int> GetOffersCountByStatusAsync(string status)
        {
            return await _context.Offers
                .CountAsync(o => o.Status == status);
        }

        public async Task<Dictionary<string, int>> GetOfferCountsByGameAsync()
        {
            return await _context.Offers
                .GroupBy(o => o.GameName)
                .Select(g => new { Game = g.Key, Count = g.Count() })
                .ToDictionaryAsync(x => x.Game, x => x.Count);
        }

        public async Task<Dictionary<string, int>> GetOfferCountsByStatusAsync()
        {
            return await _context.Offers
                .GroupBy(o => o.Status)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToDictionaryAsync(x => x.Status, x => x.Count);
        }

        // Performance queries
        public async Task<List<Offer>> GetTopPricedOffersAsync(string gameName, int limit = 10)
        {
            return await _context.Offers
                .Where(o => o.GameName == gameName)
                .OrderByDescending(o => o.ConvertedPrice)
                .Take(limit)
                .ToListAsync();
        }

        public async Task<decimal> GetAveragePriceByGameAsync(string gameName)
        {
            var offers = await _context.Offers
                .Where(o => o.GameName == gameName && o.ConvertedPrice > 0)
                .ToListAsync();

            return offers.Any() ? offers.Average(o => o.ConvertedPrice) : 0;
        }

        public async Task<(decimal min, decimal max, decimal avg)> GetPriceStatsAsync(string gameName)
        {
            var offers = await _context.Offers
                .Where(o => o.GameName == gameName && o.ConvertedPrice > 0)
                .ToListAsync();

            if (!offers.Any())
                return (0, 0, 0);

            return (
                offers.Min(o => o.ConvertedPrice),
                offers.Max(o => o.ConvertedPrice),
                offers.Average(o => o.ConvertedPrice)
            );
        }

        // Cleanup operations
        public async Task<int> DeleteOldOffersAsync(DateTime olderThan)
        {
            var oldOffers = await _context.Offers
                .Where(o => o.CreatedAt < olderThan)
                .ToListAsync();

            _context.Offers.RemoveRange(oldOffers);
            await _context.SaveChangesAsync();

            _logger.LogInformation("[OfferRepo] 🗑️ Deleted {Count} old offers", oldOffers.Count);
            return oldOffers.Count;
        }

        public async Task<int> DeleteFailedOffersAsync(DateTime olderThan)
        {
            var failedOffers = await _context.Offers
                .Where(o => o.Status == "Failed" && o.CreatedAt < olderThan)
                .ToListAsync();

            _context.Offers.RemoveRange(failedOffers);
            await _context.SaveChangesAsync();

            _logger.LogInformation("[OfferRepo] 🗑️ Deleted {Count} old failed offers", failedOffers.Count);
            return failedOffers.Count;
        }
    }
}
