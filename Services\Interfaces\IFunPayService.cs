using Microsoft.Playwright;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ShopBot.Services
{
    /// <summary>
    /// Interfejs dla serwisu FunPay - obsługa komunikacji z platformą FunPay
    /// </summary>
    public interface IFunPayService
    {
        /// <summary>
        /// Pobiera przefiltrowane oferty dla określonej gry
        /// </summary>
        Task<List<OfferListItem>> GetFilteredOffers(OfferFilter filter, string gameName);

        /// <summary>
        /// Pobiera szczegóły konkretnej oferty
        /// </summary>
        Task<OfferDetails?> GetOfferDetails(string offerUrl);

        /// <summary>
        /// Sprawdza czy oferta jest nadal aktywna na FunPay
        /// </summary>
        Task<bool> IsOfferActive(string offerUrl);

        /// <summary>
        /// Pobiera listę ofert z określonej strony
        /// </summary>
        Task<List<OfferListItem>> GetOffersFromPage(string gameUrl, int pageNumber);

        /// <summary>
        /// Sprawdza dostępność serwisu FunPay
        /// </summary>
        Task<bool> IsServiceAvailable();
    }
}
