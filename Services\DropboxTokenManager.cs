using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using ShopBot.Config;

namespace ShopBot.Services
{
    public class DropboxTokenManager
    {
        private readonly DropboxAuthService _authService;
        private readonly ILogger<DropboxTokenManager> _logger;
        private readonly string _tokenFilePath = "dropbox_refresh_token.json";
        
        private string? _accessToken;
        private string? _refreshToken;
        private DateTime _tokenExpiryTime;
        
        private const int TOKEN_REFRESH_HOURS = 3; // Odświeżaj co 3 godziny (token wygasa po 4h)

        public string? AccessToken => _accessToken;

        public DropboxTokenManager(DropboxAuthService authService, ILogger<DropboxTokenManager> logger)
        {
            _authService = authService;
            _logger = logger;
        }

        public async Task InitializeAsync()
        {
            _logger.LogInformation("[Dropbox Token] Inicjalizacja token managera...");

            // Sprawdź czy mamy zapisany refresh token
            if (await LoadRefreshTokenAsync())
            {
                _logger.LogInformation("[Dropbox Token] Znaleziono zapisany refresh token");
                
                // Sprawdź czy access token jest jeszcze ważny
                if (DateTime.UtcNow >= _tokenExpiryTime)
                {
                    _logger.LogInformation("[Dropbox Token] Access token wygasł, odświeżam...");
                    await RefreshAccessTokenAsync();
                }
                else
                {
                    _logger.LogInformation("[Dropbox Token] Access token jest jeszcze ważny");
                }
            }
            else
            {
                _logger.LogWarning("[Dropbox Token] Brak zapisanego refresh token - wymagana autoryzacja");
                await RequestInitialAuthorizationAsync();
            }
        }

        public async Task<string> GetValidAccessTokenAsync()
        {
            // Sprawdź czy token wygaśnie w ciągu najbliższych 10 minut
            if (DateTime.UtcNow.AddMinutes(10) >= _tokenExpiryTime)
            {
                _logger.LogInformation("[Dropbox Token] Token wygaśnie wkrótce, odświeżam...");
                await RefreshAccessTokenAsync();
            }

            return _accessToken ?? throw new InvalidOperationException("Brak ważnego access token");
        }

        private async Task RequestInitialAuthorizationAsync()
        {
            Console.WriteLine("\n🔐 === DROPBOX AUTHORIZATION REQUIRED ===");
            Console.WriteLine("Wymagana autoryzacja Dropbox.");
            Console.WriteLine($"1. Otwórz ten URL w przeglądarce: {_authService.GetAuthorizationUrl()}");
            Console.WriteLine("2. Zaloguj się do Dropbox i autoryzuj aplikację");
            Console.WriteLine("3. Skopiuj authorization code z URL (po ?code=...)");
            Console.WriteLine("4. Wprowadź kod poniżej:");
            Console.Write("Authorization code: ");
            
            string? authCode = Console.ReadLine();
            
            if (string.IsNullOrWhiteSpace(authCode))
            {
                throw new InvalidOperationException("Nie podano authorization code");
            }

            var authResponse = await _authService.GetAccessTokenWithCodeAsync(authCode);
            _accessToken = authResponse.AccessToken;
            _refreshToken = authResponse.RefreshToken;
            _tokenExpiryTime = DateTime.UtcNow.AddSeconds(authResponse.ExpiresIn - 300); // 5 minut buforu

            await SaveRefreshTokenAsync();
            Console.WriteLine($"✅ [Dropbox] Token zostanie odświeżony za {TOKEN_REFRESH_HOURS} godzin");
        }

        private async Task RefreshAccessTokenAsync()
        {
            try
            {
                // Jeśli refresh token jest pusty, spróbuj załadować z pliku
                if (string.IsNullOrEmpty(_refreshToken))
                {
                    _logger.LogWarning("[Dropbox Token] Refresh token pusty, próbuję załadować z pliku...");
                    if (!await LoadRefreshTokenAsync())
                    {
                        _logger.LogError("[Dropbox Token] Brak refresh token - wymagana ponowna autoryzacja");
                        await RequestInitialAuthorizationAsync();
                        return;
                    }
                }

                if (string.IsNullOrEmpty(_refreshToken))
                {
                    _logger.LogError("[Dropbox Token] Nadal brak refresh token po załadowaniu z pliku");
                    await RequestInitialAuthorizationAsync();
                    return;
                }

                var authResponse = await _authService.RefreshAccessTokenAsync(_refreshToken);
                _accessToken = authResponse.AccessToken;
                // Refresh token pozostaje ten sam w Dropbox
                _tokenExpiryTime = DateTime.UtcNow.AddSeconds(authResponse.ExpiresIn - 300); // 5 minut buforu

                await SaveRefreshTokenAsync();
                _logger.LogInformation("[Dropbox Token] ✅ Token został odświeżony. Następne odświeżenie za {Hours} godzin", TOKEN_REFRESH_HOURS);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[Dropbox Token] ❌ Błąd odświeżania tokena");
                await RequestInitialAuthorizationAsync();
            }
        }

        private async Task<bool> LoadRefreshTokenAsync()
        {
            try
            {
                if (!File.Exists(_tokenFilePath))
                    return false;

                var json = await File.ReadAllTextAsync(_tokenFilePath);
                var tokenData = JsonSerializer.Deserialize<DropboxTokenData>(json);

                if (tokenData == null || string.IsNullOrEmpty(tokenData.RefreshToken))
                    return false;

                _refreshToken = tokenData.RefreshToken;
                _accessToken = tokenData.AccessToken;
                _tokenExpiryTime = tokenData.ExpiryTime;

                _logger.LogDebug("[Dropbox Token] Załadowano token z pliku - wygasa: {ExpiryTime}", _tokenExpiryTime);
                _logger.LogDebug("[Dropbox Token] Refresh token length: {Length}", _refreshToken?.Length ?? 0);
                _logger.LogDebug("[Dropbox Token] Access token length: {Length}", _accessToken?.Length ?? 0);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[Dropbox Token] Błąd ładowania refresh token z pliku");
                return false;
            }
        }

        private async Task SaveRefreshTokenAsync()
        {
            try
            {
                var tokenData = new DropboxTokenData
                {
                    RefreshToken = _refreshToken,
                    AccessToken = _accessToken,
                    ExpiryTime = _tokenExpiryTime
                };

                var json = JsonSerializer.Serialize(tokenData, new JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(_tokenFilePath, json);

                _logger.LogDebug("[Dropbox Token] Zapisano token do pliku");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[Dropbox Token] Błąd zapisywania refresh token do pliku");
            }
        }

        private class DropboxTokenData
        {
            public string? RefreshToken { get; set; }
            public string? AccessToken { get; set; }
            public DateTime ExpiryTime { get; set; }
        }
    }
}
