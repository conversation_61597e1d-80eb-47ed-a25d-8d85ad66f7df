{"Application": {"BrowserId": "7bd614cc-90c2-4607-902a-66cedded3494", "SyncIntervalMinutes": 15, "CleanupIntervalHours": 24, "MaxWorkerThreads": 30, "MaxSyncWorkerThreads": 3}, "Timeouts": {"DeleteOfferTimeoutSeconds": 90, "DeleteOfferCheckIntervalSeconds": 3, "PageLoadTimeoutSeconds": 30, "ElementWaitTimeoutSeconds": 15, "PublishOfferTimeoutSeconds": 30, "GameSelectionTimeoutSeconds": 30, "FormFillDelayMs": 200, "GameProcessDelayMs": 8000, "DropdownSelectionDelayMs": 500, "PriceSetDelayMs": 300, "KeyboardDelayMs": 100, "FunPayDetailsIntervalMs": 500}, "Retry": {"MaxRetryAttempts": 3, "RetryDelaySeconds": 5, "ExponentialBackoff": true}, "Logging": {"LogLevel": {"Default": "Information", "ShopBot": "Debug", "Microsoft": "Warning"}, "Console": {"IncludeScopes": false, "TimestampFormat": "HH:mm:ss"}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"ShopBot": "Debug", "Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/shopbot-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext"]}, "Features": {"EnableDetailedLogging": true, "EnablePerformanceMetrics": false, "EnableConfigValidation": true, "EnableServiceValidation": true, "EnableAutoRetry": true, "EnableStartupTests": false}, "Pricing": {"DefaultMarginPercent": 30.0, "MinimumPrice": 1.0, "MaximumPrice": 2500.0, "ProgressiveMargins": {"Under30": 40.0, "From30To50": 35.0, "From50To300": 30.0, "Above300": 25.0}}, "Dropbox": {"ClientId": "fpcsydbu278cbii", "ClientSecret": "gj0fgcgd0rx6rsg", "AccessToken": "", "RefreshToken": "", "Enabled": true}, "Imgur": {"ClientId": "9e830302bbd7760", "ClientSecret": "e27238db50fae6a73f86b1545fa5c22e740593d8", "AccessToken": "", "Enabled": false}, "Database": {"ConnectionString": "Server=localhost;Database=shopbotdb;Uid=root;Pwd=**********;", "EnableMigrations": true, "CommandTimeoutSeconds": 30}}