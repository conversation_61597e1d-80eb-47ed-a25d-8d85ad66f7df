using HtmlAgilityPack;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ShopBot.Services.Parsers
{
    /// <summary>
    /// Parser odpowiedzialny za wyciąganie obrazów z HTML
    /// </summary>
    public class ImageParser : IImageParser
    {
        private readonly ILogger<ImageParser> _logger;
        private readonly IUrlValidator _urlValidator;

        private static readonly string[] ValidImageExtensions = { ".jpg", ".jpeg", ".png", ".gif", ".webp" };

        public ImageParser(ILogger<ImageParser> logger, IUrlValidator urlValidator)
        {
            _logger = logger;
            _urlValidator = urlValidator;
        }

        public List<string> ParseImages(HtmlDocument document)
        {
            var images = new List<string>();

            try
            {
                _logger.LogDebug("🖼️ Rozpoczynam parsowanie obrazów");

                // Pobieranie obrazków z sekcji Images
                var imageNodes = document.DocumentNode.SelectNodes(
                    "//div[contains(@class, 'param-item')]/h5[text()='Images']/following-sibling::div//li[contains(@class, 'attachments-item')]/a");

                if (imageNodes != null)
                {
                    foreach (var imageNode in imageNodes)
                    {
                        var href = imageNode.GetAttributeValue("href", "");
                        if (!string.IsNullOrEmpty(href))
                        {
                            var validatedUrl = ValidateImageUrl(href);
                            if (!string.IsNullOrEmpty(validatedUrl))
                            {
                                images.Add(validatedUrl);
                            }
                        }
                    }
                }

                // Fallback - szukaj obrazków w innych miejscach
                if (images.Count == 0)
                {
                    images.AddRange(ParseImagesFromGallery(document));
                }

                if (images.Count == 0)
                {
                    images.AddRange(ParseImagesFromContent(document));
                }

                _logger.LogDebug("📸 Znaleziono {Count} obrazów", images.Count);
                return images.Distinct().ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Błąd podczas parsowania obrazów");
                return images;
            }
        }

        public string ValidateImageUrl(string imageUrl)
        {
            if (string.IsNullOrEmpty(imageUrl))
                return "";

            try
            {
                // Walidacja podstawowa URL
                var validatedUrl = _urlValidator.ValidateAndFixUrl(imageUrl);
                
                // Sprawdź czy to prawidłowy format obrazu
                if (!IsValidImageFormat(validatedUrl))
                {
                    _logger.LogDebug("⚠️ Nieprawidłowy format obrazu: {Url}", imageUrl);
                    return "";
                }

                // Sprawdź czy URL jest dostępny (podstawowa walidacja)
                if (!IsValidImageUrl(validatedUrl))
                {
                    _logger.LogDebug("⚠️ Nieprawidłowy URL obrazu: {Url}", imageUrl);
                    return "";
                }

                return validatedUrl;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "⚠️ Błąd walidacji URL obrazu: {Url}", imageUrl);
                return "";
            }
        }

        public bool IsValidImageFormat(string imageUrl)
        {
            if (string.IsNullOrEmpty(imageUrl))
                return false;

            try
            {
                var uri = new Uri(imageUrl);
                var path = uri.LocalPath.ToLowerInvariant();
                
                return ValidImageExtensions.Any(ext => path.EndsWith(ext));
            }
            catch
            {
                return false;
            }
        }

        private List<string> ParseImagesFromGallery(HtmlDocument document)
        {
            var images = new List<string>();

            try
            {
                // Szukaj w galerii obrazów
                var gallerySelectors = new[]
                {
                    "//div[contains(@class, 'gallery')]//img",
                    "//div[contains(@class, 'images')]//img",
                    "//div[contains(@class, 'screenshots')]//img"
                };

                foreach (var selector in gallerySelectors)
                {
                    var imgNodes = document.DocumentNode.SelectNodes(selector);
                    if (imgNodes != null)
                    {
                        foreach (var imgNode in imgNodes)
                        {
                            var src = imgNode.GetAttributeValue("src", "") ?? 
                                     imgNode.GetAttributeValue("data-src", "");
                            
                            if (!string.IsNullOrEmpty(src))
                            {
                                var validatedUrl = ValidateImageUrl(src);
                                if (!string.IsNullOrEmpty(validatedUrl))
                                {
                                    images.Add(validatedUrl);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "⚠️ Błąd podczas parsowania galerii obrazów");
            }

            return images;
        }

        private List<string> ParseImagesFromContent(HtmlDocument document)
        {
            var images = new List<string>();

            try
            {
                // Szukaj wszystkich obrazków na stronie
                var imgNodes = document.DocumentNode.SelectNodes("//img");
                if (imgNodes != null)
                {
                    foreach (var imgNode in imgNodes)
                    {
                        var src = imgNode.GetAttributeValue("src", "") ?? 
                                 imgNode.GetAttributeValue("data-src", "");
                        
                        if (!string.IsNullOrEmpty(src))
                        {
                            // Filtruj tylko obrazy związane z ofertą (nie avatary, ikony itp.)
                            if (IsOfferRelatedImage(src, imgNode))
                            {
                                var validatedUrl = ValidateImageUrl(src);
                                if (!string.IsNullOrEmpty(validatedUrl))
                                {
                                    images.Add(validatedUrl);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "⚠️ Błąd podczas parsowania obrazów z treści");
            }

            return images;
        }

        private bool IsValidImageUrl(string url)
        {
            try
            {
                var uri = new Uri(url);
                
                // Sprawdź czy to HTTP/HTTPS
                if (uri.Scheme != "http" && uri.Scheme != "https")
                    return false;

                // Sprawdź czy host nie jest pusty
                if (string.IsNullOrEmpty(uri.Host))
                    return false;

                return true;
            }
            catch
            {
                return false;
            }
        }

        private bool IsOfferRelatedImage(string src, HtmlNode imgNode)
        {
            // Filtruj obrazy które prawdopodobnie nie są związane z ofertą
            var excludePatterns = new[]
            {
                "avatar", "icon", "logo", "banner", "ad", "advertisement",
                "button", "ui", "interface", "menu", "header", "footer"
            };

            var srcLower = src.ToLowerInvariant();
            var classAttr = imgNode.GetAttributeValue("class", "").ToLowerInvariant();
            var altAttr = imgNode.GetAttributeValue("alt", "").ToLowerInvariant();

            // Sprawdź czy URL lub atrybuty zawierają wykluczające wzorce
            foreach (var pattern in excludePatterns)
            {
                if (srcLower.Contains(pattern) || classAttr.Contains(pattern) || altAttr.Contains(pattern))
                {
                    return false;
                }
            }

            // Sprawdź rozmiar obrazu (jeśli dostępny)
            var width = imgNode.GetAttributeValue("width", "");
            var height = imgNode.GetAttributeValue("height", "");
            
            if (int.TryParse(width, out int w) && int.TryParse(height, out int h))
            {
                // Odrzuć bardzo małe obrazki (prawdopodobnie ikony)
                if (w < 50 || h < 50)
                    return false;
            }

            return true;
        }
    }
}
