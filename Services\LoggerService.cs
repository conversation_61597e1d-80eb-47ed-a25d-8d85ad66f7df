using System;
using System.IO;
using ShopBot.Config;

namespace ShopBot.Services
{
    public class LoggerService
    {
        private static readonly object _lock = new object();
        private readonly string _logFilePath;
        private readonly ShopBot.Tasks.TaskScheduler _taskScheduler;

        public LoggerService(ShopBot.Tasks.TaskScheduler taskScheduler)
        {
            _taskScheduler = taskScheduler;
            var logDir = "logs";
            if (!Directory.Exists(logDir))
            {
                Directory.CreateDirectory(logDir);
            }
            _logFilePath = Path.Combine(logDir, $"shopbot_{DateTime.Now:yyyy-MM-dd}.log");
        }

        // Konstruktor bezparametrowy dla DI (bez TaskScheduler)
        public LoggerService()
        {
            _taskScheduler = null;
            var logDir = "logs";
            if (!Directory.Exists(logDir))
            {
                Directory.CreateDirectory(logDir);
            }
            _logFilePath = Path.Combine(logDir, $"shopbot_{DateTime.Now:yyyy-MM-dd}.log");
        }

        public void Log(string message, LogLevel level = LogLevel.Info)
        {
            var appSettings = AppSettings.Instance;
            var timestamp = DateTime.Now.ToString(appSettings.Logging.Console.TimestampFormat);

            // Sprawdź czy logować na podstawie poziomu
            if (!ShouldLog(level))
            {
                return;
            }

            var formattedMessage = $"[{timestamp}] [{level}] {message}";

            // Dodaj status zadań tylko dla poziomów Debug i przy włączonym szczegółowym loggingu
            if (level == LogLevel.Debug && appSettings.Features.EnableDetailedLogging)
            {
                var status = GetTaskStatus();
                formattedMessage += $"\n{status}";
            }

            Console.WriteLine(formattedMessage);

            lock (_lock)
            {
                File.AppendAllText(_logFilePath, formattedMessage + "\n");
            }
        }

        private bool ShouldLog(LogLevel level)
        {
            var appSettings = AppSettings.Instance;
            var configuredLevel = ParseLogLevel(appSettings.Logging.LogLevel.ShopBot);
            return level >= configuredLevel;
        }

        private LogLevel ParseLogLevel(string levelString)
        {
            return levelString.ToLower() switch
            {
                "debug" => LogLevel.Debug,
                "information" => LogLevel.Info,
                "warning" => LogLevel.Warning,
                "error" => LogLevel.Error,
                _ => LogLevel.Info
            };
        }

        private string GetTaskStatus()
        {
            if (_taskScheduler == null) return string.Empty;
            
            var (activeWorkers, queueLength, activeSyncWorkers, syncQueueLength) = _taskScheduler.GetStatus();
            
            return $"=== Stan zadań ===\n" +
                   $"Aktywni workerzy: {activeWorkers}\n" +
                   $"Zadania w kolejce: {queueLength}\n" +
                   $"Aktywne zadania synchronizacji: {activeSyncWorkers}\n" +
                   $"Zadania synchronizacji w kolejce: {syncQueueLength}\n" +
                   "================";
        }

        public enum LogLevel
        {
            Debug,
            Info,
            Warning,
            Error
        }
    }
} 